include: # For Request Deploy To Production
  - project: 'devops/gitlab-ci-templates'
    ref: master
    file: "/common/libs-next-gen.yml"
image: asia.gcr.io/vinid-devops/k8s-deployer:latest

stages:
  - build jar
  - docker build
  - tag
  - deploy
  - request deploy to production

.before_script: &before_script |
  source ./k8s/before_script.sh

before_script:
  - *before_script

variables:
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA
  MAVEN_CLI_OPTS: "-s maven/settings.xml --batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"

docker build:
  stage: docker build
  only:
     - master
     - develop
     - /^release/
     - /^hotfix/
  script:
    - authen-gcr-pusher
    - gcloud builds submit --project=vinid-devops --tag asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG}

deploy-to-dev:
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-dev/
  variables:
    ENVIRONMENT: "dev"
    NAMESPACE: "oneloyalty-dev"
    SERVICE_PATH: "/oneloyalty-ops"
    HOST: "api-dev.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-qc:
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - develop
    - /^v.*-qc/
  variables:
    ENVIRONMENT: "qc"
    NAMESPACE: "oneloyalty-qc"
    SERVICE_PATH: "/oneloyalty-ops"
    HOST: "api-qc.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-uat:
  stage: deploy
  when: manual
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-uat/
  variables:
    ENVIRONMENT: "uat"
    NAMESPACE: "oneloyalty-uat"
    SERVICE_PATH: "/oneloyalty-ops"
    HOST: "api-uat.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-staging:
  stage: deploy
  when: manual
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-staging/
  variables:
    ENVIRONMENT: "staging"
    NAMESPACE: "oneloyalty-staging"
    SERVICE_PATH: "/oneloyalty-ops"
    HOST: "api-staging.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

tag:
  stage: tag
  only:
    - tags
  script:
    - authen-gcr-pusher
    - gcloud container images add-tag asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG} asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${CI_COMMIT_TAG} --quiet

request to prod:
  extends: .request_to_prod
  only:
    - /^v.*\d+$/
  when: manual
