FROM openjdk:11-jdk
EXPOSE 8080

ENV JVM_XMS 1024m
ENV JVM_XMX 1024m

RUN apt-get update && apt-get install -y \
  curl \
  unzip \
  && rm -rf /var/lib/apt/lists/*

RUN mkdir -p monitor \
  && curl -o /monitor/newrelic.zip https://download.newrelic.com/newrelic/java-agent/newrelic-agent/current/newrelic-java.zip \
  && unzip '/monitor/newrelic.zip' -d /monitor/ && rm /monitor/newrelic.zip || true \;

COPY newrelic/newrelic.yml /monitor
COPY bin/entrypoint.sh /entrypoint.sh
COPY src/main/resources/* /conf/
COPY compiled/app.jar /app.jar

RUN chmod a+x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]