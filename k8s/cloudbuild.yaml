steps:
- name: 'gcr.io/cloud-builders/docker'
  args: [
          'build',
          '-t', 'asia.gcr.io/vinid-devops/${_CI_PROJECT_NAME}:${_IMAGE_TAG}',
          '--build-arg', 'IMAGE_TAG=${_IMAGE_TAG}',
          '--build-arg', 'COMMIT_TAG=${_COMMIT_TAG}',
          '--build-arg', 'JOB_ID=${_JOB_ID}',
          '--cache-from', 'asia.gcr.io/vinid-devops/${_CI_PROJECT_NAME}:latest',
          '.']
  id: build-service-image
- name: 'gcr.io/cloud-builders/docker'
  args: [
      "push",
      "asia.gcr.io/vinid-devops/${_CI_PROJECT_NAME}:${_IMAGE_TAG}"
  ]
  waitFor: ['build-service-image']

timeout: 1200s
options:
  machineType: 'N1_HIGHCPU_8'