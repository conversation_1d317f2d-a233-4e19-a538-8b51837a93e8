apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  PROFILE: "uat"
  APP_MODE: "MODE_UAT"
  APP_ENVIRONMENT: "uat"
  MEMBER_SERVICE_URL: "https://api-uat.int.vinid.dev/oneloyalty-member"
  MAKER_CHECKER_URL_CHANGE: "https://api-uat.int.vinid.dev/makerchecker/v1/changes"

  CARD_SERVICE_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-card"
  VOUCHER_SERVICE_URL: "https://api-uat.vinid.dev/onevc/tcb-vsm-ops"
  CARD_SERVICE_URL: "https://api-uat.int.vinid.dev/oneloyalty-card/v1"
  ONELOYALTY_SERVICE_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-service/v1"
  MAKER_CHECKER_URL: "https://api-uat.int.vinid.dev/makerchecker/v1"
  MAKER_CHECKER_INTERNAL_URL: "http://oneloyalty-makerchecker/oneloyalty-makerchecker/v1"
  ONELOYALTY_MASTER_WORKER_BASE_URL: "http://oneloyalty-mw-master/oneloyalty-mw-service/v1"
  DEFAULT_SCHEMA_ORACLE: "OLOYALTY"
  SAP_BASE_URL: "https://api-sap1mg-dev.vinid.dev/RESTAdapter/salesorder"
  ES_ENDPOINTS: "es.int.onemount.dev:443"
  ES_INDEX_ACTIVITY: "db-oneloyalty-transaction-history-uat"
  OPS_AUTHEN_URL: https://api-uat.int.vinid.dev/admin/v1
  VD_CARD_PREFIX_CODES: "6666"
  KAFKA_BOOTSTRAP_ADDRESS: "confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093, confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093"
  KAFKA_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  KAFKA_TOPIC_NAME_COMMON_EVENTS: "oneloyalty-common-events-uat"
  KAFKA_SSL_ENABLE: "true"
  SFTP_SESSION_TIMEOUT: "15000"
  SFTP_CHANNEL_TIMEOUT: "15000"
  CPR_SFTP_HOST: "**************"
  CPR_SFTP_PORT: "2022"
  CPR_SFTP_REMOTE_FOLDER: "/reconcile/oneloyalty-ops/uat/member_card"
  ONELOYALTY_INTEGRATION_OPS: https://api-uat.int.vinid.dev/oneloyalty-ops-integration
  VD_CARD_TYPE_CODES: "101"
  VGC_BUSINESS_CODE: "VGC"
  TCB_CORPORATION_CODE: "TCBLOYALTY"
  TCB_BATCH_ADJ_TXN_BUCKET_DIR: "loyalty/ops/uat/tcb-batch-adj-txn"
  GCP_STORAGE_OPS_BUCKET_NAME: "vinid-loyalty-ops-internal-np"
  GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY: "/gcp/storage/gcp-storage-key.json"
  ES_SCHEME_MANAGEMENT_INDEX: "db-oneloyalty-scheme-management-uat"
  ES_TRANSACTION_HISTORY_INDEX: "db-oneloyalty-transaction-history-uat"
  VERSIONING_CURRENT_FORMAT: "v.%d(current)"
  VERSIONING_BASIC_FORMAT: "v.%d"
  GCT_DEFAULT_SEND_SMS_PASSWORD_TIME_SEC: "60"
  OAUTH2_URL: "https://oauth-uat.vinid.dev"
  NOTIFICATION_CENTER_URL: "https://api-uat.int.vinid.dev/notification"
  NOTIFICATION_CENTER_TEMPLATE_ID_PASSWORD_FOLDER: "72d4eefe-a1d3-4cd5-8e07-e9a285ef2d89"
  OPS_EXCEL_ADJUSTMENT_MAX_RECORD: "100000"
  ENCRYPTION_AES_SECRET_KEY: "n2r5u8x/A?D(G+Kb"
  ONELOYALTY_RULES_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-rules"
  KAFKA_RESET_RULE_TOPIC: "oneloyalty-reset-rule-uat"
  KAFKA_RESET_SCHEME_RULE_TOPIC: "oneloyalty-scheme-rule-uat"
  ONELOYALTY_SCHEME_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-scheme"
  APP_BATCH_ADJUST_MAX_RECORD: "100000"
  CPM_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-cpm-service"
  LOC_BASE_URL: "https://api-uat.int.vinid.dev/loyalty/loc"
  RETRY_FILE_BASE_URL: "http://ps2-retry-file-worker/ps2-retry-file-worker/v1"

  CPM_JDBC_PASSWORD: "YjmHEqky9dGiRHyOOYS7TQ"
  CPM_JDBC_URL: "jdbc:mysql://***********:3306/oneloyalty_cpm_uat?autoReconnect=true&useUnicode=yes&characterEncoding=UTF-8&useSSL=false"
  CPM_JDBC_USERNAME: "oneloyalty_cpm_uat"
  LOC_JDBC_PASSWORD: "W2Edgcr9GR7UOstwwzk2"
  LOC_JDBC_URL: "jdbc:mysql://***********:3306/oneloyalty_checkout_uat?autoReconnect=true&useUnicode=yes&characterEncoding=UTF-8&useSSL=false"
  LOC_JDBC_USERNAME: "oneloyalty_checkout_uat"
  LOC_BASIC_AUTH: "loc_key:KtHb|}wLA]:YC+s:FJVW"
  REDIS_HOST: "*************"
  REDIS_DB: "10"
  REDIS_RESET_RULE_TOPIC: "oneloyalty-reset-rule-uat"
  APP_BUSINESS_TCB: "TCB"
  APP_BUSINESS_VGC: "VGC"
  APP_PROGRAM_TCB: "TCBC"
  APP_PROGRAM_VGC: "VGC"
