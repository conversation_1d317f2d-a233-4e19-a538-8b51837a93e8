apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  replicas: 1
  progressDeadlineSeconds: 400
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      name: ${CI_PROJECT_NAME}
  template:
    metadata:
      name: ${CI_PROJECT_NAME}
      labels:
        name: ${CI_PROJECT_NAME}
    spec:
      containers:
        - name: ${CI_PROJECT_NAME}
          image: asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG}
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - configMapRef:
                name: ${CI_PROJECT_NAME}
            - secretRef:
                name: ${CI_PROJECT_NAME}
          volumeMounts:
            - name: oneloyalty-kafka-cert
              mountPath: "/kafka"
              readOnly: true
            - name: gcp-storage-key
              mountPath: "/gcp/storage"
              readOnly: true
      volumes:
        - name: oneloyalty-kafka-cert
          secret:
            secretName: oneloyalty-kafka-cert
        - name: gcp-storage-key
          secret:
            secretName: gcp-storage-key
      restartPolicy: Always
      imagePullSecrets:
        - name: docker-image-pull-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    name: ${CI_PROJECT_NAME}
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  ports:
    - name: http
      port: 80
      targetPort: 8080
  selector:
    name: ${CI_PROJECT_NAME}
  type: ClusterIP
---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  scaleTargetRef:
    apiVersion: extensions/v1beta1
    kind: Deployment
    name: ${CI_PROJECT_NAME}
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,X-LANG,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,X-Api-Key,X-Device-Id,Access-Control-Allow-Origin,Authorization,TimezoneOffset"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "2m"
  name: ${CI_PROJECT_NAME}
spec:
  rules:
    - host: "$HOST"
      http:
        paths:
          - pathType: Prefix
            path: ${SERVICE_PATH}
            backend:
              service:
                name: ${CI_PROJECT_NAME}
                port:
                  number: 80