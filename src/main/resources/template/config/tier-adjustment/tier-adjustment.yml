clzName: TierAdjustmentExportEntry
elements:
  - entries:
      - { index: 0, propertyName: id, type: Integer }
      - { index: 1, propertyName: businessCode, type: String }
      - { index: 2, propertyName: programCode, type: String }
      - { index: 3, propertyName: memberCode, type: String }
      - { index: 4, propertyName: idType, type: String }
      - { index: 5, propertyName: idNo, type: String }
      - { index: 6, propertyName: initialTier, type: String }
      - { index: 7, propertyName: adjustmentTier, type: String }
      - { index: 8, propertyName: reason, type: String }
      - { index: 9, propertyName: description, type: String }
      - { index: 10, propertyName: updatedBy, type: String }
      - { index: 11, propertyName: status, type: String }
    sheetName: Tier Adjustment
