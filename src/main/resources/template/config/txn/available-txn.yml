clzName: TransactionExportEntry
elements:
  - entries:
      - { index: 0, propertyName: txnRefNo, type: String }
      - { index: 1, propertyName: invoiceNo, type: String }
      - { index: 2, propertyName: originalInvoiceNo, type: String }
      - { index: 3, propertyName: businessCode, type: String }
      - { index: 4, propertyName: programCode, type: String }
      - { index: 5, propertyName: corporationCode, type: String }
      - { index: 6, propertyName: chainCode, type: String }
      - { index: 7, propertyName: storeCode, type: String }
      - { index: 8, propertyName: terminalCode, type: String }
      - { index: 9, propertyName: memberCode, type: String }
      - { index: 10, propertyName: productAccountType, type: String }
      - { index: 11, propertyName: productAccountCode, type: String }
      - { index: 12, propertyName: transactionTime, type: String }
      - { index: 13, propertyName: transactionType, type: String }
      - { index: 14, propertyName: gmv, type: Long }
      - { index: 15, propertyName: grossAmount, type: Long }
      - { index: 16, propertyName: nettAmount, type: Long }
      - { index: 17, propertyName: awardPoint, type: Long }
      - { index: 18, propertyName: redeemPoint, type: Long }
      - { index: 19, propertyName: poolCode, type: String }
      - { index: 20, propertyName: currencyCode, type: String }
      - { index: 21, propertyName: pointBalanceBefore, type: Long }
      - { index: 22, propertyName: pointBalanceAfter, type: Long }
      - { index: 23, propertyName: awardPointBeforeLimit, type: Long }
      - { index: 24, propertyName: awardRetentionTime, type: String }
      - { index: 25, propertyName: description, type: String }
      - { index: 26, propertyName: cancellation, type: String }
      - { index: 27, propertyName: cancellationTime, type: String }
      - { index: 28, propertyName: cancellationType, type: String }
      - { index: 29, propertyName: reasonCode, type: String }
      - { index: 30, propertyName: schemeCode, type: String }
      - { index: 31, propertyName: channel, type: String }
      - { index: 32, propertyName: serviceCode, type: String }
      - { index: 33, propertyName: status, type: String }
      - { index: 34, propertyName: errorCode, type: String }
      - { index: 35, propertyName: errorMessage, type: String }
    sheetName: Transaction

