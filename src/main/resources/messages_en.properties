message_200=Success
message_400=Bad request
message_401=Unauthorized
message_404=Resource not found
message_500=Internal server error

message_4040101=Business not found
message_4040102=Business not active
message_4040103=Corporation not found
message_4040104=Corporation not active
message_4040105=Chain not found
message_4040106=Chain not active
message_4040107=Store not found
message_4040108=Terminal not found
message_4040109=Terminal not active
message_4040110=Program not found
message_4040111=Program not active
message_4040112=Member not found
message_4040113=Member not active
message_4040114=Member product account not active
message_4040115=Member product account not found
message_4040116=Member product account code already existed
message_4040117=program tier not found
message_4040118=Program tier not active
message_4040119=Program tier code existed
message_4040120=Store not active
message_4040121=Program accumulation type not found
message_4040122=Currency not found
message_4040123=Currency rate not found
message_4040124=Base Currency is the same Currency
message_4040125=Base currency not found
message_4040126=Program corporation not found
message_4040127=Corporation not in business
message_4040128=User profile not found
message_4040129=User profile not active
message_4040130=Pool not found
message_4040300=Schema not found
message_4040301=Schema not active
message_4090104=Program corporation existed
message_4090105=Currency existed
message_4090106=Currency rate existed
message_4090107=Pool existed
message_4000101=Cannot add multi corporation into multi program
message_4000102=Id type not valid
message_5000002=System call database error
message_5000101=System call schema engine error
message_5000201=System call core service error
# 409XXXX
message_4090101=Member already existed
message_4090102=Program code existed
message_4090103=Program tier rank no existed in program
message_5009003=Scheme Sequence is already requested for approval

#407XXX
message_4070120=Accumulation type existed

message_4040131=Pool not found
message_4040132=Business code existed
message_4040133=Business name existed
message_4040134=Business phone existed

message_4040135=Country is not found
message_4040136=Province is not found
message_4040137=District is not found
message_4040138=Ward is not found

message_4040140=Tier Rule Condition with id '%s' does not exist.
message_4040141=Duplicate values are not allowed. An existing attribute '%s' lead with the same value '%s' has been found.
message_4040142=Tier Rule Attribute '%s' does not valid.
message_4040143=Tier Rule Condition Operator '%s' does not valid. Tier Rule Attribute '%s' is only supported for '%s'.
message_4040144=Tier Rule Attribute Data Type '%s' is not supported.
message_4040145=Tier Rule Condition value '%s' does not valid for data type '%s'.

message_4041322=Attribute not found
message_4041323=Operator not found
message_4040304=Formula not found
message_4090112=Scheme code existed
message_4000103=Redeem amount must less gross amout
message_4000104=Cannot create multi condition into multi rule
message_4000105=Pool not in program
message_4000106=Start date is less end date
message_4000107=Condition not in rule
message_4000108=Scheme cannot update
message_4040303=Scheme rule not found
message_5000202=Formula not in scheme


# specify for ops
message_4049101=Corporation name exist
message_4049102=Corporation phone exist
message_4049104=Store phone exist
message_4049105=Tax identification number exist
message_4041374=System attribute exist

message_4041347=Corporation name existed
message_4041349=Store name existed
message_4041350=Terminal name existed
message_4090117=Card bin existed
message_4090118=Card type exist
message_4090119=GiftCard bin exist
message_4090120=GiftCard type existed

message_4041337=Ward not belong district
message_4041336=District not belong province
message_4041332=Card production request not found
message_4041342=GiftCard type not found
message_4041326=Card bin not found
message_4041343=GiftCard type not active
message_4041344=GiftCard bin not found
message_4041345=GiftCard bin not active
message_4041324=Card type not found
message_4041325=Card type not active
message_4041327=Card bin not active
message_40001128=Card has been closed
message_40001129=Code block is not matched with status
message_40001130=Status update is not matched by definition

message_4090110=Member is already registered at program
message_4090121=Identify no is already existed
message_4041358=Card policy type incorrect
message_4000600=Transaction not found
message_4000601=Card policy not matched
message_4000115=Over max card per request

message_4041340=Gift card request not found
message_4041360=Can not update gift card init status
message_4041366=Checklog not found
message_4041359=Gift card request is in progress
message_4040305=Card not active
message_4099103=new_txn_info_for_revert_not_found
message_4041339=Gift card is not active

message_4040306=Unsupported having multiple base currency rates for transaction '%s'

message_4041361=Can not accept to change gift card status
message_4030100=Access denied

message_4041330=CardNo not found
message_4041331=CardTMP not pending
message_4090114=Chain has already existed
message_4041348=Chain name has already existed
message_4049103=Chain phone has already existed
message_4090113=Corporation has already existed
message_4090115= Store has already existed
message_4090116=Terminal existed
message_40001135=Reason Code not found
message_40001136=Reason Code not active
message_4099104=Adjust point must greater than zero
message_5009002=Reason Code is not valid
message_4000605=Scheme Sequence not found
message_4000606=Scheme Sequence not active
message_4000607=Scheme Sequence is already requested for approval
message_4000608=Scheme Sequence setting payload is invalid. The given schemes do not matched with the available schemes
message_4000610=Can not found pending change request of Scheme Sequence id '%s' 
message_4000602=Operator id is existed
#message_4000605=Request failed need to retry
message_4000618=Counter not found
message_4000619=Counter not active
message_4000620=Code '%s' is being used
message_4000621=Start date is invalid
message_4000622=End date is invalid
message_4000623=Counter request not found
message_4000624=Counter request not active
message_4000625=Counter request is already requested for approval
message_4000626=Can not edit rejected request
message_4000628=Rule attribute '%s' does not exist
message_4000630=Counter '%s' is being used by another Limitation
message_4000629=Start date is invalid
message_4000631=End date is invalid
message_4000632=The previous version can not be edited
message_4000603=Operator is waiting for approve
message_4000636=Code '%s' is being used
message_4000637=Tier Policy '%s' is invalid
message_4000638=Tier not found
message_4000639=Tier request is already requested for approval
message_4000640=Can not edit rejected request
message_4000642=Tier Privilege not found
message_4000643=Tier policy request not found
message_4000644=Tier policy code existed
message_4000645=Tier policy request already requested
message_4000646=Tier policy request cannot be edit
message_4000647=Tier policy request previous version can not be edited
message_4000648=Only one Tier Policy is applied for a Loyalty Program at a time
message_4000649=Operator request cannot be edit
message_4000609=Request failed need retry
message_4000611=Service not found
message_4000612=Request not found
message_4000613=Rule not found
message_4000614=Condition not found
message_4000616=Limitation request not found
message_4000617=Rule condition not found
message_4000627=Limitation code existed
message_4000633=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4000634=Limitation request can not be edited
message_4000635=Limitation request previous version can not be edited
message_4000650=Service type '%s' is not available for Counter
message_4000651=Cannot change service type of counter when being linked to another service type
message_4000652=Counter level '%s' is not available for service type '%s'
message_4000653=Cannot deactivate counter when being linked to another service type
message_4000654=Partner supplier id '%s' is not valid
message_4000655=The given tier privilege's payload is invalid format
message_4000656=The given rule condition's payload is invalid format
message_4000641=The given rule's payload is invalid format
message_4049106=The rule condition's value is not blank
message_4049107=The rule condition is not empty
message_4000657=Gift card production request is already requested
message_4000658=Gift card production request cannot be edited
message_4000659=Gift card production request previous version cannot be edited
message_4000660=The corporation is not supported this feature
message_4000663=Invalid file format. The size of columns must be '%s'
message_4000664=Invalid file format. The size of rows must less than or equal to '%s'
message_4000661=The payload must not be null
message_4000662=At row number '%s', cell number '%s' content must not be blank
message_4000665=The batch adjust transaction is not found
message_4000666=The batch file extension is invalid format
message_4000667=The batch file record is empty
message_4000668=Voucher code '%s' not found
message_4000669=Benefit not found
message_4000670=Code '%s' is being used
message_4000671=Start date is invalid
message_4000672=End date is invalid
message_4000673=The benefit is already requested for approval
message_4000674=Can not edit rejected request
message_4000675=The previous version can not be edited
message_4000676=Invalid benefit reward payload
message_4000677=Tier adjustment request not found
message_4000678=Member already existed tier adjustment request
message_4000679=Member card changed status request not found
message_4000680=This member card cannot be blocked as it has been assigned to a Member
message_4000681=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4000682=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4000683=Single gift card request not found
message_4000702=Required parameter %s
message_4100001=Service Type for %s has been set up
message_4100002=Program attribute service type is not configured
message_4100003=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4100004=Program attribute service type request not found
message_4100005=System attribute code '%s' not found
message_4100006=Program attribute code '%s' not found
message_4100007=Member attribute code '%s' not found
message_4100008=Attribute '%s' has been used and cannot be unselected
message_4100009=Can not edit rejected request
message_4100010=The previous version can not be edited
message_4100011=Transaction attribute not found
message_4100053=Program transaction attribute not valid
message_4100054=Program transaction attribute code existed
message_4100055=Attribute master data value not valid
message_4100056=Program attribute code existed
message_4100057=Attribute master data value duplicated
message_4100058=Attribute master data status not valid
message_4200001=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4200002=Can not edit rejected request
message_4200003=The previous version can not be edited
message_4200004=The attribute request not found
message_4200005=The syntax of Regex validation is invalid.
message_4200006=The data type '%s' is not available for this data type display
message_4200007=The operator '%s' is not available for this data type display
message_4200008=You cannot remove the operator is '%s' from old request
message_4200009=The attribute '%s' existed with attribute type is '%s'.
message_4100012=Tier Mapping code existed
message_4100013=Tier Mapping request not found
message_4100014=There is a pending request and waiting for process to finish by checker role, you cannot change information on it
message_4100015=Can not edit rejected request
message_4100016=The previous version can not be edited
message_4100018=Tier Mapping Config Request Id '%s' not found
message_4100019=Base tier id '%s' is invalid
message_4100020=Matched tier id '%s' is invalid
message_4100021=The configuration 's status must be '%s' for empty mapping matched tier id
message_4100022=The tier id '%s' is required for configuration
message_4100023=Duplicated tier id '%s' mapping for configuration
message_4100024=Cannot update 'start_date' when Tier Matching has been started
message_4100025='start_date' must be greater than current time
message_4200010=Attribute '%s' value is invalid with validation format '%s'
message_4200011=Attribute '%s' value is invalid with number format
message_4200012=Attribute '%s' value is invalid with datetime format
message_4200013=Attribute '%s' value is invalid with date format '%'
message_4100026=Request change id is not found
message_4100027=Request change id is duplicated
message_4100028=Maker checker cannot change status is reject
message_4100029=Maker checker cannot change status
message_4040314=Program Level not found
message_40001137=Function code not found
message_40001138=Function code not active
message_4100030=Program refer invalid
message_4100031=The previous version can not be edited
message_4100032=There was a configuration for this program, you cannot create a new request, please do editing if you want to change the configuration
message_4100033=Program function profile attribute invalid
message_4000691=Limitation is not enable reset counter value
message_4000692=Limitation not found
message_4000693=Counter history not found
message_4000694=The Reset value is greater than limit threshold
message_4000695=The counter period is not support to reset value
message_4000696=Limitation is not active
message_4000697=Tier adjustment batch not found
message_4000698=Tier adjustment batch not active
message_4000701=Tier adjustment batch request is not completed
message_4000703=Transaction batch request not found
message_4000704=Transaction batch request not active
message_4000705=Transaction request not found
message_4000706=Transaction request not active
message_4040315=Program Level have duplicated rank
message_4100034=Maker checker request id not found
message_4100035=Maker checker request type not match
message_4100036=Program is not configured
message_4100037=Counter type invalid attribute
message_4040312=Program tier policy not found
message_4100038=Request code not found
message_4100039=Request status must be pending
message_40001148=Rule code '%s' is duplicated
message_4100040=Recipient code existed
message_4100041=Recipient not found
message_4100044=Tier adjustment approval status not valid
message_4100045=Tier adjustment process status not valid
message_4100046=Invalid length invoice no
message_4100047=Invalid invoice no pattern
message_4100048=Tier adjustment completely finished
message_4100049=Transaction request approval status not valid
message_4100050=Transaction request process status not valid
message_4100051=Transaction request completely finished
message_4100059=Transaction attribute not active
message_4100076=Member status not found
message_4100081=Member status code existed
message_4100082=Member status vi name existed
message_4100083=Member status en name existed
message_4100084=Already exist default status
message_4100086=Member status already use
message_4100088=Member function not found
message_4100092=Member status transition existed
message_4100093=Member status transition not found
message_4100094=Beginning status of member status transition existed
message_4100095=Beginning status of member status transition not found
message_4100096=Destination status of member status transition not found
message_4100098=Rule and condition of member status transition cannot null
message_4000709=%s
message_4000710=Already have SAP sale order
message_4000711=Not have SAP sale order
message_4000712=SAP sale order not found
message_4000722=Reset rule failed
message_4041368=This batch does not exist
message_4041369=Gift card transfer does not exist
message_4041370=Gift card transfer not active
message_4041371=The following batch no '%s' are not existed in SFTP folder
message_4100043=Missing sell order (SO) in description of first gift card production request
message_4041372=Next tier and current tier are same
message_4041373=Identification Type not found
message_5000503=Call SAP error
message_5009004=Reset value must be lower than counter accumulation
message_5009005=Total counted must be lower than limit threshold
message_4000731=Control file not found
message_4000732=Cannot mark ignored
message_4040323=Original File Not Found
message_4040324=Retry file request not found
message_4040325=Control file status must be SUCCESS
message_4040326=Control file doesn't have failed transactions
message_4040327=This file is processing retry
