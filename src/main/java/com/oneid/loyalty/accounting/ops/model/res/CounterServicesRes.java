package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;
import java.util.Date;

import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class CounterServicesRes {

    private Integer id;

    private String name;

    private String code;

    private ECommonStatus status;

    private BigDecimal maximum;

    private Date startDate;

    private Date endDate;

    public static CounterServicesRes valueOf(ProgramTierPolicy programTierPolicy, Limitation limitation){
        if (limitation != null){
            return CounterServicesRes.builder()
            .id(limitation.getId())
            .name(limitation.getName())
            .code(limitation.getCode())
            .maximum(limitation.getThreshold())
            .status(limitation.getStatus())
            .startDate(limitation.getStartDate())
            .endDate(limitation.getEndDate())
            .build();
        }

        return CounterServicesRes.builder()
        .id(programTierPolicy.getId())
        .name(programTierPolicy.getName())
        .code(programTierPolicy.getCode())
        .status(programTierPolicy.getStatus())
        .startDate(programTierPolicy.getStartDate())
        .endDate(programTierPolicy.getEndDate())
        .build();
    }
}
