package com.oneid.loyalty.accounting.ops.model.req;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.*;

import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CreateLimitationReq.CreateLimitationReqBuilder.class)
public class CreateLimitationReq {

    private Integer limitId;

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @Length(max = 255)
    private String editKey;

    @NotBlank(message = "'code' must not be blank")
    @Length(max = 100)
    @OpsCode
    private String code;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    @OpsName
    private String name;

    @OpsName
    @Length(max = 512)
    private String description;

    @Length(max = 512)
    private String newDescriptionCounter;

    @NotNull(message = "'start_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date startDate;

    @NotNull(message = "'end_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date endDate;

    @NotBlank(message = "status must not blank")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;

    @NotNull(message = "'counter_id' must not be null")
    private Integer counterId;

    @NotBlank(message = "'allow_reset_counter' must not blank")
    @Pattern(regexp = "^(Y|N)?$", message = "'allow_reset_counter' Only accept Y/N values")
    private String allowResetCounter;

    @NotNull(message = "'threshold' must not be null")
    private BigDecimal threshold;

    private BigDecimal warningThreshold;

    @NotBlank(message = "'allow_with_remaining_value' must not blank")
    @Pattern(regexp = "^(Y|N)?$", message = "'allow_with_remaining_value' Only accept Y/N values")
    private String allowWithRemainingValue;

    @Valid
    @NotNull(message = "'rules' must not be null")
    @Size(min = 1, max = 100, message = "'rules' size between 1, 100")
    private List<RuleReq> rules;

    private String resetType;
    private BigDecimal resetCounterValue;

    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateLimitationReqBuilder {
    }

    @AssertTrue(message = "'Warning threshold' must be less than threshold")
    public boolean isValidWarningThreshold() {

        if (warningThreshold != null && threshold.compareTo(warningThreshold)<=0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
