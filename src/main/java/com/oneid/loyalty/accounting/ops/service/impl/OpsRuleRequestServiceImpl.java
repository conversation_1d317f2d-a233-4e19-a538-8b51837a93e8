package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleConditionReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleConditionRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.entity.RuleConditionRequest;
import com.oneid.oneloyalty.common.entity.RuleRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.RuleConditionRequestRepository;
import com.oneid.oneloyalty.common.repository.RuleRepository;
import com.oneid.oneloyalty.common.repository.RuleRequestRepository;
import com.oneid.oneloyalty.common.service.RuleConditionService;
import com.oneid.oneloyalty.common.service.RuleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsRuleRequestServiceImpl implements OpsRuleRequestService {

    @Autowired
    private RuleRequestRepository ruleRequestRepository;

    @Autowired
    private RuleConditionRequestRepository ruleConditionRequestRepository;

    @Autowired
    ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Autowired
    private AttributeValueFactory attributeValueFactory;

    @Autowired
    private OpsConditionService opsConditionService;

    @Autowired
    private RuleRepository ruleRepository;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private RuleConditionService ruleConditionService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Override
    public void validateRules(
            Integer programId,
            EServiceType serviceType,
            List<RuleReq> rulesReq
    ) {
        Map<String, ConditionAttributeDto> attributeMap = getAttributeMap(programId);

        // Validate rules request
        for (RuleReq ruleReq : rulesReq) {
            validateRule(programId, serviceType, attributeMap, ruleReq);
        }
    }

    private void validateRule(
            Integer programId,
            EServiceType serviceType,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleReq ruleReq
    ) {
        if (!ruleReq.isArchive() && CollectionUtils.isEmpty(ruleReq.getConditions())) {
            throw new BusinessException(
                    OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(),
                    "Rule condition must not empty",
                    null
            );
        }

        // Validate rule code in case create new
        if (ruleReq.getCode() != null && ruleReq.getId() == null) {
            Rule rule = ruleService.findByProgramIdAndCodeAndServiceType(
                    programId,
                    serviceType.getValue(),
                    ruleReq.getCode()
            );
            if (rule != null) {
                throw new BusinessException(
                        ErrorCode.DUPLICATE_RULE_CODE,
                        "Rule code is duplicate",
                        null,
                        new Object[]{ruleReq.getCode()}
                );
            }
        }

        // Validate rule condition
        ruleReq.getConditions().forEach(e -> validateRuleCondition(programId, attributeMap, e));
    }

    private void validateRuleCondition(
            Integer programId,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleConditionReq ruleConditionReq
    ) {
        if (!ruleConditionReq.getArchive()) {
            ConditionAttributeDto conditionAttribute = attributeMap.get(ruleConditionReq.getAttribute());
            if (conditionAttribute == null) {
                throw new BusinessException(
                        ErrorCode.RULE_ATTRIBUTE_NOT_FOUND,
                        "Rule condition attribute is not found",
                        null,
                        new Object[]{ruleConditionReq.getAttribute()}
                );
            }
            ruleConditionReq.setDataTypeDisplay(conditionAttribute.getDataTypeDisplay());

            // Validate rule condition value
            AttributeValueStrategy<?> valueStrategy = attributeValueFactory.lookup(conditionAttribute);
            String conditionValue = valueStrategy.getWriteValue(
                    ruleConditionReq.getAttribute(),
                    ruleConditionReq.getOperator(),
                    ruleConditionReq.getValue(),
                    programId
            );
            if (conditionValue == null) {
                throw new BusinessException(
                        OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(),
                        "Rule condition value must not empty",
                        null
                );
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void createRuleRequest(Integer requestId, Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs) {

        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, AttributeValueStrategy<?>> map =
                availableProgramAttributes.stream()
                        .collect(Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
                        ));

        ruleReqs.stream()
                .forEach(rule -> {
                    if (CollectionUtils.isEmpty(rule.getConditions()))
                        throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);

                    RuleRequest ruleRequest = new RuleRequest();

                    ruleRequest.setRequestId(requestId);
                    ruleRequest.setActionType(ECommonActionType.Create);
                    ruleRequest.setChangedRuleLogic(rule.getConditionLogic());
                    ruleRequest.setServiceType(serviceType);
                    ruleRequest.setStatus(ECommonStatus.ACTIVE);
                    ruleRequest.setStartDate(rule.getStartDate());
                    ruleRequest.setEndDate(rule.getEndDate());
                    ruleRequest.setName(rule.getName());

                    ruleRequestRepository.save(ruleRequest);

                    List<RuleConditionRequest> ruleConditionRequests = rule.getConditions()
                            .stream()
                            .map(condition -> {
                                if (!map.containsKey(condition.getAttribute()))
                                    throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});

                                if (condition.getValue() == null)
                                    throw new BusinessException(OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(), null, null);

                                String value = map.get(condition.getAttribute())
                                        .getWriteValue(
                                                condition.getAttribute(),
                                                condition.getOperator(),
                                                condition.getValue(),
                                                programId);

                                RuleConditionRequest ruleConditionRequest = new RuleConditionRequest();

                                ruleConditionRequest.setRuleRequestId(ruleRequest.getId());
                                ruleConditionRequest.setActionType(ECommonActionType.Create);
                                ruleConditionRequest.setAttribute(condition.getAttribute());
                                ruleConditionRequest.setOperator(condition.getOperator().getExpression());
                                ruleConditionRequest.setValue(value);
                                ruleConditionRequest.setStatus(ECommonStatus.ACTIVE);

                                return ruleConditionRequest;
                            })
                            .collect(Collectors.toList());

                    ruleConditionRequestRepository.saveAll(ruleConditionRequests);
                });
    }

    @Override
    @Transactional
    public List<ResetRuleReq> cuRules(Integer programId, EServiceType serviceType, String serviceCode, List<RuleReq> rulesReq) {

        // Validate request
        this.validateRules(programId, serviceType, rulesReq);

        Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(programId);
        Map<String, AttributeValueStrategy<?>> valueStrategyMap =
                attributes.stream()
                        .collect(Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
                        ));

        Map<String, ConditionAttributeDto> attributesMap =
                attributes.stream().collect(Collectors.toMap(ConditionAttributeDto::getAttribute, c -> c));

        // Create rule
        List<ResetRuleReq> rules = new ArrayList<>();
        for (RuleReq ruleReq : rulesReq) {
            ResetRuleReq rule = cuRule(programId, serviceType, serviceCode, attributesMap, valueStrategyMap, ruleReq);
            rules.add(rule);
        }
        return rules;
    }

    private ResetRuleReq cuRule(
            Integer programId,
            EServiceType serviceType,
            String serviceCode,
            Map<String, ConditionAttributeDto> attributeMap,
            Map<String, AttributeValueStrategy<?>> valueStrategyMap,
            RuleReq ruleReq
    ) {
        Rule rule;
        // ------------------ Update ------------------
        if (ruleReq.getId() != null) {
            rule = ruleService.findById(ruleReq.getId());
            // Case inactive rule
            if (ruleReq.isArchive()) {
                if (ECommonStatus.ACTIVE.equals(rule.getStatus())) {
                    rule.setStatus(ECommonStatus.INACTIVE);
                    ruleService.save(rule);
                }

                return null;
            }
        }
        // ------------------ Create ------------------
        else {
            rule = new Rule();
            rule.setProgramId(programId);
            rule.setServiceType(serviceType);
            rule.setServiceCode(serviceCode);
            String code = ruleReq.getCode();
            if (code == null) {
                code = String.format("%s_%s", serviceCode, UUID.randomUUID());
            }
            rule.setCode(code);
        }

        if (ruleReq.getId() == null || ruleReq.hasEdit(rule)) {
            rule.setName(ruleReq.getName());
            rule.setDescription(null);
            rule.setRuleLogic(ruleReq.getConditionLogic());
            rule.setStatus(ECommonStatus.ACTIVE);
            rule.setStartDate(ruleReq.getStartDate());
            rule.setEndDate(ruleReq.getEndDate());
            // TODO: Audit trace
            rule = ruleService.save(rule);
        }

        List<ResetRuleReq.ConditionReq> conditionReqs = new ArrayList<>();

        if (!CollectionUtils.isEmpty(ruleReq.getConditions())) {
            // Create rule conditions
            List<RuleCondition> ruleConditions = new ArrayList<>();

            for (RuleConditionReq ruleConditionReq : ruleReq.getConditions()) {
                RuleCondition ruleCondition =
                        createRuleCondition(programId, rule, attributeMap, valueStrategyMap, ruleConditionReq);
                if (ruleCondition != null) {
                    ruleConditions.add(ruleCondition);
                }
            }

            List<RuleCondition> conditions = ruleConditionService.saveAll(ruleConditions);

            for (RuleCondition condition : conditions) {
                conditionReqs.add(ResetRuleReq.buildConditionReq(condition));
            }
        }

        ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(rule);
        resetRuleReq.setConditions(conditionReqs);

        return resetRuleReq;
    }

    // TODO: Audit
    private RuleCondition createRuleCondition(
            Integer programId,
            Rule rule,
            Map<String, ConditionAttributeDto> attributeMap,
            Map<String, AttributeValueStrategy<?>> attributeValueStrategyMap,
            RuleConditionReq ruleConditionReq
    ) {
        RuleCondition ruleCondition;
        AttributeValueStrategy<?> valueStrategy = attributeValueStrategyMap.get(ruleConditionReq.getAttribute());
        String conditionValue = valueStrategy.getWriteValue(
                ruleConditionReq.getAttribute(),
                ruleConditionReq.getOperator(),
                ruleConditionReq.getValue(),
                programId
        );

        // --------------- Update -------
        if (ruleConditionReq.getId() != null) {
            ruleCondition = ruleConditionService.findById(ruleConditionReq.getId());
            // Inactive rule condition
            if (ruleConditionReq.getArchive()) {
                ruleCondition.setStatus(ECommonStatus.INACTIVE);
                return ruleCondition;
            }
            // No changed
            if (!ruleConditionReq.hasEdit(ruleCondition, conditionValue)) {
                return null;
            }
        } else { // Create
            ruleCondition = new RuleCondition();
            ruleCondition.setRuleId(rule.getId());
        }

        ConditionAttributeDto attribute = attributeMap.get(ruleConditionReq.getAttribute());

        ruleCondition.setAttribute(ruleConditionReq.getAttribute());
        ruleCondition.setOperator(ruleConditionReq.getOperator().getExpression());
        ruleCondition.setValue(conditionValue);
        ruleCondition.setDataType(attribute.getDataType());
        ruleCondition.setStatus(ECommonStatus.ACTIVE);

        return ruleCondition;
    }

    @Override
    public List<RuleRes> getRuleRequests(Integer requestId, Integer programId, EServiceType serviceType) {
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, ConditionAttributeDto> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> conditionAttributeDto
        ));

        List<RuleRequest> ruleRequests = ruleRequestRepository.findByRequestIdAndServiceType(requestId, serviceType);
        final Map<Integer, String> mapRuleIdToCode = ruleRepository.findAllById(
                ruleRequests.stream().map(RuleRequest::getRuleId).collect(Collectors.toList())
        ).stream().collect(Collectors.toMap(Rule::getId, Rule::getCode));

        final Map<Integer, String> mapRequestIdToCode = ruleRequests.stream()
                .filter(ruleRequest -> ruleRequest.getRuleId() != null)
                .collect(Collectors.toMap(
                        RuleRequest::getId,
                        ruleRequest -> mapRuleIdToCode.getOrDefault(ruleRequest.getRuleId(), null)
                ));
        return ruleRequests
                .stream()
                .filter(rule -> !rule.getActionType().equals(ECommonActionType.Delete))
                .map(rule -> {
                    List<RuleConditionRes> conditions = ruleConditionRequestRepository.findByRuleRequestId(rule.getId())
                            .stream()
                            .filter(condition -> !condition.getActionType().equals(ECommonActionType.Delete))
                            .map(condition -> {
                                EAttributeOperator operator = EAttributeOperator.lookup(condition.getOperator());
                                ConditionAttributeDto conditionAttributeDto = map.get(condition.getAttribute());
                                AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(conditionAttributeDto);
                                Object value = attributeValueStrategy.getReadValue(condition.getAttribute(), operator, condition.getValue(), programId);
                                return RuleConditionRes.builder()
                                        .id(condition.getId())
                                        .attribute(condition.getAttribute())
                                        .operator(operator)
                                        .dataTypeDisplay(conditionAttributeDto.getDataTypeDisplay())
                                        .value(value)
                                        .build();
                            })
                            .collect(Collectors.toList());

                    return RuleRes.builder()
                            .id(rule.getId())
                            .conditionLogic(rule.getChangedRuleLogic())
                            .conditions(conditions)
                            .name(rule.getName())
                            .startDate(rule.getStartDate())
                            .endDate(rule.getEndDate())
                            .code(mapRequestIdToCode.getOrDefault(rule.getId(), null))
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<RuleRes> getRules(Integer programId, EServiceType serviceType, String serviceCode) {
        List<Rule> rules = ruleService.findAll(programId, serviceType, serviceCode, ECommonStatus.ACTIVE);
        return rules.stream()
                .map(r -> {
                    List<RuleConditionRes> conditions = getRuleConditionsRes(r.getProgramId(), r.getId());
                    return RuleRes.builder()
                            .id(r.getId())
                            .conditionLogic(r.getRuleLogic())
                            .conditions(conditions)
                            .name(r.getName())
                            .startDate(r.getStartDate())
                            .endDate(r.getEndDate())
                            .code(r.getCode())
                            .build();
                })
                .collect(Collectors.toList());
    }

    private List<RuleConditionRes> getRuleConditionsRes(Integer programId, Integer ruleId) {
        List<RuleCondition> ruleConditions = ruleConditionService.findAll(ruleId, ECommonStatus.ACTIVE);
        Collection<ConditionAttributeDto> attributes = opsConditionService.conditionAttributeDtos(programId);
        Map<String, ConditionAttributeDto> attributeMap =
                attributes.stream()
                        .collect(Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> conditionAttributeDto
                        ));

        return ruleConditions.stream()
                .map(rc -> this.getRuleConditionRes(rc, programId, attributeMap))
                .collect(Collectors.toList());

    }

    private RuleConditionRes getRuleConditionRes(
            RuleCondition ruleCondition,
            Integer programId,
            Map<String, ConditionAttributeDto> attributeMap
    ) {
        EAttributeOperator operator = EAttributeOperator.lookup(ruleCondition.getOperator());
        ConditionAttributeDto conditionAttributeDto = attributeMap.get(ruleCondition.getAttribute());
        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(conditionAttributeDto);
        Object value = attributeValueStrategy.getReadValue(ruleCondition.getAttribute(), operator, ruleCondition.getValue(), programId);

        return RuleConditionRes.builder()
                .id(ruleCondition.getId())
                .attribute(ruleCondition.getAttribute())
                .operator(operator)
                .dataTypeDisplay(conditionAttributeDto.getDataTypeDisplay())
                .value(value)
                .build();
    }

    private Map<String, ConditionAttributeDto> getAttributeMap(Integer programId) {
        Collection<ConditionAttributeDto> attributes =
                opsConditionService.conditionAttributeDtos(programId);
        return attributes.stream()
                .collect(
                        Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> conditionAttributeDto
                        ));
    }

    @Override
    public void editRuleRequest(Integer availableRequestId, Integer newRequestId, Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs) {
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, AttributeValueStrategy<?>> map = availableProgramAttributes.stream().collect(
                Collectors.toMap(
                        ConditionAttributeDto::getAttribute,
                        conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
                )
        );

        Map<Integer, RuleRequest> availableRuleRequests = ruleRequestRepository.findByRequestIdAndServiceType(availableRequestId, serviceType)
                .stream()
                .filter(entity -> entity.getActionType() != ECommonActionType.Delete)
                .collect(Collectors.toMap(RuleRequest::getId, entity -> entity));

        long totalGivenUpdatedRules = ruleReqs
                .stream()
                .filter(rule -> rule.getId() != null)
                .count();

        if (totalGivenUpdatedRules != availableRuleRequests.size())
            throw new BusinessException(ErrorCode.INVALID_RULE_PAYLOAD, null, null);

        ruleReqs
                .stream()
                .forEach(ruleReq -> {
                    RuleRequest newRuleRequest = new RuleRequest();

                    newRuleRequest.setRequestId(newRequestId);
                    newRuleRequest.setChangedRuleLogic(ruleReq.getConditionLogic());
                    newRuleRequest.setServiceType(serviceType);
                    newRuleRequest.setStatus(ECommonStatus.ACTIVE);

                    Map<Integer, RuleConditionRequest> conditionRequests = new HashMap<>();

                    if (ruleReq.getId() != null) {
                        RuleRequest availableRuleRequest = availableRuleRequests.get(ruleReq.getId());

                        if (availableRuleRequest == null)
                            throw new BusinessException(ErrorCode.RULE_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});

                        newRuleRequest.setRuleId(availableRuleRequest.getRuleId());

                        if (ruleReq.isArchive()) {
                            newRuleRequest.setActionType(ECommonActionType.Delete);
                            newRuleRequest.setStatus(ECommonStatus.INACTIVE);
                        } else {
                            newRuleRequest.setName(ruleReq.getName());
                            newRuleRequest.setStartDate(ruleReq.getStartDate());
                            newRuleRequest.setEndDate(ruleReq.getEndDate());
                            newRuleRequest.setActionType(ECommonActionType.Update);
                        }

                        conditionRequests.putAll(ruleConditionRequestRepository.findByRuleRequestId(ruleReq.getId())
                                .stream()
                                .filter(entity -> entity.getActionType() != ECommonActionType.Delete)
                                .collect(Collectors.toMap(RuleConditionRequest::getId, entity -> entity)));
                    } else {
                        newRuleRequest.setName(ruleReq.getName());
                        newRuleRequest.setStartDate(ruleReq.getStartDate());
                        newRuleRequest.setEndDate(ruleReq.getEndDate());
                        newRuleRequest.setActionType(ECommonActionType.Create);
                    }

                    if (!newRuleRequest.getActionType().equals(ECommonActionType.Delete) && CollectionUtils.isEmpty(ruleReq.getConditions()))
                        throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);

                    ruleRequestRepository.save(newRuleRequest);

                    long totalGivenUpdatedConditionRules = ruleReq.getConditions()
                            .stream()
                            .filter(each -> each.getId() != null)
                            .count();

                    if (!newRuleRequest.getActionType().equals(ECommonActionType.Delete) && totalGivenUpdatedConditionRules != conditionRequests.size())
                        throw new BusinessException(ErrorCode.INVALID_RULE_CONDITION_PAYLOAD, null, null);

                    List<RuleConditionRequest> ruleConditionRequests = ruleReq.getConditions()
                            .stream()
                            .map(condition -> {
                                if (!map.containsKey(condition.getAttribute()))
                                    throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});

                                RuleConditionRequest newConditionRequest = new RuleConditionRequest();

                                String value = map.get(condition.getAttribute()).getWriteValue(
                                        condition.getAttribute(),
                                        condition.getOperator(),
                                        condition.getValue(),
                                        programId
                                );

                                newConditionRequest.setRuleRequestId(newRuleRequest.getId());
                                newConditionRequest.setActionType(ECommonActionType.Create);
                                newConditionRequest.setAttribute(condition.getAttribute());
                                newConditionRequest.setOperator(condition.getOperator().getExpression());
                                newConditionRequest.setValue(value);
                                newConditionRequest.setStatus(ECommonStatus.ACTIVE);

                                if (condition.getId() != null) {
                                    RuleConditionRequest availableConditionRequest = conditionRequests.get(condition.getId());

                                    if (availableConditionRequest == null)
                                        throw new BusinessException(ErrorCode.RULE_CONDITION_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});

                                    newConditionRequest.setRuleConditionId(availableConditionRequest.getRuleConditionId());

                                    if (condition.getArchive()) {
                                        newConditionRequest.setActionType(ECommonActionType.Delete);
                                        newConditionRequest.setStatus(ECommonStatus.INACTIVE);
                                        newConditionRequest.setValue(availableConditionRequest.getValue());
                                    } else {
                                        newConditionRequest.setActionType(ECommonActionType.Update);
                                    }
                                }

                                if (!newConditionRequest.getActionType().equals(ECommonActionType.Delete) && value == null)
                                    throw new BusinessException(OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(), null, null);

                                return newConditionRequest;
                            })
                            .collect(Collectors.toList());

                    ruleConditionRequestRepository.saveAll(ruleConditionRequests);
                });
    }
}