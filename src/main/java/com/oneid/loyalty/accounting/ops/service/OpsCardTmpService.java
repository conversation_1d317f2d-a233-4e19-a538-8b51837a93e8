package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.CardTempRes;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpsCardTmpService {
    Page<CardTempRes> filter(Integer businessId, Integer programId, Integer storeId, Integer batchNo,
                             ECardStatus status, String cardNo, Pageable pageRequest);
}
