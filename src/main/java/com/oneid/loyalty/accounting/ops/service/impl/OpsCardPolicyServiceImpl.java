package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EOpsCardExpireUnit;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicySearchReq;
import com.oneid.loyalty.accounting.ops.model.res.CardPolicyRes;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardPolicyService;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CardPolicyRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.util.DateTimes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.stream.Collectors;

@Service
public class OpsCardPolicyServiceImpl implements OpsCardPolicyService {

    @Autowired
    private CardPolicyRepository cardPolicyRepository;

    @Autowired
    private BusinessService businessService;

    @Override
    public CardPolicyRes create(CardPolicyCreateReq req) {
        Business business = businessService.findActive(req.getBusinessId());
        CardPolicy cardPolicy = cardPolicyRepository.save(createCardPolicyFromCreateRequest(req));
        return setBusinessName(CardPolicyRes.valueOf(cardPolicy), business);
    }

    @Override
    public CardPolicyRes update(CardPolicyUpdateReq req) {
        CardPolicy cardPolicy = cardPolicyRepository.findById(req.getId()).orElseThrow(
                () -> new BusinessException(ErrorCode.CARD_POLICY_NOT_FOUND, "Card policy not found", req.getId())
        );
        Business business = businessService.findActive(cardPolicy.getBusinessId());
        cardPolicy.setName(req.getName());
        cardPolicy.setDescription(req.getDescription());
        if (req.getQrUrl() != null) {
            cardPolicy.setQrUrl(req.getQrUrl());
        }
        if (ECommonStatus.ACTIVE.equals(cardPolicy.getStatus()) &&
                (req.getStatus() != null && !ECommonStatus.ACTIVE.equals(req.getStatus()))) {
            throw new BusinessException(ErrorCode.CANNOT_UPDATE, "Cannot update Policy because status is active", req);
        }

        if (req.getStatus() != null) {
            cardPolicy.setStatus(req.getStatus());
        }
        return setBusinessName(CardPolicyRes.valueOf(cardPolicyRepository.save(cardPolicy)), business);
    }

    @Override
    public CardPolicyRes viewDetails(Integer cardPolicyId) {
        CardPolicy cardPolicy = cardPolicyRepository.findById(cardPolicyId).orElseThrow(
                () -> new BusinessException(ErrorCode.CARD_POLICY_NOT_FOUND, "Card policy not found", cardPolicyId)
        );
        Business business =  businessService.find(cardPolicy.getBusinessId()).orElseThrow(
                ()-> new BusinessException(ErrorCode.SERVER_ERROR,
                        "Database save cardpolicy with business does not exist", cardPolicy)
        );
        return setBusinessName(CardPolicyRes.valueOf(cardPolicy), business);
    }

    @Override
    public Page<CardPolicyRes> searchPage(CardPolicySearchReq req, Pageable pageable) {
        SpecificationBuilder<CardPolicy> builder = new SpecificationBuilder<>();
        if (req.getBusinessId() != null) {
            builder.add(new SearchCriteria("businessId", req.getBusinessId(), SearchOperation.EQUAL));
        }
        if (req.getStatus() != null) {
            builder.add(new SearchCriteria("status", req.getStatus(), SearchOperation.EQUAL));
        }
        if (req.getCardPolicyType() != null) {
            builder.add(new SearchCriteria("policyType", req.getCardPolicyType(), SearchOperation.EQUAL));
        }

        Page<CardPolicy> cardPolicyPage = cardPolicyRepository.findAll(builder, pageable);

        return new PageImpl<>(
                cardPolicyPage.getContent()
                        .stream().map(c -> {
                    Business business = businessService.find(c.getBusinessId()).orElseThrow(
                            ()-> new BusinessException(ErrorCode.SERVER_ERROR,
                                    "Database save card policy with business does not exist", c)
                    );
                    return setBusinessName(CardPolicyRes.valueOf(c), business);
                }).collect(Collectors.toList())
                ,pageable
                ,cardPolicyPage.getTotalElements()
        );
    }

    private CardPolicy createCardPolicyFromCreateRequest(CardPolicyCreateReq req) {
        CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setName(req.getName());
        cardPolicy.setDescription(req.getDescription());
        cardPolicy.setPolicyType(req.getType());
        cardPolicy.setBusinessId(req.getBusinessId());
        cardPolicy.setCardNoLength(req.getLength());
        cardPolicy.setMaxCardCpr(req.getMaxCardPerCPR());
        cardPolicy.setQrUrl(req.getQrUrl());

        if (EOpsCardExpireUnit.DAY.equals(req.getCardExpirationType())) {
            cardPolicy.setExpirationUnit(ECardExpireUnit.DAY);
            cardPolicy.setExpirationPeriod(Math.toIntExact(req.getCardExpirationValue()));
        } else if (EOpsCardExpireUnit.MONTH.equals(req.getCardExpirationType())) {
            cardPolicy.setExpirationUnit(ECardExpireUnit.MONTH);
            cardPolicy.setExpirationPeriod(Math.toIntExact(req.getCardExpirationValue()));
        } else if (EOpsCardExpireUnit.YEAR.equals(req.getCardExpirationType())) {
            cardPolicy.setExpirationUnit(ECardExpireUnit.YEAR);
            cardPolicy.setExpirationPeriod(Math.toIntExact(req.getCardExpirationValue()));
        } else if (EOpsCardExpireUnit.NEVER.equals(req.getCardExpirationType())) {
            cardPolicy.setExpirationUnit(ECardExpireUnit.NEVER);
            cardPolicy.setExpirationFixedTime(DateTimes.timeUnlimited());
        } else if (EOpsCardExpireUnit.FIXED_TIME.equals(req.getCardExpirationType())) {
            cardPolicy.setExpirationFixedTime(new Date(req.getCardExpirationValue() * 1000));
        }
        cardPolicy.setExpirationTrigger(req.getCardExpirationTrigger());

        if (ERetensionType.END_DAY.equals(req.getCardRetentionType())) {
            cardPolicy.setRetentionType(req.getCardRetentionType());
            cardPolicy.setRetentionPeriod(0);
        } else {
            cardPolicy.setRetentionType(req.getCardRetentionType());
            cardPolicy.setRetentionPeriod(Math.toIntExact(req.getCardRetentionValue()));
        }

        cardPolicy.setRetentionTrigger(req.getCardRetentionTrigger());
        cardPolicy.setStatus(req.getStatus());
        if (req.getHasChecksum() != null) {
            cardPolicy.setHasChecksum(req.getHasChecksum());
        }
        return cardPolicy;
    }

    private CardPolicyRes setBusinessName(CardPolicyRes cardPolicyRes, Business business) {
        cardPolicyRes.setBusinessName(business.getName());
        return cardPolicyRes;
    }
}