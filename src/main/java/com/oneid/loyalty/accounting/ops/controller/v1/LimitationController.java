package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateLimitationReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterLimitationAvailableReq;
import com.oneid.loyalty.accounting.ops.model.res.LimitationInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationRes;
import com.oneid.loyalty.accounting.ops.model.res.LimitationStatisticRes;
import com.oneid.loyalty.accounting.ops.service.OpsLimitationService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;

@RestController
@RequestMapping("v1/limitations")
@Validated
public class LimitationController extends BaseController {

    @Autowired
    private OpsLimitationService opsLimitationService;

    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> creatingLimitationRequest(@Valid @RequestBody CreateLimitationReq req) {
        return success(opsLimitationService.createRequest(req));
    }

    @PostMapping("requests/{request_id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> creatingLimitationRequest(
            @PathVariable("request_id") Integer requestId,
            @Valid @RequestBody CreateLimitationReq req) {
        return success(opsLimitationService.update(requestId, req));
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListInReview(@RequestParam(value = "approval_status", required = false, defaultValue = "P") EApprovalStatus approvalStatus,
                                             @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
                                             @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
                                             @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
                                             @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
                                             @RequestParam(value = "created_by", required = false) String createdBy,
                                             @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
                                             @MakerCheckerOffsetPageable Pageable pageable) {

        Page<LimitationInReviewRes> inReviewResPage = opsLimitationService
                .getInReview(approvalStatus, fromCreatedAt, toCreatedAt, fromReviewedAt, toReviewedAt, createdBy, reviewedBy, pageable);

        return success(inReviewResPage.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) inReviewResPage.getTotalElements());
    }


    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewLimitationRequestById(@PathVariable("id") Integer reviewId) {
        return success(opsLimitationService.getInReviewLimitationRequestById(reviewId));
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableLimitationByRequestId(@PathVariable("id") Integer requestId) {
        return success(opsLimitationService.getAvailableLimitationDetailById(requestId));
    }

    @GetMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getChangeableByRequestId(@PathVariable("id") Integer requestId) {
        return success(opsLimitationService.getChangeableByRequestId(requestId));
    }

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableLimitationRequest(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "counter_id", required = false) Integer counterId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        FilterLimitationAvailableReq req = FilterLimitationAvailableReq.builder()
                .businessId(businessId)
                .programId(programId)
                .counterId(counterId)
                .code(code)
                .status(status)
                .build();
        Page<LimitationRes> page = opsLimitationService.getAvailableLimitations(req, new OffsetBasedPageRequest(offset, limit, Sort.by(Sort.Direction.DESC, "id")));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("counters-available/business/{business_id}")
    public ResponseEntity<?> getAvailableCounters(@PathVariable("business_id") Integer businessId) {
        return success(opsLimitationService.getAvailableCounters(businessId));
    }

    @GetMapping("/counters-available")
    public ResponseEntity<?> getAvailableCoutersByBusinessAndProgram(
            @RequestParam(value = "business_id") @NotNull Integer businessId,
            @RequestParam(value = "program_id") @NotNull Integer programId
    ){
        return success(opsLimitationService.getAvailableCountersByBusinessIdAndProgramId(businessId, programId));
    }

    @GetMapping("/counters-available/business-program-status")
    public ResponseEntity<?> getAvailableCoutersByBusinessAndProgram(
            @RequestParam(value = "business_id") @NotNull Integer businessId,
            @RequestParam(value = "program_id") @NotNull Integer programId,
            @RequestParam(value = "status", required = false) String status
    ){
        return success(opsLimitationService.getCountersByBusinessIdAndProgramIdAndStatus(businessId, programId, status));
    }

    @GetMapping("/counters-available/{id}/rules")
    public ResponseEntity<?> getCounterById(@PathVariable("id") Integer id) {
        return success(opsLimitationService.getAvailableCounterRuleById(id));
    }

    @PostMapping("/{id}/reset")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> reset(@PathVariable("id") Integer id, @Valid @RequestBody CreateLimitationReq req) {
        return success( opsLimitationService.resetCounterRequest(id,req));
    }

    @GetMapping("sub-statistic/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getSummaryStatis(@PathVariable("id") Integer id) {
        return success(opsLimitationService.getSummaryStatistic(id));
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsLimitationService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/statistic")
    @Authorize(role = AccessRole.LIMITATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> statisticLimitation(
            @RequestParam(value = "limitation_id", required = true) Integer limitationId,
            @RequestParam(value = "started_at", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date startedAt,
            @RequestParam(value = "ended_at", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date endedAt,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "history_status", required = false) ECommonStatus historyStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        Pageable pageable = new OffsetBasedPageRequest(offset, limit, Sort.by("id").descending());

        LimitationStatisticRes res = opsLimitationService.limitationStatistic(limitationId, startedAt, endedAt, code, historyStatus, pageable);

        return success(res, offset, limit, res.getTotalHistories());
    }
}
