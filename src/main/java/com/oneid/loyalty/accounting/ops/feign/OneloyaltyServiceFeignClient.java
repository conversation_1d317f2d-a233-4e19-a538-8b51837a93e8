package com.oneid.loyalty.accounting.ops.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.ConfirmTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MembershipInquiryFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ReverseTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ConfirmTransactionFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.CreateTransactionFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.res.MemberBalanceRes;

@FeignClient(name = "oneloyalty-service", url = "${oneloyalty-service.url}", configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface OneloyaltyServiceFeignClient {
    @RequestMapping(method = RequestMethod.POST, value = "/transaction/adjustment")
    APIResponse<CreateTransactionFeignRes> createAdjustmentTransaction(@RequestBody CreateTransactionFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/transaction/sale")
    APIResponse<CreateTransactionFeignRes> createSaleTransaction(@RequestBody CreateTransactionFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/transaction/earn")
    APIResponse<CreateTransactionFeignRes> createEarnTransaction(@RequestBody CreateTransactionFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/transaction/burn")
    APIResponse<CreateTransactionFeignRes> createBurnTransaction(@RequestBody CreateTransactionFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/transaction/reverse")
    APIResponse<CreateTransactionFeignRes> refundTransaction(@RequestBody ReverseTransactionFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/member/inquiry")
    APIResponse<MemberBalanceRes> inquiry(@RequestBody MembershipInquiryFeignReq req);

    @RequestMapping(method = RequestMethod.POST, value = "/transaction/confirm")
    APIResponse<ConfirmTransactionFeignRes> confirmTransaction(@RequestBody ConfirmTransactionFeignReq req);
}
