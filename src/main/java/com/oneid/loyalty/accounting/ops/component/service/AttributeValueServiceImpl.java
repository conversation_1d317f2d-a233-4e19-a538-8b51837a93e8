package com.oneid.loyalty.accounting.ops.component.service;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.feign.VoucherServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.VoucherRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.entity.AttributeServiceCode;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Function;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.RewardPool;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionCode;
import com.oneid.oneloyalty.common.repository.FunctionRepository;
import com.oneid.oneloyalty.common.service.AttributeServiceCodeService;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.RewardPoolService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.TransactionCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AttributeValueServiceImpl implements AttributeValueService {

    private final String VOUCHER_PURPOSE_GIFT = "GIFT";
    private final String VOUCHER_SALE_STATUS_ACTIVE = "ACTIVE";

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private PosService posService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private CardTypeService cardTypeService;

    @Autowired
    private FunctionRepository functionRepository;

    @Autowired
    private TransactionCodeService transactionCodeService;

    @Autowired
    private RewardPoolService rewardPoolService;

    @Autowired
    private AttributeServiceCodeService attributeServiceCodeService;

    @Override
    public Page<AttributeCombobox> getCorporationsAttributeValue(final Integer programId, String nameOrCode, Pageable pageable) {

        Page<Corporation> corporations = corporationService
                .findActiveByProgram(programService.findActive(programId), nameOrCode, pageable);

        return new PageImpl<>(
                corporations.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        )
                        .collect(Collectors.toList()),
                pageable,
                corporations.getTotalElements()
        );
    }

    @Autowired
    private VoucherServiceFeignClient voucherServiceFeignClient;


    @Override
    public List<AttributeCombobox> getListMemberStatusAttributeValue(Integer programId) {
        return Arrays.stream(ECommonStatus.values())
                .map(
                        c -> AttributeCombobox.builder()
                                .name(c.getDisplayName())
                                .value(c.getValue())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList());
    }

    @Override
    public Page<AttributeCombobox> getChainsAttributeValue(final Integer programId, String nameOrCode, Pageable pageable) {
        Page<Chain> pageChain = chainService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(
                pageChain.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                pageChain.getTotalElements());

    }

    @Override
    public Page<AttributeCombobox> getStoresAttributeValue(final Integer programId, String nameOrCode, Pageable pageable) {
        Page<Store> pageStore = storeService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(
                pageStore.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                pageStore.getTotalElements());
    }

    @Override
    public Page<AttributeCombobox> getTerminalsAttributeValue(Integer programId, String nameOrCode, Pageable pageable) {
        Page<Pos> pageTerminal = posService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(pageTerminal.stream()
                .map(
                        c -> AttributeCombobox.builder()
                                .name(c.getName())
                                .value(c.getCode())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList()),
                pageable,
                pageTerminal.getTotalElements());
    }

    @Override
    public Page<AttributeCombobox> getTierAttributeValue(Integer programId, String nameOrCode, Pageable pageable) {
        Page<ProgramTier> pageProgramTier = programTierService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(pageProgramTier.stream()
                .map(
                        c -> AttributeCombobox.builder()
                                .name(c.getName())
                                .value(c.getTierCode())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList()),
                pageable,
                pageProgramTier.getTotalElements());
    }

    @Override
    public List<AttributeCombobox> getListTxnAttributeValue(Integer programId) {
        return Arrays.stream(ETransactionType.values())
                .map(
                        c -> AttributeCombobox.builder()
                                .name(c.getDisplayName())
                                .value(c.getValue())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<AttributeCombobox> getListCardType(final Integer programId) {
        Program program = programService.findActive(programId);
        List<CardType> cardTypes = cardTypeService.findByBusinessIdAndProgramIdAndStatus(program.getBusinessId(),
                programId, ECommonStatus.ACTIVE);

        return cardTypes
                .stream()
                .map(
                        c -> AttributeCombobox.builder()
                                .name(c.getName())
                                .value(c.getCardType())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<AttributeCombobox> getFunctionCodes(final Integer programId) {
        programService.findActive(programId);
        List<Function> functions = functionRepository.findAll();
        return functions.stream()
                .filter(f -> f.getStatus() == ECommonStatus.ACTIVE)
                .map(c -> AttributeCombobox.builder()
                        .name(c.getName())
                        .value(c.getCode())
                        .checksumKeys(Collections.singletonList(programId))
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public Page<AttributeCombobox> getTransactionCodesAttributeValue(Integer programId, String nameOrCode, Pageable pageable) {
        Page<TransactionCode> pageTransactionCode = transactionCodeService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(
                pageTransactionCode.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                pageTransactionCode.getTotalElements());
    }

    @Override
    public Page<AttributeCombobox> getPoolCodeAttributeValue(Integer programId, String nameOrCode, Pageable pageable) {
        Page<RewardPool> pageRewardPools = rewardPoolService.searchActiveByProgramIdAndName(programId, nameOrCode, pageable);
        return new PageImpl<>(
                pageRewardPools.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getPoolCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                pageRewardPools.getTotalElements());
    }

    @Override
    public Page<AttributeCombobox> getVoucherCodeAttributeValue(Integer programId, String keyword, Pageable pageable) {
        APIResponse<VoucherRequestPageFeignRes> voucherPage = voucherServiceFeignClient.getVoucherPage(VOUCHER_PURPOSE_GIFT, VOUCHER_SALE_STATUS_ACTIVE, keyword, pageable.getPageNumber(), pageable.getPageSize());
        if (voucherPage.getMeta().getCode() != 200) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
        return new PageImpl<>(
                voucherPage.getData().getContent().stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getName())
                                        .value(c.getCode())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                voucherPage.getData().getTotalElements());
    }

    @Override
    public Page<AttributeCombobox> getListServiceCodeAttributeValue(Integer programId, String code, Pageable pageable) {
        Page<AttributeServiceCode> attributeServiceCodes = attributeServiceCodeService.searchActiveByProgramIdAndCode(programId, code, pageable);
        return new PageImpl<>(
                attributeServiceCodes.stream()
                        .map(
                                c -> AttributeCombobox.builder()
                                        .name(c.getCode())
                                        .value(c.getValue())
                                        .checksumKeys(Collections.singletonList(programId))
                                        .build()
                        ).collect(Collectors.toList()),
                pageable,
                attributeServiceCodes.getTotalElements());
    }
}