package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.service.PosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.mapper.StoreMapper;
import com.oneid.loyalty.accounting.ops.model.req.CreateStoreReq;
import com.oneid.loyalty.accounting.ops.model.req.StoreUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.StoreEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.StoreRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CountryService;
import com.oneid.oneloyalty.common.service.DistrictService;
import com.oneid.oneloyalty.common.service.ProvinceService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.WardService;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsStoreServiceImpl implements OpsStoreService {
    @Autowired
    CountryService countryService;

    @Autowired
    ProvinceService provinceService;

    @Autowired
    DistrictService districtService;

    @Autowired
    WardService wardService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    CorporationRepository corporationRepository;

    @Autowired
    ChainRepository chainRepository;

    @Autowired
    StoreRepository storeRepository;

    @Autowired
    StoreService storeService;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsCorporationService opsCorporationService;

    @Autowired
    OpsChainService opsChainService;
    
    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private ChainService chainService;

    @Autowired
    private PosService posService;

    @Autowired
    private CorporationService corporationService;
    
    @Autowired
    private WardRepository wardRepository;
    
    @Autowired
    private DistrictRepository districtRepository;

    @Override
    public Page<StoreRes> filter(Integer businessId, Integer corporationId, Integer chainId, String storeName, String storeCode, String status, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<Store> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (corporationId != null)
            specificationBuilder.add(new SearchCriteria("corporationId", corporationId, SearchOperation.EQUAL));

        if (chainId != null)
            specificationBuilder.add(new SearchCriteria("chainId", chainId, SearchOperation.EQUAL));

        if (storeName != null)
            specificationBuilder.add(new SearchCriteria("name", storeName, SearchOperation.EQUAL));

        if (storeCode != null)
            specificationBuilder.add(new SearchCriteria("code", storeCode, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        Page<Store> stores = storeService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = stores.getContent().stream().map(Store::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> corporationIds = stores.getContent().stream().map(Store::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationbyIds = opsCorporationService.getMapById(corporationIds);
        Set<Integer> chainIds = stores.getContent().stream().map(Store::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainbyIds = opsChainService.getMapById(chainIds);

        return new PageImpl<StoreRes>(stores.getContent()
                .stream().map(it -> StoreRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        corporationbyIds.get(it.getCorporationId()),
                        chainbyIds.get(it.getChainId())
                )).collect(Collectors.toList())
                , pageRequest, stores.getTotalElements());
    }

    @Override
    public StoreRes get(Integer id) {
        Store store = storeService.find(id);
        
        if (store == null)
            throw new BusinessException(ErrorCode.STORE_NOT_FOUND, "Store not found in business", LogData.createLogData().append("store_id", id));
      
        Business business = businessRepository.getOne(store.getBusinessId());
        Corporation corporation = corporationRepository.getOne(store.getCorporationId());
        Chain chain = chainRepository.getOne(store.getChainId());
        
        Integer wardId = null;
        Integer districtId = null;

        if(store.getWardId() != null) {
            Optional<Ward> ward = wardRepository.findById(store.getWardId());

            if (ward.isPresent()){
                Ward existedWard = ward.get();
                if(!existedWard.getStatus().equals(ECommonStatus.ACTIVE) || !existedWard.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedWard = wardRepository.findByCodeAndStatusAndNewStatus(existedWard.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                wardId = existedWard.getId();
                districtId = existedWard.getDistrictId();
            }
        }

        if(districtId == null && store.getDistrictId() != null) {
            Optional<District> district = districtRepository.findById(store.getDistrictId());

            if(district.isPresent()) {
                District existedDistrict = district.get();
                if (!existedDistrict.getStatus().equals(ECommonStatus.ACTIVE) || !existedDistrict.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedDistrict = districtRepository.findByCodeAndStatusAndNewStatus(existedDistrict.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                districtId = existedDistrict.getId();
            }
        }
        
        StoreRes storeRes = new StoreRes();
        storeRes.setId(store.getId());
        storeRes.setCode(store.getCode());
        storeRes.setBusinessId(store.getBusinessId());
        storeRes.setBusinessCode(business != null ? business.getCode() : null);
        storeRes.setBusinessName(business != null ? business.getName() : null);
        storeRes.setCorporationId(store.getCorporationId());
        storeRes.setCorporationName(corporation != null ? corporation.getName() : null);
        storeRes.setChainId(store.getChainId());
        storeRes.setChainName(chain != null ? chain.getName() : null);
        storeRes.setName(store.getName());
        storeRes.setEnName(store.getEnName());
        storeRes.setDescription(store.getDescription());
        storeRes.setEnDescription(store.getEnDescription());
        storeRes.setStatus(store.getStatus() != null ? store.getStatus().getValue() : null);
        storeRes.setCreatedBy(store.getCreatedBy());
        storeRes.setUpdatedBy(store.getUpdatedBy());
        storeRes.setApprovedBy(store.getApprovedBy());
        storeRes.setCreatedAt(store.getCreatedAt() != null ? store.getCreatedAt().toInstant().getEpochSecond() : null);
        storeRes.setUpdatedAt(store.getUpdatedAt() != null ? store.getUpdatedAt().toInstant().getEpochSecond() : null);
        storeRes.setApprovedAt(store.getApprovedAt() != null ? store.getApprovedAt().toInstant().getEpochSecond() : null);
        storeRes.setCreatedYmd(store.getCreatedYmd());
        storeRes.setContactPerson(store.getContactPerson());
        storeRes.setEmailAddress(store.getEmailAddress());
        storeRes.setPhoneNo(store.getPhoneNo());
        storeRes.setTaxNo(store.getTaxNo());
        storeRes.setWebSite(store.getWebSite());
        storeRes.setAddress1(store.getAddress1());
        storeRes.setCountryId(store.getCountryId());
        storeRes.setProvinceId(store.getProvinceId());
        storeRes.setDistrictId(districtId);
        storeRes.setWardId(wardId);
        storeRes.setServiceStartDate(store.getServiceStartDate() != null ? store.getServiceStartDate().toInstant().getEpochSecond() : null);
        storeRes.setServiceEndDate(store.getServiceEndDate() != null ? store.getServiceEndDate().toInstant().getEpochSecond() : null);
        storeRes.setPostalCode(store.getPostalCode());
        storeRes.setAddress2(store.getAddress2());
        storeRes.setCardStockLimit(store.getCardStockLimit());
        
        return storeRes;
    }

    @Override
    public List<StoreEnumAll> getEnumAll() {
        return storeRepository.findAll().stream().map(StoreEnumAll::of).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void update(Integer id, StoreUpdateReq storeUpdateReq) {
        validateLocation(storeUpdateReq.getCountryId(), storeUpdateReq.getProvinceId(), storeUpdateReq.getDistrictId(), storeUpdateReq.getWardId());
        validateUniqueStoreNameForUpdate(storeUpdateReq.getName(), id);
        validateUniqueStorePhoneForUpdate(storeUpdateReq.getPhoneNo(), id);
        Store store = storeService.find(id);
        if (store == null)
            throw new BusinessException(ErrorCode.STORE_NOT_FOUND, "Store not found in business", LogData.createLogData().append("store_id", id));
        Store storeUpdate = StoreMapper.toStoreOne(store, storeUpdateReq);
        if (!store.getCorporationId().equals(storeUpdateReq.getCorporationId()) ||
                !store.getChainId().equals(storeUpdateReq.getChainId())) {
            if (!store.getCorporationId().equals(storeUpdateReq.getCorporationId())) {
                Corporation corporation = corporationService.findActive(storeUpdateReq.getCorporationId());
                storeUpdate.setCorporationId(corporation.getId());
            }
            if (!store.getChainId().equals(storeUpdateReq.getChainId())) {
                Chain chain = chainService.findActive(storeUpdateReq.getChainId());
                storeUpdate.setChainId(chain.getId());
            }
            SpecificationBuilder<Pos> specificationBuilderPos = new SpecificationBuilder<>();
            specificationBuilderPos.add(new SearchCriteria("storeId", id, SearchOperation.EQUAL));
            List<Pos> posList = posService.find(specificationBuilderPos, Pageable.unpaged()).getContent();
            if (!posList.isEmpty()) {
                for (Pos pos : posList) {
                    pos.setCorporationId(storeUpdate.getCorporationId());
                    pos.setChainId(storeUpdate.getChainId());
                    posService.save(pos);
                }
            }
        }
        storeService.update(storeUpdate);
    }

    private void validateUniqueStoreNameForUpdate(String name, Integer id) {
        if (name == null) {
            return;
        }
        List<Store> stores = storeRepository.getByName(name);
        for (Store store : stores) {
            if (!store.getId().equals(id))
                throw new BusinessException(ErrorCode.STORE_NAME_EXISTED, "store name existed", LogData.createLogData().append("store_name", name));
        }
    }

    @Override
    public Map<Integer, Store> getMapById(Collection<Integer> ids) {
        List<Store> stores = this.storeRepository.findAllByIdIn(ids);
        return stores.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t,
                (value1, value2) -> value2
        ));
    }

    private void validateUniqueStorePhoneForUpdate(String phone, Integer id) {
        if (phone == null) return;
        List<Store> stores = storeRepository.getByPhoneNo(phone);
        for (Store store : stores) {
            if (!store.getId().equals(id))
                throw new OpsBusinessException(OpsErrorCode.STORE_PHONE_EXIST, "store phone exist", LogData.createLogData().append("store_phone", phone));
        }
    }

    private void validateLocation(Integer countryId, Integer provinceId, Integer districtId, Integer wardId) {
        if (countryId != null) {
            this.countryService.getById(countryId).orElseThrow(() -> new BusinessException(
                    ErrorCode.COUNTRY_NOT_FOUND, "Country is not found", LogData.createLogData().append("country", countryId)));
        }

        if (provinceId != null && !this.provinceService.isValid(provinceId, countryId)) {
            throw new BusinessException(
                    ErrorCode.PROVINCE_NOT_FOUND, "Province is not found", LogData.createLogData().append("province", provinceId));
        }

        if (districtId != null && !this.districtService.isValid(districtId, provinceId)) {
            throw new BusinessException(
                    ErrorCode.DISTRICT_NOT_FOUND, "District is not found", LogData.createLogData().append("district", districtId));
        }

        if (wardId != null && !this.wardService.isValid(wardId, districtId)) {
            throw new BusinessException(
                    ErrorCode.WARD_NOT_FOUND, "Ward is not found", LogData.createLogData().append("ward", wardId));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreRes create(CreateStoreReq req) {
        Business business = businessService.findActive(req.getBusinessId());
        
        Chain chain = chainService.findActive(req.getChainId());
        
        Corporation corporation = corporationService.findActive(req.getCorporationId());
        
        Store store = storeRepository.findByBusinessIdAndCode(req.getBusinessId(), req.getCode());
        
        if(store != null)
            throw new BusinessException(ErrorCode.STORE_EXISTED, "store existed", null);
        
        validateLocation(req.getCountryId(), req.getProvinceId(), req.getDistrictId(), req.getWardId());
        
        if(storeRepository.getByName(req.getName())
        .stream()
        .filter(entity -> entity.getBusinessId().equals(business.getId()))
        .count() > 0)
            throw new BusinessException(ErrorCode.STORE_NAME_EXISTED, "store name existed", null);
        
        if(req.getPhoneNo() !=null && storeRepository.getByPhoneNo(req.getPhoneNo())
                .stream()
                .filter(entity -> entity.getBusinessId().equals(business.getId()))
                .count() > 0)
                    throw new BusinessException(OpsErrorCode.STORE_PHONE_EXIST.getValue(), "store name existed", null);
        
        store = new Store();
        store.setBusinessId(business.getId());
        store.setCode(req.getCode());
        store.setChainId(chain.getId());
        store.setCorporationId(corporation.getId());
        
        store = StoreMapper.toStoreOne(store, req);
        
        storeRepository.save(store);
        
        return StoreRes.of(store, business, corporation, chain);
    }

}
