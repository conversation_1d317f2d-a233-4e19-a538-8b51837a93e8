package com.oneid.loyalty.accounting.ops.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.SystemAttributeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.SystemAttributeRes;
import com.oneid.loyalty.accounting.ops.service.OpsSystemAttributeService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.SystemAttributeRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.SystemAttributeRequestRepository;

@Service
public class OpsSystemAttributeServiceImpl implements OpsSystemAttributeService {
    
    @Value("${maker-checker.module.system-attribute}")
    private String moduleId;
    
    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @Autowired
    private SystemAttributeRequestRepository systemAttributeRequestRepository;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    
    private TransactionTemplate transactionTemplate;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }
    
    @Override
    public Page<SystemAttributeRes> getRequestPage(String code, String name, ECommonStatus status, EApprovalStatus approvalStatus, Pageable pageable) {
        ECommonStatus requestStatus = null;
        if(approvalStatus != null) {
            switch (approvalStatus) {
                case APPROVED:
                    requestStatus = ECommonStatus.ACTIVE;
                    break;
                case REJECTED:
                    requestStatus = ECommonStatus.INACTIVE;
                    break;
                default:
                    requestStatus = ECommonStatus.PENDING;
                    break;
            }
        }
        
        Page<SystemAttributeRequest> page = systemAttributeRequestRepository
                .findPage(code, name, status, requestStatus, approvalStatus, pageable);
        
        List<SystemAttributeRes> content = this.convertToRes(page.getContent());
        return new PageImpl<SystemAttributeRes>(content, pageable, page.getTotalElements());
    }
    
    @Override
    public SystemAttributeRes getRequestById(Integer requestId) {
        SystemAttributeRequest request = systemAttributeRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND));
        
        SystemAttributeRes res = this.convertToDetailRes(request).build();
        return res;
    }
    
    @Override
    public SystemAttributeRes getChangeableRequestById(Integer requestId) {
        SystemAttributeRequest request = this.getChangeableRequest(requestId);
        SystemAttributeRes res = this.convertToDetailRes(request).build();
        return res;
    }
    
    @Override
    public SystemAttributeRes requestChange(Integer requestId, SystemAttributeUpdateReq req) {
        SystemAttributeRequest reqEntity = transactionTemplate
                .execute(status -> this.insertChangeRequest(requestId, req));
        
        ChangeRequestFeignReq mcReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(reqEntity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(reqEntity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(mcReq);
        
        return SystemAttributeRes.builder().requestId(reqEntity.getId()).build();
    }
    
    private SystemAttributeRequest insertChangeRequest(Integer requestId, SystemAttributeUpdateReq req) {
        SystemAttributeRequest effectedVersion = this.getChangeableRequest(requestId);
        
        SystemAttributeRequest newRequest = SystemAttributeRequest.builder()
                .code(effectedVersion.getCode())
                .name(effectedVersion.getName())
                .description(effectedVersion.getDescription())
                .dataType(effectedVersion.getDataType())
                .dataTypeDisplay(effectedVersion.getDataTypeDisplay())
                .operators(effectedVersion.getOperators())
                .regexValidation(effectedVersion.getRegexValidation())
                .resourcePath(effectedVersion.getResourcePath())
                .supportFilter(effectedVersion.getSupportFilter())
                .status(req.getStatus())
                .approvalStatus(EApprovalStatus.PENDING)
                .requestStatus(ECommonStatus.PENDING)
                .build();
        
        systemAttributeRequestRepository.save(newRequest);
        return newRequest;
    }
    
    @Override
    public Page<SystemAttributeRes> getInReviewPage(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable) {
        APIResponse<ChangeRequestPageFeignRes> mcResponse = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus == null ? null : approvalStatus.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromDate == null ? null : (int) fromDate.atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond(),
                toDate == null ? null : (int) toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toEpochSecond());
        
        ChangeRequestPageFeignRes mcRequestPage = mcResponse.getData();
        
        if (mcRequestPage.getTotalRecordCount() <= 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
        
        List<Integer> requestIds = mcRequestPage.getRecords().stream()
                .map(ChangeRecordFeginRes::getObjectId)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        
        List<SystemAttributeRequest> requestRecords = systemAttributeRequestRepository.findByIds(requestIds);
        List<SystemAttributeRes> requests = this.convertToRes(requestRecords);
        Map<Integer, SystemAttributeRes> requestMap = requests.stream().collect(Collectors.toMap(e -> e.getRequestId(), e -> e));
        
        List<SystemAttributeRes> content = mcRequestPage.getRecords().stream()
                .map(mcRequest -> {
                    Integer reviewId = mcRequest.getChangeRequestId();
                    Integer requestId = Integer.parseInt(mcRequest.getObjectId());
                    
                    if (requestMap.containsKey(requestId) == false) // handle mismatched data with makerchecker
                        return null;
                    
                    SystemAttributeRes request = requestMap.get(requestId);
                    request.setReviewId(reviewId);
                    return request;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        return new PageImpl<>(content, pageable, mcRequestPage.getTotalRecordCount());
    }
    
    @Override
    public SystemAttributeRes getInReviewById(int reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> mcResponse = makerCheckerServiceClient.getChangeRequestById(String.valueOf(reviewId));
        Integer requestId = Integer.parseInt(mcResponse.getData().getObjectId());
        
        SystemAttributeRequest request = systemAttributeRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND));
        
        SystemAttributeRes res = this.convertToDetailRes(request).build();
        return res;
    }
    
    private SystemAttributeRequest getChangeableRequest(Integer requestId) {
        SystemAttributeRequest request = systemAttributeRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND));
        
        if (request.getApprovalStatus() == EApprovalStatus.PENDING)
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED);
        
        if (request.getApprovalStatus() == EApprovalStatus.REJECTED)
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_CAN_NOT_BE_EDITED);
        
        if (request.getRequestStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED);
        
        systemAttributeRequestRepository.findPendingVersion(request.getCode())
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED);
                });
        
        return request;
    }
    
    private List<SystemAttributeRes> convertToRes(List<SystemAttributeRequest> records) {
        return records.stream()
                .map(record -> {
                    SystemAttributeRes res = this.convertToBaseRes(record).build();
                    return res;
                })
                .collect(Collectors.toList());
    }
    
    private SystemAttributeRes.SystemAttributeResBuilder convertToBaseRes(SystemAttributeRequest request) {
        return SystemAttributeRes.builder()
                .attributeType(EAttributeType.SYSTEM)
                .requestId(request.getId())
                .code(request.getCode())
                .name(request.getName())
                .status(request.getStatus())
                .createdBy(request.getCreatedBy())
                .createdAt(request.getCreatedAt())
                .approvalStatus(request.getApprovalStatus())
                .approvedBy(request.getApprovedBy())
                .approvedAt(request.getApprovedAt());
    }
    
    private SystemAttributeRes.SystemAttributeResBuilder convertToDetailRes(SystemAttributeRequest request) {
        return this.convertToBaseRes(request)
                .description(request.getDescription())
                .dataType(request.getDataType())
                .dataTypeDisplay(request.getDataTypeDisplay())
                .operators(request.getOperators())
                .regexValidation(request.getRegexValidation())
                .resourcePath(request.getResourcePath())
                .supportFilter(request.getSupportFilter() == EBoolean.YES ? true : false)
                .rejectedReason(request.getRejectedReason());
    }
    
}
