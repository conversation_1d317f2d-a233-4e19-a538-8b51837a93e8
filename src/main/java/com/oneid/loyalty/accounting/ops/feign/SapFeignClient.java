package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.SapFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderCreateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderUpdateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderCreateFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderGetFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderUpdateFeignRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "sap", url = "${sap.url}",
        configuration = SapFeignConfig.class)
public interface SapFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "")
    SAPSaleOrderCreateFeignRes createSaleOrder(@RequestBody SAPSaleOrderCreateFeignReq request);

    @RequestMapping(method = RequestMethod.PUT, value = "/{saleOrder}")
    SAPSaleOrderUpdateFeignRes updateSaleOrder(@PathVariable String saleOrder, @RequestBody SAPSaleOrderUpdateFeignReq request);

    @RequestMapping(method = RequestMethod.GET, value = "/customer/{customer}")
    SAPSaleOrderGetFeignRes getSaleOrders(@PathVariable String customer);
}
