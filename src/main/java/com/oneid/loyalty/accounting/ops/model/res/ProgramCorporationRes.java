package com.oneid.loyalty.accounting.ops.model.res;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProgramCorporationRes implements Serializable {
    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("status")
    private ECommonStatus status;

    @JsonProperty("having_corporation")
    private Boolean corporations;


    public static ProgramCorporationRes valueOf(Program program, Business business){
        ProgramCorporationRes response = new ProgramCorporationRes();
        response.setProgramId(program.getId());
        response.setProgramCode(program.getCode());
        response.setProgramName(program.getName());
        response.setBusinessId(business.getId());
        response.setBusinessCode(business.getCode());
        response.setBusinessName(business.getName());
        response.setStatus(program.getStatus());
        return response;
    }
}
