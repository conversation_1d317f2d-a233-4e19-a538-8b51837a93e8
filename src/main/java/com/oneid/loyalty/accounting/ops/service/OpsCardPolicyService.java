package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicySearchReq;
import com.oneid.loyalty.accounting.ops.model.res.CardPolicyRes;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpsCardPolicyService {
    CardPolicyRes create(CardPolicyCreateReq req);
    CardPolicyRes update(CardPolicyUpdateReq req);
    CardPolicyRes viewDetails(Integer cardPolicyId);
    Page<CardPolicyRes> searchPage(CardPolicySearchReq req, Pageable pageable);
}