package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberStatusTransitionReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberStatusTransitionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/member-status-transition")
@Validated
public class MemberStatusTransitionController extends BaseController {

    @Autowired
    private OpsMemberStatusTransitionService opsMemberStatusTransitionService;

    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> creatingMemberStatus(@Valid @RequestBody MemberStatusTransitionReq req) {
        return success(opsMemberStatusTransitionService.createRequest(req));
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsMemberStatusTransitionService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getChangeableByRequestId(@PathVariable("id") Integer requestId) {
        return success(opsMemberStatusTransitionService.getChangeableByRequestId(requestId));
    }

    @PostMapping("requests/{request_id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> updateMemberStatusRequest(
            @PathVariable("request_id") Integer requestId,
            @Valid @RequestBody MemberStatusTransitionReq req) {
        return success(opsMemberStatusTransitionService.update(requestId, req));
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewCounterRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
            @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
            @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
            @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
            @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {
        Page<MemberStatusTransitionInReviewRes> page = opsMemberStatusTransitionService.getInReviewRequests(approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewCounterRequestById(@PathVariable("id") Integer reviewId) {
        return success(opsMemberStatusTransitionService.getInReviewRequestById(reviewId));
    }

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableCounterRequests(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "member_status_id", required = false) Integer memberStatusId,
            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(opsMemberStatusTransitionService.getPageAvailable(businessId, programId, memberStatusId, status));
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.MEMBER_STATUS_TRANSITION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableRequestById(@PathVariable("id") Integer reviewId) {
        return success(opsMemberStatusTransitionService.getAvailableRequestById(reviewId));
    }
}