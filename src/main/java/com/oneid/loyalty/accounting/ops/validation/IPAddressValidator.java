package com.oneid.loyalty.accounting.ops.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class IPAddressValidator implements ConstraintValidator<IPAddress, String> {
    private static final Pattern IP_ADDRESS_PATTERN = Pattern.compile("^(([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}([01]?\\d\\d?|2[0-4]\\d|25[0-5])$");

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        return IP_ADDRESS_PATTERN.matcher(value).matches();
    }
}