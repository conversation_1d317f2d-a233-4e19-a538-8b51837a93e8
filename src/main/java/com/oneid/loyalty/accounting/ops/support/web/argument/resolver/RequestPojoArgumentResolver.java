package com.oneid.loyalty.accounting.ops.support.web.argument.resolver;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.core.ResolvableType;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.oneloyalty.common.exception.BusinessException;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class RequestPojoArgumentResolver implements HandlerMethodArgumentResolver {
    private ObjectMapper objectMapper;
    
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(RequestPojo.class) != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        RequestPojo requestPojo = parameter.getParameterAnnotation(RequestPojo.class);
        
        ResolvableType resolvableType = ResolvableType.forMethodParameter(parameter);
        
        String parameterValue = webRequest.getParameter(requestPojo.value());
        
        if(StringUtils.isNoneBlank(parameterValue)) {
            return objectMapper.readValue(parameterValue, resolvableType.getRawClass());
        }
        
        if(requestPojo.required())
            throw new BusinessException(requestPojo.errorCode(), null, null);
        
        return null;
    }

}
