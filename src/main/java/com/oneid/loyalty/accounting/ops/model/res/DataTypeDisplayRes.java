package com.oneid.loyalty.accounting.ops.model.res;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DataTypeDisplayRes {
    
    private EAttributeDataTypeDisplay dataTypeDisplay;

    private List<EAttributeDataType> dataTypes;

    private List<EAttributeOperator> operators;
    
}
