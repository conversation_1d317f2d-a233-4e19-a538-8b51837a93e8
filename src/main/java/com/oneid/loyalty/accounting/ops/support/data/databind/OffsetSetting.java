package com.oneid.loyalty.accounting.ops.support.data.databind;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@ConstructorBinding
@ConfigurationProperties(prefix = "app.offset")
public class OffsetSetting {
    private int defaultOffset;
    private int defaultLimit;
    private int minOffset;
    private int maxLimit;
    private int minLimit;
}
