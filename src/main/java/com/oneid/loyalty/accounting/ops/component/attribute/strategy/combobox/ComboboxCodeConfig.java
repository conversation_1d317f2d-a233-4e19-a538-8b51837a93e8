package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Configuration
public class ComboboxCodeConfig {
    @Value("${rule-condition.attributes.card-type-code:CardTypeCode}")
    private String cardTypeCode;

    @Value("${rule-condition.attributes.chain-code:ChainCode}")
    private String chainCode;

    @Value("${rule-condition.attributes.corporation-code:CorporationCode}")
    private String corporationCode;

    @Value("${rule-condition.attributes.member-status:MemberStatus}")
    private String memberStatus;

    @Value("${rule-condition.attributes.pos-code:PosCode}")
    private String posCode;

    @Value("${rule-condition.attributes.store-code:StoreCode}")
    private String storeCode;

    @Value("${rule-condition.attributes.tier-code:TierCode}")
    private String tierCode;

    @Value("${rule-condition.attributes.transaction-type:TransactionType}")
    private String transactionType;

    @Value("${rule-condition.attributes.transaction-type:FunctionCode}")
    private String functionCode;

    @Value("${rule-condition.attributes.pool-code:PoolCode}")
    private String poolCode;

    @Value("${rule-condition.attributes.voucher-code:VoucherCode}")
    private String voucherCode;

    @Value("${rule-condition.attributes.service-code:ServiceCode}")
    private String serviceCode;
}