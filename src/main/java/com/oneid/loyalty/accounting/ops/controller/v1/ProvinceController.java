package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.res.ProvinceDTO;
import com.oneid.loyalty.accounting.ops.service.OpsProvinceService;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.entity.Province;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "v1/provinces")
@Validated
public class ProvinceController extends BaseController {
    @Autowired
    private OpsProvinceService opsProvinceService;

    // Should search by code. But also support to search by id for old system
    @GetMapping
    public ResponseEntity<?> getProvinces(@RequestParam(value = "country_id", required = false) Integer countryId,
                                          @RequestParam(value = "country_code", required = false) String countryCode) {
        List<Province> provinces = Collections.EMPTY_LIST;

        if (countryId != null) {
            provinces = this.opsProvinceService.getProvinces(countryId);
        } else {
            provinces = this.opsProvinceService.getProvinces(countryCode);
        }
        return success(provinces.stream().map(ProvinceDTO::valueOf).collect(Collectors.toList()));
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getOne(@PathVariable("id") Integer id) {
        return success(this.opsProvinceService.getOne(id));
    }
}
