package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDate;

@RestController
@RequestMapping(value = "v1/gift-card")
@Validated
public class GiftCardController extends BaseController {

    @Autowired
    private OpsGiftCardService opsGiftCardService;

    @GetMapping("request/available")
    @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getPage(
            @RequestParam(name = "business_id", required = true) int businessId,
            @RequestParam(name = "corporation_id", required = false) Integer corporationId,
            @RequestParam(name = "chain_id", required = false) Integer chainId,
            @RequestParam(name = "store_id", required = false) Integer storeId,
            @RequestParam(name = "program_id", required = false) Integer programId,
            @RequestParam(name = "serial", required = false) String serial,
            @RequestParam(name = "batch_no", required = false) Long batchNo,
            @RequestParam(name = "point", required = false) Long point,
            @RequestParam(name = "status", required = false) EGiftCardStatus status,
            @RequestParam(name = "approval_status", required = false, defaultValue = "A") EApprovalStatus approvalStatus,
            @RequestParam(name = "topup_date", required = false) LocalDate topupDate,
            @RequestParam(name = "expiration_date", required = false) LocalDate expirationDate,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit
    ) {
        Pageable pageable = new OffsetBasedPageRequest(offset, limit, null);
        Page<GiftCardRes> page = opsGiftCardService.getPage(businessId, corporationId, chainId,
                storeId, programId, serial, batchNo, point, status, approvalStatus, topupDate, expirationDate, pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("request/available/program/{program_id}/serial/{serial}")
    @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getDetail(
            @PathVariable("program_id") int programId,
            @PathVariable("serial") String serial,
            @RequestParam(name = "request_id", required = false) Integer requestId
    ) {
        if (requestId == null) {
            return success(opsGiftCardService.getApprovedDetail(programId, serial));
        } else {
            return success(opsGiftCardService.getRequestDetail(programId, serial, requestId));
        }
    }
    
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    @GetMapping("request/available/program/{program_id}/serial/{serial}/changeable")
    public ResponseEntity<?> getChangeable(
            @PathVariable("program_id") Integer programId,
            @PathVariable("serial") String serial
    ) {
        return success(opsGiftCardService.getChangeable(programId, serial));
    }
    
    @PostMapping("request/change")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestUpdate(@RequestBody @Valid GiftCardUpdateReq req) {
        return success(opsGiftCardService.requestUpdate(req));
    }
    
    @GetMapping("request/in-review")
    @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewPage(
            @RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(name = "from_date", required = false) LocalDate fromDate,
            @RequestParam(name = "to_date", required = false) LocalDate toDate,
            @MakerCheckerOffsetPageable Pageable pageable
    ) {
        Page<GiftCardRes> page = opsGiftCardService.getInReviewPage(approvalStatus, fromDate, toDate, pageable);
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("request/in-review/{review_id}")
    @Authorize(role = AccessRole.GIFT_CARD, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewDetail(@PathVariable("review_id") Integer reviewId) {
        return success(opsGiftCardService.getInReviewDetail(reviewId));
    }

    @GetMapping("/requests/available/{card_request_id}/export")
    public ResponseEntity<?> getExportedGiftCardByRequestId(@PathVariable("card_request_id") Integer requestId) {
        ResourceDTO resourceDTO = opsGiftCardService.exportGiftCard(requestId);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", String.format("attachment; filename=%s", resourceDTO.getFilename()));
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        return new ResponseEntity<>(resourceDTO.getResource(), headers, HttpStatus.OK);
    }

}
