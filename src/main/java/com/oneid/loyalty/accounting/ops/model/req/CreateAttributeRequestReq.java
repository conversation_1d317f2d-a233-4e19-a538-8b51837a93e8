package com.oneid.loyalty.accounting.ops.model.req;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.oneid.loyalty.accounting.ops.validation.ASCIICode;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.oneloyalty.common.constant.EAttributeType;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateAttributeRequestReq {

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotBlank(message = "'code' must not be blank")
    @Length(max = 100)
    @ASCIICode
    private String code;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    private String name;

    private String description;

    @NotNull(message = "'data_type' must not be null")
    private EAttributeDataType dataType;

    @NotNull(message = "'data_type_display' must not be null")
    private EAttributeDataTypeDisplay dataTypeDisplay;

    @NotEmpty(message = "'operators' must not be null")
    private List<EAttributeOperator> operators;

    private String regexValidation;

    private EAttributeType attributeType;
}
