package com.oneid.loyalty.accounting.ops.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateChainReq;
import com.oneid.loyalty.accounting.ops.model.res.ChainEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.ChainRes;
import com.oneid.oneloyalty.common.entity.Chain;

public interface OpsChainService {
    Page<ChainRes> filter(Integer businessId, Integer corporationId, String chainName, String chainCode, String status, Integer offset, Integer limit);

    ChainRes get(Integer id);

    List<ChainEnumAll> getEnumAll();

    void update(Integer id, ChainUpdateReq chainUpdateReq);

    Map<Integer, Chain> getMapById(Collection<Integer> ids);
    
    ChainRes create(CreateChainReq req);
}
