package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Builder
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = MemberStatusTransitionReq.MemberStatusTransitionReqBuilder.class)
public class MemberStatusTransitionReq {

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @Length(max = 255)
    private String editKey;

    private Integer beginningStatusId;

    @Size(min = 1, message = "status transition list can not be null")
    private List<StatusTransition> statusTransitions;

    private ECommonStatus status;

    private ERequestType requestType;

    @JsonPOJOBuilder(withPrefix = "")
    public static class MemberStatusTransitionReqBuilder {
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class StatusTransition {

        public StatusTransition() {
        }

        @NotNull(message = "destination_status not null")
        private Integer destinationStatusId;

        @NotNull(message = "is_automatic_transition not null")
        private EBoolean isAutomaticTransition;

        private List<RuleReq> ruleReqs;

        private Integer id;

        @NotNull(message = "archive not null")
        private Boolean archive;

        @NotNull(message = "rule_logic: must not be null")
        private EConditionType ruleLogic;
    }
}