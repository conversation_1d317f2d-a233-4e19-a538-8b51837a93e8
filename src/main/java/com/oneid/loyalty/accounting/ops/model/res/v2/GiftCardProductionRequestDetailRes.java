package com.oneid.loyalty.accounting.ops.model.res.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Builder
public class GiftCardProductionRequestDetailRes {

    private static final long serialVersionUID = 5731291494086103824L;

    private Long id;

    private String description;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private ShortEntityRes corporation;

    private ShortEntityRes store;

    private ShortEntityRes chain;

    private Integer requestId;

    private Long batchNo;

    private Integer version;

    private EGiftCardBatchType batchType;

    private EGiftCardStatus giftCardStatus;

    private Integer noOfCard;

    private ShortEntityRes giftCardBin;

    private ShortEntityRes giftCardType;

    private ShortEntityRes cardPolicy;

    private String gcPostfix;

    private EBoolean generateQr;

    private EGiftCardIndicator generationInd;

    private Long generationDate;

    private String reason;

    private EApprovalStatus approvalStatus;

    private String createdBy;

    private String updatedBy;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;
}
