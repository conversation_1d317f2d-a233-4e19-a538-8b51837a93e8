package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Builder
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CreateMemberStatusReq.CreateMemberStatusReqBuilder.class)
public class CreateMemberStatusReq {

    private Integer id;

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @Length(max = 255)
    private String editKey;

    @NotBlank(message = "'code' must not be blank")
    @Length(max = 5)
    @OpsCode
    private String code;

    @NotBlank(message = "'en_name' must not be blank")
    @Length(max = 20)
    private String enName;

    @NotBlank(message = "'vi_name' must not be blank")
    @Length(max = 20)
    private String viName;

    @Length(max = 255)
    private String enDescription;

    @Length(max = 255)
    private String viDescription;

    private EBoolean revertLatestStatus;

    private EBoolean expiredAllPoints;

    private EBoolean defaultStatus;

    private ECommonStatus status;

    private List<Function> functions;

    @Getter
    @Setter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class Function {
        public Function() {
        }

        private String functionCode;

        private String functionName;

        private String group;
    }

    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateMemberStatusReqBuilder {
    }
}