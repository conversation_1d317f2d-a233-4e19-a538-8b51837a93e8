package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TransactionTypeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    public TransactionTypeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getTransactionType().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {

        return AttributeCombobox.builder()
                .name(ETransactionType.of(value).getDisplayName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> transactionTypes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return transactionTypes.stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(ETransactionType.of(s).getDisplayName())
                            .value(s)
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}