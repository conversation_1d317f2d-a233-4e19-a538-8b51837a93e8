package com.oneid.loyalty.accounting.ops.controller.v1;


import com.oneid.loyalty.accounting.ops.model.req.CpmTxnReverseReq;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import org.springframework.data.domain.Page;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.req.CpmTransactionReq;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionRes;
import com.oneid.loyalty.accounting.ops.service.OpsCpmService;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/cpm-management")
@Validated
public class CpmController extends BaseController {

    @Autowired
    private OpsCpmService opsCpmService;

    @PostMapping("/transactions")
    @Authorize(role = AccessRole.CPM_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getCpmTransactions(
            @Valid @RequestBody CpmTransactionReq cpmTransactionReq,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {
        Page<CpmTransactionRes> cpmTransactionRes = opsCpmService.getCpmTransactions(cpmTransactionReq, offset, limit);
        return success(cpmTransactionRes, offset, limit);
    }

    @GetMapping("/transactions/detail")
    @Authorize(role = AccessRole.CPM_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getCpmTransactionDetail(
            @RequestParam(value = "transaction_ref") String transactionRef
    ){
        return success(opsCpmService.getCpmTransactionDetail(transactionRef));
    }

    @PostMapping("/transactions/refund")
    @Authorize(role = AccessRole.CPM_MANAGEMENT, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> reverseTransaction(
            @Valid @RequestBody CpmTxnReverseReq reverseReq
            ){
        opsCpmService.reverseCpmTransaction(reverseReq);
        return success(null);
    }
}
