package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusInReviewRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberStatusService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@RestController
@RequestMapping("v1/member-status")
@Validated
public class MemberStatusController extends BaseController {

    @Autowired
    private OpsMemberStatusService opsMemberStatusService;

    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> creatingMemberStatus(@Valid @RequestBody CreateMemberStatusReq req) {
        return success(opsMemberStatusService.createRequest(req));
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
            @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
            @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
            @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<MemberStatusInReviewRes> page = opsMemberStatusService.getListInReviewRequests(approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewRequestById(@PathVariable("id") Long reviewId) {
        return success(opsMemberStatusService.getInReviewRequestById(reviewId));
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsMemberStatusService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getChangeableByRequestId(@PathVariable("id") Integer requestId) {
        return success(opsMemberStatusService.getChangeableByRequestId(requestId));
    }

    @PostMapping("requests/{request_id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> updateMemberStatusRequest(
            @PathVariable("request_id") Integer requestId,
            @Valid @RequestBody CreateMemberStatusReq req) {
        return success(opsMemberStatusService.update(requestId, req));
    }

    @GetMapping("/functions")
    @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getAvailableProgramCorporationRequest(
            @RequestParam(value = "program_id") Integer programId
    ) {
        return success(opsMemberStatusService.getFunctions(programId));
    }

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(name = "business_id") @NotNull(message = "'business_id' must not be null'") Integer businessId,
            @RequestParam(name = "program_id", required = false) Integer programId,
            @RequestParam(name = "code", required = false) String code,
            @RequestParam(name = "status", required = false) @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<MemberStatusAvaiRes> page = opsMemberStatusService.getListAvailableRequests(businessId, programId, code, status, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.MEMBER_STATUS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableRequestById(@PathVariable("id") Integer id) {
        return success(opsMemberStatusService.getAvailableRequestById(id));
    }
}