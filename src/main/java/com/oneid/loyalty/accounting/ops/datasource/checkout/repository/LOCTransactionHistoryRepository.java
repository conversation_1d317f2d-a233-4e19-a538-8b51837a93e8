package com.oneid.loyalty.accounting.ops.datasource.checkout.repository;

import com.oneid.loyalty.accounting.ops.datasource.checkout.entity.LOCTransactionHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LOCTransactionHistoryRepository extends JpaRepository<LOCTransactionHistory, Long>, JpaSpecificationExecutor<LOCTransactionHistory> {

    LOCTransactionHistory findByCpmTransactionRef(String cpmTransactionRef);

    LOCTransactionHistory findByTransactionRef(String transactionRef);

    LOCTransactionHistory findByCpmTransactionRefOrInvoiceNoAndProgramCode(String cpmTransactionRef, String invoiceNo, String programCode);
}
