package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class RetentionPolicyReq {
    @NotBlank
    @Pattern(regexp="^(END_DAY|N_DAY)?$")
    @JsonProperty("type")
    private String type;

    @Min(0)
    @JsonProperty("period_day")
    private Integer periodDay;
}
