package com.oneid.loyalty.accounting.ops.feign;


import com.oneid.loyalty.accounting.ops.feign.config.NotificationCenterConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.SendSmsInternalReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.Oauth2GetTokenRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SendSmsInternalRes;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@FeignClient(name = "notification-center", url = "${notification-center.url}", configuration = NotificationCenterConfig.class)
public interface NotificationCenterFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/v1/send" , consumes = "application/json")
    @Headers("Content-Type: application/json")
    SendSmsInternalRes sendSms(
            @RequestHeader(value = "Authorization", required = true) String authorizationHeader,
            @RequestBody SendSmsInternalReq req);
}
