package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerTierAdjustmentFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerTransactionRequestFeignReq;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "oneloyalty-master-worker", url = "${oneloyalty-master-worker.url}",
        configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface MasterWorkerFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/jobs/tier-adjustment/exec")
    APIResponse<?> adjustTiers(@RequestBody MasterWorkerTierAdjustmentFeignReq request);

    @RequestMapping(method = RequestMethod.POST, value = "/jobs/transaction-request/exec")
    APIResponse<?> requestTransactions(@RequestBody MasterWorkerTransactionRequestFeignReq request);
}
