package com.oneid.loyalty.accounting.ops.datasource.cpm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "transaction_history_attribute")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CPMTransactionHistoryAttribute implements Serializable {


    private static final long serialVersionUID = 919613236491960401L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "transaction_ref")
    private String transactionRef;

    @Column(name = "code")
    private String code;

    @Column(name = "value")
    private String value;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date createdAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "approved_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date approvedAt;

    @Column(name = "created_ymd")
    private Long createdYmd;
}
