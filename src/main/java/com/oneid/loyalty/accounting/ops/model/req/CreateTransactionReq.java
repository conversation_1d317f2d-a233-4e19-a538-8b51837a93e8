package com.oneid.loyalty.accounting.ops.model.req;

import java.math.BigDecimal;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.EAdjustmentType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateTransactionReq {
    @JsonProperty("member_id")
    private String memberCode;

    @NotNull(message = "Id Type not null")
    @JsonProperty("id_type")
    private EOpsIdType idType;

    @JsonProperty("business_id")
    private Integer businessId;
    
    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("chain_id")
    private Integer chainId;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("terminal_id")
    private Integer terminalId;

    @NotNull(message = "Transaction type not null")
    @JsonProperty("transaction_type")
    private EOpsTransactionType transactionType;

    @JsonProperty("description")
    private String description;

    @JsonProperty("transaction_time")
    private Long transactionTime;

    @Valid
    @JsonProperty("transaction")
    private Transaction transaction;

    @JsonProperty("remark")
    private String remark;

    @Getter
    @Setter
    public static class Transaction {
        @Size(max = 50, message = "'Invoice no' max length is 50")
        @JsonProperty("invoice_no")
        private String invoiceNo;

        @Min(0)
        @JsonProperty("gmv_amount")
        private BigDecimal Gmv;

        @Min(0)
        @JsonProperty("gross_amount")
        private BigDecimal grossAmount;

        @Min(0)
        @JsonProperty("redeem_point")
        private BigDecimal redeemPoint;

        @JsonProperty("currency_code")
        private String currencyCode;
        
        @JsonProperty("adjustment_type")
        private EAdjustmentType adjustmentType;
        
        @JsonProperty("reason_id")
        private Integer reasonId;
        
        @JsonProperty("adjust_point")
        private BigDecimal adjustPoint;
        
        @JsonProperty("pool_id")
        private Integer poolId;
    }
}
