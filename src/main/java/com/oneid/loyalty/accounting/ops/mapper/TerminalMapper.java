package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.TerminalCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TerminalUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Pos;

import java.util.Date;

public class TerminalMapper {
    public static Pos toTerminalOne(TerminalCreateReq req) {
        Pos pos = new Pos();
        pos.setBusinessId(req.getBusinessId());
        pos.setCorporationId(req.getCorporationId());
        pos.setChainId(req.getChainId());
        pos.setStoreId(req.getStoreId());
        pos.setCode(req.getCode());
        pos.setName(req.getName());
        pos.setDescription(req.getDescription());
        pos.setServiceStartDate(new Date(req.getServiceStartDate() * 1000));
        pos.setServiceEndDate(new Date(req.getServiceEndDate() * 1000));
        pos.setStatus(ECommonStatus.of(req.getStatus()));
        return pos;
    }

    public static Pos toTerminalOne(Pos pos, TerminalUpdateReq request) {
        pos.setName(request.getName());
        pos.setDescription(request.getDescription());
//        pos.setServiceStartDate(new Date(request.getServiceStartDate()));
        pos.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        pos.setStatus(ECommonStatus.of(request.getStatus()));
        return pos;
    }
}
