package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import com.oneid.oneloyalty.common.entity.CardProductionRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CardProductionRequestRes {
    private Integer id;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("batch_no")
    private Long batchNo;

    @JsonProperty("no_of_card")
    private Integer noOfCard;

    private String description;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("processing_date")
    private Date processingDate;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("generation_date")
    private Date generationDate;

    @JsonProperty("card_range_from")
    private String cardRangeFrom;

    @JsonProperty("card_range_to")
    private String cardRangeTo;

    private String status;

    @JsonProperty("card_bin_id")
    private Integer cardBinId;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("created_at")
    private Date createdAt;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("updated_at")
    private Date updatedAt;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("card_type_id")
    private Integer cardTypeId;

    @JsonProperty("rejected_reason")
    private String rejectedReason;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("approved_at")
    private Date approvedAt;


    public static CardProductionRequestRes valueOf(CardProductionRequest entity) {
        CardProductionRequestRes result = new CardProductionRequestRes();
        result.setId(entity.getId());
        result.setProgramId(entity.getProgramId());
        result.setBatchNo(entity.getCprBatchNo());
        result.setNoOfCard(entity.getNoOfCard());
        result.setDescription(entity.getDescription());
        result.setProcessingDate(entity.getProcessingDate());
        result.setGenerationDate(entity.getGenerationDate());
        result.setCardRangeFrom(entity.getCardRangeFrom());
        result.setCardRangeTo(entity.getCardRangeTo());
        result.setStatus(entity.getStatus().getValue());
        result.setRejectedReason(entity.getRejectedReason());

        result.setCardBinId(entity.getCardBinId());
        result.setCardTypeId(entity.getCardTypeId());
        result.setBusinessId(entity.getBusinessId());
        result.setStoreId(entity.getStoreId());

        result.setCreatedBy(entity.getCreatedBy());
        result.setUpdatedBy(entity.getUpdatedBy());
        result.setCreatedAt(entity.getCreatedAt());
        result.setUpdatedAt(entity.getUpdatedAt());
        result.setApprovedBy(entity.getApprovedBy());
        result.setApprovedAt(entity.getApprovedAt());

        return result;
    }
}
