package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyUpdateReq;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Currency;

public class CurrencyMapper {
    public static Currency toCurrencyOne(CurrencyCreateReq request, String userName) {
        Currency result = new Currency();

        result.setBusinessId(request.getBusinessId());
        result.setCode(request.getCode());
        result.setName(request.getName());
        result.setDescription(request.getDescription());
        result.setStatus(ECommonStatus.of(request.getStatus()));
        result.setCreatedAt(DateTimes.currentDate());
        result.setUpdatedAt(DateTimes.currentDate());
        result.setCreatedBy(userName);
        result.setUpdatedBy(userName);

        return result;
    }

    public static Currency toCurrencyOne(Currency currency, CurrencyUpdateReq request, String userName) {
        currency.setBusinessId(request.getBusinessId());
        currency.setCode(currency.getCode());
        currency.setName(request.getName());
        currency.setDescription(request.getDescription());
        currency.setStatus(ECommonStatus.of(request.getStatus()));
        currency.setUpdatedAt(DateTimes.currentDate());
        currency.setUpdatedBy(userName);

        return currency;
    }
}