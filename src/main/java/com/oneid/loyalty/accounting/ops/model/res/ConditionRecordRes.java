package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode
public class ConditionRecordRes {
    private Integer id;

    private String attribute;

    private String operator;

    private Object value;

    private ECommonStatus status;

    private String resourcePath;

    private Boolean supportFilter;

    private EAttributeDataDisplayType dataDisplayType;

    private boolean existMasterData;

    private Boolean enableValidateMasterData;

    private String attributeType;

    @JsonIgnore
    private Integer ruleId;

    @JsonIgnore
    private EBoolean isIgnoreRefund;

    @JsonIgnore
    private String dataType;

    @JsonIgnore
    private String createdBy;

    @JsonIgnore
    private Date createdAt;

    @JsonIgnore
    private String updatedBy;

    @JsonIgnore
    private Date updatedAt;

    @JsonIgnore
    private String approvedBy;

    @JsonIgnore
    private Date approvedAt;

    @JsonIgnore
    private String valueString;
}