package com.oneid.loyalty.accounting.ops.service;

import org.springframework.data.domain.Page;

import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateCardReq;
import com.oneid.loyalty.accounting.ops.model.res.CardRes;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

public interface OpsCardService {
	Page<CardRes> filter(Integer cardType, String cardNo, String bussinesId, String programId, String storeId, String memberId, Integer offset, Integer limit);
	
	Object addCard(OPSAuthenticatedPrincipal principal, AddCardReq addCardModel);
	
	CardRes getDetail(Integer id, String businessId, String programId);
	
	Object update(OPSAuthenticatedPrincipal principal, Integer id, String cardNo, UpdateCardReq card);

}
