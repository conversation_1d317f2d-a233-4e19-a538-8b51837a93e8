package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class TransactionBatchRequestRes {

    private Long batchNo;
    private String batchName;
    private ShortEntityRes business;
    private ShortEntityRes program;
    private EBatchRequestType type;
    private String createdBy;
    private Date createdAt;
    private String approvedBy;
    private Date approvedAt;
    private EApprovalStatus approvalStatus;
    private String rejectReason;
}
