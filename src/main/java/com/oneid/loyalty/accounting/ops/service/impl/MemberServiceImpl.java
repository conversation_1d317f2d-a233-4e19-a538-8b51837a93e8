//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.constant.ErrorCode;
//import com.oneid.loyalty.accounting.ops.entity.WlMember;
//import com.oneid.loyalty.accounting.ops.entity.WlMemberProductAccount;
//import com.oneid.loyalty.accounting.ops.exception.BusinessException;
//import com.oneid.loyalty.accounting.ops.model.req.SearchMemberReq;
//import com.oneid.loyalty.accounting.ops.model.res.MemberRes;
//import com.oneid.loyalty.accounting.ops.repository.WlMemberRepository;
//import com.oneid.loyalty.accounting.ops.service.core.CorePartnerService;
//import com.oneid.loyalty.accounting.ops.service.core.CoreProductAccountService;
//import com.oneid.loyalty.accounting.ops.service.search.SearchCriteria;
//import com.oneid.loyalty.accounting.ops.service.search.SearchOperation;
//import com.oneid.loyalty.accounting.ops.service.search.SpecificationBuilder;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.data.domain.Pageable;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Service
//public class MemberServiceImpl implements MemberService {
//    @Autowired
//    WlMemberRepository wlMemberRepository;
//
//    @Autowired
//    CorePartnerService partnerService;
//
//    @Autowired
//    CoreProductAccountService productAccountService;
//
//    @Override
//    public Page<MemberRes> searchMembers(SearchMemberReq searchMemberReq, Pageable pageRequest) {
//        SpecificationBuilder<WlMember> specification = new SpecificationBuilder<>();
//
//        if (searchMemberReq.getCardNo() != null) {
//            WlMemberProductAccount wlMemberProductAccount = productAccountService.findOne(searchMemberReq.getCardNo());
//            if (wlMemberProductAccount == null)
//                throw new BusinessException(ErrorCode.CARD_NOT_FOUND, "Card not found", null);
//            specification.add(new SearchCriteria("memberCode", wlMemberProductAccount.getMemberCode(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getFullName() != null) {
//            specification.add(new SearchCriteria("fullName", searchMemberReq.getFullName(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getIdentifyNo() != null) {
//            specification.add(new SearchCriteria("identifyNo", searchMemberReq.getIdentifyNo(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getIdentifyType() != null) {
//            specification.add(new SearchCriteria("identifyType", searchMemberReq.getIdentifyType(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getMemberCode() != null) {
//            specification.add(new SearchCriteria("memberCode", searchMemberReq.getMemberCode(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getPartnerCode() != null) {
//            specification.add(new SearchCriteria("partnerCode", searchMemberReq.getPartnerCode(), SearchOperation.EQUAL));
//        }
//        if (searchMemberReq.getPhoneNo() != null) {
//            specification.add(new SearchCriteria("phoneNo", searchMemberReq.getPhoneNo(), SearchOperation.EQUAL));
//        }
//        Page<WlMember> membersPage = wlMemberRepository.findAll(specification, pageRequest);
//
//        Map<String, String> partnerCodeToName = partnerService.getMapCodeToNamePartner();
//        List<MemberRes> memberResList = membersPage.getContent().stream().map(mem -> {
//            MemberRes res = MemberRes.valueOf(mem);
//            res.setPartnerName(partnerCodeToName.get(mem.getPartnerCode()));
//            return res;
//        }).collect(Collectors.toList());
//
//        return new PageImpl<>(memberResList, pageRequest, membersPage.getTotalElements());
//    }
//
//    @Override
//    public MemberRes getDetails(String memberCode) {
//        WlMember member = wlMemberRepository.findOneByMemberCode(memberCode).orElseThrow(
//                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", memberCode));
//
//        MemberRes res = MemberRes.valueOf(member);
//
//        Map<String, String> partnerCodeToName = partnerService.getMapCodeToNamePartner();
//        res.setPartnerName(partnerCodeToName.get(member.getPartnerCode()));
//
//        return res;
//    }
//}