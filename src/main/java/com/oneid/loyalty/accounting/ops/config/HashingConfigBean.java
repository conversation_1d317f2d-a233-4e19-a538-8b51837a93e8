package com.oneid.loyalty.accounting.ops.config;

import com.oneid.oneloyalty.common.support.Hashing;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HashingConfigBean {

    @Value("${app.attribute.value.secret}")
    private String secret;

    @Bean
    public Hashing hashingMD5() {
        return new Hashing(secret,"HMACMD5");
    }
}