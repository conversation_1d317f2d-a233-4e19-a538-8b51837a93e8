package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business;

import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.HttpEntityService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class GetApproveBusinessMCServiceImpl extends AbsGetApproveService implements GetApproveBusinessMCService{
    @Value("${maker-checker.module.business}")
    private String module;

    @Value("${maker-checker.action.create}")
    private String create;

    @Value("${maker-checker.action.update}")
    private String update;

    @Value("${maker-checker.param-keys.business-id}")
    private String keyBusinessId;

    @Override
    public AbsGetApproveRes getApproveCreate(CreateBusinessMCReq req) {
        req.setModule(module);
        req.setActionType(create);
        req.setObjectId(null);
        req.getPayload().setRequestCreatedAt(DateTimes.toEpochSecond(new Date()));
        return createChange(HttpEntityService.getHttpEntity(req));
    }

    @Override
    public AbsGetApproveRes getApproveUpdate(UpdateBusinessMCReq req) {
        req.setModule(module);
        req.setActionType(update);
        req.getRequestParamsCallback().put(keyBusinessId, req.getPayload().getBusinessId().toString());
        req.setObjectId(req.getPayload().getBusinessId().toString());
        req.getPayload().setRequestCreatedAt(DateTimes.toEpochSecond(new Date()));
        return createChange(HttpEntityService.getHttpEntity(req));
    }
}
