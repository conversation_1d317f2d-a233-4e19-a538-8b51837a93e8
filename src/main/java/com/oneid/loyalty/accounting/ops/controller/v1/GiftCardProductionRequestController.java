package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardSearchReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardProductionRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collections;

@RestController
@RequestMapping("v1/gift-card-requests")
public class GiftCardProductionRequestController extends BaseController {

    @Autowired
    private OpsGiftCardProductionRequestService opsGiftCardProductionRequestService;

    @Autowired
    private OpsGiftCardService opsGiftCardService;

    @PostMapping
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> createNewChangeRequest(@Valid @RequestBody CreateGiftCardRequestReq request) {
        return success(Collections.singletonMap("review_id",
                opsGiftCardProductionRequestService.createNewChangeRequest(request)));
    }

    @GetMapping("/requests/available/{gift_card_request_id}/export")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.EXPORT}),
    })
    public ResponseEntity<?> getExportedGiftCardByRequestId(@PathVariable("gift_card_request_id") Integer requestId) {
        ResourceDTO resourceDTO = opsGiftCardService.exportGiftCard(requestId);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", String.format("attachment; filename=%s", resourceDTO.getFilename()));
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        headers.add("Access-Control-Expose-Headers", "*");
        return new ResponseEntity<>(resourceDTO.getResource(), headers, HttpStatus.OK);
    }

    @GetMapping("in-review")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListInReview(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_date", required = false) Integer fromDate,
            @RequestParam(value = "to_date", required = false) Integer toDate,
            @MakerCheckerOffsetPageable Pageable pageable) {
        Page<GiftCardProductionRequestRes> page = opsGiftCardProductionRequestService
                .searchInReview(approvalStatus, fromDate, toDate, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("in-review/{review_id}")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getDetailInReview(@PathVariable("review_id") Integer reviewId) {
        return success(opsGiftCardProductionRequestService.getDetailInReview(reviewId));
    }

    @GetMapping("available")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListAvailable(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "corporation_id", required = false) Integer corporationId,
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "store_id", required = false) Integer storeId,
            @RequestParam(value = "cpr_batch_no", required = false) Long cprBatchNo,
            @RequestParam(value = "status", required = false) EGiftCardStatus status,
            @RequestParam(value = "generation_ind", required = false) EGiftCardIndicator generationInd,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "batch_type", required = false) EGiftCardBatchType batchType
    ) {
        GiftCardSearchReq req = GiftCardSearchReq.builder()
                .generationStatus(generationInd)
                .cprBatchNo(cprBatchNo)
                .businessId(businessId)
                .corporationId(corporationId)
                .chainId(chainId)
                .storeId(storeId)
                .generationStatus(generationInd)
                .giftCardStatus(status)
                .approvalStatus(approvalStatus)
                .batchType(batchType)
                .build();

        Pageable pageable = new OffsetBasedPageRequest(offset, limit);

        Page<GiftCardProductionRequestRes> page = opsGiftCardProductionRequestService
                .searchAvailable(req, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("available/{request_id}")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getDetailAvailable(@PathVariable("request_id") Integer requestId) {
        return success(opsGiftCardProductionRequestService.getDetailAvailable(requestId));
    }

    @PutMapping("{request_id}")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.EDIT})
    public ResponseEntity<?> update(@PathVariable("request_id") Integer requestId,
                                    @Valid @RequestBody UpdateGiftCardRequestReq req) {
        return success(Collections.singletonMap("review_id", opsGiftCardProductionRequestService.update(requestId, req)));
    }

    @PostMapping("/approve")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsGiftCardProductionRequestService.approve(req);
        return success(null);
    }
}
