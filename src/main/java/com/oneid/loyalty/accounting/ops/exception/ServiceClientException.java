package com.oneid.loyalty.accounting.ops.exception;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ServiceClientException  extends RuntimeException {
    private static final long serialVersionUID = 1771038915821927818L;
    
    private int errorCode;
    private String errorMessage;
}
