package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.FormulaGroupReq;
import com.oneid.loyalty.accounting.ops.model.res.FormulaGroupRes;
import com.oneid.oneloyalty.common.entity.Scheme;

public interface OpsFormulaService {
    void verify(FormulaGroupReq req);

    FormulaGroupRes create(Scheme scheme, FormulaGroupReq req);

    FormulaGroupRes getFormulaGroupByScheme(Scheme scheme);

    FormulaGroupRes updateFormula(Scheme scheme, FormulaGroupReq formulaGroup);
}