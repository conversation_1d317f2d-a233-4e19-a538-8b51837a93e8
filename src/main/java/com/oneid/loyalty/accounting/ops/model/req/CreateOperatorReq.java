package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CreateOperatorReq.CreateOperatorReqBuilder.class)
public class CreateOperatorReq {
    @NotBlank(message = "operator_id must not be blank")
    @OpsName(message = "operator_id must not be contain character special")
    @Length(max = 10, message = "operator_id max length is 10 character")
    private String operatorId;

    @Length(max = 10, message = "operator_id max length is 10 character")
    @Pattern(regexp = "^[0-9]*$", message = "pin code accepts only numbers")
    private String pinCode;

    @NotNull(message = "business_id must not be blank")
    private Integer businessId;

    @Valid
    private PayloadOperatorReq payload;

    @NotNull(message = "status must not be null")
    private ECommonStatus status;

    private LegacyPayload legacyPayload;

    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateOperatorReqBuilder {
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonDeserialize(builder = LegacyPayload.LegacyPayloadBuilder.class)
    public static class LegacyPayload {
        private List<String> operatorCorps;
        private List<String> operatorChains;
        private List<String> operatorStores;
        private List<String> operatorTerminals;

        @JsonPOJOBuilder(withPrefix = "")
        public static class LegacyPayloadBuilder {
        }
    }
}