package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.EAdjustmentType;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionHistory;
import com.oneid.oneloyalty.common.entity.UserProfile;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@JsonInclude(value = Include.NON_NULL)
@EqualsAndHashCode
public class TransactionRes {
    @JsonProperty("id")
    private String id;

    @JsonProperty("transaction_ref")
    private String transactionRef;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("member_id")
    private Long memberId;

    @JsonProperty("account_code")
    private String accountCode;

    @JsonProperty("account_type")
    private EOpsIdType accountType;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("corporation_code")
    private String corporationCode;

    @JsonProperty("corporation_name")
    private String corporationName;

    @JsonProperty("chain_id")
    private Integer chainId;

    @JsonProperty("chain_code")
    private String chainCode;

    @JsonProperty("chain_name")
    private String chainName;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("store_code")
    private String storeCode;

    @JsonProperty("store_name")
    private String storeName;

    @JsonProperty("terminal_id")
    private Integer terminalId;

    @JsonProperty("terminal_code")
    private String terminalCode;

    @JsonProperty("terminal_name")
    private String terminalName;

    @JsonProperty("invoice_no")
    private String invoiceNo;

    @JsonProperty("original_invoice_no")
    private String originalInvoiceNo;

    @JsonProperty("transaction_ymd")
    private Integer transactionYmd;

    @JsonProperty("transaction_time")
    private Long transactionTime;

    @JsonProperty("transaction_type")
    private String type;
    
    @JsonProperty("transaction_display_type")
    private EOpsTransactionType transactionDisplayType;

    @JsonProperty("cancellation")
    private String cancellation;

    @JsonProperty("cancellation_type")
    private EOpsCancellationType cancellationType;

    @JsonProperty("cancellation_time")
    private Long cancellationTime;

    @JsonProperty("gmv")
    private String gmv;

    @JsonProperty("award_point")
    private String awardPoint;

    @JsonProperty("redeem_point")
    private String redeemPoint;

    @JsonProperty("adjust_point")
    private String adjustPoint;

    @JsonProperty("gross_amount")
    private String grossAmount;

    @JsonProperty("nett_amount")
    private String nettAmount;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("refunded_invoice")
    private TransactionRefunded refundedInvoices;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("point_redeem_details")
    private List<PointRedeemDetail> pointRedeemDetails;

    @JsonProperty("point_award_details")
    private List<PointAwardDetail> pointAwardDetails;

    @JsonProperty("adjustment_type")
    private EAdjustmentType adjustmentType;

    @JsonProperty("poolname")
    private String poolName;

    @JsonProperty("reason_name")
    private String reasonName;

    @JsonProperty("reason_en_name")
    private String reasonEnName;

    @JsonProperty("sell_rate")
    private Double sellRate;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("pools")
    private List<ShortEntityRes> pools;

    @Getter
    @Setter
    @AllArgsConstructor
    public static class TransactionRefunded {
        @JsonProperty("invoice_no")
        private String invoiceNo;

        @JsonProperty("transaction_id")
        private String transactionId;

        @JsonProperty("refund_gross_amount")
        private BigDecimal refundGrossAmount;
    }

    public static TransactionRes valueOf(
            Map<ETransactionType, List<BigDecimal>> points,
            TransactionHistory transactionHistory,
            EOpsTransactionType transactionType,
            Business business,
            Corporation corporation,
            Chain chain,
            Store store,
            Pos pos,
            Program program,
            Member member,
            UserProfile userProfile
    ) {

        TransactionRes transactionRes = new TransactionRes();

        transactionRes.setTransactionRef(String.valueOf(transactionHistory.getPointTransactionId()));
        transactionRes.setBusinessCode(business != null ? business.getCode() : null);
        transactionRes.setBusinessName(business != null ? business.getName() : null);
        transactionRes.setProgramCode(program != null ? program.getCode() : null);
        transactionRes.setProgramName(program != null ? program.getName() : null);
        transactionRes.setMemberCode(member != null ? member.getMemberCode() : null);
        transactionRes.setUserId(userProfile.getMasterUserId());
        transactionRes.setAccountCode(transactionHistory.getMemberProductAccountCode());
        transactionRes.setAccountType(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()));
        transactionRes.setCorporationCode(corporation != null ? corporation.getCode() : null);
        transactionRes.setCorporationName(corporation != null ? corporation.getName() : null);
        transactionRes.setChainCode(chain != null ? chain.getCode() : null);
        transactionRes.setChainName(chain != null ? chain.getName() : null);
        transactionRes.setStoreCode(store != null ? store.getCode() : null);
        transactionRes.setStoreName(store != null ? store.getName() : null);
        transactionRes.setTerminalCode(pos != null ? pos.getCode() : null);
        transactionRes.setTerminalName(pos != null ? pos.getName() : null);
        transactionRes.setInvoiceNo(transactionHistory.getInvoiceNo());
        transactionRes.setOriginalInvoiceNo(transactionHistory.getOriginalInvoiceNo());
        transactionRes.setTransactionTime(transactionHistory.getTransactionTime() != null ? transactionHistory.getTransactionTime().toInstant().getEpochSecond() : null);

        transactionRes.setTransactionDisplayType(transactionType);

        transactionRes.setCancellationType(EOpsCancellationType.of(transactionHistory.getCancellationType()));

        transactionRes.setGrossAmount(transactionHistory.getGrossAmount() != null ? transactionHistory.getGrossAmount().toString() : null);
        transactionRes.setNettAmount(transactionHistory.getNettAmount() != null ? transactionHistory.getNettAmount().toString() : null);
        transactionRes.setDescription(transactionHistory.getDescription());
        transactionRes.setStatus(transactionHistory.getStatus() != null ? transactionHistory.getStatus().getValue() : null);
        transactionRes.setCancellation(transactionHistory.getCancellation());
        transactionRes.setCancellationTime(transactionHistory.getCancellationTime() != null ? transactionHistory.getCancellationTime().toInstant().getEpochSecond() : null);

        BigDecimal awardPoint = BigDecimal.ZERO;
        BigDecimal redeemPoint = BigDecimal.ZERO;

        transactionRes.setAwardPoint(awardPoint.toString());
        transactionRes.setRedeemPoint(redeemPoint.toString());

        if (transactionHistory.getType().equals(ETransactionType.ADJUSTMENT)) {
            EAdjustmentType adjustmentType =
                    (transactionHistory.getAwardPoint() != null && transactionHistory.getAwardPoint().compareTo(BigDecimal.ZERO) == 1)
                            ? EAdjustmentType.AWARD : EAdjustmentType.REDEEM;
            
            if (EOpsTransactionType.ADJUSTMENT.equals(transactionType)) {
                awardPoint = transactionHistory.getAwardPoint() != null ? transactionHistory.getAwardPoint() : BigDecimal.valueOf(0);
                redeemPoint = transactionHistory.getRedeemPoint() != null ? transactionHistory.getRedeemPoint() : BigDecimal.valueOf(0);
                transactionRes.setAwardPoint(awardPoint.toString());
                transactionRes.setRedeemPoint(redeemPoint.toString());
            }

            transactionRes.setAdjustmentType(adjustmentType);
        } else {
            awardPoint = points.containsKey(ETransactionType.AWARD) ? points.get(ETransactionType.AWARD).stream().reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
            redeemPoint = points.containsKey(ETransactionType.REDEEM) ? points.get(ETransactionType.REDEEM).stream().reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
            
            transactionRes.setAwardPoint(awardPoint.toString());
            transactionRes.setRedeemPoint(redeemPoint.toString());
        }
        
        return transactionRes;
    
    }
    
    public static TransactionRes valueOf(
            Map<ETransactionType, List<BigDecimal>> points,
            TransactionHistory transactionHistory,
            EOpsTransactionType transactionType,
            Business business,
            Corporation corporation,
            Chain chain,
            Store store,
            Pos pos,
            Program program,
            Member member,
            TransactionRefunded refundedInvoice,
            List<PointAwardDetail> pointAwardDetails,
            List<PointRedeemDetail> pointRedeemDetails,
            ReasonCode reasonCode,
            Pool pool,
            Double sellRate,
            List<ShortEntityRes> pools
    ) {
        TransactionRes transactionRes = new TransactionRes();

        transactionRes.setId(transactionHistory.getPointTransactionId());
        transactionRes.setTransactionRef(String.valueOf(transactionHistory.getPointTransactionId()));
        transactionRes.setBusinessId(transactionHistory.getBusinessId());
        transactionRes.setBusinessCode(business != null ? business.getCode() : null);
        transactionRes.setBusinessName(business != null ? business.getName() : null);
        transactionRes.setProgramId(transactionHistory.getProgramId());
        transactionRes.setProgramCode(program != null ? program.getCode() : null);
        transactionRes.setProgramName(program != null ? program.getName() : null);
        transactionRes.setMemberId(transactionHistory.getMemberId());
        transactionRes.setMemberCode(member != null ? member.getMemberCode() : null);
        transactionRes.setAccountCode(transactionHistory.getMemberProductAccountCode());
        transactionRes.setAccountType(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()));
        transactionRes.setCorporationId(transactionHistory.getCorporationId());
        transactionRes.setCorporationCode(corporation != null ? corporation.getCode() : null);
        transactionRes.setCorporationName(corporation != null ? corporation.getName() : null);
        transactionRes.setChainId(transactionHistory.getChainId());
        transactionRes.setChainCode(chain != null ? chain.getCode() : null);
        transactionRes.setChainName(chain != null ? chain.getName() : null);
        transactionRes.setStoreId(transactionHistory.getStoreId());
        transactionRes.setStoreCode(store != null ? store.getCode() : null);
        transactionRes.setStoreName(store != null ? store.getName() : null);
        transactionRes.setTerminalId(transactionHistory.getPosId());
        transactionRes.setTerminalCode(pos != null ? pos.getCode() : null);
        transactionRes.setTerminalName(pos != null ? pos.getName() : null);
        transactionRes.setInvoiceNo(transactionHistory.getInvoiceNo());
        transactionRes.setOriginalInvoiceNo(transactionHistory.getOriginalInvoiceNo());
        transactionRes.setTransactionTime(transactionHistory.getTransactionTime() != null ? transactionHistory.getTransactionTime().toInstant().getEpochSecond() : null);
        transactionRes.setTransactionYmd(transactionHistory.getTransactionYmd());

        transactionRes.setType(transactionType.getValue());

        transactionRes.setGmv(transactionHistory.getGmv() != null ? transactionHistory.getGmv().toString() : null);
        transactionRes.setGrossAmount(transactionHistory.getGrossAmount() != null ? transactionHistory.getGrossAmount().toString() : null);
        transactionRes.setNettAmount(transactionHistory.getNettAmount() != null ? transactionHistory.getNettAmount().toString() : null);
        transactionRes.setDescription(transactionHistory.getDescription());
        transactionRes.setStatus(transactionHistory.getStatus() != null ? transactionHistory.getStatus().getValue() : null);
        transactionRes.setRefundedInvoices(refundedInvoice);
        transactionRes.setCreatedAt(transactionHistory.getCreatedAt() != null ? transactionHistory.getCreatedAt().toInstant().getEpochSecond() : null);
        transactionRes.setCancellation(transactionHistory.getCancellation());
        transactionRes.setCancellationType(EOpsCancellationType.of(transactionHistory.getCancellationType()));
        transactionRes.setCancellationTime(transactionHistory.getCancellationTime() != null ? transactionHistory.getCancellationTime().toInstant().getEpochSecond() : null);

        transactionRes.setPointAwardDetails(pointAwardDetails);
        transactionRes.setPointRedeemDetails(pointRedeemDetails);

        BigDecimal awardPoint = BigDecimal.ZERO;
        BigDecimal redeemPoint = BigDecimal.ZERO;

        transactionRes.setAwardPoint(awardPoint.toString());
        transactionRes.setRedeemPoint(redeemPoint.toString());


        transactionRes.setSellRate(sellRate);
        if (transactionHistory.getType().equals(ETransactionType.ADJUSTMENT)) {
            EAdjustmentType adjustmentType =
                    (transactionHistory.getAwardPoint() != null && transactionHistory.getAwardPoint().compareTo(BigDecimal.ZERO) == 1)
                            ? EAdjustmentType.AWARD : EAdjustmentType.REDEEM;
            
            if (EOpsTransactionType.ADJUSTMENT.equals(transactionType)) {
                awardPoint = transactionHistory.getAwardPoint() != null ? transactionHistory.getAwardPoint() : BigDecimal.valueOf(0);
                redeemPoint = transactionHistory.getRedeemPoint() != null ? transactionHistory.getRedeemPoint() : BigDecimal.valueOf(0);
                transactionRes.setAwardPoint(awardPoint.toString());
                transactionRes.setRedeemPoint(redeemPoint.toString());
            }

            transactionRes.setAdjustmentType(adjustmentType);
            transactionRes.setPoolName(pool != null ? pool.getName() : null);
            transactionRes.setReasonName(reasonCode != null ? reasonCode.getName() : null);
            transactionRes.setReasonEnName(reasonCode != null ? reasonCode.getEnName() : null);
        } else {
            awardPoint = points.containsKey(ETransactionType.AWARD) ? points.get(ETransactionType.AWARD).stream().reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
            redeemPoint = points.containsKey(ETransactionType.REDEEM) ? points.get(ETransactionType.REDEEM).stream().reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO;
            
            transactionRes.setAwardPoint(awardPoint.toString());
            transactionRes.setRedeemPoint(redeemPoint.toString());
        }

        transactionRes.setPools(pools);
        
        return transactionRes;
    }

    public static TransactionRes valueOf(
            Map<ETransactionType, List<BigDecimal>> points,
            TransactionHistory transactionHistory,
            EOpsTransactionType transactionType,
            Business business,
            Corporation corporation,
            Chain chain,
            Store store,
            Pos pos,
            Program program,
            Member member,
            UserProfile userProfile,
            TransactionRefunded refundedInvoice,
            List<PointAwardDetail> pointAwardDetails,
            List<PointRedeemDetail> pointRedeemDetails,
            ReasonCode reasonCode,
            Pool pool,
            Double sellRate
    ) {
        TransactionRes transactionRes = valueOf(points, transactionHistory, transactionType, business,
                corporation, chain, store, pos, program, member, refundedInvoice,
                pointAwardDetails, pointRedeemDetails, reasonCode, pool, sellRate, null);
        if (userProfile != null) {
            transactionRes.setUserId(userProfile.getMasterUserId());
        }
        return transactionRes;
    }

    public static String convertBigDecimal(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }
        return bigDecimal.toString();
    }
}