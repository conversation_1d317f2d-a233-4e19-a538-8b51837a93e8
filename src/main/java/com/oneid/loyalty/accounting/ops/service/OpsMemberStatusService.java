package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailAvailableRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionInfo;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface OpsMemberStatusService {
    MakerCheckerInternalMakerRes createRequest(CreateMemberStatusReq req);

    Page<MemberStatusInReviewRes> getListInReviewRequests(EApprovalStatus approvalStatus,
                                                          String fromCreatedAt, String toCreatedAt,
                                                          String fromReviewedAt, String toReviewedAt,
                                                          String createdBy, String checkedBy,
                                                          Integer offset, Integer limit);

    MemberStatusDetailInReviewRes getInReviewRequestById(Long reviewId);

    void approve(ApprovalReq req);

    MemberStatusDetailAvailableRes getChangeableByRequestId(Integer memberStatusId);

    MakerCheckerInternalMakerRes update(Integer memberStatusId, CreateMemberStatusReq req);

    Map<String, List<ProgramFunctionInfo>> getFunctions(Integer programId);

    Page<MemberStatusAvaiRes> getListAvailableRequests(Integer businessId,
                                                       Integer programId,
                                                       String code,
                                                       String status,
                                                       Integer offset,
                                                       Integer limit);

    MemberStatusDetailAvaiRes getAvailableRequestById(Integer id);
}