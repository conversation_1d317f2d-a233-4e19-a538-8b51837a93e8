package com.oneid.loyalty.accounting.ops.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonDeserialize(builder = MakerCheckerChangeRequestResponse.MakerCheckerChangeRequestResponseBuilder.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MakerCheckerChangeRequestResponse {
    private Long id;
    private Long makerId;
    private String makerName;
    private String module;
    private String actionType;
    private String objectId;
    @JsonPOJOBuilder(withPrefix = "")
    public static class MakerCheckerChangeRequestResponseBuilder {}
}
