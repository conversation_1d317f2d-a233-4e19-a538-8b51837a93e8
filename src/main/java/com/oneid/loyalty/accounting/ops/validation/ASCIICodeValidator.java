package com.oneid.loyalty.accounting.ops.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class ASCIICodeValidator implements ConstraintValidator<ASCIICode, String> {
    private static final Pattern ASCII_PATTERN = Pattern.compile("^[ -~]+$");
    private static final Pattern NON_WHITESPACE_PATTERN = Pattern.compile("^[\\S]+$");

    private boolean nonWhitespace;
    private String noneWhitespaceMessage;

    @Override
    public void initialize(ASCIICode constraintAnnotation) {
        nonWhitespace = constraintAnnotation.nonWhitespace();
        noneWhitespaceMessage = constraintAnnotation.nonWhitespaceMessage();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.length() == 0) {
            return true;
        }
        if (nonWhitespace && !NON_WHITESPACE_PATTERN.matcher(value).matches()) {
            context.buildConstraintViolationWithTemplate(noneWhitespaceMessage).addConstraintViolation();

            return false;
        }

        return ASCII_PATTERN.matcher(value).matches();
    }

}
