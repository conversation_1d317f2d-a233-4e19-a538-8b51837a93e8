package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.*;

import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CounterRes extends VersioningRes {
    private static final long serialVersionUID = -971272362907140445L;
    private String editKey;
    private Long id;
    private Integer requestId;
    private Integer reviewId;
    private Integer counterId;

    private BigDecimal allowReversal;

    private Integer businessId;
    private String businessName;
    private String businessCode;

    private Integer programId;
    private String programName;
    private String programCode;

    private Integer nextVersion;
    private String name;
    private String description;
    private String code;
    private String rejectedReason;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;
    
    private ECounterPeriod period;
    private String periodDisplay;
    private Integer restoredFromVersion;
    private Integer restoredFromId;
    private Integer version;
    private ECounterLevel counterLevel;
//    private ECounterPeriod counterPeriod;
    private ECounterType counterType;
    private String counterTypeDisplay;
    private ECounterAttribute counterAttribute;
    private ECommonStatus counterStatus;
    private EApprovalStatus approvalStatus;
    private String reason;
    private ECommonStatus requestStatus;

    private EBoolean enableRevert;
    
    private EServiceType serviceType;
    
    private List<RuleRes> rules;

    private BigDecimal resetValue;

    private List<CounterServicesRes> counterServicesRes;
    
    private String createdBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    private String reviewedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date reviewedAt;
    private Date updatedAt;
    private String updatedBy;
    private Integer counterStatusHeader;

}
