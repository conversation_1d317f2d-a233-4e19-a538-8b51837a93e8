package com.oneid.loyalty.accounting.ops.util.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Paths;
import java.util.List;

public class ExcelWriter<T> {
    private String formatDate = "dd-MM-yyyy";
    private boolean headerStyleBold = true;
    private int batchSizeFlush = 100;

    public String export(String fileName, String sheetName, List<String> columns, List<T> data, DataRowGetter<T> dataRowGetter) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(batchSizeFlush);
        Sheet sheet = workbook.createSheet(sheetName);

        // Create Header Row
        CellStyle headerCellStyle = this.getCellStyleHeader(workbook);
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < columns.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns.get(i));
            cell.setCellStyle(headerCellStyle);
        }

        // Create Data Rows
        int rowNum = 1;
        for (T object : data) {
            Row row = sheet.createRow(rowNum++);
            List<String> dataRow = dataRowGetter.get(object);
            for (int i = 0; i < dataRow.size(); i++) {
                row.createCell(i).setCellValue(dataRow.get(i));
            }
        }

        // Write file
        String localPath = getLocalPath(fileName);
        try {
            File file = new File(localPath);
            file.createNewFile();
            FileOutputStream fileOut = new FileOutputStream(localPath, false);
            workbook.write(fileOut);
            workbook.close();
            fileOut.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return localPath;
    }

    private String getLocalPath(String fileName) {
        return Paths.get("/tmp", fileName).toString();
    }

    CellStyle getCellStyleHeader(Workbook workbook) {
        Font headerFont = workbook.createFont();
        headerFont.setBold(this.headerStyleBold);
        CellStyle cellStyleHeader = workbook.createCellStyle();
        cellStyleHeader.setFont(headerFont);
        return cellStyleHeader;
    }
}
