package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.EOpsCardExpireUnit;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.util.DateTimes;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class CardPolicyRes extends BaseDetail<CardPolicy> {

    private Integer id;

    private String name;
    private String description;
    private ECardPolicyType type;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    private Integer length;

    @JsonProperty("max_card_per_cpr")
    private Integer maxCardPerCPR;

    @JsonProperty("card_expiration_type")
    private EOpsCardExpireUnit cardExpirationType;

    @JsonProperty("card_expiration_value")
    private Long cardExpirationValue;

    @JsonProperty("card_expiration_trigger")
    private ECardPolicyTrigger cardExpirationTrigger;

    @JsonProperty("card_retention_type")
    private ERetensionType cardRetentionType;

    @JsonProperty("card_retention_value")
    private Integer cardRetentionValue;

    @JsonProperty("card_retention_trigger")
    private ECardPolicyTrigger cardRetentionTrigger;

    private ECommonStatus status;

    @JsonProperty("has_checksum")
    private EHasChecksum hasChecksum;

    @JsonProperty("qr_url")
    private String qrUrl;

    public CardPolicyRes(CardPolicy cardPolicy) {
        super(cardPolicy);
    }

    public CardPolicyRes() {
        super();
    }


    public static CardPolicyRes valueOf(CardPolicy cardPolicy) {
        CardPolicyRes res = new CardPolicyRes(cardPolicy);

        res.setId(cardPolicy.getId());
        res.setName(cardPolicy.getName());
        res.setDescription(cardPolicy.getDescription());
        res.setType(cardPolicy.getPolicyType());
        res.setBusinessId(cardPolicy.getBusinessId());
        res.setLength(cardPolicy.getCardNoLength());
        res.setMaxCardPerCPR(cardPolicy.getMaxCardCpr());

        if (cardPolicy.getExpirationFixedTime() == null) {
            ECardExpireUnit expirationUnit = cardPolicy.getExpirationUnit();
            if (ECardExpireUnit.DAY.equals(expirationUnit)) {
                res.setCardExpirationType(EOpsCardExpireUnit.DAY);
                res.setCardExpirationValue(Long.valueOf(cardPolicy.getExpirationPeriod()));
            } else if (ECardExpireUnit.MONTH.equals(expirationUnit)) {
                res.setCardExpirationType(EOpsCardExpireUnit.MONTH);
                res.setCardExpirationValue(Long.valueOf(cardPolicy.getExpirationPeriod()));
            } else if (ECardExpireUnit.YEAR.equals(expirationUnit)) {
                res.setCardExpirationType(EOpsCardExpireUnit.YEAR);
                res.setCardExpirationValue(Long.valueOf(cardPolicy.getExpirationPeriod()));
            }
        } else if (ECardExpireUnit.NEVER.equals(cardPolicy.getExpirationUnit())) {
            res.setCardExpirationType(EOpsCardExpireUnit.NEVER);
        } else {
            res.setCardExpirationType(EOpsCardExpireUnit.FIXED_TIME);
            res.setCardExpirationValue(DateTimes.toEpochSecond(cardPolicy.getExpirationFixedTime()));
        }

        res.setCardExpirationTrigger(cardPolicy.getExpirationTrigger());
        res.setCardRetentionType(cardPolicy.getRetentionType());
        res.setCardRetentionValue(cardPolicy.getRetentionPeriod());
        res.setCardRetentionTrigger(cardPolicy.getRetentionTrigger());
        res.setStatus(cardPolicy.getStatus());
        res.setHasChecksum(cardPolicy.getHasChecksum());
        res.setQrUrl(cardPolicy.getQrUrl());
        return res;
    }
}