package com.oneid.loyalty.accounting.ops.model.res;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierRes implements Serializable {
    private Integer id;

    private Integer requestId;

    @Deprecated
    private Integer reviewId;

    private Integer businessId;

    private String businessName;

    private Integer programId;

    private String programName;

    private String programCode;

    private Integer tierPolicyId;

    private String tierPolicyName;

    private Integer tierRank;

    private String description;

    private String tierCode;

    private String enDescription;

    private String tierName;

    private String tierEnName;

    private String cardBackgroundUrl;

    private String badgeIconUrl;

    private Long qualifyPoint;

    private ECounterPeriod expirePeriodType;

    private Integer expirePeriod;

    private ECommonStatus tierStatus;

    private EApprovalStatus approvalStatus;

    private Boolean nonePrivilege;

    private List<TierPrivilegeRes> privileges;

    private List<RuleRes> rules;

    private Integer version;

    private Integer nextVersion;

    private String editKey;

    private Date lastUpdateAt;

    private String lastUpdateBy;

    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    private String reviewedBy;
    private Long reviewedAt;
    private Long createdAt;
    private Long updatedAt;
    private Long approvedAt;
}
