package com.oneid.loyalty.accounting.ops.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum EOpsTransactionType {
    SALE("SALE"),

    EARN("EARN"),

    BURN("BURN"),
    
    ADJUSTMENT("ADJUSTMENT");

    @JsonValue
    private String value;

    public static EOpsTransactionType lookup(String value) {
        if (StringUtils.isEmpty(value))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, EOpsTransactionType> {
        @Override
        public EOpsTransactionType convert(String value) {
            return EOpsTransactionType.lookup(value);
        }
    }
}
