package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.scheme;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveReq;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
public class UpdateReq extends AbsGetApproveReq {
    @JsonProperty("payload")
    private UpdateSchemeReq payload;

    @Override
    public HttpEntity build(HttpHeaders headers) {
        return new HttpEntity<>(this, headers);
    }
}