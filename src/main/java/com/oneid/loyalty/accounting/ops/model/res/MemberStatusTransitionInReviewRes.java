package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusTransitionInReviewRes {
    private Long id;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private ShortEntityRes beginningStatus;

    private ECommonStatus status;

    private List<StatusTransition> statusTransitions;

    private String memberStatusCode;
    private String reason;

    private String memberStatusName;

    private String createdBy;

    private String updatedBy;
    private String reviewedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date reviewedAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private Integer version;

    private EApprovalStatus approvalStatus;

    private ERequestType requestType;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class StatusTransition {

        @NotNull(message = "destination_status not null")
        private ShortEntityRes destinationStatus;

        @NotNull(message = "is_automatic_transition not null")
        private EBoolean isAutomaticTransition;

        private List<RuleRes> ruleResList;

        private boolean archive;

        private EConditionType ruleLogic;
    }
}