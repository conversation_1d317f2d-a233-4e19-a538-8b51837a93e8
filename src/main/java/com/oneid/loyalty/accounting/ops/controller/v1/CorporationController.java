package com.oneid.loyalty.accounting.ops.controller.v1;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CorporationUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.CorporationEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.CorporationRes;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/corporations")
public class CorporationController extends BaseController {
    @Autowired
    OpsCorporationService opsCorporationService;

    @GetMapping(value = "/all-by-business/{business_id}")
    public ResponseEntity<?> getCardsByMemberCode(
            @PathVariable(name = "business_id", required = true) Integer businessId,
            @RequestParam(value = "status", required = false) ECommonStatus status) {

        SearchCorporationReq req = new SearchCorporationReq();
        req.setStatus(status);
        req.setBusinessId(businessId);
        return success(opsCorporationService.searchListCorporation(req));
    }

    @GetMapping("enum-all")
    public ResponseEntity<?> getEnumAll() {
        List<CorporationEnumAll> result = opsCorporationService.getEnumAll();
        return success(result);
    }

    @GetMapping("")
    @Authorize(role = AccessRole.CORPORATION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterCorporations(@RequestParam(name = "business_id", required = false) Integer businessId,
                                                @RequestParam(name = "corporation_name", required = false) String corporationName,
                                                @RequestParam(name = "corporation_code", required = false) String corporationCode,
                                                @RequestParam(name = "status", required = false)
                                                @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                                @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                                @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<CorporationRes> page = opsCorporationService.filter(businessId, corporationName, corporationCode, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.CORPORATION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCorporation(@PathVariable(value = "id") Integer id) {
        CorporationRes result = opsCorporationService.get(id);
        return success(result);
    }

    @GetMapping("check")
    public ResponseEntity<?> getCorporation(@RequestParam(value = "corporation_code", required = true) String code) {
        CorporationRes result = opsCorporationService.check(code);
        return success(result);
    }

    @PostMapping("create")
    @Authorize(role = AccessRole.CORPORATION, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createCorporation(@RequestBody @Valid CorporationCreateReq corporationCreateReq) {
        return success(opsCorporationService.add(corporationCreateReq));
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.CORPORATION, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCorporation(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid CorporationUpdateReq corporationUpdateReq) {
        this.opsCorporationService.update(id, corporationUpdateReq);
        return success(null);
    }
}