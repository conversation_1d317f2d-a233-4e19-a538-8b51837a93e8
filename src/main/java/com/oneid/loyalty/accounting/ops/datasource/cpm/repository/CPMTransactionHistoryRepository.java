package com.oneid.loyalty.accounting.ops.datasource.cpm.repository;

import com.oneid.loyalty.accounting.ops.datasource.cpm.entity.CPMTransactionHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CPMTransactionHistoryRepository
        extends JpaRepository<CPMTransactionHistory, Long>, JpaSpecificationExecutor<CPMTransactionHistory> {

    List<CPMTransactionHistory> findByMasterId(Long masterId);
}
