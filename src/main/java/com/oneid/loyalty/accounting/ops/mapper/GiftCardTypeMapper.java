package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.GiftCardType;

public class GiftCardTypeMapper {
    public static GiftCardType toGiftCardTypeOne(GiftCardTypeCreateReq request) {
        GiftCardType giftCardType = new GiftCardType();
        giftCardType.setCode(request.getCode());
        giftCardType.setDescription(request.getDescription());
        giftCardType.setBusinessId(request.getBusinessId());
        giftCardType.setProgramId(request.getProgramId());
        giftCardType.setPrice(request.getPrice());
        giftCardType.setBaseCurrencyId(request.getBaseCurrencyId());
        giftCardType.setPoint(request.getPoint());
        giftCardType.setCurrencyId(request.getCurrencyId());
        giftCardType.setPolicyId(request.getGiftCardPolicyId());
        giftCardType.setStatus(ECommonStatus.of(request.getStatus()));
        return giftCardType;
    }

    public static GiftCardType toGiftCardTypeOne(GiftCardType giftCardType, GiftCardTypeUpdateReq request) {
        giftCardType.setDescription(request.getDescription());
        giftCardType.setStatus(ECommonStatus.of(request.getStatus()));
        return giftCardType;
    }
}
