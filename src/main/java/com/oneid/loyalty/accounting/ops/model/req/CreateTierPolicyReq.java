package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.ValidatorUtil;
import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateTierPolicyReq implements Serializable {
    private static final long serialVersionUID = -8806315965575224095L;

    private Integer tierPolicyId;

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @OpsCode
    @NotBlank(message = "'code' must not be blank")
    private String code;

    @OpsName
    @NotBlank(message = "'name' must not be blank")
    private String name;

    private String description;

    @NotNull(message = "'counter_ids' must not be null")
    @Size(max = 100, min = 1, message = "'counter_ids' size in range [1, 100]")
    private List<Integer> counterIds;

    @NotNull(message = "'period_value' must not be null")
    private Integer periodValue;

    @NotNull(message = "'expirations' must not be null")
    @Size(max = 100, min = 1, message = "'expirations' size in range [1, 100]")
    private List<String> expirations;

    @NotNull(message = "'event_qualification_policy' must not be null")
    private Boolean eventQualificationPolicy;

    private List<String> qualificationEventCodes;

    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;

    @JsonDeserialize(using = DateDeserializer.class)
    @NotNull(message = "'start_date' must not be null")
    private Date startDate;

    @JsonDeserialize(using = DateDeserializer.class)
    @NotNull(message = "'end_date' must not be null")
    private Date endDate;

    @AssertTrue(message = "'start_date' invalid")
    public boolean isValidStartDate() {
        return startDate == null || startDate.after(new Date());
    }

    @AssertTrue(message = "'end_date' invalid")
    public boolean isValidEndDate() {
        Date startDateTmp = startDate != null ? startDate : new Date();
        return (endDate == null) || (
                endDate.after(startDateTmp)
                        && endDate.getTime() / DateTimes.DAY_IN_MILLIS != startDateTmp.getTime() / DateTimes.DAY_IN_MILLIS
        );
    }

    @AssertTrue(message = "'qualification_event_ids' must not empty")
    public boolean isValidQualificationEventIds() {
        return !Boolean.TRUE.equals(this.eventQualificationPolicy)
                || (qualificationEventCodes != null && qualificationEventCodes.size() > 0);
    }

    @AssertTrue(message = "'expirations' invalid")
    public boolean isValidExpirations() {
        boolean ok = true;
        for (String expiration : this.expirations) {
            ok &= ValidatorUtil.isValidFormatMMMdd(expiration);
        }
        return ok;
    }
}