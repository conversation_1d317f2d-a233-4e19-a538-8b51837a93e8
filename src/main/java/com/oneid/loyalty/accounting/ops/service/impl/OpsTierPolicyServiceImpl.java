package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ActionOnCounterDto;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierOfPolicyRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPolicyRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierPolicyService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.MessageEvent;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CounterRepository;
import com.oneid.oneloyalty.common.repository.MessageEventRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierPolicyRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.MessageEventService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierPolicyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsTierPolicyServiceImpl implements OpsTierPolicyService {

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private MessageEventRepository eventRepository;

    @Autowired
    private ProgramTierPolicyRepository programTierPolicyRepository;

    @Autowired
    private CounterRepository counterRepository;

    @Autowired
    private ProgramTierRepository programTierRepository;

    @Autowired
    private ProgramTierPolicyService programTierPolicyService;

    @Autowired
    private MessageEventService messageEventService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public TierPolicyRes getAvailableDetail(Integer tierPolicyId) {
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(tierPolicyId);
        Program program = programService.findById(programTierPolicy.getProgramId());
        Business business = businessService.findById(programTierPolicy.getBusinessId());

        List<Counter> counters = counterRepository.findByIdIn(programTierPolicy.getCounterIds());
        List<TierPolicyRes.TierPolicyCounter> tierPolicyCounters = getListCounterAndExpiredFromCounterRequest(counters);

        List<TierPolicyRes.Event> events = getEvents(programTierPolicy.getQualificationEvent());

        // Check edit version
        String editKey = opsReqPendingValidator.generateEditKey(programTierPolicy.getRequestCode(), programTierPolicy.getVersion());

        return TierPolicyRes.builder()
                .requestId(programTierPolicy.getId())
                .id(programTierPolicy.getId())
                .businessName(business.getName())
                .businessId(business.getId())
                .programId(program.getId())
                .programCode(program.getCode())
                .programName(program.getName())
                .counters(tierPolicyCounters)
                .name(programTierPolicy.getName())
                .code(programTierPolicy.getCode())
                .description(programTierPolicy.getDescription())
                .qualificationEvent(programTierPolicy.getQualificationEvent())
                .events(events)
                .periodCycle(counters.get(0).getPeriod())
                .periodCycleValue(programTierPolicy.getCounterPeriod())
                .expirationDates(programTierPolicy.getQualificationPeriod())
                .status(programTierPolicy.getStatus())
                .approvalStatus(EApprovalStatus.APPROVED)
                .editKey(editKey)
                .createdBy(programTierPolicy.getCreatedBy())
                .updatedBy(programTierPolicy.getUpdatedBy())
                .approvedBy(programTierPolicy.getApprovedBy())
                .createdAt(DateTimes.toEpochSecond(programTierPolicy.getCreatedAt()))
                .updatedAt(DateTimes.toEpochSecond(programTierPolicy.getUpdatedAt()))
                .approvedAt(DateTimes.toEpochSecond(programTierPolicy.getApprovedAt()))
                .startDate(DateTimes.toEpochSecond(programTierPolicy.getStartDate()))
                .endDate(DateTimes.toEpochSecond(programTierPolicy.getEndDate()))
                .build();
    }

    @Override
    public TierPolicyRes getInReviewDetail(Integer reviewId) {
        MakerCheckerInternalDataDetailRes detailRes =
                makerCheckerInternalFeignClient.previewDetailDefault(Long.valueOf(reviewId), EMakerCheckerType.PROGRAM_TIER_POLICY);
        CreateTierPolicyReq createTierPolicyReq = this.jsonMapper.convertValue(detailRes.getPayload(), CreateTierPolicyReq.class);
        Program program = programService.findById(createTierPolicyReq.getProgramId());
        Business business = businessService.findById(createTierPolicyReq.getBusinessId());

        List<Counter> counters = counterRepository.findByIdIn(createTierPolicyReq.getCounterIds());
        List<TierPolicyRes.TierPolicyCounter> tierPolicyCounters = getListCounterAndExpiredFromCounterRequest(counters);

        List<TierPolicyRes.Event> events = getEvents(createTierPolicyReq.getQualificationEventCodes());

        return TierPolicyRes.builder()
                .tierPolicyId(createTierPolicyReq.getTierPolicyId())
                .requestId(detailRes.getId().intValue())
                .id(detailRes.getId().intValue())
                .businessName(business.getName())
                .businessId(business.getId())
                .programId(program.getId())
                .programCode(program.getCode())
                .programName(program.getName())
                .counters(tierPolicyCounters)
                .name(createTierPolicyReq.getName())
                .code(createTierPolicyReq.getCode())
                .description(createTierPolicyReq.getDescription())
                .qualificationEvent(createTierPolicyReq.getQualificationEventCodes())
                .events(events)
                .periodCycle(counters.get(0).getPeriod())
                .periodCycleValue(createTierPolicyReq.getPeriodValue())
                .expirationDates(createTierPolicyReq.getExpirations())
                .status(createTierPolicyReq.getStatus())
                .startDate(DateTimes.toEpochSecond(createTierPolicyReq.getStartDate()))
                .endDate(DateTimes.toEpochSecond(createTierPolicyReq.getEndDate()))
                .approvalStatus(EApprovalStatus.of(detailRes.getStatus()))
                .createdBy(detailRes.getMadeByUserName())
                .approvedBy(detailRes.getCheckedByUserName())
                .createdAt(detailRes.getMadeDateToTimestamp())
                .approvedAt(detailRes.getCheckedDateToTimestamp())
                .build();
    }

    @Override
    public void update(Integer tierPolicyId, UpdateTierPolicyReq req) {
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(tierPolicyId);
        req.setTierPolicyId(programTierPolicy.getId());
        req.setBusinessId(programTierPolicy.getBusinessId());
        req.setProgramId(programTierPolicy.getProgramId());
        req.setCode(programTierPolicy.getCode());

        if (req.getStartDate() != null &&
                !req.getStartDate().equals(programTierPolicy.getStartDate()) &&
                req.getStatus() == ECommonStatus.ACTIVE) {
            throw new BusinessException(
                    ErrorCode.BAD_REQUEST,
                    "Cannot update start date with policy active",
                    null
            );
        }

        if (req.getStatus() == ECommonStatus.ACTIVE &&
                !ECommonStatus.ACTIVE.equals(programTierPolicy.getStatus())) {
            Optional<ProgramTierPolicy> activeByProgramOpt =
                    programTierPolicyRepository.findActiveByProgramId(programTierPolicy.getProgramId());
            if (activeByProgramOpt.isPresent()) {
                throw new BusinessException(
                        ErrorCode.PROGRAM_TIER_POLICY_ALREADY_EXIST,
                        "Program has tier policy",
                        null
                );
            }
        }

        // validate Counter
        List<Integer> counterIds = req.getCounters().stream()
                .map(ActionOnCounterDto::getId)
                .collect(Collectors.toList());
        List<Counter> counters = counterRepository.findByIdIn(counterIds);
        verifyCounter(counterIds, counters, programTierPolicy.getBusinessId());
        req.setCounterIds(counterIds);

        // Validate events
        if (Boolean.TRUE.equals(req.getEventQualificationPolicy())) {
            verifyEvents(req.getQualificationEventCodes());
        }

        // Validate edit key
        if (req.getEditKey() != null) {
            opsReqPendingValidator.verifyEditKey(req.getEditKey(), programTierPolicy.getRequestCode(), programTierPolicy.getVersion());
        }

        // Validate request is pending
        makerCheckerInternalFeignClient.validateRequestPending(
                EMakerCheckerType.PROGRAM_TIER_POLICY,
                programTierPolicy.getRequestCode(),
                ErrorCode.TIER_POLICY_REQUEST_IS_ALREADY_REQUESTED
        );

        // Cal maker
        makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.PROGRAM_TIER_POLICY, programTierPolicy.getRequestCode(), req);
    }

    @Override
    public void create(CreateTierPolicyReq req) {
        // Validate business,program
        programService.findActive(req.getProgramId());
        businessService.findActive(req.getBusinessId());
        // Check duplicate code
        Optional<ProgramTierPolicy> programTierPolicyOpt =
                programTierPolicyRepository.findByBusinessIdAndCode(req.getBusinessId(), req.getCode());
        if (programTierPolicyOpt.isPresent()) {
            throw new BusinessException(
                    ErrorCode.TIER_POLICY_CODE_EXISTED,
                    "Tier policy code existed",
                    null
            );
        }
        programTierPolicyOpt = programTierPolicyRepository.findActiveByProgramId(req.getProgramId());
        if (ECommonStatus.ACTIVE.equals(req.getStatus()) && programTierPolicyOpt.isPresent()) {
            throw new BusinessException(
                    ErrorCode.PROGRAM_TIER_POLICY_ALREADY_EXIST,
                    "Program has tier policy",
                    null
            );
        }

        // Validate counters
        List<Counter> counters = counterRepository.findByIdIn(req.getCounterIds());
        verifyCounter(req.getCounterIds(), counters, req.getBusinessId());

        // Validate events
        if (Boolean.TRUE.equals(req.getEventQualificationPolicy())) {
            verifyEvents(req.getQualificationEventCodes());
        }

        // Call to maker
        makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.PROGRAM_TIER_POLICY, UUID.randomUUID().toString(), req);
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.PROGRAM_TIER_POLICY);
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                CreateTierPolicyReq basePayload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateTierPolicyReq.class);
                // Approve edit
                if (basePayload.getTierPolicyId() != null) {
                    UpdateTierPolicyReq updateTierPolicyReq = this.jsonMapper.convertValue(detailResData.getPayload(), UpdateTierPolicyReq.class);
                    processUpdate(detailResData, updateTierPolicyReq);
                } else { // Approve create
                    CreateTierPolicyReq updateTierPolicyReq = this.jsonMapper.convertValue(detailResData.getPayload(), CreateTierPolicyReq.class);
                    processCreate(detailResData, updateTierPolicyReq);
                }
            }
        }

        makerCheckerInternalFeignClient.checkerDefault(req);
    }

    @Override
    public List<ShortEntityRes> getCounterAvailable(Integer businessId, ECounterPeriod period) {
        List<Counter> counters = counterRepository.findByServiceAndPeriodAvailable(
                EServiceType.TIER,
                ECommonStatus.ACTIVE,
                businessId, period,
                Collections.singletonList(ECounterLevel.MEMBER)
        );

        return counters.stream()
                .map(counter -> new ShortEntityRes(counter.getId(), counter.getName(), counter.getCode()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<TierPolicyRes> getInReviews(EApprovalStatus approvalStatus,
                                            String fromCreatedAt, String toCreatedAt,
                                            String fromReviewedAt, String toReviewedAt,
                                            String createdBy, String checkedBy,
                                            Integer offset, Integer limit) {

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.PROGRAM_TIER_POLICY.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromCreatedAt, toCreatedAt),
                        checkedBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromReviewedAt, toReviewedAt)
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<TierPolicyRes> tierPolicyRes = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());

        return new PageImpl<>(tierPolicyRes, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private TierPolicyRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateTierPolicyReq req = this.jsonMapper.convertValue(data.getPayload(), CreateTierPolicyReq.class);
        Program program = programService.findById(req.getProgramId());
        Business business = businessService.findById(req.getBusinessId());

        return TierPolicyRes.builder()
                .requestId(data.getId().intValue())
                .id(data.getId().intValue())
                .businessId(business.getId())
                .businessName(business.getName())
                .programId(program.getId())
                .programName(program.getName())
                .programCode(program.getCode())
                .name(req.getName())
                .code(req.getCode())
                .description(req.getDescription())
                .status(req.getStatus())
                .createdAt(data.getMadeDate() != null ? DateTimes.toEpochSecond(Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant())) : null)
                .createdBy(data.getMadeByUserName())
                .reviewedAt(data.getCheckedDate() != null ? DateTimes.toEpochSecond(Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant())) : null)
                .reviewedBy(data.getCheckedByUserName())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .build();
    }

    @Override
    public Page<TierPolicyRes> getAvailable(FilterTierPolicyReq req, Pageable pageable) {
        Business business = businessService.findById(req.getBusinessId());
        SpecificationBuilder<ProgramTierPolicy> specification = req.getSpecificationBuilder();
        if (req.getProgramId() == null) {
            List<Program> programs = programService.findByBusinessId(business.getId());
            Set<Integer> programIds = programs.stream()
                    .map(Program::getId)
                    .collect(Collectors.toSet());
            specification.add(new SearchCriteria("programId", programIds, SearchOperation.IN));
        }

        Page<ProgramTierPolicy> page = programTierPolicyRepository.findAll(specification, pageable);

        Set<Integer> programIds = page.getContent().stream()
                .map(ProgramTierPolicy::getProgramId)
                .collect(Collectors.toSet());
        Map<Integer, Program> programMap = programService.findByIdIn(programIds)
                .stream().collect(Collectors.toMap(Program::getId, e -> e));

        List<TierPolicyRes> tierPolicyRes = page.stream()
                .map(tierPolicy -> {
                            Program program = programMap.get(tierPolicy.getProgramId());
                            return TierPolicyRes.builder()
                                    .businessId(business.getId())
                                    .businessName(business.getName())
                                    .programId(program.getId())
                                    .programName(program.getName())
                                    .programCode(program.getCode())
                                    .name(tierPolicy.getName())
                                    .code(tierPolicy.getCode())
                                    .description(tierPolicy.getDescription())
                                    .status(tierPolicy.getStatus())
                                    .approvalStatus(EApprovalStatus.APPROVED)
                                    .tierDefaultId(null)
                                    .requestId(tierPolicy.getId())
                                    .id(tierPolicy.getId())
                                    .build();
                        }
                ).collect(Collectors.toList());

        return new PageImpl<>(tierPolicyRes, pageable, page.getTotalElements());
    }

    @Override
    public List<TierOfPolicyRes> getTiersByRequestId(Integer tierPolicyId) {
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(tierPolicyId);
        List<ProgramTier> programTiers = programTierRepository.findByProgramIdAndStatus(
                programTierPolicy.getProgramId(),
                ECommonStatus.ACTIVE,
                Sort.by(Sort.Direction.ASC, "rankNo")
        );

        return programTiers.stream()
                .map(programTier ->
                        TierOfPolicyRes.builder()
                                .id(programTier.getId())
                                .rankNo(programTier.getRankNo())
                                .name(programTier.getName())
                                .code(programTier.getTierCode())
                                .status(programTier.getStatus())
                                .isTierDefault(EBoolean.YES.equals(programTier.getIsTierDefault()))
                                .build())
                .collect(Collectors.toList());
    }

    private void verifyCounter(List<Integer> counterIdRequests, List<Counter> counters, Integer businessId) {
        if (counterIdRequests.size() != counters.size()) {
            throw new BusinessException(
                    ErrorCode.COUNTER_NOT_FOUND,
                    "Counter not found",
                    null
            );
        }
        final Date currentTime = new Date();
        boolean counterMatched = counterIdRequests.stream().allMatch(
                counterId -> counters.stream().anyMatch(
                        counter -> counterId.equals(counter.getId())
                                && counter.getBusinessId().equals(businessId)
                                && counter.getLevel() == ECounterLevel.MEMBER
                                && counter.getServiceType() == EServiceType.TIER
                                && counter.getStatus() == ECommonStatus.ACTIVE
                                && counter.getEndDate().after(currentTime)
                )
        );
        if (counters.size() > 1) {
            ECounterPeriod period = counters.get(0).getPeriod();
            for (Counter counter : counters) {
                counterMatched &= counter.getPeriod() == period;
            }
        }
        if (!counterMatched) {
            throw new BusinessException(
                    ErrorCode.BAD_REQUEST,
                    "counter not match",
                    null
            );
        }
    }

    private List<TierPolicyRes.TierPolicyCounter> getListCounterAndExpiredFromCounterRequest(List<Counter> counters) {
        List<TierPolicyRes.TierPolicyCounter> result = new ArrayList<>();
        Date currentDateTime = new Date();
        long milliSecondCurrentDateTime = currentDateTime.getTime()
                - currentDateTime.getTime() % DateTimes.DAY_IN_MILLIS;

        for (Counter counter : counters) {
            Integer numberDayCounterExpired = counter.getEndDate().after(currentDateTime) ? Math.toIntExact(
                    (counter.getEndDate().getTime() - milliSecondCurrentDateTime)
                            / DateTimes.DAY_IN_MILLIS) : -1;

            TierPolicyRes.TierPolicyCounter tierPolicyCounter = TierPolicyRes.TierPolicyCounter.builder()
                    .counterId(counter.getId())
                    .counterName(counter.getName())
                    .numberDayCounterExpired(numberDayCounterExpired)
                    .build();

            result.add(tierPolicyCounter);
        }
        return result;
    }

    private void verifyEvents(List<String> eventCodes) {
        List<MessageEvent> events = eventRepository.findByStatus(ECommonStatus.ACTIVE);
        boolean eventMatched = Optional.ofNullable(eventCodes)
                .orElse(Collections.emptyList())
                .stream().allMatch(
                        eventReq -> events.stream().anyMatch(event -> eventReq.equals(event.getCode()))
                );
        if (!eventMatched) {
            throw new BusinessException(
                    ErrorCode.BAD_REQUEST,
                    "event not match",
                    null
            );
        }
    }

    private void processCreate(
            MakerCheckerInternalDataDetailRes detailResData,
            CreateTierPolicyReq req
    ) {
        // Validate counters
        List<Counter> counters = counterRepository.findByIdIn(req.getCounterIds());
        verifyCounter(req.getCounterIds(), counters, req.getBusinessId());

        // Get from list of counters
        List<Integer> programsEventAffected = counters.stream()
                .map(Counter::getProgramId)
                .collect(Collectors.toList());
        // Validate events
        if (Boolean.TRUE.equals(req.getEventQualificationPolicy())) {
            verifyEvents(req.getQualificationEventCodes());
        }
        String createdBy, updatedBy;
        updatedBy = createdBy = detailResData.getMadeByUserName();

        Business business = businessService.findActive(req.getBusinessId());
        Program program = programService.findActive(req.getProgramId());

        ProgramTierPolicy programTierPolicy = new ProgramTierPolicy();
        programTierPolicy.setBusinessId(business.getId());
        programTierPolicy.setProgramId(program.getId());
        programTierPolicy.setName(req.getName());
        programTierPolicy.setCode(req.getCode());
        programTierPolicy.setDescription(req.getDescription());
        programTierPolicy.setCounterPeriodType(counters.get(0).getPeriod());
        programTierPolicy.setCounterIds(req.getCounterIds());
        programTierPolicy.setCounterPeriod(req.getPeriodValue());
        programTierPolicy.setQualificationPeriod(req.getExpirations());
        programTierPolicy.setQualificationEvent(req.getQualificationEventCodes());
        programTierPolicy.setStatus(req.getStatus());
        programTierPolicy.setProgramsEventAffected(programsEventAffected);
        programTierPolicy.setStartDate(req.getStartDate());
        programTierPolicy.setEndDate(req.getEndDate());
        programTierPolicy.setRequestCode(detailResData.getRequestCode());
        programTierPolicy.setVersion(detailResData.getVersion());

        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        opsReqPendingValidator.updateInfoChecker(programTierPolicy, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);

        programTierPolicyRepository.save(programTierPolicy);
    }

    private void processUpdate(
            MakerCheckerInternalDataDetailRes detailResData,
            UpdateTierPolicyReq req
    ) {
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(req.getTierPolicyId());

        List<Integer> counterIds = req.getCounters()
                .stream()
                .map(ActionOnCounterDto::getId)
                .collect(Collectors.toList());
        List<Counter> counters = counterRepository.findByIdIn(counterIds);
        verifyCounter(counterIds, counters, programTierPolicy.getBusinessId());

        // Get from list of counters
        List<Integer> programsEventAffected = counters.stream()
                .map(Counter::getProgramId)
                .collect(Collectors.toList());

        // Validate events
        if (Boolean.TRUE.equals(req.getEventQualificationPolicy())) {
            verifyEvents(req.getQualificationEventCodes());
        }

        String updatedBy = detailResData.getMadeByUserName();

        programTierPolicy.setName(req.getName());
        programTierPolicy.setDescription(req.getDescription());
        programTierPolicy.setCounterIds(counterIds);
        programTierPolicy.setCounterPeriod(req.getPeriodValue());
        programTierPolicy.setCounterPeriodType(counters.get(0).getPeriod());
        programTierPolicy.setQualificationPeriod(req.getExpirations());
        programTierPolicy.setQualificationEvent(req.getQualificationEventCodes());
        programTierPolicy.setStatus(req.getStatus());
        programTierPolicy.setProgramsEventAffected(programsEventAffected);
        programTierPolicy.setStartDate(req.getStartDate());
        programTierPolicy.setEndDate(req.getEndDate());

        // Process Update default tier
        if (req.getTierDefaultId() != null) {
            List<ProgramTier> programTiers = programTierRepository.findByProgramId(programTierPolicy.getProgramId());
            for (ProgramTier programTier : programTiers) {
                if (programTier.getId().equals(req.getTierDefaultId()) &&
                        !EBoolean.YES.equals(programTier.getIsTierDefault())) {
                    programTier.setIsTierDefault(EBoolean.YES);
                    programTierRepository.save(programTier);
                } else if (EBoolean.YES.equals(programTier.getIsTierDefault())) {
                    programTier.setIsTierDefault(EBoolean.NO);
                    programTierRepository.save(programTier);
                }
            }
        }

        programTierPolicy.setRequestCode(detailResData.getRequestCode());
        programTierPolicy.setVersion(detailResData.getVersion());
        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        opsReqPendingValidator.updateInfoChecker(programTierPolicy, detailResData.getMadeDate(), null, updatedBy, approvedBy);

        programTierPolicyRepository.save(programTierPolicy);
    }

    private List<TierPolicyRes.Event> getEvents(List<String> eventCodes) {
        List<TierPolicyRes.Event> events = new ArrayList<>();
        if (!CollectionUtils.isEmpty(eventCodes)) {
            Map<String, MessageEvent> eventsMap = messageEventService.findAllToMap();
            events = eventCodes.stream()
                    .map(event -> {
                        MessageEvent messageEvent = eventsMap.get(event);
                        return TierPolicyRes.Event.builder()
                                .code(messageEvent.getCode())
                                .name(messageEvent.getName())
                                .build();
                    })
                    .collect(Collectors.toList());
        }
        return events;
    }
}
