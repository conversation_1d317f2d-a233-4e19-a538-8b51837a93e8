package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
final public class SearchTransactionReq {

    @JsonProperty("transaction_from")
    private String transactionFrom;

    @JsonProperty("transaction_to")
    private String transactionTo;

    @JsonProperty("transaction_id")
    private Long transactionId;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("original_invoice")
    private String originalInvoice;
}