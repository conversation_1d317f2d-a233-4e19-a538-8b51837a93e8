package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.model.res.DistrictDTO;
import com.oneid.loyalty.accounting.ops.service.OpsDistrictService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Province;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CountryRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.ProvinceRepository;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsDistrictServiceImpl implements OpsDistrictService {

    @Autowired
    private DistrictRepository districtRepository;

    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Override
    public DistrictDTO getOne(Integer id) {
        final District district = this.districtRepository
                .findById(id)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.DISTRICT_NOT_FOUND, "District is not found",
                        LogData.createLogData().append("district", id)));
        return DistrictDTO.valueOf(district);
    }

    @Override
    public List<District> getByProvince(Integer provinceId) {
        return districtRepository.findByProvinceIdAndStatusAndNewStatus(provinceId, ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
    }

    @Override
    public List<District> getByProvince(String countryCode, String provinceCode) {
        final Optional<Country> country = this.countryRepository.findByCode(countryCode);
        if(!country.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        final Optional<Province> province = this.provinceRepository
                .findByCountryIdAndCode(country.get().getId(), provinceCode);
        if(!province.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        return districtRepository.findByProvinceIdAndStatusAndNewStatus(province.get().getId(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
    }
}
