package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.model.dto.CustomerIdentifierDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberTierReq;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentBatchRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierAdjustmentBatchService;
import com.oneid.loyalty.accounting.ops.service.OpsTierAdjustmentService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojo;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentBatchProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

@RestController
@RequestMapping("v1/tier-adjustments")
@Validated
public class TierAdjustmentController extends BaseController {

    @Autowired
    private OpsTierAdjustmentService opsTierAdjustmentService;

    @Autowired
    private OpsTierAdjustmentBatchService opsTierAdjustmentBatchService;

    @Autowired
    OpsCommonExcelService opsCommonExcelService;


    @GetMapping("member")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> detailMemberTier(
            @RequestParam("business_id") Integer businessId,
            @RequestParam("program_id") Integer programId,
            @RequestParam("id_type") EOpsIdType idType,
            @RequestParam("id") String id
    ) {
        MemberTierReq req = MemberTierReq.builder()
                .businessId(businessId)
                .programId(programId)
                .customer(
                        CustomerIdentifierDTO.builder()
                                .id(id)
                                .idType(idType.getMapping())
                                .build())
                .build();
        return success(opsTierAdjustmentService.findMemberTier(req));
    }

    @PostMapping("/member")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createRequest(
            @RequestBody @Valid CreateTierAdjustmentMemberReq req) {
        return success(opsTierAdjustmentService.createRequest(req));
    }

    @GetMapping("/batch/template")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getTemplateBathRequest() throws IOException {
        return opsCommonExcelService.getTemplate(OPSConstant.TIER_ADJ, "template_tier_adjustment_request");
    }

    @GetMapping("/available/{batch_id}/export")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.EXPORT})
    })
    public ResponseEntity<?> exportFile(@PathVariable("batch_id") Integer batchId) throws Exception {
        ResourceDTO dto = opsTierAdjustmentService.exportFileAvailableRequest(batchId);
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);
        headers.add("Content-Disposition", "attachment; filename="+ dto.getFilename());
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        return new ResponseEntity<>(dto.getResource(), headers, HttpStatus.OK);
    }

    @PostMapping("/batch")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createBatchRequest(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid CreateTierAdjustmentReq req,
            @RequestPart(name = "file") MultipartFile multipartFile) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", "attachment; filename=" + multipartFile.getOriginalFilename());
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        InputStream file = opsTierAdjustmentService.createBatchRequest(req, multipartFile);
        return file != null ? new ResponseEntity<>(new InputStreamResource(file), headers, HttpStatus.BAD_REQUEST) : success(null);
    }

    @PostMapping("/batch/verify")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> verifyRequest(
            @RequestParam("business_id") @NotNull Integer businessId,
            @RequestParam("program_id") @NotNull Integer programId,
            @RequestPart(name = "file") MultipartFile multipartFile) throws Exception {
        CreateTierAdjustmentReq req = CreateTierAdjustmentReq.builder()
                .businessId(businessId)
                .programId(programId)
                .build();
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", "attachment; filename=" + multipartFile.getOriginalFilename());
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        InputStream file = opsTierAdjustmentService.verifyFile(req, multipartFile);
        return file != null ? new ResponseEntity<>(new InputStreamResource(file), headers, HttpStatus.BAD_REQUEST) : success(null);
    }


    @GetMapping("/in-review/{id}")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewDetail(@PathVariable("id") Integer inReviewId) {
        return success(opsTierAdjustmentBatchService.inReviewDetail(inReviewId));
    }

    @GetMapping("/available/{id}")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableDetail(@PathVariable("id") Integer requestId) {
        return success(opsTierAdjustmentBatchService.availableDetail(requestId));
    }

    @GetMapping("/available")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(value = "business_id", required = true) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "batch_no", required = false) Integer batchNo,
            @RequestParam(value = "process_status", required = false) ETierAdjustmentBatchProcessStatus processStatus,
            @RequestParam(value = "type", required = false) ETierAdjustmentType type,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdEnd,
            @RequestParam(value = "failed_records", required = false) Boolean failedRecords,
            @MakerCheckerOffsetPageable Pageable pageable) throws ParseException {
        Page<TierAdjustmentBatchRequestRes> page = opsTierAdjustmentBatchService.filterAdjustment(businessId, programId, batchNo, processStatus, type, EApprovalStatus.APPROVED, null, createdStart, createdEnd, null, null, null, failedRecords, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/in-review")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewRequests(
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdEnd,
            @RequestParam(value = "approved_by", required = false) String approvedBy,
            @RequestParam(value = "approved_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date approvedStart,
            @RequestParam(value = "approved_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date approvedEnd,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @MakerCheckerOffsetPageable Pageable pageable) throws ParseException {
        Page<TierAdjustmentBatchRequestRes> page = opsTierAdjustmentBatchService.filterAdjustment(null, null, null, null, null, approvalStatus, createdBy, createdStart, createdEnd, approvedBy, approvedStart, approvedEnd, null, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/in-review/detail/{id}")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewDetails(@PathVariable("id") Integer id,
                                                @RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                                @RequestParam(value = "limit", defaultValue = "10") Integer limit) throws ParseException {
        Page<TierAdjustmentRequestRes> page = opsTierAdjustmentService.getInReviewDetails(id, new OffsetBasedPageRequest(offset, limit, Sort.by("id")));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/in-review/{batch_id}/export")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.EXPORT})
    })
    public ResponseEntity<?> exportFileInReview(@PathVariable("batch_id") Integer batchId) throws Exception {
        ResourceDTO dto = opsTierAdjustmentService.exportFileInReviewRequest(batchId);
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);
        headers.add("Content-Disposition", "attachment; filename="+ dto.getFilename());
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        return new ResponseEntity<>(dto.getResource(), headers, HttpStatus.OK);
    }

    @GetMapping("/available/detail/{id}")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableDetails(@PathVariable("id") Integer id,
                                                 @RequestParam(value = "process_status", required = false) ETierAdjustmentProcessStatus processStatus,
                                                 @RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                                 @RequestParam(value = "limit", defaultValue = "10") Integer limit) throws ParseException {
        Page<TierAdjustmentRequestRes> page = opsTierAdjustmentService.getAvailableDetails(id, processStatus, new OffsetBasedPageRequest(offset, limit, Sort.by("id")));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @PostMapping("/batch/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approveBatchRequest(@Valid @RequestBody ApprovalReq req) {
        return success(opsTierAdjustmentBatchService.approveBatchRequest(req));
    }

    @PostMapping("/batch/requests/retry/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> retryBatchRequest(@PathVariable Integer id) {
        return success(opsTierAdjustmentBatchService.retryBatchRequest(id));
    }

    @GetMapping("/available/{id}/statistic")
    @Authorize(role = AccessRole.ADJUST_MEMBER_TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionStatisticById(@PathVariable("id") Integer requestId) {
        return success(opsTierAdjustmentService.getTierAdjustmentStatisticById(requestId));
    }
}
