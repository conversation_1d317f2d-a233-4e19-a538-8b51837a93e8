package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.model.req.CpmMemberInquiryReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CpmTxnRefundReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberInquiryResData;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@FeignClient(name = "oneloyalty-cpm-service", url = "${cpm.url}")
public interface CPMServiceFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/v1/transactions/refund")
    APIResponse<?> refund(@RequestBody CpmTxnRefundReq request);

    @RequestMapping(method = RequestMethod.POST, value = "/v1/members/inquiry")
    MemberInquiryResData inquiry(@RequestBody @Valid CpmMemberInquiryReq request);
}