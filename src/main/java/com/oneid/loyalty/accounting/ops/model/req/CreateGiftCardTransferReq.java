package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESendVia;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Convert;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateGiftCardTransferReq implements Serializable {

    private static final long serialVersionUID = 9080582552058277297L;

    @NotBlank(message = "'recipient_code' must not be blank")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "recipient_code only letters and numbers")
    @Length(max = 16)
    private String recipientCode;

    @NotBlank(message = "'recipient_name' must not be blank")
    @Length(max = 255, message = "Program name: length must be between 1 and 255")
    private String recipientName;

    @JsonProperty("sftp_ip_address")
    private String sftpIPAddress;

    @JsonProperty("sftp_folder_path")
    private String sftpFolderPath;

    @JsonProperty("sftp_username")
    private String sftpUsername;

    @JsonProperty("sftp_password")
    private String sftpPassword;

    @NotNull(message = "'archive_folder' must not be null")
    private EBoolean archiveFolder;

    @JsonProperty("email_address")
    private String emailAddress;

    @JsonProperty("pgp_public_key")
    private String pgpPublicKey;

    @JsonProperty("encrypt_pgp")
    private EBoolean encryptPGP;

    @NotBlank(message = "send_via must not blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(SFTP|M)?$", message = "'send_via' Only accept SFTP/M values")
    private String sendVia;

    @JsonProperty("archive_phone_receive")
    private String archivePhoneReceive;

    @NotNull(message = "Business id: must not be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull(message = "Program id: must not be null")
    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("transfer_no")
    private long transferNo;

    @JsonProperty("selected_batches")
    private List<SelectedBatch> selectedBatches = new ArrayList<>();

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class SelectedBatch {
        private Integer batchId;
        private Long batchNo;
        private String batchType;
        private String description;
        private EBoolean convertXlsxFlag;
        private Integer noOfCards;
        private ShortEntityRes corporation;
        private ShortEntityRes chain;
        private ShortEntityRes store;
        private ShortEntityRes giftCardType;
    }

    @AssertTrue(message = "'archive_phone_receive' invalid")
    public boolean isValidArchivePhoneReceive() {
        boolean matches = true;
        if (EBoolean.YES.equals(archiveFolder)) {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^0\\d{9,10}|84\\d{9,11}$");
            matches = pattern.matcher(archivePhoneReceive).matches();
        }
        return notBlankArchive(archivePhoneReceive) && matches;
    }

    @AssertTrue(message = "'sftp_password' invalid")
    public boolean isValidPassword() {
        return notBlankSFTP(sftpPassword);
    }


    @AssertTrue(message = "'sftp_user_name' invalid")
    public boolean isValidUserName() {
        return notBlankSFTP(sftpUsername);
    }

    @AssertTrue(message = "gift card transfer request must have at least one protection method")
    public boolean isExistProtectionMethod() {
        if (EBoolean.YES.equals(encryptPGP) || EBoolean.YES.equals(archiveFolder)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @AssertTrue(message = "'sftp_folder_path' invalid")
    public boolean isValidFolderPath() {
        return notBlankSFTP(sftpFolderPath);
    }

    private Boolean notBlankSFTP(String value) {
        if (ESendVia.SFTP.getValue().equals(sendVia)) {
            if (Strings.isNotBlank(value)) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } else {
            return Boolean.TRUE;
        }
    }

    private Boolean notBlankArchive(String value) {
        if (EBoolean.YES.equals(archiveFolder)) {
            if (Strings.isNotBlank(value)) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } else {
            if (Strings.isNotBlank(value)) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        }
    }

    @AssertTrue(message = "'email_address' invalid")
    public boolean isValidEmail() {
        if (ESendVia.Manual.getValue().equals(sendVia)) {
            if (Objects.nonNull(emailAddress)) {
                org.apache.commons.validator.routines.EmailValidator emailValidator
                        = org.apache.commons.validator.routines.EmailValidator.getInstance();
                if (!emailValidator.isValid(emailAddress)) {
                    return Boolean.FALSE;
                }
                String[] attributes = emailAddress.split("@");

                return attributes[0].length() <= 64
                        && attributes[1].length() <= 255;
            } else {
                return Boolean.FALSE;
            }
        } else {
            return Boolean.TRUE;
        }
    }

    @AssertTrue(message = "'batch_no' must not be null")
    public boolean isValidBatchNo() {
        if (!selectedBatches.isEmpty()) {
            Optional<SelectedBatch> selectedBatch = selectedBatches
                    .stream()
                    .filter(ele -> Objects.isNull(ele.getBatchNo()))
                    .findAny();
            if (selectedBatch.isPresent()) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        } else {
            return Boolean.TRUE;
        }
    }

    @AssertTrue(message = "'pgp_public_key' invalid")
    public boolean isValidPublicKey() {
        if (EBoolean.YES.equals(encryptPGP) && Objects.isNull(pgpPublicKey)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
