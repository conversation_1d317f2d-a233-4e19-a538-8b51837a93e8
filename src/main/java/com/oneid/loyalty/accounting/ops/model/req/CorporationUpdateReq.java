package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.validation.Address;
import com.oneid.oneloyalty.common.validation.PersonName;
import com.oneid.oneloyalty.common.validation.ValidMeta;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Getter
@Setter
public class CorporationUpdateReq {
    @NotNull(message = "'BusinessId' cannot be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @NotBlank(message = "'Name' cannot be empty")
    @ValidMeta(message = "'Name' is invalid")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
//    @Pattern(regexp = "^[ a-zA-Z0-9]{0,255}$", message = "'Description' is invalid")
    private String description;

    @NotNull(message = "'Service start date' cannot be null")
    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @NotNull(message = "'Service end date' cannot be null")
    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @Pattern(regexp = "^[a-zA-Z0-9]{0,255}$", message = "'ServiceRegistrationNo' is invalid")
    @JsonProperty("service_registration_no")
    private String serviceRegistrationNo;

    @NotBlank(message = "'Settlement mode' cannot be empty")
    @Pattern(regexp = "^(AUTO|BATCH_RECON)?$", message = "'Settlement mode' Only accept AUTO | BATCH_RECON values")
    @JsonProperty("settlement_mode")
    private String settlementMode;

    @NotNull(message = "'Card limit in stock' cannot be null")
    @Min(0)
    @Max(value = 999,message = "'Card limit in stock' max length is 999")
    @JsonProperty("card_limit_in_stock")
    private Integer cardLimitInStock;

    // @NotNull(message = "'Tax identification number' cannot be null")
    @Min(0)
    @JsonProperty("tax_identification_number")
    private Integer taxIdentificationNumber;

//    @NotNull(message = "'Contact person' is required")
    @PersonName(message = "'Contact person' is invalid")
    @JsonProperty("contact_person")
    private String contactPerson;

    @Size(max = 320, message = "'Email Address' cannot exceed 320 characters")
    @Pattern(regexp = "^[a-zA-Z0-9\\.]{1,64}@[a-zA-Z0-9]{1,255}.[a-zA-Z0-9]{1,255}$", message = "'Email Address' is invalid")
    @JsonProperty("email_address")
    private String emailAddress;

    @Address(message = "'Address 1' is invalid")
    @Size(min = 2, message = "'Address 1' min length is 2")
    @NotNull(message = "'Address1' cannot be null")
    @JsonProperty("address1")
    private String address1;

    @Address(message = "'Address 2' is invalid")
    @Size(min = 2, message = "'Address 2' min length is 2")
    @JsonProperty("address2")
    private String address2;

//    @NotNull(message = "'Country Id' cannot be null")
    @JsonProperty("country_id")
    private Integer countryId;

//    @NotNull(message = "'Province Id' cannot be null")
    @JsonProperty("province_id")
    private Integer provinceId;

//    @NotNull(message = "'District Id' cannot be null")
    @JsonProperty("district_id")
    private Integer districtId;

//    @NotNull(message = "'Ward Id' cannot be null")
    @JsonProperty("ward_id")
    private Integer wardId;

    @Size(max = 16, message = "'Postal code' cannot exceed 16 characters")
    @Pattern(regexp = "^[0-9]{0,16}$", message = "'Postal code' is invalid")
    @JsonProperty("postal_code")
    private String postalCode;

    @Pattern(regexp = "^0\\d{9,10}|84\\d{9,11}$", message = "Phone is invalid")
    @JsonProperty("phone_no")
    private String phoneNo;

    @Size(max = 63, message = "'Website' max length is 63")
    @Pattern(regexp = "(^$)|(^[A-Z0-9a-z./:-]+$)", message = "Website is invalid")
    @JsonProperty("website")
    private String website;

    @NotBlank(message = "'Status' cannot be empty")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;
}
