package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SchemeRes {
    private Integer id;
    private String name;
    private String code;
    private ESchemeType schemeType;
    private ECommonStatus status;
    private Integer sequenceNo;
    
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date startDate;
    
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date endDate;
    
    
}
