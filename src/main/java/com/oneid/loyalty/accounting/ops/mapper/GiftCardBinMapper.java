package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinCreateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.CardBin;
import com.oneid.oneloyalty.common.entity.GiftCardBin;

public class GiftCardBinMapper {
    public static GiftCardBin toGiftCardBinOne(GiftCardBinCreateReq request) {
        GiftCardBin giftCardBin = new GiftCardBin();
        giftCardBin.setBusinessId(request.getBusinessId());
        giftCardBin.setProgramId(request.getProgramId());
        giftCardBin.setBinCode(request.getBinCode());
        giftCardBin.setDescription(request.getDescription());
        giftCardBin.setStatus(ECommonStatus.of(request.getStatus()));
        return giftCardBin;
    }

    public static GiftCardBin toGiftCardBinOne(GiftCardBin giftCardBin, GiftCardBinUpdateReq request) {
        giftCardBin.setDescription(request.getDescription());
        giftCardBin.setStatus(ECommonStatus.of(request.getStatus()));
        return giftCardBin;
    }
}
