package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramFunctionReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramFunctionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/program-function/requests")
@Validated
public class ProgramFunctionController extends BaseController {
    @Autowired
    private OpsProgramCorporationService loyaltyProgramCorporationService;

    @Autowired
    private OpsProgramFunctionService loyaltyProgramFunctionService;

    @GetMapping("/functions")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableProgramCorporationRequest() {
        return success(loyaltyProgramFunctionService.getFunctions());
    }

    @GetMapping("/available/{id}/edit")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEditProgramCorporationRequestSetting(@PathVariable("id") Integer requestId) {
        return success(loyaltyProgramFunctionService.getEditAttributeRequestSetting(requestId));
    }

    @PostMapping
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> create(
            @Valid @RequestBody ProgramFunctionReq body) {
        return success(loyaltyProgramFunctionService.create(body));
    }

    @PutMapping
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> requestEditingAttributeRequest(@Valid @RequestBody ProgramFunctionReq req) {
        return success(loyaltyProgramFunctionService.requestEditingAttributeRequest(req));
    }

    @PostMapping("/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        loyaltyProgramFunctionService.approve(req);
        return success(null);
    }

    @GetMapping("/available")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> search(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "sort_direction", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @Valid @RequestParam(value = "sort_by", required = false, defaultValue = "id") String sortBy,
            @RequestParam(value = "program_code", required = false) String programCode,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "status", required = false) ECommonStatus status
    ) {
        Sort sort = Sort.by(sortDirection, sortBy);
        Pageable pageable = PageRequest.of(offset / limit, limit, sort);
        PageImpl<ProgramFunctionRes> resPage = loyaltyProgramFunctionService.searchAvailable(businessId, programCode, status, pageable);
        return success(resPage, offset, limit);
    }

    @GetMapping("/in-review")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReview(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus) {
        PageImpl<ProgramFunctionRes> resPage = loyaltyProgramFunctionService.searchInReview(approvalStatus, offset, limit);
        return success(resPage, offset, limit);
    }

    @GetMapping("/available/{program_id}/view")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> availableDetail(@PathVariable("program_id") Integer programId) {

        return success(loyaltyProgramFunctionService.getAvailableDetail(programId));
    }

    @GetMapping("/in-review/{id}/view")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> inReviewDetail(@PathVariable("id") Integer id) {
        
        return success(loyaltyProgramFunctionService.getInReviewDetail(id));
    }

    @GetMapping("/all-by-business/{business_id}")
    @Authorize(role = AccessRole.PROGRAM_FUNCTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAllByBusinessId(@PathVariable("business_id") Integer programId) {
        return success(loyaltyProgramFunctionService.getProgramByBusinessExcludeExist(programId));
    }
}