package com.oneid.loyalty.accounting.ops.model.res;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProductAccountType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
@Getter
@Setter
@EqualsAndHashCode
public class MemberProductAccountRes {
    @JsonProperty("product_account_code")
    private String productAccountCode;

    @JsonProperty("product_code")
    private String productCode;

    @JsonProperty("card_type")
    private String cardType;

    @JsonProperty("created_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Long createdAt;

    @JsonProperty("expired_at")
    private Long expiredAt;

    @JsonProperty("status")
    private ECommonStatus status;
    
    @JsonProperty("code_block")
    private String codeBlock;
    
    @JsonProperty("card_status")
    private String cardStatus;
    
    @JsonProperty("store_code")
    private String storeCode;

    @JsonProperty("remark")
    private String remark;
}