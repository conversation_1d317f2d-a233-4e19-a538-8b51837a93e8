package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.MakerCheckerFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.res.VoucherDetailRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.VoucherRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "onevc-service", url = "${oneloyalty.voucher-service.url}"
        , configuration = MakerCheckerFeignConfig.class)
public interface VoucherServiceFeignClient {

    @RequestMapping(method = RequestMethod.GET, value = "/v1/sale-integration")
    APIResponse<VoucherRequestPageFeignRes> getVoucherPage(
            @RequestParam(name = "purpose") String purpose,
            @RequestParam(name = "status") String status,
            @RequestParam(name = "keyword") String keyword,
            @RequestParam(name = "page") Integer page,
            @RequestParam(name = "size") Integer size);

    @RequestMapping(method = RequestMethod.GET, value = "/v1/sale/{code}")
    APIResponse<VoucherDetailRequestPageFeignRes> getVoucherDetail(@PathVariable("code") String code);
}