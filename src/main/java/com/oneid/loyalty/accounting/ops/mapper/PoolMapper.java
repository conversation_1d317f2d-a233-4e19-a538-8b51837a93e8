package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Pool;

public class PoolMapper {
    public static Pool toPoolOne(PoolCreateReq request, Integer retentionPolicyId, Integer balanceExpiredPolicyId, String userName) {
        Pool result = new Pool();

        result.setBusinessId(request.getBusinessId());
        result.setProgramId(request.getProgramId());
        result.setCode(request.getCode());
        result.setName(request.getName());
        result.setDescription(request.getDescription());
        result.setCurrencyId(request.getCurrencyId());
        result.setRetentionPolicyId(retentionPolicyId);
        result.setBalanceExpiredPolicyId(balanceExpiredPolicyId);
        result.setNegativeBalance(EBoolean.of(request.getNegativeBalance()));
        result.setAllowRefundRemainingBalance(EBoolean.of(request.getAllowRefundRemainingBalance()));
        result.setStatus(ECommonStatus.of(request.getStatus()));
        result.setCreatedAt(DateTimes.currentDate());
        result.setUpdatedAt(DateTimes.currentDate());
        result.setCreatedBy(userName);
        result.setUpdatedBy(userName);

        return result;
    }

    public static Pool toPoolOne(Pool pool, PoolUpdateReq request, String userName) {

        pool.setBusinessId(request.getBusinessId() != null ? request.getBusinessId() : pool.getBusinessId());
        pool.setProgramId(request.getProgramId() != null ? request.getProgramId() : pool.getProgramId());
        pool.setCode(request.getCode() != null ? request.getCode() : pool.getCode());
        pool.setName(request.getName() != null ? request.getName() : pool.getName());
        pool.setDescription(request.getDescription() != null ? request.getDescription() : pool.getDescription());
        pool.setCurrencyId(request.getCurrencyId() != null ? request.getCurrencyId() : pool.getCurrencyId());
        pool.setNegativeBalance(request.getNegativeBalance() != null ? EBoolean.of(request.getNegativeBalance()) : pool.getNegativeBalance());
        pool.setAllowRefundRemainingBalance(request.getAllowRefundRemainingBalance() != null ? EBoolean.of(request.getAllowRefundRemainingBalance()) : pool.getAllowRefundRemainingBalance());
        pool.setStatus(request.getStatus() != null ? ECommonStatus.of(request.getStatus()) : pool.getStatus());
        pool.setUpdatedAt(DateTimes.currentDate());
        pool.setUpdatedBy(userName);

        return pool;
    }
}