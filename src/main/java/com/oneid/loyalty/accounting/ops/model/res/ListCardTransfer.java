package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ListCardTransfer {
    private ShortEntityRes cardType;
    private String description;
    private ECardStatus status;
    private List<String> cardNos;
    @JsonIgnore
    private long offset;
    @JsonIgnore
    private long limit;
    @JsonIgnore
    private long total;
}
