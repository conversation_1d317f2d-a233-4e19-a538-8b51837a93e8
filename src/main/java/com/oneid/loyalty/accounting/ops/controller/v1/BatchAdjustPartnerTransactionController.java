package com.oneid.loyalty.accounting.ops.controller.v1;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

import java.net.URL;
import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.BatchAdjustPartnerTransactionReq;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionItemRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionRes;
import com.oneid.loyalty.accounting.ops.service.OpsBatchAdjustPartnerTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojo;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;
import com.oneid.oneloyalty.common.constant.EProgressStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/batch-adjust-partner-transaction")
@Validated
public class BatchAdjustPartnerTransactionController extends BaseController {
    @Autowired
    private OpsBatchAdjustPartnerTransactionService batchAdjustPartnerTransactionService;
    
    @GetMapping("/tcb-setting")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getTcbSetting(){
        return success(batchAdjustPartnerTransactionService.getTcbPartnerSetting());
    }
    
    @GetMapping("/corporation/{id}/batch-file-template")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> exportBatchFileTemplate(@PathVariable("id") Integer corporationId){
       ResourceDTO resourceDTO = batchAdjustPartnerTransactionService.exportBatchFileTemplate(corporationId);
        
        HttpHeaders headers = new HttpHeaders();
        
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", String.format("attachment; filename=%s", resourceDTO.getFilename()));
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        
        return new ResponseEntity<>(resourceDTO.getResource(), headers, HttpStatus.OK);
    }
    
    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestCreatingBatchRequest(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid BatchAdjustPartnerTransactionReq req,
            @RequestPart(name = "file") MultipartFile multipartFile){
        return success(batchAdjustPartnerTransactionService.requestCreatingBatchRequest(req, multipartFile));
    }
    
    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(value = "batch_name", required = false) String batchName,
            @RequestParam(value = "batch_no", required = false) Integer batchNo,
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "corporation_id") Integer corporationId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "from_date", required = false) LocalDate fromDate,
            @RequestParam(value = "to_date", required = false) LocalDate toDate,
            @RequestParam(value = "batch_status", required = false) EBatchProcessStatusType batchProcessStatusType,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit){
        Page<BatchAdjustPartnerTransactionRes> page = batchAdjustPartnerTransactionService.getAvailableRequests(
                batchName,
                batchNo,
                corporationId, 
                businessId, 
                programId, 
                batchProcessStatusType, 
                approvalStatus, 
                fromDate, 
                toDate,
                new OffsetBasedPageRequest(offset, limit, null));
        
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/available/{id}/transactions")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getTransactionsById(
            @PathVariable("id") Integer requestId,
            @RequestParam(value = "status", required = false) EProgressStatus progressStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit){
        Page<BatchAdjustPartnerTransactionItemRes> page = batchAdjustPartnerTransactionService.getTransactionsById(requestId, progressStatus, new OffsetBasedPageRequest(offset, limit, null));
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/available/{id}/transactions/export")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.EXPORT })
    public ResponseEntity<?> getExportedTransactionsById(@PathVariable("id") Integer requestId){
        ResourceDTO resourceDTO = batchAdjustPartnerTransactionService.exportAllTransactionsByRequestId(requestId);
        
        HttpHeaders headers = new HttpHeaders();
        
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", String.format("attachment; filename=%s", resourceDTO.getFilename()));
        headers.add(HttpHeaders.CONTENT_TYPE, "text/csv");
        
        return new ResponseEntity<>(resourceDTO.getResource(), headers, HttpStatus.OK);
    }
    
    @GetMapping("/requests/available/{id}/transaction-statistic")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getTransactionStatisticById(@PathVariable("id") Integer requestId){
        return success(batchAdjustPartnerTransactionService.getTransactionStatisticById(requestId));
    }
    
    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableRequestById(@PathVariable("id") Integer requestId){
        return success(batchAdjustPartnerTransactionService.getAvailableRequestById(requestId));
    }
    
    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewRequestById(@PathVariable("id") Integer reviewId){
        return success(batchAdjustPartnerTransactionService.getInReviewRequestById(reviewId));
    }
    
    @GetMapping("/request/{id}/batch-file")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.EXPORT })
    public ResponseEntity<?> downloadBatchFile( @PathVariable("id") Integer requestId){
        URL url = batchAdjustPartnerTransactionService.getDownloadedBatchFileLink(requestId);
        
        if(url != null) {
            return success(url); 
        }
         
        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
    }
    
    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.BATCH_ADJUST_PARTNER_TRANSACTION, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_date", required = false) LocalDate fromDate,
            @RequestParam(value = "to_date", required = false) LocalDate toDate,
            @MakerCheckerOffsetPageable Pageable pageable){
        Page<BatchAdjustPartnerTransactionRes> page = batchAdjustPartnerTransactionService.getInReviewRequests(approvalStatus, fromDate, toDate, pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
}
