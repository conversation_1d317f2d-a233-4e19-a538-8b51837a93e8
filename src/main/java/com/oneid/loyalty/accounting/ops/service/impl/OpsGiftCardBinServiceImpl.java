package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.GiftCardBinMapper;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardBinRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardBinService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.GiftCardBin;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.GiftCardBinService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OpsGiftCardBinServiceImpl implements OpsGiftCardBinService {
    @Autowired
    GiftCardBinService giftCardBinService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsProgramService opsProgramService;

    @Override
    public Page<GiftCardBinRes> filter(Integer businessId, Integer programId, String binCode, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit , Sort.Direction.DESC, "createdAt");

        SpecificationBuilder<GiftCardBin> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (binCode != null)
            specificationBuilder.add(new SearchCriteria("binCode", binCode, SearchOperation.EQUAL));

        Page<GiftCardBin> cardBins = giftCardBinService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = cardBins.getContent().stream().map(GiftCardBin::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> programIds = cardBins.getContent().stream().map(GiftCardBin::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programbyIds = opsProgramService.getMapById(programIds);

        return new PageImpl<GiftCardBinRes>(cardBins.getContent()
                .stream().map(it -> GiftCardBinRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        programbyIds.get(it.getProgramId())
                )).collect(Collectors.toList()), pageRequest, cardBins.getTotalElements());
    }

    @Override
    public GiftCardBinRes get(Integer id) {
        Optional<GiftCardBin> giftCardBin = giftCardBinService.find(id);
        if (!giftCardBin.isPresent())
            throw new BusinessException(ErrorCode.GIFT_CARD_BIN_NOT_FOUND, "GiftCard bin not found in business", LogData.createLogData().append("id", id));
        ;
        return GiftCardBinRes.of(
                giftCardBin.get(),
                businessRepository.findById(giftCardBin.get().getBusinessId()).orElse(null),
                programRepository.findById(giftCardBin.get().getProgramId()).orElse(null)
        );
    }

    @Override
    public void add(GiftCardBinCreateReq giftCardBinCreateReq) {
        giftCardBinService.create(GiftCardBinMapper.toGiftCardBinOne(giftCardBinCreateReq));
    }

    @Override
    public void update(Integer id, GiftCardBinUpdateReq GiftCardBinUpdateReq) {
        GiftCardBin giftCardBin = giftCardBinService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.GIFT_CARD_BIN_NOT_FOUND, "GiftCard bin not found in business", LogData.createLogData().append("id", id)));
        giftCardBinService.update(GiftCardBinMapper.toGiftCardBinOne(giftCardBin, GiftCardBinUpdateReq));
    }
}
