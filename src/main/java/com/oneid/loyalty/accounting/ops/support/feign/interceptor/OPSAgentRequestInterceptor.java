package com.oneid.loyalty.accounting.ops.support.feign.interceptor;


import com.oneid.loyalty.accounting.ops.constant.ERequestAgentType;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;

import java.util.UUID;

public class OPSAgentRequestInterceptor implements RequestInterceptor {

    public static final String X_OPS_AGENT = "X-OPS-Agent";
    public static final String X_REQUEST_ID = "x-request-id";
    public static final String X_SERVICE_ID = "x-service-id";
    public static final String X_VALUE_SERVICE_ID = "oneloyalty-ops";


    @Override
    public void apply(RequestTemplate template) {
        template.header(X_OPS_AGENT, ERequestAgentType.OPS.getValue());
        template.header(X_REQUEST_ID, UUID.randomUUID().toString());
        template.header(X_SERVICE_ID, X_VALUE_SERVICE_ID);
    }
}