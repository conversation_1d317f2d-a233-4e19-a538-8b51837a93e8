package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.dto.ActionOnCounterDto;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.CounterRequest;

import java.util.List;

public interface OpsServiceCounterRequestService {
    void saveAllNewRequest(EServiceType type, Integer serviceRequestId, List<ActionOnCounterDto> counterRequestIds);
    List<CounterRequest> getCounterRequestsByServiceRequestIdAndType(EServiceType type, Integer serviceRequestId);
}