package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CardMemberFeignRes.CardMemberFeignResBuilder.class)
public class CardMemberFeignRes {
    private Integer Id;

    private Long memberId;
    
    private String memberCode;

    private String cardNo;

    private Integer cardType;

    private Long expiryDate;

    private Long retentionDate;

    private Long issueDate;

    private Long activationDate;

    private String cardStatus;

    private Long createdAt;

    private Integer businessId;

    private Integer chainId;

    private Integer programId;
    
    private Integer storeId;
    
    private String codeBlock;

    private String remark;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class CardMemberFeignResBuilder {
    }
}