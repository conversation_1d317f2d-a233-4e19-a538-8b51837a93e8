package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationPreviewRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramCorporationService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/program-corporation/requests")
@Validated
public class ProgramCorporationController extends BaseController {
    @Autowired
    private OpsProgramCorporationService loyaltyProgramCorporationService;

    @GetMapping("/available")
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> search(
            @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit,
            @RequestParam(value = "program_code", required = false) String programCode,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "status", required = false) ECommonStatus status) {

        Page<ProgramCorporationRes> response = loyaltyProgramCorporationService.searchProgramCorporation(businessId,
                programCode,
                status, offset, limit);
        return success(response, offset, limit);
    }

    @GetMapping("/available/{program_id}/view")
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableProgramCorporationRequest(@PathVariable("program_id") Integer programId) {
        return success(loyaltyProgramCorporationService.getAvailableProgramCorporationRequest(programId));
    }

    @GetMapping("/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEditProgramCorporationRequestSetting(@PathVariable("id") Integer requestId) {
        return success(loyaltyProgramCorporationService.getEditAttributeRequestSetting(requestId));
    }

    @PostMapping("/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> requestEditingAttributeRequest(@PathVariable("id") Integer id, @Valid @RequestBody UpdateProgramCorporationReq req) {
        return success(loyaltyProgramCorporationService.requestEditingAttributeRequest(id, req));
    }

    @GetMapping("/in-review/{id}/view")
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewProgramCorporationRequestById(@PathVariable("id") Integer reviewId) {
        return success(loyaltyProgramCorporationService.getInReviewProgramCorporationRequestById(reviewId));
    }

    @GetMapping("/in-review")
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewProgram(
            @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus) {

        Page<ProgramCorporationPreviewRes> page = loyaltyProgramCorporationService
                .getProgramCorporationInReview(approvalStatus, offset, limit);

        return success(page.getContent(), (int) offset, limit,
                (int) page.getTotalElements());
    }

    @GetMapping("/all-by-business/{business_id}")
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAllByBusinessId(@PathVariable("business_id") Integer programId) {
        return success(loyaltyProgramCorporationService.getProgramByBusinessExcludeExist(programId));
    }

    @PostMapping
    @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> create(
            @Valid @RequestBody CreateProgramCorporationReq body) {
        return success(loyaltyProgramCorporationService.create(body));
    }

    @PostMapping("/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_CORPORATION, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> update(@Valid @RequestBody ApprovalReq req) {
        loyaltyProgramCorporationService.update(req);
        return success(null);
    }
}