package com.oneid.loyalty.accounting.ops.controller.v1;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.CreateStoreReq;
import com.oneid.loyalty.accounting.ops.model.req.StoreUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.StoreEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.StoreRes;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/stores")
public class StoreController extends BaseController {
    @Autowired
    OpsStoreService opsStoreService;

    @GetMapping("enum-all")
    public ResponseEntity<?> getEnumAll() {
        List<StoreEnumAll> result = opsStoreService.getEnumAll();
        return success(result);
    }

    @GetMapping("")
    @Authorize(role = AccessRole.STORE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterStores(@RequestParam(name = "business_id", required = false) Integer businessId,
                                                @RequestParam(name = "corporation_id", required = false) Integer corporationId,
                                                @RequestParam(name = "chain_id", required = false) Integer chainId,
                                                @RequestParam(name = "store_name", required = false) String storeName,
                                                @RequestParam(name = "store_code", required = false) String storeCode,
                                                @RequestParam(name = "status", required = false)
                                                @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                                @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                                @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<StoreRes> page = opsStoreService.filter(businessId, corporationId, chainId, storeName, storeCode, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.CHAIN, permissions = {AccessPermission.VIEW }),
            @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW }),
    }, any = true)
    public ResponseEntity<?> getStore(@PathVariable(value = "id") Integer id) {
        StoreRes result = opsStoreService.get(id);
        return success(result);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.STORE, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateStore(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid StoreUpdateReq storeUpdateReq) {
        this.opsStoreService.update(id, storeUpdateReq);
        return success(null);
    }
    
    @PostMapping("")
    @Authorize(role = AccessRole.STORE, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createStore(@RequestBody @Valid CreateStoreReq req) {
        return success(this.opsStoreService.create(req));
    }
}
