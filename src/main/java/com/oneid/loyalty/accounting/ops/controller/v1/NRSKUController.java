package com.oneid.loyalty.accounting.ops.controller.v1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.OpsIntegrationFeignClient;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@Controller
@RequestMapping("/v1/new-retails/sku")
@Validated
public class NRSKUController extends BaseController {
    @Autowired
    private OpsIntegrationFeignClient opsIntegrationFeignClient;
    
    @GetMapping("/list")
    @Authorize(role = AccessRole.SKU_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getSKUList(
            @RequestParam(name = "assigned_award_rate", required = false) Boolean assignedAwardRate,
            @RequestParam(value = "sku_code", required = false) String skuCode,
            @RequestParam(value = "merchant_code", required = false) String merchantCode,
            @RequestParam(value = "product_category_name", required = false) String productCategoryName,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "sort_by", required = false) String sortBy,
            @RequestParam(value = "sort_type", required = false) String sortType,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit){
        return ResponseEntity.ok(opsIntegrationFeignClient.getSKUList(
                assignedAwardRate, 
                skuCode, 
                productCategoryName, 
                merchantCode, 
                status != null ? status.getValue() : null, 
                sortBy, 
                sortType, 
                offset, 
                limit));
    }
    
    @GetMapping("/{id}/detail")
    @Authorize(role = AccessRole.SKU_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getSKUById(@PathVariable("id") Integer id){
        return ResponseEntity.ok(opsIntegrationFeignClient.getSKUById(id));
    }
}