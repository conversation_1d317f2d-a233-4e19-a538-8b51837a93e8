package com.oneid.loyalty.accounting.ops.feign.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import com.oneid.loyalty.accounting.ops.support.feign.interceptor.OpsAdminInterceptor;

import feign.RequestInterceptor;

public class OpsAdminFeignConfig {
    @Value("${ops.basic.auth}")
    private String basicAuth;
    
    @Bean
    public RequestInterceptor opsAgentRequestInterceptor() {
      return new OpsAdminInterceptor(basicAuth);
    }
}