package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@AllArgsConstructor
public class PointRedeemDetail {
    private ShortEntityRes pool;
    private ShortEntityRes currency;
    private ShortEntityRes scheme;
    private BigDecimal point;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
}