package com.oneid.loyalty.accounting.ops.model.dto;

import com.poiji.annotation.ExcelCellName;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Data
@Validated
public class AdjustTransactionExcelDTO extends ActionTransactionExcelDTO{

    @ExcelCellName("ADJUST_TYPE")
    private String adjustType;

    @ExcelCellName("ADJUST_POINT")
    @Min(0)
    private BigDecimal adjustPoint;

    @ExcelCellName("POOL_CODE")
    private String poolCode;

    @ExcelCellName("REASON_CODE")
    private String reasonCode;

}
