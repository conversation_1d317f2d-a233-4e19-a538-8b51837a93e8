package com.oneid.loyalty.accounting.ops.util;


import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;

public class Base64InputStream extends FilterInputStream {
    private static boolean a = false;
    private byte[] b = new byte[]{0};
    private byte[] c = new byte[4];
    private byte[] d = new byte[4];
    private boolean e = false;
    private int f = 0;
    private int g = 0;
    private boolean h;
    protected int[] decoding = new int[256];
    protected static final int ERROR = -1;
    protected static final int IGNORE = -2;
    protected static final int NOTIFY = -3;
    private static final byte[] i = new byte[]{65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47, 61};

    public Base64InputStream(InputStream var1) {
        super(var1);
        this.h = a;
        a(this.decoding);
    }

    public Base64InputStream(InputStream var1, boolean var2) {
        super(var1);
        this.h = var2;
        a(this.decoding);
    }

    public void setIgnoreInvalidCharacters(boolean var1) {
        this.h = var1;
    }

    public int read(byte[] var1, int var2, int var3) throws IOException {
        if (this.e) {
            return -1;
        } else {
            int var4 = 0;
            int var5 = 0;

            do {
                while(this.f >= this.g) {
                    this.f = 0;

                    while(true) {
                        while(var5 < 4) {
                            int var6 = this.in.read(this.d, 0, 4 - var5);
                            if (var6 == -1) {
                                if (var5 != 0) {
                                    throw new IOException("Invalid length.");
                                }

                                this.e = true;
                                return var4 == 0 ? -1 : var4;
                            }

                            for(int var7 = 0; var7 < var6; ++var7) {
                                int var8 = this.d[var7] & 255;
                                int var9 = this.decoding[var8];
                                if (var9 >= 0) {
                                    this.c[var5++] = (byte)var9;
                                } else {
                                    if (var9 == -1 && !this.h) {
                                        throw new IOException("Invalid character '" + var8 + "'!");
                                    }

                                    if (var9 == -3) {
                                        if (var5 != 0) {
                                            throw new IOException("Invalid character '" + var8 + "'!");
                                        }

                                        this.notify(this.d);
                                        break;
                                    }
                                }
                            }
                        }

                        if (this.c[2] == 64) {
                            if (this.c[3] != 64) {
                                throw new IOException("Invalid padding!");
                            }

                            this.g = 1;
                        } else if (this.c[3] == 64) {
                            this.g = 2;
                        } else {
                            this.g = 3;
                        }

                        var5 = 0;
                        break;
                    }
                }

                var1[var2 + var4++] = (byte)a(this.c, ++this.f);
            } while(var4 < var3);

            return var4;
        }
    }

    protected void notify(byte[] var1) throws IOException {
    }

    public int read() throws IOException {
        return this.read(this.b, 0, 1) < 0 ? -1 : this.b[0] & 255;
    }

    public static void setDefaultIgnoreInvalidCharacters(boolean var0) {
        a = var0;
    }

    public boolean markSupported() {
        return false;
    }

    private static final int a(byte[] var0, int var1) {
        switch(var1) {
            case 1:
                return (var0[0] & 63) << 2 | (var0[1] & 48) >>> 4;
            case 2:
                return (var0[1] & 15) << 4 | (var0[2] & 60) >>> 2;
            case 3:
                return (var0[2] & 3) << 6 | var0[3] & 63;
            default:
                return 0;
        }
    }

    private static void a(int[] var0) {
        int var1;
        for(var1 = 0; var1 < var0.length; ++var1) {
            var0[var1] = -1;
        }

        byte var2;
        for(var1 = 0; var1 < i.length; var0[var2] = var1++) {
            var2 = i[var1];
        }

        var0[13] = -2;
        var0[10] = -2;
    }
}
