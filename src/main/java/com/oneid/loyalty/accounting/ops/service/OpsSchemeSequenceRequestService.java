package com.oneid.loyalty.accounting.ops.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.SchemeSequenceSettingReq;
import com.oneid.loyalty.accounting.ops.model.res.SchemeSequenceRequestSettingRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeSequenceRes;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;

public interface OpsSchemeSequenceRequestService {
    SchemeSequenceRequestSettingRes getRequestApprovalSetting(OPSAuthenticatedPrincipal principal, Integer businessId, Integer programId, ESchemeType schemeType);
    
    Integer sendRequestForApproval(Integer businessId, Integer programId, ESchemeType schemeType, SchemeSequenceSettingReq req);
    
    Page<SchemeSequenceRes> getChangeRequests(EApprovalStatus approvalStatus, Integer fromDate, Integer toDate, Pageable pageable);
    
    SchemeSequenceRequestSettingRes getApprovedRequestById(Integer requestId);
    
    Page<SchemeSequenceRes> getApprovedRequests(
            Integer businessId, 
            Integer programId, 
            ESchemeType schemeType, 
            Integer version, 
            ECommonStatus status,
            Integer fromDate,
            Integer toDate,
            Pageable pageable);
    
    SchemeSequenceRequestSettingRes getChangeRequestDetailById(Integer changeRequestId);
}
