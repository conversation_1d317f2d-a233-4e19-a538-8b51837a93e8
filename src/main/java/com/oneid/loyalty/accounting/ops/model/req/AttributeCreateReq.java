package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.model.dto.AttributeMasterDataExcelDTO;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AttributeCreateReq {

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @NotBlank(message = "'code' must not be blank")
    @Length(max = 64)
    @Pattern(regexp = "^[A-Za-z0-9]+$")
    private String code;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 128)
    @Pattern(regexp = "^[A-Za-z0-9 ]+$")
    private String name;

    @NotNull(message = "'data_type' must not be null")
    private EAttributeDataType dataType;

    @NotNull(message = "'data_type_display' must not be null")
    private EAttributeDataTypeDisplay dataTypeDisplay;

    @NotEmpty(message = "'operators' must not be empty")
    private List<EAttributeOperator> operators;

    private String regexValidation;

    @Valid
    private List<MasterData> masterDatas = Collections.emptyList();

    private ECommonStatus status = ECommonStatus.ACTIVE;

    private Boolean enableValidateMasterData = false;

    private Boolean havingMasterData;

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class MasterData {
        @NotBlank(message = "'value' must not be blank")
        @Size(max = 64, message = "'value' length must less than 65")
        private String value;

        @Size(max = 255, message = "'description' length must less than 256")
        private String description;

        @NotNull(message = "'status' must not be null")
        private ECommonStatus status = ECommonStatus.ACTIVE;

        public static MasterData valueOf(AttributeMasterDataExcelDTO excelDTO) {
            MasterData masterData = new MasterData();
            masterData.setValue(excelDTO.getValue());
            masterData.setDescription(excelDTO.getDescription());
            masterData.setStatus(ECommonStatus.of(excelDTO.getAttributeStatus()));
            return masterData;
        }
    }
}