package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberStatusTransitionReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionDetailAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import org.springframework.data.domain.Page;

import java.util.List;

public interface OpsMemberStatusTransitionService {
    MakerCheckerInternalMakerRes createRequest(MemberStatusTransitionReq req);

    void approve(ApprovalReq req);

    MemberStatusTransitionRes getChangeableByRequestId(Integer id);

    MakerCheckerInternalMakerRes update(Integer beginningStatusId, MemberStatusTransitionReq req);

    Page<MemberStatusTransitionInReviewRes> getInReviewRequests(EApprovalStatus approvalStatus,
                                                                String fromCreatedAt, String toCreatedAt,
                                                                String fromReviewedAt, String toReviewedAt,
                                                                String createdBy, String checkedBy,
                                                                Integer offset, Integer limit);

    MemberStatusTransitionInReviewRes getInReviewRequestById(Integer reviewId);

    List<MemberStatusTransitionRes> getPageAvailable(Integer businessId,
                                                     Integer programId,
                                                     Integer memberStatusId,
                                                     ECommonStatus status);

    MemberStatusTransitionDetailAvaiRes getAvailableRequestById(Integer beginningStatusId);
}