package com.oneid.loyalty.accounting.ops.validation;

import com.oneid.oneloyalty.common.util.DateTimes;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Date;

public class TimeStampValidator implements ConstraintValidator<TimeStamp, Long> {

    private String message;

    @Override
    public void initialize(TimeStamp constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(Long value, ConstraintValidatorContext context) {
        if (value == null) {
            context.buildConstraintViolationWithTemplate
                    (this.message).addConstraintViolation();
            return false;
        }
        Long limit = DateTimes.timeUnlimited().toInstant().getEpochSecond();
        if (limit.compareTo(value) <= 0) {
            context.buildConstraintViolationWithTemplate
                    (this.message).addConstraintViolation();
            return false;
        }
        return true;
    }
}
