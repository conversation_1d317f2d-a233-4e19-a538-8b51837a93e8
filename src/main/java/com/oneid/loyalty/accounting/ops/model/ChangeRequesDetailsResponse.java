package com.oneid.loyalty.accounting.ops.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ChangeRequesDetailsResponse {
    private Integer id;
    private String makerName;
    private String objectId;
    private Object payload;
    // and more..
}