package com.oneid.loyalty.accounting.ops.component;

import java.io.InputStream;
import java.util.Vector;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.oneid.loyalty.accounting.ops.setting.SFTPSetting;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SFTPClient {
    private final Logger LOGGER = LoggerFactory.getLogger(SFTPClient.class);
    
    private SFTPSetting setting;
    
    @PostConstruct
    private void postConstruct() {
        if(setting.isPingable()) {
            ping();
        }
    }
    
    public String uploadFile(InputStream inputStream, String uploadedPathFile) {
        ChannelSftp channelSftp = null;
        
        String dst = setting.getDestinationRootPath() + "/" + uploadedPathFile;
        
        try {
            channelSftp = createChannelSftp();
            
            mkdirs(channelSftp, dst);
            
            channelSftp.put(inputStream, dst);
            
            return dst;
        } catch (Exception e) {
            LOGGER.error("{}", e);
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
        } finally {
            disconnectChannelSftp(channelSftp);
        }
    }
    
    public boolean ping() {
        ChannelSftp channelSftp = null;
        
        try {
            channelSftp = createChannelSftp();
            return channelSftp != null;
        } catch (Exception e) {
            LOGGER.error(String.format("Can not connect to sftp server '%s'", setting.getHost()), e);
            
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            disconnectChannelSftp(channelSftp);
        }
    }
    
    public void mkdirs(ChannelSftp ch, String path) throws SftpException {
        String[] folders = path.split("/");
        if (folders[0].isEmpty()) folders[0] = "/";
        String fullPath = folders[0];
        
        for (int i = 1; i < folders.length - 1; i ++) {
            Vector<?> ls = ch.ls(fullPath);
            boolean isExist = false;
            for (Object o : ls) {
                if (o instanceof LsEntry) {
                    LsEntry e = (LsEntry) o;
                    if (e.getAttrs().isDir() && e.getFilename().equals(folders[i])) {
                        isExist = true;
                    }
                }
            }
            if (!isExist && !folders[i].isEmpty()) {
                ch.mkdir(fullPath + folders[i]); 
            }
            fullPath = fullPath + folders[i] + "/";
        }
    }
    
    private ChannelSftp createChannelSftp() throws JSchException {
        JSch jSch = new JSch();
        
        Session session = jSch.getSession(setting.getUsername(), setting.getHost(), setting.getPort());
        session.setConfig("StrictHostKeyChecking", "no");
        session.setPassword(setting.getPassword());
        
        session.connect(setting.getSessionTimeout());
        
        Channel channel = session.openChannel("sftp");
        channel.connect(setting.getChannelTimeout());
        
        return (ChannelSftp) channel;
    }
    
    private void disconnectChannelSftp(ChannelSftp channelSftp) {
        try {
            if (channelSftp == null)
                return;

            if (channelSftp.isConnected())
                channelSftp.disconnect();

            if (channelSftp.getSession() != null)
                channelSftp.getSession().disconnect();

        } catch (Exception ex) {
            LOGGER.error("{}", ex);
        }
    }
}
