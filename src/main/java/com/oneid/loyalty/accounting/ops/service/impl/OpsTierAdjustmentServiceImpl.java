package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.dto.VerifyTierDTO;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberTierReq;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberTierRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentExportEntry;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentStatisticRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierAdjustmentService;
import com.oneid.loyalty.accounting.ops.util.RequestProvider;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentBatchProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.TierAdjustmentBatchRequest;
import com.oneid.oneloyalty.common.entity.TierAdjustmentRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.ProgramTierPolicyRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import com.oneid.oneloyalty.common.repository.ReasonCodeRepository;
import com.oneid.oneloyalty.common.repository.TierAdjustmentBatchRequestRepository;
import com.oneid.oneloyalty.common.repository.TierAdjustmentRequestRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.ReasonCodeService;
import com.oneid.oneloyalty.common.service.TierAdjustmentBatchRequestService;
import com.oneid.oneloyalty.common.service.TierAdjustmentRequestService;
import com.oneid.oneloyalty.common.util.DateUtil;
import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpsTierAdjustmentServiceImpl implements OpsTierAdjustmentService {

    @Autowired
    private MemberService memberService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramTierRepository programTierRepository;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private ProgramTierPolicyRepository programTierPolicyRepository;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ReasonCodeService reasonCodeService;

    @Autowired
    private ReasonCodeRepository reasonCodeRepository;

    @Autowired
    private TierAdjustmentRequestRepository tierAdjustmentRequestRepository;

    @Autowired
    private TierAdjustmentBatchRequestRepository tierAdjustmentBatchRequestRepository;

    @Autowired
    private TierAdjustmentRequestService tierAdjustmentRequestService;

    @Autowired
    private TierAdjustmentBatchRequestService tierAdjustmentBatchRequestService;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Value("${maker-checker.module.tier-adjustment:adjust_member_tier}")
    public String module;

    private final static Logger LOGGER = LoggerFactory.getLogger(OpsTierAdjustmentServiceImpl.class);

    @Override
    public MemberTierRes findMemberTier(MemberTierReq req) {
        Program program = programService.find(req.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", req)
        );
        if (!req.getBusinessId().equals(program.getBusinessId())) {
            return null;
        }
        CustomerIdentify dto = new CustomerIdentify();
        dto.setId(req.getCustomer().getId());
        dto.setIdType(req.getCustomer().getIdType());
        Member member = memberService.findActive(dto, req.getProgramId());
        if (member == null) {
            throw new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", req.getCustomer());
        }

        ProgramTier programTier = programTierRepository.findById(member.getTierId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_TIER_NOT_FOUND,
                        "Program tier not found because conflict data in database", member)
        );

        ProgramTierPolicy policy = programTierPolicyRepository.findById(programTier.getTierPolicyId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_TIER_POLICY_NOT_FOUND,
                        "Program tier policy not found", programTier)
        );

        List<DropdownRes> tierForUpdates = programTierRepository.findByTierPolicyId(policy.getId())
                .stream()
                .filter(iter -> !iter.getId().equals(programTier.getId()) && iter.getStatus() == ECommonStatus.ACTIVE)
                .map(DropdownRes::valueOf)
                .collect(Collectors.toList());

        return MemberTierRes
                .builder()
                .memberId(member.getId())
                .memberName(member.getFullName())
                .memberPhoneNo(member.getPhoneNo())
                .policyId(policy.getId())
                .policyName(policy.getName())
                .policyCode(policy.getCode())
                .currentTierId(programTier.getId())
                .currentTierName(programTier.getName())
                .currentTierCode(programTier.getTierCode())
                .currentTierIcon(programTier.getBadgeIconUrl())
                .programId(member.getProgramId())
                .businessId(program.getBusinessId())
                .tierForUpdates(tierForUpdates)
                .avatar(null)
                .status(member.getStatus())
                .build();
    }


    @Override
    public Integer createRequest(CreateTierAdjustmentMemberReq req) {
        businessService.findActive(req.getBusinessId());
        Program program = programService.findByIdAndBusinessId(req.getProgramId(), req.getBusinessId());
        // Check reason code
        ReasonCode adjustmentReasonCode = reasonCodeService
                .findByCode(program.getBusinessId(), program.getId(), req.getReasonCode());
        if (adjustmentReasonCode == null || adjustmentReasonCode.getStatus() != ECommonStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.REASON_CODE_NOT_FOUND, "Reason code not exist", null);
        }
        EOpsIdType idType = EOpsIdType.lookup(req.getIdType().toUpperCase());
        if (idType == null) {
            throw new BusinessException(ErrorCode.IDENTIFY_TYPE_NOT_FOUND, "Identification Type not found", null);
        }
        CustomerIdentify identify = new CustomerIdentify();
        identify.setIdType(idType.getMapping());
        identify.setId(req.getIdCode());
        Member member = memberService.findActive(identify, program.getId());
        if (!program.getId().equals(member.getProgramId())) {
            throw new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", req);
        }
        if (req.getNextTierId().equals(member.getTierId())) {
            throw new BusinessException(ErrorCode.NEXT_TIER_INVALID, "Next tier and current tier are same", req);
        }
        // Check tier code
        programTierService.findActive(req.getNextTierId());
        List requestsMember = tierAdjustmentRequestService
                .findRequestByMemberIdAndBatchApprovalStatus(member.getId(), EApprovalStatus.PENDING);
        if(requestsMember.size() > 0){
            throw new BusinessException(ErrorCode.MEMBER_ALREADY_EXISTED_TIER_ADJUSTMENT_REQUEST,
                    "Member already existed tier adjustment request", req);
        }
        // Set descriptioin
        req.setDesReplace(true);
        VerifyTierDTO dto = VerifyTierDTO.builder()
                .memberId(member.getId())
                .currentTierId(member.getTierId())
                .nextTierId(req.getNextTierId())
                .reasonCode(req.getReasonCode())
                .idType(identify.getIdType().getValue())
                .idCode(req.getIdCode())
                .build();
        return this.saveTierAdjustment(ETierAdjustmentType.MEMBER, req, List.of(dto), null);
    }

    @Override
    public InputStream verifyFile(CreateTierAdjustmentReq req, MultipartFile multipartFile) throws Exception {
        if (!FilenameUtils.getExtension(multipartFile.getOriginalFilename()).equals("xlsx"))
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid format file", null);
        List<VerifyTierDTO> data = new ArrayList<>();
        boolean isValidFile = this.verify(multipartFile, data, req.getBusinessId(), req.getProgramId());
        return isValidFile ? null : this.exportFile(data);
    }

    @Override
    public InputStream createBatchRequest(CreateTierAdjustmentReq req, MultipartFile multipartFile) throws Exception {
        if (!FilenameUtils.getExtension(multipartFile.getOriginalFilename()).equals("xlsx"))
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid format file", null);
        List<VerifyTierDTO> datas = new ArrayList<>();
        // Verify before create batch request
        boolean isValidFile = this.verify(multipartFile, datas, req.getBusinessId(), req.getProgramId());
        if (!isValidFile) {
            return this.exportFile(datas);
        }
        // Create batch request
        this.saveTierAdjustment(ETierAdjustmentType.BATCH, req, datas, multipartFile.getOriginalFilename());
        return null;
    }

    @Override
    public ResourceDTO exportFileAvailableRequest(Integer batchId) {
        TierAdjustmentBatchRequest tierAdjustmentBatchRequest = tierAdjustmentBatchRequestService.findActive(batchId);

        // Check tier adjustment request is completed or not
        if(!ETierAdjustmentBatchProcessStatus.COMPLETED.equals(tierAdjustmentBatchRequest.getProcessStatus())){
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_BATCH_REQUEST_NOT_FOUND,
                    "Tier adjustment batch not found", null);
        }
        return this.exportFileList(tierAdjustmentBatchRequest);

    }

    @Override
    public ResourceDTO exportFileInReviewRequest(Integer batchId) {
        TierAdjustmentBatchRequest tierAdjustmentBatchRequest = tierAdjustmentBatchRequestService.findActive(batchId);
        return this.exportFileList(tierAdjustmentBatchRequest);
    }

    @Override
    public TierAdjustmentRequestRes inReviewDetail(Integer inReviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> response = makerCheckerServiceClient.getChangeRequestById(String.valueOf(inReviewId));
        TierAdjustmentRequest tierAdjustmentRequest = tierAdjustmentRequestRepository
                .findById(Integer.parseInt(response.getData().getObjectId())).orElseThrow(
                        () -> new BusinessException(ErrorCode.TIER_ADJUSTMENT_REQUEST_NOT_FOUND,
                                "Tier adjustment request not found", response)
                );
        return getDetail(tierAdjustmentRequest, inReviewId);
    }

    @Override
    public TierAdjustmentRequestRes availableDetail(Integer requestId) {
        TierAdjustmentRequest tierAdjustmentRequest = tierAdjustmentRequestRepository.findById(requestId).orElseThrow(
                () -> new BusinessException(ErrorCode.TIER_ADJUSTMENT_REQUEST_NOT_FOUND,
                        "Tier adjustment request not found", requestId)
        );
        return getDetail(tierAdjustmentRequest, null);
    }

    @Override
    public Page<TierAdjustmentRequestRes> getInReview(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable) {
        Integer fromDateSecond = fromDate != null ? Math.toIntExact(fromDate.atTime(LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000)
                : null;
        Integer toDateSecond = toDate != null ? Math.toIntExact(toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000)
                : null;

        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                module,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromDateSecond,
                toDateSecond);

        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        List<TierAdjustmentRequestRes> tierAdjustmentRequests;

        if (changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeignResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each));

            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());

            tierAdjustmentRequests = transform(tierAdjustmentRequestRepository.filter(
                    requestIds, null, null, null, null, EApprovalStatus.PENDING, PageRequest.of(0, pageable.getPageSize())
            ).getContent(), changeRecordFeignResMap);

        } else {
            tierAdjustmentRequests = Collections.emptyList();
        }

        return new PageImpl<>(tierAdjustmentRequests, pageable, changeRequestPageFeignRes.getTotalRecordCount());
    }

    @Override
    public Page<TierAdjustmentRequestRes> getAvailableRequest(Integer businessId, Integer programId, String id, EIdType idType, ECommonStatus status, EApprovalStatus approvalStatus, Pageable pageable) {
        Long memberId = null;
        if (id != null) {
            if (idType == null || programId == null) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Search by phone_no/user_id is required program id and id type", null);
            }
            try {
                Member member = memberService.find(new CustomerIdentify(id, idType), programId);
                memberId = member.getId();
            } catch (BusinessException e) {
                LOGGER.error(e.getMessage());
                return Page.empty();
            }
        }
        Page<Object[]> page = tierAdjustmentRequestRepository
                .filter(null, businessId, programId, memberId, status, approvalStatus, pageable);

        List<TierAdjustmentRequestRes> content = transform(page.getContent(), null);

        return new PageImpl<>(content, pageable, page.getTotalElements());
    }


    @Override
    public Page<TierAdjustmentRequestRes> getInReviewDetails(Integer batchId, Pageable pageable) {
        return getDetails(batchId, null, pageable);
    }

    @Override
    public Page<TierAdjustmentRequestRes> getAvailableDetails(Integer batchId, ETierAdjustmentProcessStatus processStatus, Pageable pageable) {
        return getDetails(batchId, processStatus, pageable);
    }

    private Page<TierAdjustmentRequestRes> getDetails(Integer batchId, ETierAdjustmentProcessStatus processStatus, Pageable pageable) {
        TierAdjustmentBatchRequest tierAdjustmentBatchRequest = tierAdjustmentBatchRequestService.findActive(batchId);

        SpecificationBuilder<TierAdjustmentRequest> specificationBuilder = new SpecificationBuilder<>();

        specificationBuilder.add(new SearchCriteria("batchId", batchId, SearchOperation.EQUAL));

        if (processStatus != null) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        Page<TierAdjustmentRequest> result = tierAdjustmentRequestService.find(specificationBuilder, pageable);

        List<ProgramTier> programTiers = programTierService.findByProgramId(tierAdjustmentBatchRequest.getProgramId());

        List<ReasonCode> reasonCodes = reasonCodeService.findByProgramId(tierAdjustmentBatchRequest.getProgramId());

        Map<Integer, ProgramTier> programTierMap = programTiers.stream()
                .collect(Collectors.toMap(ProgramTier::getId, Function.identity()));

        Map<String, ReasonCode> reasonCodeMap = reasonCodes.stream()
                .collect(Collectors.toMap(ReasonCode::getCode, Function.identity()));

        List<TierAdjustmentRequestRes> tierAdjustmentRequestRes = result.getContent().stream().map(
                tar -> {
                    ProgramTier currentTier = programTierMap.get(tar.getCurrentTierId());
                    ProgramTier nextTier = programTierMap.get(tar.getNextTierId());
                    ReasonCode reasonCode = reasonCodeMap.get(tar.getReasonCode());

                    Optional<Member> member = memberService.find(tar.getMemberId());

                    Date updatedAt = tar.getProcessStatus().equals(ETierAdjustmentProcessStatus.PENDING) ? null : tar.getUpdatedAt();

                    return TierAdjustmentRequestRes.builder()
                            .requestId(tar.getId())
                            .memberId(tar.getMemberId())
                            .memberCode(member.isPresent() ? member.get().getMemberCode() : null)
                            .idType(Objects.requireNonNull(EOpsIdType.lookup(EIdType.of(tar.getIdType()))).getValue())
                            .idNo(tar.getIdNo())
                            .currentTier(currentTier != null ? new ShortEntityRes(currentTier.getId(), currentTier.getName(), currentTier.getTierCode()) : null)
                            .nextTier(nextTier != null ? new ShortEntityRes(nextTier.getId(), nextTier.getName(), nextTier.getTierCode()) : null)
                            .reasonDescription(reasonCode != null ? reasonCode.getName() : null)
                            .description(tar.getDescription())
                            .updatedAt(updatedAt)
                            .status(tar.getStatus())
                            .processStatus(tar.getProcessStatus())
                            .errorMessage(tar.getErrorMessage())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(tierAdjustmentRequestRes, pageable, result.getTotalElements());
    }

    private TierAdjustmentRequestRes getDetail(TierAdjustmentRequest tierAdjustmentRequest, Integer reviewId) {
        Program program = programService.find(tierAdjustmentRequest.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND,
                        "Program not found", tierAdjustmentRequest)
        );

        Member member = memberService.find(tierAdjustmentRequest.getMemberId()).orElseThrow(
                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND,
                        "Member not found", tierAdjustmentRequest)
        );

        ProgramTier currentTier = programTierService.find(tierAdjustmentRequest.getCurrentTierId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_TIER_NOT_FOUND,
                        "Program tier not found", tierAdjustmentRequest)
        );

        ProgramTier nextTier = programTierService.find(tierAdjustmentRequest.getNextTierId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_TIER_NOT_FOUND,
                        "Program tier not found", tierAdjustmentRequest)
        );

        ProgramTierPolicy policy = programTierPolicyRepository.findById(nextTier.getTierPolicyId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_TIER_POLICY_NOT_FOUND,
                        "Program tier policy not found", tierAdjustmentRequest)
        );

        Business business = businessService.find(program.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", tierAdjustmentRequest)
        );

        ReasonCode reasonCode = reasonCodeService
                .findByCode(program.getBusinessId(), program.getId(), tierAdjustmentRequest.getReasonCode());

        return TierAdjustmentRequestRes.builder()
                .requestId(tierAdjustmentRequest.getId())
                .reviewId(reviewId)
                .programName(program.getName())
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .fullName(member.getFullName())
                .phoneNo(member.getPhoneNo())
                .policyName(policy.getName())
                .nextTier(new ShortEntityRes(nextTier.getId(), nextTier.getName(), nextTier.getTierCode()))
                .currentTier(new ShortEntityRes(currentTier.getId(), currentTier.getName(), currentTier.getTierCode()))
                .reasonDescription(reasonCode.getName())
                .note(tierAdjustmentRequest.getDescription())
                .approvalStatus(tierAdjustmentRequest.getApprovalStatus())
                .status(tierAdjustmentRequest.getStatus())
                .businessName(business.getName())
                .createdAt(tierAdjustmentRequest.getCreatedAt())
                .createdBy(tierAdjustmentRequest.getCreatedBy())
                .updatedAt(tierAdjustmentRequest.getUpdatedAt())
                .updatedBy(tierAdjustmentRequest.getUpdatedBy())
                .approvedAt(tierAdjustmentRequest.getApprovedAt())
                .approvedBy(tierAdjustmentRequest.getApprovedBy())
                .reason(tierAdjustmentRequest.getRejectReason())
                .build();
    }

    private List<TierAdjustmentRequestRes> transform(List<Object[]> resultSet, Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeignResMap) {
        if (changeRecordFeignResMap == null) {
            changeRecordFeignResMap = new HashMap<>();
        }

        Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> finalChangeRecordFeignResMap = changeRecordFeignResMap;

        return resultSet.stream().map(
                iter -> {
                    TierAdjustmentRequest request = (TierAdjustmentRequest) iter[0];
                    Program program = (Program) iter[1];
                    ProgramTier nextTier = (ProgramTier) iter[2];
                    ProgramTier currentTier = (ProgramTier) iter[3];
                    ReasonCode reasonCode = (ReasonCode) iter[4];
                    Business business = (Business) iter[5];

                    ChangeRequestPageFeignRes.ChangeRecordFeginRes changeRequest = finalChangeRecordFeignResMap.getOrDefault(request.getId(), null);

                    return TierAdjustmentRequestRes.builder()
                            .reviewId(changeRequest != null ? changeRequest.getChangeRequestId() : null)
                            .requestId(request.getId())
                            .programName(program.getName())
                            .memberId(request.getMemberId())
                            .nextTier(new ShortEntityRes(nextTier.getId(), nextTier.getName(), nextTier.getTierCode()))
                            .currentTier(new ShortEntityRes(currentTier.getId(), currentTier.getName(), currentTier.getTierCode()))
                            .reasonDescription(reasonCode.getName())
                            .note(request.getDescription())
                            .approvalStatus(request.getApprovalStatus())
                            .status(request.getStatus())
                            .businessName(business.getName())
                            .createdAt(request.getCreatedAt())
                            .createdBy(request.getCreatedBy())
                            .updatedAt(request.getUpdatedAt())
                            .updatedBy(request.getUpdatedBy())
                            .approvedAt(request.getApprovedAt())
                            .approvedBy(request.getApprovedBy())
                            .reason(request.getRejectReason())
                            .build();
                }
        ).collect(Collectors.toList());
    }

    private String toStringOrBlank(Object s) {
        if (s == null) return "";
        return String.valueOf(s);
    }

    private boolean verify(MultipartFile multipartFile,
                           List<VerifyTierDTO> data,
                           Integer businessId,
                           Integer programId) {
        businessService.findActive(businessId);
        programService.findByIdAndBusinessId(programId, businessId);
        Map<Long, Integer> listMemner = new HashMap<>();
        boolean isValid = true;
        try (XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream())) {
            Map<String, Integer> listTierCode = programTierService
                    .findActiveByProgramOrderRankNoDesc(programId)
                    .stream()
                    .filter(ele -> Objects.nonNull(ele.getTierCode()))
                    .collect(Collectors.toMap(tier -> tier.getTierCode().toUpperCase(), ProgramTier::getId, (k1 , k2) -> k2));
            Map<String, Integer> listReasonCode = reasonCodeRepository
                    .findByBusinessIdAndProgramIdAndStatus(businessId, programId, ECommonStatus.ACTIVE)
                    .stream()
                    .filter(ele -> Objects.nonNull(ele.getCode()))
                    .collect(Collectors.toMap(rc -> rc.getCode().toUpperCase(), ReasonCode::getId, (k1 , k2) -> k2));
            XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(0);

            int startRowNum = 1;
            Row row = null;
            DataFormatter formatter = new DataFormatter();
            if (xssfSheet.getLastRowNum() - 1 > 10000) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid file max row", null);
            }
            for (int rownum = 0; rownum < xssfSheet.getLastRowNum(); rownum++) {
                row = xssfSheet.getRow(startRowNum + rownum);
                if (row == null) {
                    break;
                }
                Member member = null;
                String cellBusinessId = formatter.formatCellValue(row.getCell(0)).trim();
                String cellProgramId = formatter.formatCellValue(row.getCell(1)).trim();
                String cellIdType = formatter.formatCellValue(row.getCell(2)).trim().toUpperCase();
                String cellIdCode = formatter.formatCellValue(row.getCell(3)).trim();
                if (StringUtils.isEmpty(cellIdCode) && StringUtils.isEmpty(cellIdType)) {
                    continue;
                }
                String cellTierCode = formatter.formatCellValue(row.getCell(4)).trim().toUpperCase();
                String cellReasonCode = formatter.formatCellValue(row.getCell(5)).trim().toUpperCase();
                String cellDescription = formatter.formatCellValue(row.getCell(6)).trim();

                VerifyTierDTO dto = VerifyTierDTO.builder()
                        .businessId(cellBusinessId)
                        .programId(cellProgramId)
                        .idType(cellIdType)
                        .idCode(cellIdCode)
                        .tierCode(cellTierCode)
                        .reasonCode(cellReasonCode)
                        .description(cellDescription)
                        .build();
                data.add(dto);
                dto = data.get(data.size() - 1);
                try {
                    EOpsIdType idType = EOpsIdType.lookup(cellIdType);
                    if (idType == null) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid Identification Type", null);
                    }
                    CustomerIdentify identify = new CustomerIdentify();
                    identify.setId(dto.getIdCode());
                    identify.setIdType(idType.getMapping());
                    dto.setIdType(identify.getIdType().getValue());
                    dto.setIdCode(identify.getId());
                    member = memberService.findActive(identify, programId);
                    List requestsMember = tierAdjustmentRequestService
                            .findRequestByMemberIdAndBatchApprovalStatus(member.getId(), EApprovalStatus.PENDING);
                    if(requestsMember.size() > 0){
                        throw new BusinessException(ErrorCode.MEMBER_ALREADY_EXISTED_TIER_ADJUSTMENT_REQUEST,
                                "Member already existed tier adjustment request", null);
                    }
                    // Check duplicate member
                    if (listMemner.get(member.getId()) == null) {
                        listMemner.put(member.getId(), startRowNum + rownum);
                    } else {
                        Integer duplicateRow = listMemner.get(member.getId()) + 1;
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Duplicate member row " + duplicateRow, null);
                    }
                    // Check tier code
                    String valueTierCode = formatter.formatCellValue(row.getCell(4)).trim().toUpperCase();
                    if (listTierCode.get(valueTierCode) == null) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid Tier Code", null);
                    }
                    if(member.getTierId() == null) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Current tier not found", null);
                    }
                    if (listTierCode.get(valueTierCode).equals(member.getTierId())) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Next tier and current tier are same", null);
                    }
                    // Check reason code
                    String valueReasonCode = formatter.formatCellValue(row.getCell(5)).trim().toUpperCase();
                    if (listReasonCode.get(valueReasonCode) == null) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid Reason Code", null);
                    }
                    // Set valid after check
                    dto.setValidateStatus(true);
                    // set row to list data
                    dto.setMemberId(member.getId());
                    dto.setCurrentTierId(member.getTierId());
                    dto.setNextTierId(listTierCode.get(valueTierCode));
                } catch (Exception be) {
                    be.printStackTrace();
                    Log.error("Oneloyalty ops - Tier Adjustment verify fail", be);
                    isValid = false;
                    dto.setValidateStatus(false);
                    dto.setErrorMessage(toStringOrBlank(be.getMessage()));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.error("Oneloyalty ops - Tier Adjustment verify fail", e);
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
        }
        if(data.isEmpty()) {
            isValid = false;
        }
        return isValid;
    }

    private InputStream exportFile(List<VerifyTierDTO> data) {
        try (XSSFWorkbook wbTmp = new XSSFWorkbook()) {
            wbTmp.createSheet();
            Sheet sheet = wbTmp.getSheetAt(0);
            // Create row header
            Row header = sheet.createRow(0);
            Cell headerCell = null;
            List<String> headerTitle = List.of("BUSINESS_CODE", "PROGRAM_CODE", "IDENTIFICATION_TYPE", "IDENTIFICATION_CODE",
                    "TIER_CODE", "REASON_CODE", "DESCRIPTION", "Validation status", "Error message");
            CellStyle cellStyle = this.getCellStyleHeader(wbTmp);
            for (int cell = 0; cell < headerTitle.size(); cell++) {
                headerCell = header.createCell(cell);
                headerCell.setCellValue(headerTitle.get(cell));
                headerCell.setCellStyle(cellStyle);
            }
            // Create row data
            Row row = null;
            Cell cell = null;
            int rowIndexAddress = 1;
            for (VerifyTierDTO dto : data) {
                row = sheet.createRow(rowIndexAddress);

                cell = row.createCell(0);
                cell.setCellValue(toStringOrBlank(dto.getBusinessId()));

                cell = row.createCell(1);
                cell.setCellValue(toStringOrBlank(dto.getProgramId()));

                cell = row.createCell(2);
                cell.setCellValue(toStringOrBlank(EOpsIdType.lookup(EIdType.of(dto.getIdType())).getValue()));

                cell = row.createCell(3);
                cell.setCellValue(toStringOrBlank(dto.getIdCode()));

                cell = row.createCell(4);
                cell.setCellValue(toStringOrBlank(dto.getTierCode()));

                cell = row.createCell(5);
                cell.setCellValue(toStringOrBlank(dto.getReasonCode()));

                cell = row.createCell(6);
                cell.setCellValue(toStringOrBlank(dto.getDescription()));

                cell = row.createCell(7);
                cell.setCellValue(dto.getValidateStatus() ? "Valid" : "Invalid");

                cell = row.createCell(8);
                cell.setCellValue(toStringOrBlank(dto.getErrorMessage()));

                ++rowIndexAddress;
            }
            Path tmpOutputFile = Files.createTempFile(null, null);
            FileOutputStream out = new FileOutputStream(tmpOutputFile.toFile());
            wbTmp.write(out);
            out.close();
            return new FileSystemResource(tmpOutputFile).getInputStream();
        } catch (Exception e) {
            e.printStackTrace();
            Log.error("Tier Adjustment export file fail ", e);
            return null;
        }
    }

    @Transactional
    public Integer saveTierAdjustment(ETierAdjustmentType type, CreateTierAdjustmentReq req,
                                   List<VerifyTierDTO> datas, String fileNameUpload) {
        // Create TierAdjustmentBatchRequest
        TierAdjustmentBatchRequest request = new TierAdjustmentBatchRequest();
        request.setBusinessId(req.getBusinessId());
        request.setProgramId(req.getProgramId());
        request.setType(type);
        request.setReferenceCode(req.getReferenceCode());
        request.setDescription(req.getDescription());
        request.setDesReplace(req.getDesReplace());

        request.setStatus(ECommonStatus.ACTIVE);
        request.setProcessStatus(ETierAdjustmentBatchProcessStatus.PENDING);
        request.setApprovalStatus(EApprovalStatus.PENDING);

        request.setTotalRequests(datas.size());
        request.setPendingRequests(datas.size());

        request = tierAdjustmentBatchRequestRepository.save(request);
        // Create list TierAdjustmentRequest
        List<TierAdjustmentRequest> tierRequests = new ArrayList<>();
        for (VerifyTierDTO data : datas) {
            TierAdjustmentRequest adjustmentRequest = new TierAdjustmentRequest();
            adjustmentRequest.setBusinessId(req.getBusinessId());
            adjustmentRequest.setProgramId(req.getProgramId());
            adjustmentRequest.setMemberId(data.getMemberId());
            adjustmentRequest.setCurrentTierId(data.getCurrentTierId());
            adjustmentRequest.setNextTierId(data.getNextTierId());
            adjustmentRequest.setReasonCode(data.getReasonCode());
            adjustmentRequest.setBatchId(request.getId());
            if (req.getDesReplace() != null && req.getDesReplace() && StringUtils.isBlank(data.getDescription()) ) {
                adjustmentRequest.setDescription(req.getDescription());
            } else {
                adjustmentRequest.setDescription(data.getDescription());
            }
            adjustmentRequest.setStatus(ECommonStatus.ACTIVE);
            adjustmentRequest.setProcessStatus(ETierAdjustmentProcessStatus.PENDING);
            adjustmentRequest.setApprovalStatus(EApprovalStatus.PENDING);

            adjustmentRequest.setIdType(EIdType.of(data.getIdType()).getValue());
            adjustmentRequest.setIdNo(data.getIdCode());
            tierRequests.add(adjustmentRequest);
        }
        tierAdjustmentRequestRepository.saveAll(tierRequests);

        return request.getId();
    }

    @Override
    @Transactional
    public TierAdjustmentStatisticRes getTierAdjustmentStatisticById(Integer requestId) {
        TierAdjustmentBatchRequest request = tierAdjustmentBatchRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.TIER_ADJUSTMENT_BATCH_REQUEST_NOT_FOUND, null, null));

        TierAdjustmentStatisticRes response = new TierAdjustmentStatisticRes();

        response.setTotal(request.getTotalRequests() != null ? request.getTotalRequests() : 0);
        response.setTotalSuccess(request.getSuccessRequests() != null ? request.getSuccessRequests() : 0);
        response.setTotalFail(request.getFailedRequests() != null ? request.getFailedRequests() : 0);
        response.setTotalPending(request.getPendingRequests() != null ? request.getPendingRequests() : 0);
        return response;
    }

    private CellStyle getCellStyleHeader(Workbook workbook) {
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        CellStyle cellStyleHeader = workbook.createCellStyle();
        cellStyleHeader.setFont(headerFont);
        return cellStyleHeader;
    }

    private ResourceDTO exportFileList(TierAdjustmentBatchRequest batchRequest) {
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), batchRequest.getBusinessId());
        List<TierAdjustmentRequest> requests = tierAdjustmentRequestService
                .findByBatchId(batchRequest.getId());
        Map<Integer, String> mapTierCode = programTierService
                .findActiveByProgramOrderRankNoDesc(batchRequest.getProgramId())
                .stream()
                .collect(Collectors.toMap(ProgramTier::getId, tier -> tier.getTierCode().toUpperCase()));
        Map<String, ReasonCode> mapReasonCode = reasonCodeRepository
                .findByBusinessIdAndProgramIdAndStatus(batchRequest.getBusinessId(), batchRequest.getProgramId(), ECommonStatus.ACTIVE)
                .stream()
                .collect(Collectors.toMap(rc -> rc.getCode().toUpperCase(), rc -> rc));
        // Create file excel
        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());
        String fileName = String.format("OLSTierAdjustment_%s_%s_%s_%s.xlsx",
                ddMMyyyy, batchRequest.getId(), business.getCode(), program.getCode());
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.TIER_ADJ)
                .objectId(OPSConstant.TIER_ADJ)
                .build();
        AtomicInteger rowNum = new AtomicInteger(1);
        List<TierAdjustmentExportEntry> data = requests.stream().map(ele -> {
            Member member = memberService.find(ele.getMemberId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found",
                    LogData.createLogData()
                            .append("request_id", RequestProvider.getRequestID())
                            .append("member_id", ele.getMemberId())));
            ReasonCode reasonCode = mapReasonCode.get(ele.getReasonCode());
            String strReasonCode = reasonCode  == null ? null :
                    String.format(OPSConstant.REASON_FORMAT, reasonCode.getCode(), reasonCode.getName());
            String strUpdateAt = ETierAdjustmentProcessStatus.PENDING.equals(ele.getProcessStatus()) ? null :
                    DateUtil.formatDD_MM_yy(ele.getUpdatedAt());
            return TierAdjustmentExportEntry.builder()
                    .id(rowNum.getAndIncrement())
                    .businessCode(business.getCode())
                    .programCode(program.getCode())
                    .memberCode(member.getMemberCode())
                    .idType(Objects.requireNonNull(EOpsIdType.lookup(EIdType.of(ele.getIdType()))).getValue())
                    .idNo(ele.getIdNo())
                    .initialTier(mapTierCode.get(ele.getCurrentTierId()))
                    .adjustmentTier(mapTierCode.get(ele.getNextTierId()))
                    .reason(strReasonCode)
                    .description(ele.getDescription())
                    .updatedBy(strUpdateAt)
                    .status(ele.getProcessStatus().getDisplayName().toUpperCase())
                    .build();
        }).collect(Collectors.toList());
        return commonExcelService.opsExport(context, fileName, data);
    }
}