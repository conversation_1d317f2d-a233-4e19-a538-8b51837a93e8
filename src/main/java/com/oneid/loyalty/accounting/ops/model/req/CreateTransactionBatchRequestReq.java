package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateTransactionBatchRequestReq {

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @NotNull(message = "'transaction_batch_type' must not be null")
    private ETransactionBatchType transactionBatchType;

    private String batchName;

    private String campaignCode;

    private String referenceCode;

    private String description;

    private EBoolean isReplaceDes;

    @NotNull(message = "'gen_invoice_no_method' must not be null")
    private EGenerationInvoiceNoMethod genInvoiceNoMethod;

    private ECharacterSet genInvoiceNoCharacterSet;

    private String genInvoiceNoPattern;

    @NotNull(message = "'gen_transaction_time_method' must not be null")
    private EGenerationTransactionTimeMethod genTransactionTimeMethod;

    @JsonDeserialize(using = DateDeserializer.class)
    private Date genTransactionTimeValue;

    private String smsTemplate;

    @AssertTrue(message = "'gen_invoice_no_character_set' and 'gen_invoice_no_pattern' must not be null")
    public boolean isGenInvoiceNoValid() {
        if(EGenerationInvoiceNoMethod.CUSTOM.equals(this.getGenInvoiceNoMethod())) {
            if(this.getGenInvoiceNoCharacterSet() == null
                    || this.getGenInvoiceNoPattern() == null) {
                return false;
            }
        }
        return true;
    }

    @AssertTrue(message = "'gen_transaction_time_value' must not be null")
    public boolean isGenTransactionTimeValid() {
        if(EGenerationTransactionTimeMethod.CUSTOM.equals(this.getGenTransactionTimeMethod())
            && this.getGenTransactionTimeValue() == null) {
            return false;
        }
        return true;
    }
}
