package com.oneid.loyalty.accounting.ops.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.component.SFTPClient;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateMemberCPRFeignReq;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.MakerCheckerChangeRequestResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberCPRReq;
import com.oneid.loyalty.accounting.ops.model.res.CPRExportRes;
import com.oneid.loyalty.accounting.ops.model.res.CardProductionRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.excel.ExcelWriter;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardProductionRequest;
import com.oneid.oneloyalty.common.entity.CardTMP;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CardProductionRequestRepository;
import com.oneid.oneloyalty.common.repository.CardTMPRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsCardProductionRequestServiceImpl implements OpsCardProductionRequestService {
    @Value("${maker-checker.module.cpr}")
    private String moduleId;
    
    @Autowired
    private CardProductionRequestRepository cardProductionRequestRepository;

    @Autowired
    private CardTMPRepository cardTMPRepository;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private CardTypeService cardTypeService;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @Autowired
    @Qualifier("cprSFTPClient")
    private SFTPClient sftpClient;
    
    @Autowired
    private CardServiceFeignClient cardServiceFeignClient;

    @Override
    public CardProductionRequestRes getById(Integer id) {
        CardProductionRequest entity = this.cardProductionRequestRepository.findById(id)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.CARD_PRODUCTION_REQUEST_NOT_FOUND, "Card production request not found",
                        LogData.createLogData().append("id", id)));

        return CardProductionRequestRes.valueOf(entity);
    }

    @Override
    public CPRExportRes exportDetailToSFTP(Integer id) {
        // Get data
        CardProductionRequest cardProductionRequest = this.cardProductionRequestRepository.findById(id)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.CARD_PRODUCTION_REQUEST_NOT_FOUND, "Card production request not found",
                        LogData.createLogData().append("id", id)));
        SpecificationBuilder<CardTMP> specificationBuilder = new SpecificationBuilder<>();
        specificationBuilder.add(new SearchCriteria("businessId", cardProductionRequest.getBusinessId(), SearchOperation.EQUAL));
        specificationBuilder.add(new SearchCriteria("programId", cardProductionRequest.getProgramId(), SearchOperation.EQUAL));
        specificationBuilder.add(new SearchCriteria("cprBatchNo", cardProductionRequest.getCprBatchNo(), SearchOperation.EQUAL));
        List<CardTMP> cardTMPList = cardTMPRepository.findAll(specificationBuilder);
        //
        Store store = storeService.findActive(cardProductionRequest.getStoreId());
        Corporation corporation = corporationService.findActive(store.getCorporationId());
        CardType cardType = cardTypeService.findActive(cardProductionRequest.getCardTypeId());
        // Write file
        String filename = String.format(
                "%s_%s_%s_%s_%s_%s_%d.xlsx",
                corporation.getName(),
                corporation.getCode(),
                store.getCode(),
                store.getName(),
                cardProductionRequest.getCprBatchNo(),
                cardType.getCardType(),
                new Date().toInstant().getEpochSecond()
        );
        String sheetName = "Card Production Request";
        List<String> columns = Arrays.asList("Card No");
        ExcelWriter<CardTMP> excelWriter = new ExcelWriter<CardTMP>();
        
        String localPath = excelWriter.export(
                filename, 
                sheetName,
                columns,
                cardTMPList,
                cardTMP -> Collections.singletonList(cardTMP.getCardNo()));
        
        try {
            return new CPRExportRes(uploadToSFTP(filename, localPath), filename);
        } catch (IOException e) {
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
        }
    }
    
    @Override
    public Long createCPRRequest(OPSAuthenticatedPrincipal principal, CreateMemberCPRReq req) {
       Business business = businessRepository.findByCode(req.getBusinessCode());
       Integer storeId = null;
       
       if(business != null) {
           Store store = storeService.findByBusinessIdAndCode(business.getId(), req.getStoreCode());
           storeId = store.getId();
       }
        
        CreateMemberCPRFeignReq cprFeignReq = CreateMemberCPRFeignReq
                .builder()
                .businessCode(req.getBusinessCode())
                .cardBinCode(req.getCardBinCode())
                .cardTypeCode(req.getCardTypeCode())
                .description(req.getDescription())
                .noOfCard(req.getNoOfCard())
                .programCode(req.getProgramCode())
                .status(req.getStatus())
                .storeCode(req.getStoreCode())
                .storeId(storeId)
                .userId(principal.getUserName())
                .createdAt(LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond())
                .build();
        
        cardServiceFeignClient.verifyCardProductionRequest(cprFeignReq);
        
        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .payload(cprFeignReq)
                .build();
        
        APIResponse<MakerCheckerChangeRequestResponse> response = makerCheckerServiceClient.changes(feignReq);
        
        return response.getData().getId();
    }
    
    private String uploadToSFTP(String remotePath, String localPath) throws IOException {
        Path exportedTmpFile = Paths.get(localPath);
        
        try(InputStream inputstream = Files.newInputStream(exportedTmpFile)){
            return sftpClient.uploadFile(inputstream, remotePath);
        } finally {
            Files.delete(exportedTmpFile);
        }
    }
}
