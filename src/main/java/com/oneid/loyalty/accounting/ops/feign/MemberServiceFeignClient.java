package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.model.req.MemberRegisterFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberProfileDetailFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;

@FeignClient(name = "oneloyalty-member-service", url = "${oneloyalty.member-service.url}",
        configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface MemberServiceFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/v1/members/{memberId}/profile")
    APIResponse<MemberProfileDetailFeignRes> getProfileById(@PathVariable("memberId") Long memberId);
    
    @RequestMapping(method = RequestMethod.GET, value = "/v1/members/detail")
    APIResponse<MemberProfileDetailFeignRes> getProfileByCode(
            @RequestParam(name = "business_code") String businessCode,
            @RequestParam(name = "program_code") String programCode,
            @RequestParam(name = "member_code") String memberCode,
            @RequestParam(name = "member_id") Long memberId);
    
    @RequestMapping(method = RequestMethod.POST, value = "/v1/members/add")
    APIResponse<MemberProfileDetailFeignRes> addMember(MemberRegisterFeignReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "/v1/members/{memberId}/update")
    APIResponse<MemberProfileDetailFeignRes> updateMember(@PathVariable("memberId") Long memberId, MemberRegisterFeignReq req);
}