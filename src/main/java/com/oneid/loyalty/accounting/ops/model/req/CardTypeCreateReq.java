package com.oneid.loyalty.accounting.ops.model.req;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class CardTypeCreateReq {
    @NotNull(message = "'BusinessId' must not be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull
    @JsonProperty("program_id")
    private Integer programId;

    @NotNull
    @JsonProperty("card_policy_id")
    private Integer cardPolicyId;

    @Pattern(regexp = "^[0-9]{1,3}$", message = "'Card type' is invalid")
    @Length(max = 3, message = "'CardType' max length is 3")
    @NotBlank(message = "'CardType' must not be blank")
    @JsonProperty("card_type")
    private String cardType;

    @Length(max = 255, message = "'Description' max length is 255")
    @JsonProperty("description")
    private String description;

    @NotBlank
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;
}
