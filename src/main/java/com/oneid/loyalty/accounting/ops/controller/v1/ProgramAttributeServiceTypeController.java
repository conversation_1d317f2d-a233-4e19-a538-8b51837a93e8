package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("v1")
public class ProgramAttributeServiceTypeController extends BaseController {

    @Autowired
    private OpsConditionService opsConditionService;

    @GetMapping("rule-condition/attributes/all")
    public ResponseEntity<?> getAll(@RequestParam(value = "program_id") Integer programId) {
        return success(opsConditionService.getAllConditionAttribute(programId));
    }

    @GetMapping("rule-condition/attributes/modules/{module}")
    public ResponseEntity<?> getAll(
            @RequestParam(value = "program_id") Integer programId,
            @PathVariable("module")EServiceType module) {
        return success(opsConditionService.getByProgramIdAndModule(programId, module));
    }

    @GetMapping("attributes/{service_type}/program/{program_id}")
    public ResponseEntity<?> getAllff( // todo remove later
                                       @PathVariable("program_id") Integer programId,
                                       @PathVariable("service_type") EServiceType serviceType) {
        return success(opsConditionService.getByProgramIdAndModule(programId, serviceType));
    }

    @GetMapping("scheme-attribute")
    public ResponseEntity<?> getAllfff( // todo remove later
                                       @RequestParam("program_id") Integer programId) {
        return success(opsConditionService.getByProgramIdAndModule(programId, EServiceType.SCHEME));
    }
}