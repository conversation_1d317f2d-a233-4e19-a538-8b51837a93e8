package com.oneid.loyalty.accounting.ops.feign.model.req;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter 
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ConfirmTransactionFeignReq {
    CustomerFeignReq customerIdentifier;
    
    private String txnRefNo;

    private String businessId;

    private String programId;

    private String transactionStatus;

}
