package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.model.res.MemberProductAccountRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberProductAccountService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProductAccountType;
import com.oneid.oneloyalty.common.entity.Card;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.MemberProductAccount;
import com.oneid.oneloyalty.common.entity.ProgramProduct;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.service.CardService;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.MemberProductAccountService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.util.DateTimes;

@Service
public class OpsMemberProductAccountServiceImpl implements OpsMemberProductAccountService {

    @Autowired
    MemberProductAccountService memberProductAccountService;

    @Autowired
    ProgramProductService programProductService;

    @Autowired
    private CardService cardService;

    @Autowired
    private StoreService storeService;
    
    @Autowired
    CardTypeService cardTypeService;
    
    @Value(value = "${app.exclude-vd-card-type-codes}")
    private List<String> vdCardTypeCodes;

    @Override
    public List<MemberProductAccountRes> getListMemberProductAccount(Long memberId) {
    	List<MemberProductAccount> memberProductAccounts = memberProductAccountService.findByMemberIdAndProductAccountTypes(memberId, Arrays.asList(EProductAccountType.CARD_NO));
    	
    	List<Card> availableCards = cardService.findByMemberId(memberId);
    	
    	Map<String, Card> cardMap = availableCards.stream().collect(Collectors.toMap(Card::getCardNo, item->item));
    	
    	 Map<Integer, CardType> cardTypeMap = cardTypeService.findByIdIn(availableCards.stream().map(Card::getCardTypeId).collect(Collectors.toList()))
                 .stream()
                 .collect(Collectors.toMap(CardType::getId, each -> each));
    	
    	List<MemberProductAccount> activeProductAccounts = new ArrayList<MemberProductAccount>();
    	List<MemberProductAccount> vdcardProductAccounts = new ArrayList<MemberProductAccount>();
    	List<MemberProductAccount> inactiveProductAccounts = new ArrayList<MemberProductAccount>();
    	
    	memberProductAccounts
    	.stream()
    	.forEach(each -> {
    	    if(each.getStatus().equals(ECommonStatus.INACTIVE))
    	        inactiveProductAccounts.add(each);
    	    else {
    	        Card card = cardMap.get(each.getAccountCode());
    	        String cardType = cardTypeMap.get(card.getCardTypeId()).getCardType();
    	        
    	        if(vdCardTypeCodes.contains(cardType))
    	            vdcardProductAccounts.add(each);
    	        else 
    	            activeProductAccounts.add(each);
    	    }
    	});
    	
    	sortMemberProductAccounts(activeProductAccounts);
    	sortMemberProductAccounts(inactiveProductAccounts);
    	
    	return Stream.concat(Stream.concat(activeProductAccounts.stream(), vdcardProductAccounts.stream()), inactiveProductAccounts.stream())
    	        .map(ele -> buildMemberProductAccountResp(ele, cardTypeMap, cardMap)).collect(Collectors.toList());
    }
    
    private void sortMemberProductAccounts(List<MemberProductAccount> memberProductAccounts) {
        memberProductAccounts.sort((first, second) -> {
            if(first.getCreatedAt().equals(second.getCreatedAt()))
                return 0;
            else if(first.getCreatedAt().before(second.getCreatedAt()))
                return 1;
            else 
                return -1;
        });
    }

    private MemberProductAccountRes buildMemberProductAccountResp(MemberProductAccount memberProductAccount,
                                                                  Map<Integer, CardType> mapCardType,
                                                                  Map<String, Card> mapCard) {
        MemberProductAccountRes resp = new MemberProductAccountRes();
        resp.setProductAccountCode(memberProductAccount.getAccountCode());

        if (memberProductAccount.getCreatedAt() != null) {
            resp.setCreatedAt(DateTimes.toEpochSecond(memberProductAccount.getCreatedAt()));
        }

        resp.setStatus(memberProductAccount.getStatus());

        final ProgramProduct programProduct = programProductService.findActive(memberProductAccount.getProgramProductId());

        Card card = mapCard.get(memberProductAccount.getAccountCode());

        resp.setProductCode(programProduct.getCode());

        if(card != null) {
            resp.setCardType(
                    mapCardType.getOrDefault(card.getCardTypeId(), null) != null ?
                            mapCardType.getOrDefault(card.getCardTypeId(), null).getName() : null
            );
        	resp.setCodeBlock(card.getCodeBlock());
        	resp.setCardStatus(card.getCardStatus().getValue());
        	resp.setExpiredAt(card.getExpiryDate()!=null?card.getExpiryDate().toInstant().getEpochSecond():null);
        	Store store = storeService.find(card.getStoreId());
        	resp.setStoreCode(store.getCode());
        	resp.setRemark(card.getRemark());
        }
        return resp;
    }
}