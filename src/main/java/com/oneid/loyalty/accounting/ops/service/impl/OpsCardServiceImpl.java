package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MemberServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.AddCardMemberFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.UpdateCardFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.CardMemberFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberProfileDetailFeignRes;
import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateCardReq;
import com.oneid.loyalty.accounting.ops.model.res.CardRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardService;
import com.oneid.loyalty.accounting.ops.service.OpsMemberService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Card;
import com.oneid.oneloyalty.common.entity.CardTMP;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CardTypeRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardPolicyService;
import com.oneid.oneloyalty.common.service.CardService;
import com.oneid.oneloyalty.common.service.CardTMPService;
import com.oneid.oneloyalty.common.service.CardTransferringHistoryService;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsCardServiceImpl implements OpsCardService {

    @Autowired
    CardService cardService;

    @Autowired
    BusinessService businessService;

    @Autowired
    ProgramService programService;

    @Autowired
    MemberService memberService;

    @Autowired
    ChainService chainService;

    @Autowired
    StoreService storeService;

    @Autowired
    CardTypeService cardTypeService;

    @Autowired
    CardTMPService cardTMPService;

    @Autowired
    CardPolicyService cardPolicyService;

    @Autowired
    CardTransferringHistoryService cardTransferringHistoryService;

    @Autowired
    OpsMemberService opsMemberService;

    @Autowired
    CardTypeRepository cardTypeRepository;

    @Autowired
    CorporationService corporationService;
    
    @Autowired
    private MemberServiceFeignClient memberServiceFeignClient;
    
    @Autowired
    private CardServiceFeignClient cardServiceFeignClient;

    @Override
    public Page<CardRes> filter(Integer cardType, String cardNo, String businessId, String programId, String storeId, String memberCode,
                                Integer offSet, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offSet, limit);

        Business business = businessService.findByCode(businessId);
        Optional<Program> program = programService.find(business.getId(), programId);
        if (!program.isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found",
                    LogData.createLogData().append("program_code", programId));
        }
        Store store = storeService.findByBusinessIdAndCode(business.getId(), storeId);
        Optional<Corporation> corporation = corporationService.find(store.getCorporationId());
        if (!corporation.isPresent()) {
            throw new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "corporation not found",
                    LogData.createLogData().append("corporation", store.getCorporationId()));
        }

        Long memberId = null;
        if (memberCode != null) {
            Optional<Member> member = memberService.find(memberCode, program.get().getId());
            memberId = member.get().getId();
        }
        Page<Object[]> cardMember = cardService.findCardAndMember(business.getId(), program.get().getId(), store.getId(), cardType, cardNo, memberId, pageRequest);

        List<Card> cards = cardMember.getContent().stream().map(ele -> (Card) ele[0]).collect(Collectors.toList());
        List<Member> members = cardMember.getContent().stream().map(ele -> (Member) ele[1])
                .collect(Collectors.toList());
        Map<Long, String> mapMember = members.stream()
                .collect(Collectors
                        .toMap(Member::getId, member -> member.getMemberCode(),
                                (first, second) -> first)
                );
        Set<Integer> cardTypeIds = cards.stream().map(ele -> ele.getCardTypeId()).collect(Collectors.toSet());
        List<CardType> cardTypes = cardTypeRepository.findByIdIn(cardTypeIds);
        Map<Integer, CardType> cardTypeByIds = cardTypes.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));

        return new PageImpl<CardRes>(cards.stream()
                .map(it -> CardRes.valueOf(it, business, program.get(), corporation.get(), mapMember, cardTypeByIds))
                .collect(Collectors.toList()), pageRequest, cardMember.getTotalElements());
    }

    @Override
    public Object addCard(OPSAuthenticatedPrincipal principal, AddCardReq req) {
        MemberProfileDetailFeignRes memberRes = memberServiceFeignClient.getProfileByCode(
                req.getBusinessCode(), req.getProgramCode(), req.getMemberCode(), null)
                .getData();
        
        if (memberRes == null) {
            throw new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "member not found",
                    LogData.createLogData().append("member_code", req.getMemberCode()));
        }
        
        req.setMemberId(String.valueOf(memberRes.getId()));
        Business business = businessService.findByCode(req.getBusinessCode());
        
        Store store = storeService.findByBusinessIdAndCodeWithoutStatus(business.getId(), req.getStoreCode());
        
        req.setStoreId(store.getId());
        
        Program program = programService.findActive(business.getId(), req.getProgramCode());
        CardTMP cardTMP = cardTMPService.findPending(req.getCardNo(), business.getId(), program.getId(), store.getId());
        
        if (!req.getCardTypeId().equals(cardTMP.getCardTypeId())) {
            throw new OpsBusinessException(OpsErrorCode.CARD_TYPE_NOT_MATCHED, "card type not matched",
                    LogData.createLogData().append("card_type_id", req.getCardTypeId()));
        }
        
        return cardServiceFeignClient.addMemberCard(AddCardMemberFeignReq.builder()
                .businessCode(req.getBusinessCode())
                .cardNo(req.getCardNo())
                .cardTypeId(req.getCardTypeId())
                .memberCode(req.getMemberCode())
                .programCode(req.getProgramCode())
                .status(req.getCardStatus())
                .storeCode(store.getCode())
                .userId(principal.getUserName())
                .build());
    }

    @Override
    public CardRes getDetail(Integer id, String businessId, String programId) {
        CardMemberFeignRes cardRes = cardServiceFeignClient.getMemberCardDetail(id)
        .getData();
        
        if (cardRes == null) {
            throw new BusinessException(ErrorCode.CARD_NOT_FOUND, "Card not found",
                    LogData.createLogData().append("card_id", id));
        }
        
        Optional<Business> business = businessService.find(cardRes.getBusinessId());
        
        if (!business.isPresent()) {
            throw new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found",
                    LogData.createLogData().append("business_id", cardRes.getBusinessId()));
        }
        
        Optional<Program> program = programService.find(cardRes.getProgramId());
        
        if (!program.isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found",
                    LogData.createLogData().append("program_id", cardRes.getProgramId()));
        }
        
        MemberProfileDetailFeignRes memberRes = memberServiceFeignClient.getProfileById(cardRes.getMemberId()).getData();
        
        if (memberRes == null) {
            throw new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "member not found",
                    LogData.createLogData().append("member_id", cardRes.getMemberId()));
        }
        
        Optional<CardType> cardType = cardTypeService.find(cardRes.getCardType());
        
        if (!cardType.isPresent()) {
            throw new BusinessException(ErrorCode.CARD_TYPE_NOT_FOUND, "Card Type not found",
                    LogData.createLogData().append("card_type_id", cardRes.getCardType()));
        }

        Store store = storeService.find(cardRes.getStoreId());
        
        if (store == null) {
            throw new BusinessException(ErrorCode.STORE_NOT_FOUND, "Store not found",
                    LogData.createLogData().append("store_id", cardRes.getStoreId()));
        }
        
        Optional<Chain> chain = chainService.find(store.getChainId());
        
        if (!chain.isPresent()) {
            throw new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "Chain not found",
                    LogData.createLogData().append("chain_id", cardRes.getChainId()));
        }
        
        Optional<Corporation> corporation = corporationService.find(chain.get().getCorporationId());
        
        if (!corporation.isPresent()) {
            throw new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found",
                    LogData.createLogData().append("corporation_id", chain.get().getCorporationId()));
        }
        
        return CardRes.valueOf(cardRes, memberRes, cardType.get(), program.get(), business.get(), chain.get(), corporation.get(), store);
    }

    @Override
    public Object update(OPSAuthenticatedPrincipal principal, Integer id, String cardNo, UpdateCardReq req) {
        req.setId(id);
        req.setCardNo(cardNo);
        
        Business business = businessService.findByCode(req.getBusinessCode());
        Store store = storeService.findByBusinessIdAndCodeWithoutStatus(business.getId(), req.getStoreCode());
        
        req.setStoreId(store.getId());
        req.setStatus(req.getCardStatus());
        
        return cardServiceFeignClient.updateMemberCard(UpdateCardFeignReq.builder()
                .businessCode(business.getCode())
                .cardNo(req.getCardNo())
                .cardStatus(req.getCardStatus())
                .codeBlock(req.getCodeBlock())
                .id(id)
                .programCode(req.getProgramCode())
                .remark(req.getRemark())
                .updatedBy(principal.getUserName())
                .build());
    }
}