package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogCardMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogMemberReq;
import com.oneid.loyalty.accounting.ops.model.res.DataAuditTrailDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.DataAuditTrailRes;
import com.oneid.oneloyalty.common.constant.EEntityAuditTrail;

import java.util.List;

public interface CommonCheckLogService {
    List<DataAuditTrailRes> filterCheckLogMember(Long memberId, FilterCheckLogMemberReq req);

    List<DataAuditTrailRes> filterCheckLogCardMember(Long memberId, FilterCheckLogCardMemberReq req);

    DataAuditTrailDetailRes getDetailCheckLogMemberById(Long memberId, Long checkLogId);

    DataAuditTrailDetailRes getDetailCheckLogCardMemberById(Long memberId, String cardNo, Long checkLogId);
}