package com.oneid.loyalty.accounting.ops.controller.v1;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.controller.BaseController;

@Controller
@RequestMapping("v1/fraud")
public class FraudController extends BaseController {
    @Autowired
    private OpsTransactionService opsTransactionService;
    
    @RequestMapping("user-profile/{user_profile_id}/business/{business_id}/program/{program_id}/transactions")
    public ResponseEntity<?> getTransactions(
            @PathVariable("business_id") Integer businessId,
            @PathVariable("program_id") Integer programId,
            @PathVariable("user_profile_id") String userProfileId,
            @RequestParam(value = "transaction_type", required = false) String transactionType,
            @Valid @RequestParam(value = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", defaultValue = "10") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "transaction_time_from", required = false) Integer transactionTimeFrom,
            @RequestParam(value = "transaction_time_to", required = false) Integer transactionTimeTo){
        Page<TransactionRes> page = opsTransactionService.search(
                businessId, 
                programId, 
                userProfileId, 
                transactionType, 
                transactionTimeFrom, 
                transactionTimeTo, 
                new OffsetBasedPageRequest(offset, limit, null));
        
        return success(page, offset, limit);
    }
}