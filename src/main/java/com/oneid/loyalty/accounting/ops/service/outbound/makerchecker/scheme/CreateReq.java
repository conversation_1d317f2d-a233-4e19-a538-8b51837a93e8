package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.scheme;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveReq;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateReq extends AbsGetApproveReq {
    @JsonProperty("payload")
    private CreateSchemeReq payload;

    @Override
    public HttpEntity build(HttpHeaders headers) {
        return new HttpEntity<>(this, headers);
    }
}