package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class TransactionBatchRequestDetailRes {

    private Long batchNo;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private EBatchRequestType batchRequestType;

    private ETransactionBatchType transactionBatchType;

    private String batchName;

    private String campaignCode;

    private String referenceCode;

    private String description;

    private EBoolean isReplaceDes;

    private EGenerationInvoiceNoMethod genInvoiceNoMethod;

    private ECharacterSet genInvoiceNoCharacterSet;

    private String genInvoiceNoPattern;

    private EGenerationTransactionTimeMethod genTransactionTimeMethod;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date genTransactionTimeValue;

    private String rejectReason;

    private String smsTemplate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date generationTime;

    private String createdBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private EApprovalStatus approvalStatus;

    private EBatchRequestProcessStatus processStatus;

    private int totalTransaction;

    private int totalMember;

    private BigDecimal totalGMV;

    private BigDecimal totalGrossAmount;

    private BigDecimal totalAwardPoint;

    private BigDecimal totalRedeemPoint;
}
