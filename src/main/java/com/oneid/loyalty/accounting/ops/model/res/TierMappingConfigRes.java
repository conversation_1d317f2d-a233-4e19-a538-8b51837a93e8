package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.experimental.SuperBuilder;


@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierMappingConfigRes {
    private Integer id;
    private String attributeValue;
    private ECommonStatus status;
    private TierMatchingConfigRequestDetailRes baseTier;
    private TierMatchingConfigRequestDetailRes matchedTier;
}