package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CpmMemberInquiryReq implements Serializable {
    private static final long serialVersionUID = 5439658619335266778L;

    @NotBlank(message = "business_code is missing")
    private String businessCode;

    @NotBlank(message = "program_code is missing")
    private String programCode;

    @Valid
    private CustomerIdentify customerIdentifier;

    private String currencyCode;

    private String userId;
}