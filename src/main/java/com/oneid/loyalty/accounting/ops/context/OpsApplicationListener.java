package com.oneid.loyalty.accounting.ops.context;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.stereotype.Component;

import com.oneid.loyalty.accounting.ops.model.elasticsearch.SchemeES;
import com.oneid.loyalty.accounting.ops.setting.ElasticsearchSetting;

@Component
public class OpsApplicationListener implements ApplicationListener<ApplicationReadyEvent> {
    @Autowired
    private ElasticsearchRestTemplate esRestTemplate;
    
    @Autowired
    private ElasticsearchSetting elasticsearchSetting;
    
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        if(elasticsearchSetting.getPingable()) {
            Document mapping = esRestTemplate.indexOps(SchemeES.class).createMapping();
            
            esRestTemplate.indexOps(SchemeES.class).putMapping(mapping); 
        }
    }
}