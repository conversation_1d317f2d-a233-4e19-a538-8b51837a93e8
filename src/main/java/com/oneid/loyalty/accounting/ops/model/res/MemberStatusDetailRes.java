package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusDetailRes {
    private Integer id;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private String code;

    private String viName;

    private String enName;

    private String viDescription;

    private String enDescription;

    private EBoolean revertLatestStatus;

    private EBoolean expiredAllPoints;

    private EBoolean defaultStatus;

    private ECommonStatus status;

    private String createdBy;

    private String updatedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private List<Function> functions;

    @Getter
    @Setter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class Function {
        public Function() {
        }

        private String functionCode;

        private String functionName;

        private String groupName;
    }
}