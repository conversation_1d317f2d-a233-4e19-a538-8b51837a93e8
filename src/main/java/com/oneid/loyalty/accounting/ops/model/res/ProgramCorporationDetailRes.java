package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgramCorporationDetailRes implements Serializable {

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("corporations")
    private Set<CorporationInfo> corporations;
}
