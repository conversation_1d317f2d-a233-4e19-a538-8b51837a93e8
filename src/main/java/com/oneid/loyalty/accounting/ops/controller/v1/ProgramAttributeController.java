package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramAttributeService;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/program-attribute")
public class ProgramAttributeController extends BaseController {
    @Autowired
    OpsProgramAttributeService opsProgramAttributeService;

    @GetMapping("")
    public ResponseEntity<?> searchProgramAttribute(@RequestParam(name = "program_id", required = false) Integer programId,
                                                    @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                                    @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<ProgramAttributeRes> page = opsProgramAttributeService.search(programId, offset, limit);
        return success(page, offset, limit);
    }
}
