package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramGetAllReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramListRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/programs")
@Validated
public class ProgramController extends BaseController {
    @Autowired
    OpsProgramService loyaltyProgramService;

    @PostMapping
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> create(@RequestBody @Valid CreateProgramReq programReq) {
        return success(loyaltyProgramService.create(programReq));
    }

    @PutMapping("{program_id}")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> update(
            @PathVariable("program_id") Integer programId,
            @RequestBody @Valid UpdateProgramReq programReq) {
        programReq.setProgramId(programId);
        return success(loyaltyProgramService.update(programReq));
    }

    @GetMapping
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> search(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "program_code", required = false) String programCode,
            @RequestParam(value = "program_name", required = false) String programName,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "sort_direction", required = false) Sort.Direction sortDirection,
            @Valid @RequestParam(value = "sort_by", required = false) String sortBy
    ) {
        SearchProgramReq searchProgramReq = new SearchProgramReq();
        searchProgramReq.setBusinessId(businessId);
        searchProgramReq.setProgramCode(programCode);
        searchProgramReq.setStatus(status);
        searchProgramReq.setProgramId(programId);
        searchProgramReq.setProgramName(programName);

        if (sortBy != null) {
            searchProgramReq.setSortBy(sortBy);
        }

        if (sortDirection != null)
            searchProgramReq.setSortDirection(sortDirection);

        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, searchProgramReq.getSort());
        Page<ProgramRes> programResPage = loyaltyProgramService.search(searchProgramReq, pageRequest);
        return success(programResPage.getContent(), offset, limit, (int) programResPage.getTotalElements());
    }

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableProgram(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "10") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_code", required = false) String programCode,
            @RequestParam(value = "status", required = false) ECommonStatus status
    ) {
        SearchProgramReq searchProgramReq = new SearchProgramReq();
        searchProgramReq.setBusinessId(businessId);
        searchProgramReq.setProgramCode(programCode);
        searchProgramReq.setStatus(status);
        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, sort);
        Page<ProgramListRes> programResPage = loyaltyProgramService.getAvailableProgramRequests(searchProgramReq, pageRequest);
        return success(programResPage.getContent(), offset, limit, (int) programResPage.getTotalElements());
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.PROGRAM, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewProgram(
            @RequestParam(value = "approval_status", defaultValue = "P") EApprovalStatus approvalStatus,
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "10") @Min(1) @Max(200) Integer limit){
        Page<ProgramListRes> page = loyaltyProgramService.getInReviewProgramRequests(approvalStatus, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("requests/in-review/{id}")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewProgramRequestById(@PathVariable("id") Integer reviewId) {
        return success(loyaltyProgramService.getInReviewProgramDetailRequests(reviewId));
    }

    @GetMapping("{program_id}")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getOne(@PathVariable("program_id") Integer programId) {
        return success(loyaltyProgramService.getOne(programId));
    }

    @GetMapping("/all")
    public ResponseEntity<?> getAll(@RequestParam(value = "status", required = false) ECommonStatus status,
                                    @RequestParam(value = "business_id", required = false) Integer businessId,
                                    @RequestParam(value = "business_code", required = false) String businessCode) {
        ProgramGetAllReq programGetAllReq = new ProgramGetAllReq();
        programGetAllReq.setStatus(status);
        programGetAllReq.setBusinessId(businessId);
        programGetAllReq.setBusinessCode(businessCode);
        
        return success(loyaltyProgramService.filter(programGetAllReq));
    }

    @GetMapping("/all-by-business/{business_id}")
    public ResponseEntity<?> getAllByBusinessId(@PathVariable("business_id") Integer businessId) {
        ProgramGetAllReq programGetAllReq = new ProgramGetAllReq();
        programGetAllReq.setBusinessId(businessId);
        programGetAllReq.setStatus(ECommonStatus.ACTIVE);
        return success(loyaltyProgramService.filter(programGetAllReq));
    }

    @GetMapping("detail/{program_id}")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> fullDetail(@PathVariable("program_id") Integer programId) {
        return success(loyaltyProgramService.detail(programId));
    }

    @GetMapping("/requests/available/{id}/view")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getProgramAvailableDetail(@PathVariable("id") Integer programId) {
        return success(loyaltyProgramService.getOne(programId));
    }

    @GetMapping("/requests/available/{id}/edit")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEditProgramCorporationRequestSetting(@PathVariable("id") Integer requestId) {
        return success(loyaltyProgramService.getEditAttributeRequestSetting(requestId));
    }

    @PostMapping("/requests")
    @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createMakerChecker(@Valid @RequestBody CreateProgramReq programReq) {
        return success(loyaltyProgramService.createMaker(programReq));
    }

    @PutMapping("/requests")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> requestEditingAttributeRequest(@Valid @RequestBody UpdateProgramReq req) {
        return success(loyaltyProgramService.requestEditingAttributeRequest(req));
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> checker(@Valid @RequestBody ApprovalReq req) {
        loyaltyProgramService.approve(req);
        return success(null);
    }

    @GetMapping("/{program_id}/idtype")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW})
    })
    public ResponseEntity<?> getListIdTypeInProgram(@PathVariable("program_id") Integer programId) {
        return success(loyaltyProgramService.getListProgramProductByProgramId(programId));
    }
}