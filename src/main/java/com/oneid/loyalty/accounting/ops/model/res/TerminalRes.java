package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class TerminalRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("corporation_name")
    private String corporationName;

    @JsonProperty("chain_id")
    private Integer chainId;

    @JsonProperty("chain_name")
    private String chainName;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("store_name")
    private String storeName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("en_name")
    private String enName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    public static TerminalRes of(
            Pos terminal,
            Business business,
            Corporation corporation,
            Chain chain,
            Store store
    ) {
        TerminalRes terminalRes = new TerminalRes();
        terminalRes.setId(terminal.getId());
        terminalRes.setCode(terminal.getCode());
        terminalRes.setBusinessId(terminal.getBusinessId());
        terminalRes.setBusinessName(business != null ? business.getName() : null);
        terminalRes.setCorporationId(terminal.getCorporationId());
        terminalRes.setCorporationName(corporation != null ? corporation.getName() : null);
        terminalRes.setChainId(terminal.getChainId());
        terminalRes.setChainName(chain != null ? chain.getName() : null);
        terminalRes.setStoreId(terminal.getStoreId());
        terminalRes.setStoreName(store != null ? store.getName() : null);
        terminalRes.setName(terminal.getName());
        terminalRes.setEnName(terminal.getEnName());
        terminalRes.setDescription(terminal.getDescription());
        terminalRes.setEnDescription(terminal.getEnDescription());
        terminalRes.setStatus(terminal.getStatus() != null ? terminal.getStatus().getValue() : null);
        terminalRes.setCreatedBy(terminal.getCreatedBy());
        terminalRes.setUpdatedBy(terminal.getUpdatedBy());
        terminalRes.setApprovedBy(terminal.getApprovedBy());
        terminalRes.setCreatedAt(terminal.getCreatedAt() != null ? terminal.getCreatedAt().toInstant().getEpochSecond() : null);
        terminalRes.setUpdatedAt(terminal.getUpdatedAt() != null ? terminal.getUpdatedAt().toInstant().getEpochSecond() : null);
        terminalRes.setApprovedAt(terminal.getApprovedAt() != null ? terminal.getApprovedAt().toInstant().getEpochSecond() : null);
        terminalRes.setCreatedYmd(terminal.getCreatedYmd());
        terminalRes.setServiceStartDate(terminal.getServiceStartDate() != null ? terminal.getServiceStartDate().toInstant().getEpochSecond() : null);
        terminalRes.setServiceEndDate(terminal.getServiceEndDate() != null ? terminal.getServiceEndDate().toInstant().getEpochSecond() : null);
        return terminalRes;
    }
}
