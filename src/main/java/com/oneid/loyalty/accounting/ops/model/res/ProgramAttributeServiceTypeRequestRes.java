package com.oneid.loyalty.accounting.ops.model.res;

import java.time.OffsetDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ProgramAttributeServiceTypeRequestRes {
    
    private Integer reviewId;
    
    private Integer id;
    
    private EServiceType serviceType;
    
    private Integer businessId;
    
    private String businessName;
    
    private Integer programId;
    
    private String programName;
    
    private EApprovalStatus approvalStatus;
    
    private String changedReason;
    
    private String rejectedReason;
    
    private OffsetDateTime createdAt;
    
    private String createdBy;
    
    private OffsetDateTime approvedAt;
    
    private String approvedBy;
    
    private List<ProgramAttributeServiceTypeMappingRequestRes> systemAttributeMappings;
    
    private List<ProgramAttributeServiceTypeMappingRequestRes> programAttributeMappings;
    
    private List<ProgramAttributeServiceTypeMappingRequestRes> memberAttributeMappings;
    
}
