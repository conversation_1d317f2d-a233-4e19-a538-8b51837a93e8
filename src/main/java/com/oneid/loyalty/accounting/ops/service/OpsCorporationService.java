package com.oneid.loyalty.accounting.ops.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CorporationUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.CorporationEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.CorporationInfo;
import com.oneid.loyalty.accounting.ops.model.res.CorporationRes;
import com.oneid.oneloyalty.common.entity.Corporation;

public interface OpsCorporationService {

    List<CorporationInfo> searchListCorporation(SearchCorporationReq req);

    List<CorporationInfo> listCorporationOfBusiness(Integer businessId);

    Page<CorporationRes> filter(Integer businessId, String corporationName, String corporationCode, String status, Integer offset, Integer limit);

    CorporationRes get(Integer id);

    CorporationRes add(CorporationCreateReq corporationCreateReq);

    List<CorporationEnumAll> getEnumAll();

    CorporationRes check(String code);

    void update(Integer id, CorporationUpdateReq corporationUpdateReq);

    Map<Integer, Corporation> getMapById(Collection<Integer> ids);

}