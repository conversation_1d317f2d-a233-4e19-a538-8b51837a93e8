package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AttributeMemberRes {
    private String attributeName;
    private String attributeValue;
    private Long lastUpdatedAt;
}
