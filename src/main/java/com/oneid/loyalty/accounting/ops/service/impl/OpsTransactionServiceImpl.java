package com.oneid.loyalty.accounting.ops.service.impl;

import com.google.gson.Gson;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.config.CheckLogSourceConfigPram;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MasterWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.SapFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ConfirmTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CustomerFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerTransactionRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ReverseTransactionFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ReverseTransactionInfoFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderCreateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SAPSaleOrderUpdateFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.TransactionInfoFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderCreateFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderGetFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SAPSaleOrderUpdateFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.TransactionPointWrapper;
import com.oneid.loyalty.accounting.ops.model.dto.ActionTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.AdjustTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.EarnBurnSaleTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.dto.RevertFullTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.RevertPartialTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionBatchRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionMemberRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchAvailableTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionConfirmReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.PointAwardDetail;
import com.oneid.loyalty.accounting.ops.model.res.PointRedeemDetail;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCheckLogRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionExportEntry;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionSearchRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionStatisticRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.Assert;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EAdjustmentType;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import com.oneid.oneloyalty.common.constant.ESyncWithElastic;
import com.oneid.oneloyalty.common.constant.ETransactionAttributeDataTypeDisplay;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.Function;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.SapSaleOrderCall;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionBatchRequest;
import com.oneid.oneloyalty.common.entity.TransactionHistory;
import com.oneid.oneloyalty.common.entity.TransactionHistoryAttribute;
import com.oneid.oneloyalty.common.entity.TransactionRequest;
import com.oneid.oneloyalty.common.entity.UserProfile;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.FunctionRepository;
import com.oneid.oneloyalty.common.repository.PoolRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.ProgramCorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ReasonCodeRepository;
import com.oneid.oneloyalty.common.repository.SapSaleOrderCallRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.repository.TransactionBatchRequestRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryAttributeRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryRepository;
import com.oneid.oneloyalty.common.repository.UserProfileRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramCorporationService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.ReasonCodeService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.TransactionAuditTrailService;
import com.oneid.oneloyalty.common.service.TransactionBatchRequestService;
import com.oneid.oneloyalty.common.service.TransactionHistoryService;
import com.oneid.oneloyalty.common.service.TransactionRequestService;
import com.oneid.oneloyalty.common.util.DateUtil;
import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.poiji.bind.Poiji;
import com.poiji.exception.PoijiExcelType;
import joptsimple.internal.Strings;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class OpsTransactionServiceImpl implements OpsTransactionService {
    @Value("${app.tmp.prefix.transaction.adjustment}")
    private String adjustedTransactionInvoiceNoPrefix;

    @Value("${app.tmp.prefix.transaction.reverse}")
    private String reverseTransactionInvoiceNoPrefix;

    @Value("${ops.params.default-transaction-channel}")
    private String defaultTransactionChannel;

    @Value("${ops.params.default-transaction-service-code}")
    private String defaultTransactionServiceCode;
    @Value("${app.batch-adjust-transaction.max-record:10000}")
    private int batchAdjMaxRecord;

    @Value("${app.elasticsearch.index.transaction-history:transaction_history_*}")
    private String transactionHistoryIndexPattern;

    private final char RANDOM_CHAR = '#';

    private final int MIN_PATTERN_LENGTH = 10;

    private final int MAX_PATTERN_LENGTH = 50;

    private final String SAP_API_STATUS_SUCCESS = "01";

    private final String SAP_CALL_TYPE_CREATE = "CREATE";

    private final String SAP_CALL_TYPE_UPDATE = "UPDATE";

    private final String SAP_CALL_STATUS_SUCCESS = "SUCCESS";

    private static final SimpleDateFormat FORMATTER_dd_MM_YYYY = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

    private final SimpleDateFormat FORMATTER_yyyyMMdd = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private CheckLogSourceConfigPram checkLogSourceConfigPram;

    @Autowired
    TransactionHistoryRepository transactionHistoryRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private ReasonCodeRepository reasonCodeRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ChainRepository chainRepository;

    @Autowired
    private ProgramCorporationRepository programCorporationRepository;

    @Autowired
    private PosRepository posRepository;

    @Autowired
    private PoolRepository poolRepository;

    @Autowired
    private CurrencyRepository currencyRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private PosService posService;

    @Autowired
    private PoolService poolService;

    @Autowired
    private OpsBusinessService opsBusinessService;

    @Autowired
    private OpsProgramService opsProgramService;

    @Autowired
    private OpsCorporationService opsCorporationService;

    @Autowired
    private OpsChainService opsChainService;

    @Autowired
    private OpsStoreService opsStoreService;

    @Autowired
    private OpsTerminalService opsTerminalService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private TransactionHistoryService transactionHistoryService;

    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private ReasonCodeService reasonCodeService;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private TransactionBatchRequestRepository transactionBatchRequestRepository;

    @Autowired
    private MasterWorkerFeignClient masterWorkerFeignClient;

    @Autowired
    private SapFeignClient sapFeignClient;

    @Autowired
    private OneloyaltyServiceFeignClient oneloyaltyServiceFeignClient;

    @Autowired
    private TransactionAuditTrailService transactionAuditTrailService;

    @Autowired
    private FunctionRepository functionRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ProgramProductService programProductService;

    @Autowired
    private ProgramCorporationService programCorporationService;

    @Autowired
    private FunctionService functionService;

    @Autowired
    private TransactionBatchRequestService transactionBatchRequestService;

    @Autowired
    private TransactionRequestService transactionRequestService;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private TransactionHistoryAttributeRepository transactionHistoryAttributeRepository;

    @Autowired
    private SapSaleOrderCallRepository sapSaleOrderCallRepository;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public Page<TransactionRes> search(TransactionSearchReq transactionSearchReq, Pageable pageRequest,
                                       Boolean getRefundedInvoice) {
        EOpsCancellationType cancellationType = transactionSearchReq.getCancellationType();

        // find member id
        if (transactionSearchReq.getAccountType() != null && transactionSearchReq.getAccountCode() != null) {
            Member member = memberService.find(new CustomerIdentify(transactionSearchReq.getAccountCode(), transactionSearchReq.getAccountType().getMapping()), transactionSearchReq.getProgramId());
            if (member == null) {
                return Page.empty(pageRequest);
            }
            transactionSearchReq.setMemberId(member.getId());
        }

        Long memberId = transactionSearchReq.getMemberId();

        if (transactionSearchReq.getProgramId() == null &&
                (StringUtils.isNotBlank(transactionSearchReq.getMemberCode()) || transactionSearchReq.getUserId() != null)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Search with user_id/member_code is required program_id", null);
        } else if (StringUtils.isNotBlank(transactionSearchReq.getMemberCode())
                || StringUtils.isNotBlank(transactionSearchReq.getUserId())) {

            Program program = programRepository.findById(transactionSearchReq.getProgramId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null)
            );

            if (transactionSearchReq.getBusinessId() != null &&
                    !program.getBusinessId().equals(transactionSearchReq.getBusinessId())) {
                return Page.empty(pageRequest); // conflict condition search;
            }

            if (StringUtils.isNotBlank(transactionSearchReq.getUserId())) {
                UserProfile userProfile = userProfileRepository.findByMasterUserIdAndBusinessId(
                        transactionSearchReq.getUserId(), program.getBusinessId()
                );
                if (userProfile == null) {
                    throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, "User profile not found", null);
                }
                Long memberIdTmp = memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())
                        .orElseThrow(
                                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", null)
                        ).getId();
                if (memberIdTmp.equals(memberId) || memberId == null) {
                    memberId = memberIdTmp;
                } else { // conflict condition search relative member
                    return Page.empty(pageRequest);
                }
            }
            if (StringUtils.isNotBlank(transactionSearchReq.getMemberCode())) {
                Optional<Member> member = memberService.find(transactionSearchReq.getMemberCode(), transactionSearchReq.getProgramId());
                if (member.isPresent()) {
                    Long memberIdTmp = member.get().getId();
                    if (memberIdTmp.equals(memberId) || memberId == null) {
                        memberId = memberIdTmp;
                    } else {  // conflict condition search relative member
                        return Page.empty(pageRequest);
                    }
                } else {
                    return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);
                }
            }
        }
        ETransactionType eTransactionType = null;
        if (transactionSearchReq.getTransactionType() != null) {
            switch (transactionSearchReq.getTransactionType()) {
                case EARN: {
                    eTransactionType = ETransactionType.AWARD;
                    break;
                }
                case BURN: {
                    eTransactionType = ETransactionType.REDEEM;
                    break;
                }
                case ADJUSTMENT: {
                    eTransactionType = ETransactionType.ADJUSTMENT;
                }
            }
        }

        Page<Object[]> pageResponse = transactionHistoryService.getPointTnxIds(
                transactionSearchReq.getBusinessId(),
                transactionSearchReq.getProgramId(),
                memberId,
                transactionSearchReq.getStoreId(),
                transactionSearchReq.getTerminalId(),
                transactionSearchReq.getTransactionFrom(),
                transactionSearchReq.getTransactionTo(),
                transactionSearchReq.getInvoiceNumber(),
                cancellationType != null ? cancellationType.getValue() : null,
                transactionSearchReq.getCorporationId(),
                transactionSearchReq.getChainId(),
                transactionSearchReq.getTnxRefNo(),
                transactionSearchReq.getStatus(),
                transactionSearchReq.getAttributes(),
                eTransactionType,
                transactionSearchReq.getPoolId(),
                pageRequest
        );

        List<String> pointTnxIds;
        if (pageRequest.getOffset() == 0) {
            pointTnxIds = (List) pageResponse.getContent();
        } else {
            pointTnxIds = pageResponse.getContent().stream().map(i -> (String) i[0]).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(pointTnxIds))
            return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);

        List<TransactionHistory> transactionHistoryListAll = transactionHistoryService.getByTnxPointIds(pointTnxIds);
        Map<String, List<TransactionHistory>> transactionHistoryByPointTnxIds = transactionHistoryListAll.stream().collect(
                Collectors.groupingBy(
                        TransactionHistory::getPointTransactionId,
                        Collectors.toList()
                )
        );

        List<TransactionHistory> transactionHistories = transactionHistoryByPointTnxIds.values()
                .stream()
                .filter(t -> !t.isEmpty())
                .map(t -> t.get(0))
                .sorted(getSortForSearch(Sort.Direction.DESC))
                .collect(Collectors.toList());

        Set<Integer> businessIds = transactionHistories.stream().map(TransactionHistory::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessByIds = opsBusinessService.getMapById(businessIds);

        Set<Integer> corporationIds = transactionHistories.stream().map(TransactionHistory::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationByIds = opsCorporationService.getMapById(corporationIds);

        Set<Integer> chainIds = transactionHistories.stream().map(TransactionHistory::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainByIds = opsChainService.getMapById(chainIds);

        Set<Integer> storeIds = transactionHistories.stream().map(TransactionHistory::getStoreId).collect(Collectors.toSet());
        Map<Integer, Store> storeByIds = opsStoreService.getMapById(storeIds);

        Set<Integer> posIds = transactionHistories.stream().map(TransactionHistory::getPosId).collect(Collectors.toSet());
        Map<Integer, Pos> posByIds = opsTerminalService.getMapById(posIds);

        Set<Integer> programIds = transactionHistories.stream().map(TransactionHistory::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programByIds = opsProgramService.getMapById(programIds);

        Set<Long> memberIds = transactionHistories.stream().map(TransactionHistory::getMemberId).collect(Collectors.toSet());
        Map<Long, Member> members = memberService.findByIdIn(memberIds).stream()
                .collect(Collectors.toMap(Member::getId, member -> member));

        Map<String, List<ShortEntityRes>> poolByPointTnxIds = transactionHistoryByPointTnxIds
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey(),
                        entry -> {
                            List<ShortEntityRes> resultList = new ArrayList<>();
                            Set<Integer> seenIds = new HashSet<>();

                            entry.getValue().forEach(e -> {
                                if (e.getPoolId() != null) {
                                    Pool pool = poolService.find(e.getPoolId()).orElse(null);
                                    if (pool != null) {
                                        Integer id = pool.getId();
                                        if (!seenIds.contains(id)) {
                                            ShortEntityRes shortEntityRes = new ShortEntityRes(id, pool.getName(), pool.getCode());
                                            resultList.add(shortEntityRes);
                                            seenIds.add(id);
                                        }
                                    }
                                }
                            });

                            return resultList;
                        }
                ));

        if (getRefundedInvoice != null) {
            if (memberId == null) {
                // todo verify business logic when cannot search by member id?
                return new PageImpl<TransactionRes>(Collections.emptyList(), pageRequest, 0);
            }
        }

        final Long finalMemberId = memberId;
        Map<String, Optional<TransactionRes.TransactionRefunded>> originalInvoiceToTransactionRefundedMap =
                Boolean.TRUE.equals(getRefundedInvoice) ? transactionHistoryListAll.stream()
                        .collect(Collectors.toMap(
                                TransactionHistory::getInvoiceNo,
                                txnH -> {
                                    List<TransactionHistory> txnHistoryRefundeds = transactionHistoryService
                                            .findTxnRefundedByMemberIdAndOriginalInvoiceNo(
                                                    finalMemberId, txnH.getInvoiceNo()
                                            );

                                    return Optional.ofNullable(getTransactionRefunded(txnH, txnHistoryRefundeds));
                                },
                                (first, second) -> first)) : new HashMap<>();

        return new PageImpl<>(
                transactionHistories.stream()
                        .map(each -> {
                            Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryByPointTnxIds.get(each.getPointTransactionId()));

                            return TransactionRes.valueOf(
                                    points,
                                    each,
                                    getTransactionType(points),
                                    businessByIds.get(each.getBusinessId()),
                                    corporationByIds.get(each.getCorporationId()),
                                    chainByIds.get(each.getChainId()),
                                    storeByIds.get(each.getStoreId()),
                                    posByIds.get(each.getPosId()),
                                    programByIds.get(each.getProgramId()),
                                    members.get(each.getMemberId()),
                                    originalInvoiceToTransactionRefundedMap
                                            .getOrDefault(each.getInvoiceNo(), Optional.ofNullable(null))
                                            .orElse(null),
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    poolByPointTnxIds.get(each.getPointTransactionId())
                            );
                        })
                        .collect(Collectors.toList()),
                pageRequest,
                pageResponse.getTotalElements()
        );
    }

    @Override
    public TransactionRes getTransactionDetails(String pointTnxId) {
        List<TransactionHistory> transactionHistoryListAll = transactionHistoryService.getByTnxPointIds(Collections.singletonList(pointTnxId));
        if (transactionHistoryListAll.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "transaction not found", pointTnxId);

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryListAll);
        EOpsTransactionType transactionType = getTransactionType(points);
        TransactionHistory transactionHistory = transactionHistoryListAll.get(0);

        Business business = businessRepository.findById(transactionHistory.getBusinessId()).orElse(null);
        Corporation corporation = corporationService.find(transactionHistory.getCorporationId()).orElse(null);
        Chain chain = chainService.find(transactionHistory.getChainId()).orElse(null);
        Store store = storeService.find(transactionHistory.getStoreId());
        Pos pos = posService.find(transactionHistory.getPosId()).orElse(null);

        Program program = programRepository.findById(transactionHistory.getProgramId()).orElse(null);


        Member member = memberService.find(transactionHistory.getMemberId()).orElse(null);

        List<Scheme> schemes = schemeService.findByProgramIdAndListIds(transactionHistory.getProgramId(),
                transactionHistoryListAll
                        .stream().map(TransactionHistory::getSchemeId)
                        .collect(Collectors.toList())
        );

        List<Pool> pools = poolRepository.findAllByIdIn(
                transactionHistoryListAll
                        .stream().map(TransactionHistory::getPoolId)
                        .collect(Collectors.toList())
        );

        List<Currency> currencies = currencyService.findByCurrenciesId(
                pools.stream().map(Pool::getCurrencyId).collect(Collectors.toList())
        );
        Map<Integer, ShortEntityRes> mapSchemeIdToRes, mapPoolIdToRes, mapCurrencyIdToRes;

        mapSchemeIdToRes = schemes.stream().collect(Collectors.toMap(
                Scheme::getId,
                scheme -> new ShortEntityRes(scheme.getId(), scheme.getName(), scheme.getCode())
        ));
        mapPoolIdToRes = pools.stream().collect(Collectors.toMap(
                Pool::getId,
                pool -> new ShortEntityRes(pool.getId(), pool.getName(), pool.getCode())
        ));
        mapCurrencyIdToRes = currencies.stream().collect(Collectors.toMap(
                Currency::getId,
                currency -> new ShortEntityRes(currency.getId(), currency.getName(), currency.getCode())
        ));

        Map<Integer, Integer> poolIdToCurrencyId = pools.stream()
                .collect(Collectors.toMap(Pool::getId, Pool::getCurrencyId));


        List<PointAwardDetail> pointAwardDetails = transactionHistoryListAll.stream()
                .filter(txnH -> (txnH.getType() == ETransactionType.AWARD || txnH.getType() == ETransactionType.ADJUSTMENT) && txnH.getAwardPoint().compareTo(BigDecimal.ZERO) > 0
                )
                .map(txnH -> new PointAwardDetail(
                        mapPoolIdToRes.get(txnH.getPoolId()),
                        txnH.getAwardPoint(),
                        mapCurrencyIdToRes.get(poolIdToCurrencyId.get(txnH.getPoolId())),
                        mapSchemeIdToRes.get(txnH.getSchemeId()),
                        txnH.getAwardRetentionTime() != null
                                ? txnH.getAwardRetentionTime().toInstant().getEpochSecond()
                                : null,
                        txnH.getBalanceBefore(),
                        txnH.getBalanceAfter()
                )).collect(Collectors.toList());

        List<PointRedeemDetail> pointRedeemDetails = transactionHistoryListAll.stream()
                .filter(txnH -> (txnH.getType() == ETransactionType.REDEEM || txnH.getType() == ETransactionType.ADJUSTMENT) && txnH.getRedeemPoint().compareTo(BigDecimal.ZERO) > 0
                )
                .map(txnH -> new PointRedeemDetail(
                        mapPoolIdToRes.get(txnH.getPoolId()),
                        mapCurrencyIdToRes.get(poolIdToCurrencyId.get(txnH.getPoolId())),
                        mapSchemeIdToRes.get(txnH.getSchemeId()),
                        txnH.getRedeemPoint(),
                        txnH.getBalanceBefore(),
                        txnH.getBalanceAfter()
                )).collect(Collectors.toList());

        Long finalMemberId = transactionHistory.getMemberId();
        String invoiceNo = transactionHistory.getInvoiceNo();
        List<TransactionHistory> txnHistoryRefundeds = transactionHistoryService
                .findTxnRefundedByMemberIdAndOriginalInvoiceNo(
                        finalMemberId, invoiceNo
                );

        TransactionRes.TransactionRefunded refunded = getTransactionRefunded(transactionHistory, txnHistoryRefundeds);

        ReasonCode reasonCode = null;

        if (transactionHistory.getReasonCode() != null) {
            reasonCode = reasonCodeRepository.findByBusinessIdAndProgramIdAndCode(business.getId(), program.getId(), transactionHistory.getReasonCode());
        }

        Double sellRate = null;
        
        /* Currency baseCurrency = getBaseCurrencyFromTransaction(transactionHistory, program, transactionType);
        if (baseCurrency != null && transactionHistory.getPoolId() != null) {
            sellRate = findSellRate(baseCurrency.getCode(), transactionHistory.getPoolId(), transactionHistory.getBusinessId() );
        } */

        Pool pool = null;
        if (transactionHistory.getPoolId() != null) {
            pool = poolRepository.findById(transactionHistory.getPoolId()).orElse(null);
        }

        UserProfile userProfile = member.getUserProfileId() != null ? userProfileRepository.findById(member.getUserProfileId())
                .orElseThrow(() -> new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, "User profile not found", null)) : null;
        return TransactionRes.valueOf(
                points,
                transactionHistory,
                transactionType,
                business,
                corporation,
                chain,
                store,
                pos,
                program,
                member,
                userProfile,
                refunded,
                pointAwardDetails,
                pointRedeemDetails,
                reasonCode,
                pool,
                sellRate
        );
    }

    @Override
    public TransactionBatchRequestDetailRes getTransactionBatchDetails(Long batchId) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(batchId);
        Business business = businessService.find(batchRequest.getBusinessId()).orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        Program program = programService.find(batchRequest.getProgramId()).orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .rejectReason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .generationTime(batchRequest.getUpdatedAt())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    @Override
    public Page<TransactionSearchRes> searchAvailable(SearchAvailableTransactionReq req, Pageable page) {
        if(req.getCreatedEnd() != null) {
            Date newCreatedEnd = new Date(req.getCreatedEnd().getTime() + OPSConstant.TIMES_OF_DAY);
            req.setCreatedEnd(newCreatedEnd);
        }

        Page<Object[]> result = transactionBatchRequestService.filter(page,
                EApprovalStatus.APPROVED, null, req.getCreatedStart(), req.getCreatedEnd(), null, null, null,
                req.getBusinessId(), req.getProgramId(), req.getBatchNo(), req.getBatchName(),
                req.getBatchRequestType(), req.getTransactionBatchType(), req.getEnableSMS(), req.getFailedRecords(), req.getStatus(), null, false);
        List<TransactionSearchRes> transactionBatchRequestRes = result.stream().map(
                batch -> {
                    TransactionBatchRequest transactionBatchRequest = (TransactionBatchRequest) batch[0];
                    Business business = (Business) batch[1];
                    Program program = (Program) batch[2];

                    EBoolean enableSMS = StringUtils.EMPTY.equals(transactionBatchRequest.getSmsTemplate()) ||
                            transactionBatchRequest.getSmsTemplate() == null ?
                            EBoolean.NO : EBoolean.YES;
                    Date generateDate = !EBatchRequestProcessStatus.COMPLETED.equals(transactionBatchRequest.getProcessStatus()) ?
                            null : transactionBatchRequest.getUpdatedAt();
                    return TransactionSearchRes.builder()
                            .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode())) // FIXME check null
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .batchNo(transactionBatchRequest.getId())
                            .batchName(transactionBatchRequest.getName())
                            .batchRequestType(transactionBatchRequest.getType())
                            .transactionBatchType(transactionBatchRequest.getTransactionBatchType())
                            .enableSMS(enableSMS)
                            .failedRecords(transactionBatchRequest.getFailedRequests())
                            .createdAt(transactionBatchRequest.getCreatedAt())
                            .generateDate(generateDate)
                            .processStatus(transactionBatchRequest.getProcessStatus())
                            .description(transactionBatchRequest.getDescription())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(transactionBatchRequestRes, page, result.getTotalElements());
    }

    @Override
    public APIResponse<?> createTransaction(CreateTransactionReq req) {
        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findByIdAndBusinessId(req.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        ProgramCorporation programCorporation = programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), req.getCorporationId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_CORPORATION_NOT_FOUND, "Program Corporation not found", null));

        Chain chain = chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), programCorporation.getCorporationId(), req.getChainId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "Chain not found", null));


        Store store = storeRepository.findByIdAndBusinessIdAndCorporationIdAndChainId(req.getStoreId(), business.getId(), programCorporation.getCorporationId(), chain.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.STORE_NOT_FOUND, "Store not found", null));

        if (!store.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.STORE_NOT_ACTIVE, "Store not active", null);

        Pos pos = posRepository.findByIdAndStoreId(req.getTerminalId(), store.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.POS_NOT_FOUND, "Terminal (POS) not found", null));

        if (!pos.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.TERMINAL_NOT_ACTIVE, "Terminal (POS) not active", null);

        CreateTransactionReq.Transaction transaction = req.getTransaction();

        TransactionInfoFeignReq transactionInfoFeignReq = TransactionInfoFeignReq.builder()
                .invoiceNo(transaction.getInvoiceNo())
                .transactionTime(req.getTransactionTime())
                .terminalCode(pos.getCode())
                .storeCode(store.getCode())
                .channelId(chain.getId())
                .build();

        CreateTransactionFeignReq createTransactionFeignReq = CreateTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(req.getMemberCode())
                        .idType(req.getIdType().getMapping())
                        .build())
                .businessCode(business.getCode())
                .programCode(program.getCode())
                .transaction(transactionInfoFeignReq)
                .createdBy(
                        ((OPSAuthenticatedPrincipal) SecurityContextHolder.getContext()
                                .getAuthentication()
                                .getPrincipal())
                                .getUserName())
                .build();

        if (req.getTransactionType().equals(EOpsTransactionType.ADJUSTMENT)) {
            if (transaction.getAdjustPoint() == null || transaction.getAdjustPoint().compareTo(BigDecimal.ZERO) != 1)
                throw new BusinessException(OpsErrorCode.ADJUST_POINT_MUST_GREATER_THAN_ZERO.getValue(), "Adjust point must greater than zero", null);

            Function function = functionRepository.findByCode(EOpsFunctionCode.POINT_ADJ.getValue());

            ReasonCode reasonCode = reasonCodeRepository.findByBusinessIdAndProgramIdAndId(business.getId(), program.getId(), transaction.getReasonId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.REASON_CODE_NOT_FOUND, "Reason Code not found", null));

            if (reasonCode.getFunctionId() != function.getId())
                throw new BusinessException(OpsErrorCode.REASON_CODE_IS_INVALID.getValue(), "Reason Code is invalid", null);

            Pool pool = poolRepository.findByIdAndBusinessIdAndProgramId(transaction.getPoolId(), business.getId(), program.getId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.POOL_NOT_FOUND, "Pool not found", null));

            transactionInfoFeignReq.setInvoiceNo(String.format(adjustedTransactionInvoiceNoPrefix, new Date().toInstant().toEpochMilli()));
            transactionInfoFeignReq.setReasonCode(reasonCode.getCode());
            transactionInfoFeignReq.setPoolCode(pool.getCode());
            transactionInfoFeignReq.setAdjustPoint(transaction.getAdjustPoint());
            transactionInfoFeignReq.setAdjustmentType(transaction.getAdjustmentType());

            if (!pool.getStatus().equals(ECommonStatus.ACTIVE))
                throw new BusinessException(ErrorCode.POOL_NOT_ACTIVE, "Pool not active", null);

            return oneloyaltyServiceFeignClient.createAdjustmentTransaction(createTransactionFeignReq);
        } else {
            Currency currency = currencyRepository.findByCodeAndBusinessId(transaction.getCurrencyCode(), business.getId());
            createTransactionFeignReq.setCurrencyCode(currency.getCode());
            transactionInfoFeignReq.setGrossAmount(transaction.getGrossAmount());
            transactionInfoFeignReq.setGmv(transaction.getGmv());
            transactionInfoFeignReq.setRedeemPoint(transaction.getRedeemPoint());

            switch (req.getTransactionType()) {
                case SALE:
                    return oneloyaltyServiceFeignClient.createSaleTransaction(createTransactionFeignReq);

                case EARN:
                    transactionInfoFeignReq.setRedeemPoint(BigDecimal.ZERO);
                    return oneloyaltyServiceFeignClient.createEarnTransaction(createTransactionFeignReq);

                case BURN:
                    transactionInfoFeignReq.setGmv(BigDecimal.ZERO);
                    transactionInfoFeignReq.setGrossAmount(BigDecimal.ZERO);

                    return oneloyaltyServiceFeignClient.createBurnTransaction(createTransactionFeignReq);

                default:
            }
        }

        throw new OpsBusinessException(OpsErrorCode.TRANSACTION_TYPE_NOT_SUPPORT, "Transaction type not support", null);
    }

    @Override
    public ResourceDTO verifyTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception {
        List dtos = this.mappingToExcelDto(req.getTransactionBatchType(), file);
        boolean isValid = this.verifyCreateTransaction(req, dtos);
        return isValid ? null : this.exportFileExcel(req.getTransactionBatchType(), file.getOriginalFilename(), dtos);
    }

    @Override
    public ResourceDTO createTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception {
        List dtos = this.mappingToExcelDto(req.getTransactionBatchType(), file);
        if(EGenerationInvoiceNoMethod.CUSTOM.equals(req.getGenInvoiceNoMethod())) {
            validateGenInvoiceNo(req.getGenInvoiceNoPattern());
        }
        boolean isValid = this.verifyCreateTransaction(req, dtos);
        if(!isValid) {
            return this.exportFileExcel(req.getTransactionBatchType(), file.getOriginalFilename(), dtos);
        }
        // Create transaction
        this.saveTransaction(req, dtos, EBatchRequestType.BATCH);
        return null;
    }

    @Override
    public APIResponse<?> revertTransaction(RevertTransactionReq payload) {
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(payload.getTransactionId()));

        if (transactionHistories.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);

        EOpsTransactionType transactionType = getTransactionType(points);

        if (EOpsTransactionType.ADJUSTMENT.equals(transactionType))
            throw new UnsupportedOperationException("Unsupported reverting adjustment transaction type!");

        TransactionHistory transaction = transactionHistories.get(0);

        Business business = businessRepository.findById(transaction.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findById(transaction.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        Currency baseCurrency = getBaseCurrencyFromTransaction(transaction, program, transactionType);

        if (!payload.getOriginalInvoiceNo().equals(transaction.getInvoiceNo()))
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        ReverseTransactionInfoFeignReq transactionInfoFeignReq;

        if (payload.getIsPartial()) {
            if (payload.getNewTxnInfo() == null)
                throw new OpsBusinessException(OpsErrorCode.NEW_TXN_INFO_FOR_REVERT_NOT_FOUND, "new txn info for revert not found", null);

            Store store = storeService.findActive(payload.getNewTxnInfo().getStoreId());
            Pos terminal = posService.findActive(payload.getNewTxnInfo().getTerminalId());

            RevertTransactionReq.NewTxnInfo txnInfo = payload.getNewTxnInfo();

            transactionInfoFeignReq = ReverseTransactionInfoFeignReq.builder()
                    .storeCode(store.getCode())
                    .terminalCode(terminal.getCode())
                    .invoiceNo(txnInfo.getInvoiceNo() != null ? txnInfo.getInvoiceNo() : generateTxnInvoiceNo())
                    .refundAmount(payload.getNewTxnInfo().getRefundAmount())
                    .redeemPoint(payload.getNewTxnInfo().getRedeemPoint())
                    .transactionTime(txnInfo.getTransactionTime() != null ? txnInfo.getTransactionTime() : new Date().toInstant().getEpochSecond())
                    .description(payload.getNewTxnInfo() != null ? payload.getNewTxnInfo().getDescription() : null)
                    .build();
        } else { // full
            Store store = storeService.findActive(transaction.getStoreId());
            Pos terminal = posService.findActive(transaction.getPosId());

            transactionInfoFeignReq = ReverseTransactionInfoFeignReq.builder()
                    .storeCode(store.getCode())
                    .terminalCode(terminal.getCode())
                    .invoiceNo(generateTxnInvoiceNo())
                    .refundAmount(transaction.getGrossAmount())
                    .redeemPoint(BigDecimal.valueOf(0))
                    .transactionTime(new Date().toInstant().getEpochSecond())
                    .build();
        }

        ReverseTransactionFeignReq req = ReverseTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(transaction.getMemberProductAccountCode())
                        .idType(transaction.getMemberProductAccountType())
                        .build())
                .transaction(transactionInfoFeignReq)
                .originalInvoiceNo(payload.getOriginalInvoiceNo())
                .businessCode(business.getCode())
                .programCode(program.getCode())
                .currencyCode(baseCurrency == null ? null : baseCurrency.getCode())
                .createdBy(((OPSAuthenticatedPrincipal) SecurityContextHolder.getContext()
                        .getAuthentication().getPrincipal())
                        .getUserName())
                .build();

        return oneloyaltyServiceFeignClient.refundTransaction(req);
    }

    @Override
    public List<TransactionCheckLogRes> checkLog(String pointTnxId) {
        return transactionAuditTrailService.findAllByTransactionRef(pointTnxId).stream()
                .map(en -> TransactionCheckLogRes.builder()
                        .id(en.getId())
                        .transactionRef(en.getTransactionRef())
                        .businessId(en.getBusinessId())
                        .eventType(en.getEventType())
                        .userId(en.getUserId())
                        .payload(en.getPayload())
                        .sourse(convertSourceToResponse(en.getSource(), en.getPayload()))
                        .createdAt(en.getCreatedAt() != null ? en.getCreatedAt().toInstant().getEpochSecond() : null)
                        .additionalSourceInfo(en.getAdditionalSourceInfo())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public Page<TransactionRes> search(
            Integer businessId,
            Integer programId,
            String userProfileId,
            String transactionType,
            Integer transactionTimeFrom,
            Integer transactionTimeTo,
            Pageable pageable) {
        UserProfile userProfile = userProfileRepository.findByMasterUserIdAndBusinessId(userProfileId, businessId);

        Business business = businessRepository.findById(businessId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        Program program = programRepository.findByIdAndBusinessId(programId, business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if (userProfile == null)
            throw new BusinessException(ErrorCode.USER_PROFILE_NOT_FOUND, null, null);

        Member member = memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, null, null));

        ETransactionType eTransactionType = null;

        if(StringUtils.isNoneBlank(transactionType))
            eTransactionType = ETransactionType.of(transactionType);

        Date startTime = null;
        Date endTime = null;

        if(transactionTimeFrom == null || transactionTimeTo == null) {
            endTime = new Date();

            startTime = Date.from(
                    LocalDate.now().minusDays(90).atStartOfDay()
                            .atZone(ZoneId.systemDefault())
                            .toInstant());
        } else {
            startTime = Date.from(Instant.ofEpochSecond(transactionTimeFrom));
            endTime = Date.from(Instant.ofEpochSecond(transactionTimeTo));
        }

        Page<String> page = transactionHistoryRepository.getPointTnxIds(
                businessId,
                programId,
                userProfile.getId(),
                eTransactionType,
                startTime,
                endTime,
                pageable);

        List<String> pointTnxIds = page.getContent().stream().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pointTnxIds))
            return new PageImpl<TransactionRes>(Collections.emptyList(), pageable, 0);

        List<TransactionHistory> transactionHistoryListAll = transactionHistoryService.getByTnxPointIds(pointTnxIds);

        Map<String, List<TransactionHistory>> transactionHistoryByPointTnxIds = transactionHistoryListAll.stream()
                .collect(
                        Collectors.groupingBy(
                                TransactionHistory::getPointTransactionId,
                                Collectors.toList()));

        List<TransactionHistory> transactionHistories = transactionHistoryByPointTnxIds.values()
                .stream()
                .filter(t -> t.size() > 0)
                .map(t -> t.get(0))
                .sorted(getSortForSearch(Sort.Direction.DESC))
                .collect(Collectors.toList());

        Set<Integer> corporationIds = transactionHistories.stream().map(TransactionHistory::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationByIds = opsCorporationService.getMapById(corporationIds);

        Set<Integer> chainIds = transactionHistories.stream().map(TransactionHistory::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainByIds = opsChainService.getMapById(chainIds);

        Set<Integer> storeIds = transactionHistories.stream().map(TransactionHistory::getStoreId).collect(Collectors.toSet());
        Map<Integer, Store> storeByIds = opsStoreService.getMapById(storeIds);

        Set<Integer> posIds = transactionHistories.stream().map(TransactionHistory::getPosId).collect(Collectors.toSet());
        Map<Integer, Pos> posByIds = opsTerminalService.getMapById(posIds);

        return new PageImpl<TransactionRes>(
                transactionHistories.stream()
                        .map(each -> {
                            Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryByPointTnxIds.get(each.getPointTransactionId()));

                            return TransactionRes.valueOf(
                                    points,
                                    each,
                                    getTransactionType(points),
                                    business,
                                    corporationByIds.get(each.getCorporationId()),
                                    chainByIds.get(each.getChainId()),
                                    storeByIds.get(each.getStoreId()),
                                    posByIds.get(each.getPosId()),
                                    program,
                                    member,
                                    userProfile);
                        })
                        .collect(Collectors.toList()),
                pageable,
                page.getTotalElements());
    }

    private String generateTxnInvoiceNo() {
        return String.format(reverseTransactionInvoiceNoPrefix, new Date().toInstant().toEpochMilli());
    }

    private TransactionRes.TransactionRefunded getTransactionRefunded(TransactionHistory txnH,
                                                                      List<TransactionHistory> txnHistoryRefundeds) {
        return txnHistoryRefundeds.size() != 0 ?
                new TransactionRes.TransactionRefunded(
                        txnHistoryRefundeds.get(0).getInvoiceNo(),
                        txnHistoryRefundeds.get(0).getPointTransactionId(),
                        txnH.getGrossAmount() != null
                                ? txnH.getGrossAmount().subtract(txnHistoryRefundeds.get(0).getGrossAmount())
                                : BigDecimal.ZERO
                ) : null;
    }

    private EOpsTransactionType getTransactionType(Map<ETransactionType, List<BigDecimal>> points) {
        if (points.containsKey(ETransactionType.ADJUSTMENT))
            return EOpsTransactionType.ADJUSTMENT;

        if (points.containsKey(ETransactionType.AWARD) && points.containsKey(ETransactionType.REDEEM)) {
            return EOpsTransactionType.SALE;
        } else if (points.containsKey(ETransactionType.AWARD)) {
            return EOpsTransactionType.EARN;
        } else {
            return EOpsTransactionType.BURN;
        }
    }

    private Map<ETransactionType, List<BigDecimal>> getTransactionPoints(List<TransactionHistory> transactionHistories) {
        return transactionHistories
                .stream()
                .filter(Objects::nonNull)
                .flatMap(entity -> {
                    List<TransactionPointWrapper> wrappers = new ArrayList<>();

                    if (entity.getType().equals(ETransactionType.ADJUSTMENT)) {
                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.AWARD)
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());

                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.REDEEM)
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());

                        wrappers.add(TransactionPointWrapper.builder()
                                .type(ETransactionType.ADJUSTMENT)
                                .build());
                    } else {
                        wrappers.add(TransactionPointWrapper.builder()
                                .type(entity.getType())
                                .awardPoint(entity.getAwardPoint())
                                .redeemPoint(entity.getRedeemPoint())
                                .build());
                    }

                    return wrappers.stream();
                })
                .collect(Collectors.groupingBy(
                        TransactionPointWrapper::getType, Collectors.mapping(wrapper -> {
                            switch (wrapper.getType()) {
                                case AWARD:
                                    return wrapper.getAwardPoint() != null ? wrapper.getAwardPoint() : BigDecimal.ZERO;

                                case REDEEM:
                                    return wrapper.getRedeemPoint() != null ? wrapper.getRedeemPoint() : BigDecimal.ZERO;

                                default:
                                    return BigDecimal.ZERO;
                            }
                        }, Collectors.toList())));
    }

    private Comparator<TransactionHistory> getSortForSearch(Sort.Direction direction) {
        if (Sort.Direction.ASC.equals(direction))
            return (o1, o2) -> o1.getCreatedAt().compareTo(o2.getCreatedAt());
        return (o1, o2) -> o2.getCreatedAt().compareTo(o1.getCreatedAt());
    }

    private Currency getBaseCurrencyFromTransaction(TransactionHistory transactionHistory, Program program, EOpsTransactionType type) {
        Currency baseCurrency = null;
        List<Currency> baseCurrencies = null;

        if (EOpsTransactionType.BURN.equals(type)) {
            Pool pool = null;
            if (transactionHistory.getPoolId() != null) {
                pool = poolRepository.findById(transactionHistory.getPoolId()).orElse(null);
            }

            Currency currency = currencyService.findActive(pool.getCurrencyId());

            baseCurrencies = Optional.ofNullable(currencyRepository.findBaseCurrencyByCurrencyAndBusiness(currency.getId(), program.getBusinessId(), ECommonStatus.ACTIVE))
                    .orElse(Collections.emptyList());
        } else {
            ETransactionType eTransactionType = type == EOpsTransactionType.ADJUSTMENT ? ETransactionType.ADJUSTMENT : ETransactionType.AWARD;

            baseCurrencies = Optional.ofNullable(this.currencyRepository.findByTransactionTypeAndTransactionId(eTransactionType, transactionHistory.getPointTransactionId()))
                    .orElse(Collections.emptyList());
        }

        if (baseCurrencies.size() > 1) {
            throw new BusinessException(ErrorCode.UNSUPPORTED_MULTIPLE_BASE_CURRENCY_RATES,
                    "Unsupported multiple base currency rates",
                    transactionHistory,
                    new Object[]{ transactionHistory.getPointTransactionId() });
        }

        baseCurrency = baseCurrencies.get(0);

        return baseCurrency;
    }

    @SuppressWarnings("unchecked")
    private String convertSourceToResponse(String source, Object payload) {
        Map<String, Object> hashMapPayload = (Map<String, Object>) payload;
        Map<String, Object> transaction = (Map<String, Object>) hashMapPayload.get("transaction");
        if (checkLogSourceConfigPram.SOURCE_API_WRAPPER_VALUE.equals(source)) {
            return checkLogSourceConfigPram.SOURCE_MOBILE_DEFAULT_STORE_CODES.contains(transaction.get("store_id")) ?
                    checkLogSourceConfigPram.SOURCE_MOBILE_VALUE : "GD Online: Store - " + transaction.get("store_id");
        } else {
            return source;
        }
    }

    @Override
    public APIResponse<?> confirmTransaction(TransactionConfirmReq txnConfirmReq) {
        // TODO Auto-generated method stub
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(txnConfirmReq.getTransactionId()));

        if (transactionHistories.size() == 0)
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);

        TransactionHistory transaction = transactionHistories.get(0);


        Business business = businessRepository.findById(transaction.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        if (!business.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);

        Program program = programRepository.findById(transaction.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        if (!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

        ConfirmTransactionFeignReq req = ConfirmTransactionFeignReq.builder()
                .customerIdentifier(CustomerFeignReq.builder()
                        .id(transaction.getMemberProductAccountCode())
                        .idType(transaction.getMemberProductAccountType())
                        .build())
                .txnRefNo(transaction.getPointTransactionId())
                .programId(program.getCode())
                .businessId(business.getCode())
                .transactionStatus(txnConfirmReq.getTxnStatus())
                .build();
        return oneloyaltyServiceFeignClient.confirmTransaction(req);
    }

    @Override
    public TransactionBatchRequestDetailRes getTransactionBatchRequestDetail(Long id) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(id);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .rejectReason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .generationTime(EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus())
                        ? batchRequest.getUpdatedAt() : null)
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .totalTransaction(batchRequest.getTotalRequests())
                .totalMember(batchRequest.getTotalMember())
                .totalGMV(batchRequest.getTotalGMV())
                .totalGrossAmount(batchRequest.getTotalGrossAmount())
                .totalAwardPoint(batchRequest.getTotalAwardPoint())
                .totalRedeemPoint(batchRequest.getTotalRedeemPoint())
                .build();
    }

    @Builder
    @Getter
    private static class TransactionInfo {
        private BigDecimal gmv;
        private BigDecimal grossAmount;
        private BigDecimal nettAmount;
        private BigDecimal totalAwardPoint;
        private BigDecimal totalRedeemPoint;
        private BigDecimal balanceBefore;
        private BigDecimal balanceAfter;
        private BigDecimal awardBeforeLimit;
        private Date awardRetentionTime;
        private String schemeCodes;
        private String poolCodes;
    }

    @Override
    public Page<TransactionRequestRes> getInReviewTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(batchRequestId);
        businessService.findActive(batchRequest.getBusinessId());
        programService.findActive(batchRequest.getProgramId());

        SpecificationBuilder<TransactionRequest> specificationBuilder = new SpecificationBuilder<>();
        specificationBuilder.add(new SearchCriteria("batchRequestId", batchRequestId, SearchOperation.EQUAL));
        if (Objects.nonNull(processStatus)) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        Page<TransactionRequest> transactionRequestPage = transactionRequestService.find(specificationBuilder, pageable);

        // Get memberMap, storeMap, chainMap, corporationMap
        Set<Long> memberIds = new HashSet<>();
        Set<String> storeCodes = new HashSet<>();
        Set<Integer> chainIds = new HashSet<>();
        Set<Integer> corporationIds = new HashSet<>();
        List<String> txnRefNos = new ArrayList<>();
        Set<Integer> schemeIdSet = new HashSet<>();
        Set<Integer> poolIdSet = new HashSet<>();

        List<Store> stores;
        Map<String, Store> storeMapByCode = new HashMap<>();
        Map<Long, String> memberCodeMapById;
        Map<Integer, String> chainCodeMapById;
        Map<Integer, String> corporationCodeMapById;

        for (TransactionRequest transactionRequest : transactionRequestPage.getContent()) {
            if(Objects.nonNull(transactionRequest.getMemberId())) {
                memberIds.add(transactionRequest.getMemberId());
            }
            if(Objects.nonNull(transactionRequest.getStoreCode())) {
                storeCodes.add(transactionRequest.getStoreCode());
            }
            if(Objects.nonNull(transactionRequest.getTxnRefNo())) {
                txnRefNos.add(transactionRequest.getTxnRefNo());
            }
        }

        memberCodeMapById = memberService.findByIdIn(memberIds)
                .stream().collect(Collectors.toMap(Member::getId, Member::getMemberCode));

        stores = storeService.findByCodeInAndBusinessId(storeCodes, batchRequest.getBusinessId());

        for (Store store : stores) {
            storeMapByCode.put(store.getCode(), store);
            chainIds.add(store.getChainId());
            corporationIds.add(store.getCorporationId());
        }

        chainCodeMapById = chainService.findByIdIn(chainIds)
                .stream().collect(Collectors.toMap(Chain::getId, Chain::getCode));

        corporationCodeMapById = corporationService.findByIdIn(corporationIds)
                .stream().collect(Collectors.toMap(Corporation::getId, Corporation::getCode));

        if(ETransactionBatchType.REVERT_FULL.equals(batchRequest.getTransactionBatchType())) {
            List<TransactionHistory> txnHistories = transactionHistoryService.getByTnxPointIds(txnRefNos);

            txnHistories.forEach(item -> {
                if(Objects.nonNull(item.getSchemeId())) {
                    schemeIdSet.add(item.getSchemeId());
                }
                if(Objects.nonNull(item.getPoolId())) {
                    poolIdSet.add(item.getPoolId());
                }
            });

            Map<Integer, String> schemeCodeMapById = schemeService
                    .findByProgramIdAndListIds(batchRequest.getProgramId(), new ArrayList<>(schemeIdSet))
                    .stream().collect(Collectors.toMap(
                            Scheme::getId,
                            Scheme::getCode
                    ));

            Map<Integer, String> poolCodeMapById = poolService
                    .findByIdIn(new ArrayList<>(poolIdSet))
                    .stream().collect(Collectors.toMap(
                            Pool::getId,
                            Pool::getCode
                    ));

            Map<String, List<TransactionHistory>> txnHistoryMapByTxnRefNo = txnHistories
                    .stream().collect(Collectors.groupingBy(
                                    TransactionHistory::getPointTransactionId,
                                    Collectors.toList()
                            )
                    );

            return transactionRequestPage.map(request -> buildTransactionForRevertFull(
                    batchRequest,
                    request,
                    txnHistoryMapByTxnRefNo,
                    schemeCodeMapById,
                    poolCodeMapById,
                    corporationCodeMapById,
                    chainCodeMapById,
                    storeMapByCode,
                    memberCodeMapById

            ));
        } else {
            return transactionRequestPage.map(request -> buildTransactionInReview(
                    batchRequest,
                    request,
                    corporationCodeMapById,
                    chainCodeMapById,
                    storeMapByCode,
                    memberCodeMapById
            ));
        }
    }

    private TransactionRequestRes buildTransactionInReview(TransactionBatchRequest batchRequest,
                                                           TransactionRequest request,
                                                           Map<Integer, String> corporationCodeMapById,
                                                           Map<Integer, String> chainCodeMapById,
                                                           Map<String, Store> storeMapByCode,
                                                           Map<Long, String> memberCodeMapById) {
        Store store = storeMapByCode.get(request.getStoreCode());
        String corporationCode = null;
        String chainCode = null;

        if(Objects.nonNull(store)) {
            corporationCode = corporationCodeMapById.get(store.getCorporationId());
            chainCode = chainCodeMapById.get(store.getChainId());
        }

        EOpsTransactionType transactionType = getTransactionType(batchRequest.getTransactionBatchType(), Collections.emptyList(), request);

        BigDecimal awardPoint = null;
        BigDecimal redeemPoint = null;
        if(ETransactionBatchType.ADJUST.equals(batchRequest.getTransactionBatchType())) {
            if(EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                awardPoint = request.getAdjustPoint();
            } else {
                redeemPoint = request.getAdjustPoint();
            }
        } else {
            redeemPoint = request.getRedeemPoint();
        }

        return TransactionRequestRes
                .builder()
                .txnRefNo(request.getTxnRefNo())
                .invoiceNo(request.getInvoiceNo())
                .originalInvoiceNo(request.getOriginalInvoiceNo())
                .corporationCode(corporationCode)
                .chainCode(chainCode)
                .storeCode(request.getStoreCode())
                .terminalCode(request.getTerminalCode())
                .memberId(request.getMemberId())
                .memberCode(memberCodeMapById.get(request.getMemberId()))
                .productAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue))
                .productAccountCode(request.getIdNo())
                .transactionTime(request.getTransactionTime())
                .transactionType(nullSafer(transactionType, EOpsTransactionType::getValue))
                .gmv(request.getGmv())
                .grossAmount(request.getGrossAmount())
                .awardPoint(awardPoint)
                .redeemPoint(redeemPoint)
                .poolCode(request.getPoolCode())
                .currencyCode(request.getCurrencyCode())
                .description(request.getDescription())
                .reasonCode(request.getReasonCode())
                .channel(request.getChannel() == null ? OPSConstant.DEFAULT_TRANSACTION_CHANNEL : request.getChannel())
                .serviceCode(request.getServiceCode() == null ? OPSConstant.DEFAULT_TRANSACTION_SERVICE_CODE : request.getServiceCode())
                .processStatus(request.getProcessStatus())
                .build();
    }

    private TransactionRequestRes buildTransactionForRevertFull(TransactionBatchRequest batchRequest,
                                                                        TransactionRequest request,
                                                                        Map<String, List<TransactionHistory>> txnHistoryMapByTxnRefNo,
                                                                        Map<Integer, String> schemeCodeMapById,
                                                                        Map<Integer, String> poolCodeMapById,
                                                                        Map<Integer, String> corporationCodeMapById,
                                                                        Map<Integer, String> chainCodeMapById,
                                                                        Map<String, Store> storeMapByCode,
                                                                        Map<Long, String> memberCodeMapById) {
        Store store = storeMapByCode.get(request.getStoreCode());
        List<TransactionHistory> txnHistories = txnHistoryMapByTxnRefNo.get(request.getTxnRefNo());

        String corporationCode = null;
        String chainCode = null;

        if(Objects.nonNull(store)) {
            corporationCode = corporationCodeMapById.get(store.getCorporationId());
            chainCode = chainCodeMapById.get(store.getChainId());
        }

        EOpsTransactionType transactionType = getTransactionType(batchRequest.getTransactionBatchType(), txnHistories, request);

        TransactionRequestRes res = TransactionRequestRes
                .builder()
                .txnRefNo(request.getTxnRefNo())
                .originalInvoiceNo(request.getOriginalInvoiceNo())
                .corporationCode(corporationCode)
                .chainCode(chainCode)
                .storeCode(request.getStoreCode())
                .terminalCode(request.getTerminalCode())
                .memberId(request.getMemberId())
                .memberCode(memberCodeMapById.get(request.getMemberId()))
                .productAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue))
                .productAccountCode(request.getIdNo())
                .transactionType(nullSafer(transactionType, EOpsTransactionType::getValue))
                .currencyCode(request.getCurrencyCode())
                .processStatus(request.getProcessStatus())
                .transactionTime(request.getTransactionTime())
                .errorCode(request.getErrorCode())
                .errorMessage(request.getErrorMessage())
                .smsErrorCode(request.getSmsErrorCode())
                .smsErrorMessage(request.getSmsErrorMessage())
                .build();

        if(CollectionUtils.isNotEmpty(txnHistories)) {
            TransactionHistory txnHistory = txnHistories.get(0);
            TransactionInfo txnInfo = getTransactionInfo(txnHistories, schemeCodeMapById, poolCodeMapById);

            res.setTransactionTime(txnHistory.getTransactionTime());
            res.setDescription(txnHistory.getDescription());
            res.setAwardPoint(txnInfo.getTotalAwardPoint());
            res.setRedeemPoint(txnInfo.getTotalRedeemPoint());
            res.setGmv(txnInfo.getGmv());
            res.setGrossAmount(txnInfo.getGrossAmount());
            res.setNettAmount(txnInfo.getNettAmount());
            res.setPointBalanceBefore(txnInfo.getBalanceBefore());
            res.setPointBalanceAfter(txnInfo.getBalanceAfter());
            res.setAwardPointBeforeLimit(txnInfo.getAwardBeforeLimit());
            res.setAwardRetentionTime(txnInfo.getAwardRetentionTime());
            res.setSchemeCode(txnInfo.getSchemeCodes());
            res.setPoolCode(txnInfo.getPoolCodes());
            res.setReasonCode(txnHistory.getReasonCode());
            res.setChannel(txnHistory.getChannel());
            res.setServiceCode(txnHistory.getServiceCode());
            res.setCancellation(txnHistory.getCancellation() != null);
            res.setCancellationTime(txnHistory.getCancellationTime());
            res.setCancellationType(txnHistory.getCancellationType());
        }

        return res;
    }

    private TransactionInfo getTransactionInfo(List<TransactionHistory> txnHistories, Map<Integer, String> schemeCodeMapById, Map<Integer, String> poolCodeMapById) {
        Set<Integer> schemeIds = new HashSet<>();
        Set<Integer> poolIds = new HashSet<>();
        Date awardRetentionTime = null;
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;
        BigDecimal awardBeforeLimit = BigDecimal.ZERO;
        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;
        BigDecimal gmv = BigDecimal.ZERO;
        BigDecimal grossAmount = BigDecimal.ZERO;
        BigDecimal nettAmount = BigDecimal.ZERO;
        String schemeCodes = Strings.EMPTY;
        String poolCodes = Strings.EMPTY;

        if(CollectionUtils.isNotEmpty(txnHistories)) {
            for(TransactionHistory item : txnHistories) {
                if(Objects.nonNull(item.getSchemeId())) {
                    schemeIds.add(item.getSchemeId());
                }
                if(Objects.nonNull(item.getPoolId())) {
                    poolIds.add(item.getPoolId());
                }
                if(Objects.nonNull(item.getAwardRetentionTime())) {
                    awardRetentionTime = item.getAwardRetentionTime();
                }
                if(Objects.nonNull(item.getAwardPointBeforeLimit())) {
                    awardBeforeLimit = awardBeforeLimit.add(item.getAwardPointBeforeLimit());
                }
                if(Objects.nonNull(item.getAwardPoint())) {
                    totalAwardPoint = totalAwardPoint.add(item.getAwardPoint());
                }
                if(Objects.nonNull(item.getRedeemPoint())) {
                    totalRedeemPoint = totalRedeemPoint.add(item.getRedeemPoint());
                }
                if(Objects.nonNull(item.getGmv())) {
                    gmv = item.getGmv();
                }
                if(Objects.nonNull(item.getGrossAmount())) {
                    grossAmount = item.getGrossAmount();
                }
                if(Objects.nonNull(item.getNettAmount())) {
                    nettAmount = item.getNettAmount();
                }
            }
            TransactionHistory max = txnHistories
                    .stream().filter(item -> Objects.nonNull(item.getBalanceAfter()))
                    .max(Comparator.comparing(TransactionHistory::getId))
                    .orElse(null);

            TransactionHistory min = txnHistories
                    .stream().filter(item -> Objects.nonNull(item.getBalanceBefore()))
                    .min(Comparator.comparing(TransactionHistory::getId))
                    .orElse(null);

            balanceBefore = Objects.nonNull(min) ? min.getBalanceBefore() : BigDecimal.ZERO;

            balanceAfter = Objects.nonNull(max) ? max.getBalanceAfter() : BigDecimal.ZERO;

            schemeCodes = schemeIds.stream()
                    .map(schemeCodeMapById::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));

            poolCodes = poolIds.stream()
                    .map(poolCodeMapById::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));
        }

        return TransactionInfo.builder()
                .gmv(gmv)
                .grossAmount(grossAmount)
                .nettAmount(nettAmount)
                .awardBeforeLimit(awardBeforeLimit)
                .awardRetentionTime(awardRetentionTime)
                .totalAwardPoint(totalAwardPoint)
                .totalRedeemPoint(totalRedeemPoint)
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .schemeCodes(schemeCodes)
                .poolCodes(poolCodes)
                .build();
    }

    @Override
    public Page<TransactionRequestRes> getAvailableTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable) {
        TransactionBatchRequest transactionBatchRequest = transactionBatchRequestService.find(batchRequestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.TRANSACTION_BATCH_REQUEST_NOT_FOUND));
        Business business = businessService.find(transactionBatchRequest.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        programService.find(transactionBatchRequest.getProgramId());

        SpecificationBuilder<TransactionRequest> specificationBuilder = new SpecificationBuilder<>();

        specificationBuilder.add(new SearchCriteria("batchRequestId", batchRequestId, SearchOperation.EQUAL));

        if (processStatus != null) {
            specificationBuilder.add(new SearchCriteria("processStatus", processStatus, SearchOperation.EQUAL));
        }

        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                Sort.by(Sort.Direction.ASC, "processStatus"));
        Page<TransactionRequest> transactionRequestPage = transactionRequestService.find(specificationBuilder, pageRequest);

        // Get memberMap, storeMap, chainMap, corporationMap
        Set<Long> memberIds = new HashSet<>();
        Set<String> storeCodes = new HashSet<>();
        Set<Integer> chainIds = new HashSet<>();
        Set<Integer> corporationIds = new HashSet<>();
        List<String> txnRefNos = new ArrayList<>();
        Set<Integer> schemeIdSet = new HashSet<>();
        Set<Integer> poolIdSet = new HashSet<>();

        List<Store> stores;
        Map<String, Store> storeMap = new HashMap<>();
        Map<Long, String> memberCodeMap;
        Map<Integer, String> chainCodeMap;
        Map<Integer, String> corporationCodeMap;

        for (TransactionRequest transactionRequest : transactionRequestPage.getContent()) {
            memberIds.add(transactionRequest.getMemberId());
            storeCodes.add(transactionRequest.getStoreCode());
            txnRefNos.add(transactionRequest.getTxnRefNo());
        }

        memberCodeMap = memberService.findByIdIn(memberIds)
                .stream().collect(Collectors.toMap(Member::getId, Member::getMemberCode));

        stores = storeService.findByCodeInAndBusinessId(storeCodes, business.getId());

        for (Store store : stores) {
            storeMap.put(store.getCode(), store);
            chainIds.add(store.getChainId());
            corporationIds.add(store.getCorporationId());
        }

        chainCodeMap = chainService.findByIdIn(chainIds)
                .stream().collect(Collectors.toMap(Chain::getId, Chain::getCode));

        corporationCodeMap = corporationService.findByIdIn(corporationIds)
                .stream().collect(Collectors.toMap(Corporation::getId, Corporation::getCode));

        List<TransactionHistory> txnHistories = transactionHistoryService.getByTnxPointIds(txnRefNos);

        Map<String, List<TransactionHistory>> transactionHistoryMap = txnHistories.stream().collect(
                Collectors.groupingBy(
                        TransactionHistory::getPointTransactionId,
                        Collectors.toList()
                )
        );

        txnHistories.forEach(item -> {
            if(Objects.nonNull(item.getSchemeId())) {
                schemeIdSet.add(item.getSchemeId());
            }
            if(Objects.nonNull(item.getPoolId())) {
                poolIdSet.add(item.getPoolId());
            }
        });

        Map<Integer, String> schemeCodeMapById = schemeService
                .findByProgramIdAndListIds(transactionBatchRequest.getProgramId(), new ArrayList<>(schemeIdSet))
                .stream().collect(Collectors.toMap(
                        Scheme::getId,
                        Scheme::getCode
                ));

        Map<Integer, String> poolCodeMapById = poolService
                .findByIdIn(new ArrayList<>(poolIdSet))
                .stream().collect(Collectors.toMap(
                        Pool::getId,
                        Pool::getCode
                ));

        if(ETransactionBatchType.REVERT_FULL.equals(transactionBatchRequest.getTransactionBatchType())) {
            return transactionRequestPage.map(request -> buildTransactionForRevertFull(
                            transactionBatchRequest,
                            request,
                            transactionHistoryMap,
                            schemeCodeMapById,
                            poolCodeMapById,
                            corporationCodeMap,
                            chainCodeMap,
                            storeMap,
                            memberCodeMap
                    )
            );
        } else {
            return transactionRequestPage
                    .map(request -> {
                        Store store = storeMap.get(request.getStoreCode());
                        List<TransactionHistory> transactionHistoryList = transactionHistoryMap.get(request.getTxnRefNo());
                        return buildTransactionAvailable(
                                transactionBatchRequest,
                                request,
                                transactionHistoryList,
                                schemeCodeMapById,
                                poolCodeMapById,
                                corporationCodeMap,
                                store,
                                chainCodeMap,
                                memberCodeMap
                        );
                    });
        }
    }

    @Override
    public String randomInvoiceNumber(String pattern, ECharacterSet type) {
        boolean letters = true;
        boolean numbers = true;
        switch (type) {
            case NUMBER: {
                letters = false;
                break;
            }
            case ALPHABETICAL: {
                numbers = false;
                break;
            }
        }
        if (pattern.length() > MAX_PATTERN_LENGTH || pattern.length() < MIN_PATTERN_LENGTH) {
            throw new BusinessException(ErrorCode.INVALID_LENGTH_PATTERN, "Invalid length invoice no!", null);
        }

        StringBuilder result = new StringBuilder();
        for (char character : pattern.toCharArray()) {
            if (RANDOM_CHAR == character) {
                result.append(RandomStringUtils.random(1, letters, numbers));
            } else {
                result.append(character);
            }
        }
        return result.toString();
    }

    @Override
    public MemberTransactionRes findMemberTransaction(Integer businessId, Integer programId, EOpsIdType idType, String idNo) {
        Assert.assertTrue(Objects.isNull(idType), ErrorCode.ID_TYPE_NOT_VALID);

        businessService.findActive(businessId);
        programService.findActive(programId);

        CustomerIdentify customerIdentify = new CustomerIdentify(idNo, idType.getMapping());
        Member member = memberService.findActive(customerIdentify, programId);

        Assert.assertTrue(Objects.isNull(member), ErrorCode.MEMBER_NOT_FOUND);

        return MemberTransactionRes
                .builder()
                .memberId(member.getId())
                .memberName(member.getFullName())
                .memberCode(member.getMemberCode())
                .phoneNo(member.getPhoneNo())
                .dob(member.getDob())
                .gender(member.getGender())
                .identifyType(nullSafer(member.getIdentifyType(), EIdentifyType::getCode))
                .identifyNo(member.getIdentifyNo())
                .address(member.getAddress())
                .email(member.getEmail())
                .status(member.getStatus())
                .build();
    }

    @Override
    @Transactional
    public Long createTransactionBatchRequestForMember(CreateTransactionMemberRequestReq req) {
        validate(req);

        TransactionBatchRequest batchRequest = buildTransactionBatchRequest(req);

        batchRequest = transactionBatchRequestService.save(batchRequest);

        TransactionRequest transactionRequest = buildTransactionRequest(req, batchRequest);

        transactionRequestService.save(transactionRequest);

        return batchRequest.getId();
    }

    private void validate(CreateTransactionMemberRequestReq req) {
        businessService.findActive(req.getBusinessId());
        programService.findActive(req.getProgramId());
        Store store = storeService.findActive(req.getStoreCode(), req.getBusinessId());
        corporationService.findActive(store.getCorporationId());
        chainService.findActive(store.getChainId());
        posService.findActive(store.getId(), req.getTerminalCode());

        validateMember(req.getIdType().getMapping(), req.getIdNo(), req.getProgramId());

        validateAttributeMap(req.getAttributeMap(), req.getProgramId());

        if(EGenerationInvoiceNoMethod.CUSTOM.equals(req.getGenInvoiceNoMethod())) {
            validateGenInvoiceNo(req.getGenInvoiceNoPattern());
        }

        Set<String> currencyCodeSet = getCurrencyCodeSetForTransaction(req.getBusinessId());

        String currencyCode = null;

        switch (req.getTransactionBatchType()) {
            case ADJUST:
                poolService.findActive(req.getAdjustData().getPoolCode(), req.getProgramId());
                reasonCodeService.findActive(req.getAdjustData().getReasonCode(), req.getProgramId());
                break;
            case EARN:
                currencyCode = req.getEarnData().getCurrencyCode();
                break;
            case BURN:
                currencyCode = req.getBurnData().getCurrencyCode();
                break;
            case SALE:
                currencyCode = req.getSaleData().getCurrencyCode();
                break;
        }

        Assert.assertTrue(
                Objects.nonNull(currencyCode) && !currencyCodeSet.contains(currencyCode),
                ErrorCode.CURRENCY_NOT_FOUND
        );
    }

    private Set<String> getCurrencyCodeSetForTransaction(Integer businessId) {

        return currencyRepository
                .findByBusinessId(businessId)
                .stream()
                .filter(currency -> ECommonStatus.ACTIVE.equals(currency.getStatus())
                        || Objects.nonNull(currency.getCode()))
                .map(Currency::getCode)
                .collect(Collectors.toSet());
    }

    private void validateMember(EIdType idType, String idNo, Integer programId) {
        List<EIdType> idTypes = programProductService.getActiveIdTypes(programId);

        Assert.assertTrue(!idTypes.contains(idType), ErrorCode.ID_TYPE_NOT_VALID);

        CustomerIdentify identify = new CustomerIdentify(idNo, idType);
        memberService.findActive(identify, programId);
    }

    private void validateGenInvoiceNo(String genInvoiceNoPattern) {
        char[] chars = genInvoiceNoPattern.toCharArray();
        int numberOfRandomCharacters = 0;

        Assert.assertTrue(
                chars.length < MIN_PATTERN_LENGTH || chars.length > MAX_PATTERN_LENGTH,
                ErrorCode.INVALID_LENGTH_PATTERN
        );

        for(char character : chars) {
            if(character == RANDOM_CHAR) {
                numberOfRandomCharacters++;
            } else {
                Assert.assertTrue(Character.isWhitespace(character), ErrorCode.INVALID_INVOICE_NO_PATTERN);
            }
        }

        Assert.assertTrue(numberOfRandomCharacters == 0, ErrorCode.INVALID_INVOICE_NO_PATTERN);
    }

    private void validateAttributeMap(Map<String, String> attributeMap, Integer programId) {
        Map<String, ProgramTransactionAttribute> tranAttrMap = programTransactionAttributeService
                .listAllByProgramId(programId)
                .stream()
                .filter(item -> Objects.nonNull(item.getAttribute()))
                .collect(Collectors.toMap(
                        ProgramTransactionAttribute::getAttribute,
                        programTransactionAttribute -> programTransactionAttribute,
                        (k1, k2)->k2));

        attributeMap.entrySet().stream().forEach(entry -> {
            ProgramTransactionAttribute tranAttr = tranAttrMap.get(entry.getKey());
            if(Objects.isNull(tranAttr)) {
                throw new BusinessException(ErrorCode.TRANSACTION_ATTRIBUTE_NOT_FOUND);
            } else {
                if(Objects.isNull(entry.getValue())) {
                    throw new BusinessException(ErrorCode.PROGRAM_TRANSACTION_ATTRIBUTE_NOT_VALID);
                } else {
                    if(ETransactionAttributeDataTypeDisplay.NUMBER.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        Long.valueOf(entry.getValue());
                    } else if (ETransactionAttributeDataTypeDisplay.DATE.getValue().equals(tranAttr.getDataTypeDisplay())
                            || ETransactionAttributeDataTypeDisplay.DATE_TIME.getValue().equals(tranAttr.getDataTypeDisplay())) {
                        Date.from(DateTime.parse(entry.getValue()).toDate().toInstant());
                    }
                }
            }
        });
    }

    private TransactionBatchRequest buildTransactionBatchRequest(CreateTransactionMemberRequestReq req) {
        TransactionBatchRequest batchRequest = new TransactionBatchRequest();
        batchRequest.setBusinessId(req.getBusinessId());
        batchRequest.setProgramId(req.getProgramId());
        batchRequest.setType(EBatchRequestType.MEMBER);
        batchRequest.setName(req.getBatchName());
        batchRequest.setCampaignCode(req.getCampaignCode());
        batchRequest.setReferenceCode(req.getReferenceCode());
        batchRequest.setDescription(req.getDescription());
        batchRequest.setIsReplaceDes(req.getIsReplaceDes());
        batchRequest.setGenInvoiceNoMethod(req.getGenInvoiceNoMethod());
        batchRequest.setGenInvoiceNoCharacterSet(req.getGenInvoiceNoCharacterSet());
        batchRequest.setGenInvoiceNoPattern(req.getGenInvoiceNoPattern());
        batchRequest.setGenTransactionTimeMethod(req.getGenTransactionTimeMethod());
        batchRequest.setGenTransactionTimeValue(req.getGenTransactionTimeValue());
        batchRequest.setTransactionBatchType(req.getTransactionBatchType());
        batchRequest.setSmsTemplate(req.getSmsTemplate());
        batchRequest.setTotalRequests(1);
        batchRequest.setPendingRequests(1);
        batchRequest.setTotalMember(1);
        batchRequest.setProcessStatus(EBatchRequestProcessStatus.PENDING);
        batchRequest.setApprovalStatus(EApprovalStatus.PENDING);
        batchRequest.setStatus(ECommonStatus.ACTIVE);
        batchRequest.setAttributeMap(JsonUtil.writeValueAsString(req.getAttributeMap()));
        return batchRequest;
    }

    private TransactionRequest buildTransactionRequest(CreateTransactionMemberRequestReq req, TransactionBatchRequest batchRequest) {
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;

        CustomerIdentify identify = new CustomerIdentify(req.getIdNo(), req.getIdType().getMapping());
        Member member = memberService.findActive(identify, req.getProgramId());

        TransactionRequest transactionRequest = new TransactionRequest();
        transactionRequest.setBusinessId(req.getBusinessId());
        transactionRequest.setProgramId(req.getProgramId());
        transactionRequest.setBatchRequestId(batchRequest.getId());
        transactionRequest.setMemberId(member.getId());
        transactionRequest.setStoreCode(req.getStoreCode());
        transactionRequest.setTerminalCode(req.getTerminalCode());
        transactionRequest.setIdType(req.getIdType().getMapping());
        transactionRequest.setIdNo(req.getIdNo());
        transactionRequest.setInvoiceNo(
                EGenerationInvoiceNoMethod.MANUAL.equals(req.getGenInvoiceNoMethod())
                        ? req.getInvoiceNo() : null
        );
        transactionRequest.setDescription(req.getDescription());
        transactionRequest.setProcessStatus(ERequestProcessStatus.PENDING);
        transactionRequest.setStatus(ECommonStatus.ACTIVE);

        switch (req.getTransactionBatchType()) {
            case EARN:
                CreateTransactionMemberRequestReq.EarnData earnData = req.getEarnData();
                transactionRequest.setGmv(earnData.getGmv());
                transactionRequest.setGrossAmount(earnData.getGrossAmount());
                transactionRequest.setCurrencyCode(earnData.getCurrencyCode());
                break;
            case BURN:
                CreateTransactionMemberRequestReq.BurnData burnData = req.getBurnData();
                transactionRequest.setRedeemPoint(burnData.getRedeemPoint());
                transactionRequest.setCurrencyCode(burnData.getCurrencyCode());

                totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(transactionRequest.getRedeemPoint())
                        ? transactionRequest.getRedeemPoint() : BigDecimal.ZERO);
                break;
            case SALE:
                CreateTransactionMemberRequestReq.SaleData saleData = req.getSaleData();
                transactionRequest.setGmv(saleData.getGmv());
                transactionRequest.setGrossAmount(saleData.getGrossAmount());
                transactionRequest.setRedeemPoint(saleData.getRedeemPoint());
                transactionRequest.setCurrencyCode(saleData.getCurrencyCode());

                totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(transactionRequest.getRedeemPoint())
                        ? transactionRequest.getRedeemPoint() : BigDecimal.ZERO);
                break;
            case ADJUST:
                CreateTransactionMemberRequestReq.AdjustData adjustData = req.getAdjustData();
                transactionRequest.setAdjustType(adjustData.getAdjustType());
                transactionRequest.setAdjustPoint(adjustData.getAdjustPoint());
                transactionRequest.setPoolCode(adjustData.getPoolCode());
                transactionRequest.setReasonCode(adjustData.getReasonCode());

                if (transactionRequest.getAdjustType() == EAdjustmentType.AWARD) {
                    totalAwardPoint = totalAwardPoint.add(transactionRequest.getAdjustPoint());
                } else {
                    totalRedeemPoint = totalRedeemPoint.add(transactionRequest.getAdjustPoint());
                }
                break;
        }

        batchRequest.setTotalGMV(transactionRequest.getGmv());
        batchRequest.setTotalGrossAmount(transactionRequest.getGrossAmount());
        batchRequest.setTotalAwardPoint(totalAwardPoint);
        batchRequest.setTotalRedeemPoint(totalRedeemPoint);
        transactionBatchRequestService.save(batchRequest);
        return transactionRequest;
    }

    @Override
    public Page<TransactionBatchRequestRes> filterTransaction(EApprovalStatus approvalStatus, String createdBy, Date createdStart, Date createdEnd, String approvedBy, Date approvedStart, Date approvedEnd, Pageable pageable) {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        approvedEnd = approvedEnd != null ? new Date(approvedEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;

        Page<Object[]> result = transactionBatchRequestRepository.filter(pageable, approvalStatus, createdBy, createdStart, createdEnd,
                approvedBy, approvedStart, approvedEnd, null, null, null, null, null,
                null, null, null, null, null, true
        );

        List<TransactionBatchRequestRes> transactionBatchRequestRes = result.stream().map(
                batch -> {
                    TransactionBatchRequest transactionBatchRequest = (TransactionBatchRequest) batch[0];
                    Business business = (Business) batch[1];
                    Program program = (Program) batch[2];

                    return TransactionBatchRequestRes.builder()
                            .batchNo(transactionBatchRequest.getId())
                            .batchName(transactionBatchRequest.getName())
                            .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .type(transactionBatchRequest.getType())
                            .createdBy(transactionBatchRequest.getCreatedBy())
                            .createdAt(transactionBatchRequest.getCreatedAt())
                            .approvedBy(transactionBatchRequest.getApprovedBy())
                            .approvedAt(transactionBatchRequest.getApprovedAt())
                            .approvalStatus(transactionBatchRequest.getApprovalStatus())
                            .rejectReason(transactionBatchRequest.getRejectReason())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(transactionBatchRequestRes, pageable, result.getTotalElements());
    }

    @Override
    public TransactionStatisticRes getTransactionStatisticById(Long requestId) {
        TransactionBatchRequest request = transactionBatchRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.TRANSACTION_REQUEST_NOT_FOUND));

        TransactionStatisticRes response = new TransactionStatisticRes();

        response.setTotal(request.getTotalRequests());
        response.setTotalSuccess(request.getSuccessRequests());
        response.setTotalFail(request.getFailedRequests());
        response.setTotalPending(request.getPendingRequests());
        response.setAwardSuccessPoint(request.getAwardSuccessPoint());
        response.setRedeemSuccessPoint(request.getRedeemSuccessPoint());
        return response;
    }

    @Override
    public ResourceDTO exportFileInReview(Long batchNo) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(batchNo);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        List<TransactionRequestRes> requests = new LinkedList<>();

        Pageable pageable = PageRequest.of(0, OPSConstant.SIZE_OF_BATCH, Sort.by("id").ascending());

        Page<TransactionRequestRes> page;

        while(true) {
            page = getInReviewTransactionRequests(batchNo, null, pageable);
            requests.addAll(page.getContent());
            if(page.hasNext()) {
                pageable = pageable.next();
            } else {
                break;
            }
        }

        EntryContext context = EntryContext
                .builder()
                .moduleId(OPSConstant.TXN)
                .objectId(OPSConstant.AVAILABLE_TXN)
                .build();

        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());

        String fileName = String.format(
                OPSConstant.FILE_NAME_EXPORT_TRANSACTION_BATCH,
                ddMMyyyy,
                batchRequest.getId(),
                business.getCode(),
                program.getCode()
        );

        List<TransactionExportEntry> data = requests
                .stream().map(res -> buildTransactionExportEntry(res, business, program))
                .collect(Collectors.toList());

        return commonExcelService.opsExport(context, fileName, data);
    }

    private TransactionExportEntry buildTransactionExportEntry(TransactionRequestRes res, Business business, Program program) {
        TransactionExportEntry entry = new TransactionExportEntry();
        entry.setTxnRefNo(res.getTxnRefNo());
        entry.setInvoiceNo(res.getInvoiceNo());
        entry.setOriginalInvoiceNo(res.getOriginalInvoiceNo());
        entry.setBusinessCode(business.getCode());
        entry.setProgramCode(program.getCode());
        entry.setCorporationCode(res.getCorporationCode());
        entry.setChainCode(res.getChainCode());
        entry.setStoreCode(res.getStoreCode());
        entry.setTerminalCode(res.getTerminalCode());
        entry.setMemberCode(res.getMemberCode());
        entry.setProductAccountType(res.getProductAccountType());
        entry.setProductAccountCode(res.getProductAccountCode());
        entry.setTransactionTime(nullSafer(res.getTransactionTime(), DateUtil::formatDD_MM_yy));
        entry.setTransactionType(res.getTransactionType());
        entry.setGmv(nullSafer(res.getGmv(), BigDecimal::longValue));
        entry.setGrossAmount(nullSafer(res.getGrossAmount(), BigDecimal::longValue));
        entry.setNettAmount(nullSafer(res.getNettAmount(), BigDecimal::longValue));
        entry.setAwardPoint(nullSafer(res.getAwardPoint(), BigDecimal::longValue));
        entry.setRedeemPoint(nullSafer(res.getRedeemPoint(), BigDecimal::longValue));
        entry.setPoolCode(res.getPoolCode());
        entry.setCurrencyCode(res.getCurrencyCode());
        entry.setPointBalanceBefore(nullSafer(res.getPointBalanceBefore(), BigDecimal::longValue));
        entry.setPointBalanceAfter(nullSafer(res.getPointBalanceAfter(), BigDecimal::longValue));
        entry.setAwardPointBeforeLimit(nullSafer(res.getAwardPointBeforeLimit(), BigDecimal::longValue));
        entry.setAwardRetentionTime(nullSafer(res.getAwardRetentionTime(), DateUtil::formatDD_MM_yy));
        entry.setDescription(res.getDescription());
        entry.setReasonCode(res.getReasonCode());
        entry.setSchemeCode(res.getSchemeCode());
        entry.setChannel(res.getChannel());
        entry.setServiceCode(res.getServiceCode());
        entry.setStatus(nullSafer(res.getProcessStatus(), ERequestProcessStatus::getValue));

        return entry;
    }

    private TransactionExportEntry buildTransactionExportEntryAvailable(TransactionRequestRes res, Business business, Program program) {
        TransactionExportEntry entry = this.buildTransactionExportEntry(res, business, program);
        entry.setCancellation(res.isCancellation() ? EBoolean.YES.getValue() : EBoolean.NO.getValue());
        entry.setCancellationTime(nullSafer(res.getCancellationTime(), DateUtil::formatDD_MM_yy));
        entry.setCancellationType(res.getCancellationType());
        entry.setErrorCode(nullSafer(res.getErrorCode(), String::valueOf));
        entry.setErrorMessage(res.getErrorMessage());
        return entry;
    }

    @Override
    public ResourceDTO exportFileAvailable(Long batchNo) {
        Optional<TransactionBatchRequest> optBatchRequest = transactionBatchRequestService.find(batchNo);
        if (optBatchRequest.isEmpty() || !EApprovalStatus.APPROVED.equals(optBatchRequest.get().getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_BATCH_REQUEST_NOT_FOUND,
                    "Transaction batch request not found", null);
        }
        TransactionBatchRequest batchRequest = optBatchRequest.get();
        List<TransactionRequestRes> requests = new LinkedList<>();

        Pageable pageable = PageRequest.of(0, OPSConstant.SIZE_OF_BATCH, Sort.by("id").ascending());

        Page<TransactionRequestRes> page;

        while(true) {
            page = getAvailableTransactionRequests(batchNo, null, pageable);
            requests.addAll(page.getContent());
            if(page.hasNext()) {
                pageable = pageable.next();
            } else {
                break;
            }
        }

        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.TXN)
                .objectId(OPSConstant.AVAILABLE_TXN)
                .build();

        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findByIdAndBusinessId(batchRequest.getProgramId(), batchRequest.getBusinessId());
        String ddMMyyyy = DateUtil.formatToStringDDMMYYYY(new Date());
        String fileName = String.format(OPSConstant.FILE_NAME_EXPORT_TRANSACTION_BATCH,
                ddMMyyyy, batchRequest.getId(), business.getCode(), program.getCode());

        List<TransactionExportEntry> data = requests
                .stream().map(res -> buildTransactionExportEntryAvailable(res, business, program))
                .collect(Collectors.toList());

        return commonExcelService.opsExport(context, fileName, data);
    }

    private List<? extends ActionTransactionExcelDTO> mappingToExcelDto(ETransactionBatchType type, MultipartFile file) throws Exception {
        return Poiji.fromExcel(file.getInputStream(), PoijiExcelType.XLSX, detectClz(type));
    }

    private Class<? extends ActionTransactionExcelDTO> detectClz(ETransactionBatchType type) {
        switch (type) {
            case ADJUST:
                return AdjustTransactionExcelDTO.class;
            case REVERT_FULL:
                return RevertFullTransactionExcelDTO.class;
            case REVERT_PARTIAL:
                return RevertPartialTransactionExcelDTO.class;
            default: // SALE | EARN | BURN
                return EarnBurnSaleTransactionExcelDTO.class;
        }
    }

    @Transactional
    public Long saveTransaction(CreateTransactionBatchRequestReq req, List<ActionTransactionExcelDTO> dtos, EBatchRequestType type) throws Exception {
        int totalMember = 0;
        BigDecimal totalGMV = BigDecimal.ZERO;
        BigDecimal totalGrossAmount = BigDecimal.ZERO;
        BigDecimal totalAwardPoint = BigDecimal.ZERO;
        BigDecimal totalRedeemPoint = BigDecimal.ZERO;
        Set<Long> uniqueMember = new HashSet<>();

        // Create transaction batch request
        TransactionBatchRequest batchRequest = this.saveTransactionBatchRequest(req, type, dtos);
        // Create transaction request by batch request
        List<TransactionRequest> listRequest = new ArrayList<>();
        for (ActionTransactionExcelDTO dto : dtos) {
            TransactionRequest request = new TransactionRequest();
            request.setBusinessId(req.getBusinessId());
            request.setProgramId(req.getProgramId());
            request.setIdType(EOpsIdType.lookup(dto.getIdType()).getMapping());
            request.setIdNo(dto.getIdNo());
            request.setInvoiceNo(dto.getInvoiceNo());
            request.setMemberId(dto.getMemberId());

            // Convert transaction time
            Date transactionTime = parseTxnTime(dto.getTransactionTime());
            request.setTransactionTime(transactionTime);

            request.setStoreCode(dto.getStoreCode());
            request.setTerminalCode(dto.getTerminalCode());
            // Fill description when field IsReplaceDes is YES
            if (req.getIsReplaceDes() != null && EBoolean.YES.equals(req.getIsReplaceDes()) && StringUtils.isBlank(dto.getDescription()) ) {
                request.setDescription(req.getDescription());
            } else {
                request.setDescription(dto.getDescription());
            }

            String serviceCode;
            if (Objects.isNull(dto.getServiceCode())) {
                serviceCode = defaultTransactionServiceCode;
            } else {
                serviceCode = dto.getServiceCode();
            }

            String channel;
            if (Objects.isNull(dto.getChannel())) {
                channel = defaultTransactionChannel;
            } else {
                channel = dto.getChannel();
            }

            switch (req.getTransactionBatchType()) {
                case EARN:
                case BURN:
                case SALE: {
                    EarnBurnSaleTransactionExcelDTO newDto = (EarnBurnSaleTransactionExcelDTO) dto;
                    request.setGmv(newDto.getGmv());
                    request.setGrossAmount(newDto.getGrossAmount());
                    request.setRedeemPoint(newDto.getRedeemPoint());
                    request.setCurrencyCode(newDto.getCurrencyCode());
                    request.setServiceCode(serviceCode);
                    request.setChannel(channel);


                    totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getRedeemPoint())
                            ? request.getRedeemPoint() : BigDecimal.ZERO);

                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv()) ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case ADJUST: {
                    AdjustTransactionExcelDTO newDto = (AdjustTransactionExcelDTO) dto;
                    request.setAdjustType(EAdjustmentType.of(newDto.getAdjustType()));
                    request.setAdjustPoint(newDto.getAdjustPoint());
                    request.setPoolCode(newDto.getPoolCode());
                    request.setReasonCode(newDto.getReasonCode());
                    request.setServiceCode(serviceCode);
                    request.setChannel(channel);

                    if (EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                        totalAwardPoint = totalAwardPoint.add(Objects.nonNull(request.getAdjustPoint())
                                ? request.getAdjustPoint() : BigDecimal.ZERO);
                    } else {
                        totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getAdjustPoint())
                                ? request.getAdjustPoint() : BigDecimal.ZERO);
                    }

                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv()) ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case REVERT_FULL: {
                    RevertFullTransactionExcelDTO newDto = (RevertFullTransactionExcelDTO) dto;
                    convertRevertFull(request, newDto);

                    List<TransactionHistory> transactionHistories = transactionHistoryService
                            .getByTnxPointIds(Collections.singletonList(newDto.getTxnRef()));
                    for (TransactionHistory item : transactionHistories) {
                        totalAwardPoint = totalAwardPoint.add(Objects.nonNull(item.getAwardPoint())
                                ? item.getAwardPoint() : BigDecimal.ZERO);
                        totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(item.getRedeemPoint())
                                ? item.getRedeemPoint() : BigDecimal.ZERO);
                    }

                    TransactionHistory transactionHistory = transactionHistories.get(0);
                    totalGMV = totalGMV.add(Objects.nonNull(transactionHistory.getGmv())
                            ? transactionHistory.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(transactionHistory.getGrossAmount())
                            ? transactionHistory.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                case REVERT_PARTIAL: {
                    RevertPartialTransactionExcelDTO newDto = (RevertPartialTransactionExcelDTO) dto;
                    convertRevertFull(request, newDto);
                    request.setRefundAmount(newDto.getRefundAmount());
                    request.setRedeemPoint(newDto.getRedeemPoint());
                    request.setGrossAmount(newDto.getGrossAmount());
                    request.setServiceCode(serviceCode);
                    request.setChannel(channel);

                    totalRedeemPoint = totalRedeemPoint.add(Objects.nonNull(request.getRedeemPoint())
                            ? request.getRedeemPoint() : BigDecimal.ZERO);
                    totalGMV = totalGMV.add(Objects.nonNull(request.getGmv())
                            ? request.getGmv() : BigDecimal.ZERO);
                    totalGrossAmount = totalGrossAmount.add(Objects.nonNull(request.getGrossAmount())
                            ? request.getGrossAmount() : BigDecimal.ZERO);
                    break;
                }
                default:
                    throw new OpsBusinessException(OpsErrorCode.TRANSACTION_TYPE_NOT_SUPPORT, "Transaction type not support", null);
            }
            request.setBatchRequestId(batchRequest.getId());
            request.setStatus(ECommonStatus.ACTIVE);
            request.setProcessStatus(ERequestProcessStatus.PENDING);

            listRequest.add(request);

            uniqueMember.add(request.getMemberId());
            totalMember = uniqueMember.size();
        }
        transactionRequestService.saveAll(listRequest);

        batchRequest.setTotalMember(totalMember);
        batchRequest.setTotalGMV(totalGMV);
        batchRequest.setTotalGrossAmount(totalGrossAmount);
        batchRequest.setTotalAwardPoint(totalAwardPoint);
        batchRequest.setTotalRedeemPoint(totalRedeemPoint);

        transactionBatchRequestService.save(batchRequest);
        return batchRequest.getId();
    }

    private <V extends RevertFullTransactionExcelDTO> void convertRevertFull(TransactionRequest request, V newDto) {
        request.setOriginalInvoiceNo(newDto.getOriginalInvoiceNo());
        request.setCurrencyCode(newDto.getCurrencyCode());
        request.setStoreCode(newDto.getStoreCode());
        request.setTerminalCode(newDto.getTerminalCode());
        request.setTxnRefNo(newDto.getTxnRef());
    }

    public TransactionBatchRequest saveTransactionBatchRequest(CreateTransactionBatchRequestReq req,
                                                               EBatchRequestType type,
                                                               List<ActionTransactionExcelDTO> dtos){
        TransactionBatchRequest batchRequest = new TransactionBatchRequest();
        batchRequest.setBusinessId(req.getBusinessId());
        batchRequest.setProgramId(req.getProgramId());
        batchRequest.setType(type);
        batchRequest.setName(req.getBatchName());
        batchRequest.setCampaignCode(req.getCampaignCode());
        batchRequest.setReferenceCode(req.getReferenceCode());
        batchRequest.setIsReplaceDes(req.getIsReplaceDes());
        batchRequest.setDescription(req.getDescription());
        batchRequest.setTransactionBatchType(req.getTransactionBatchType());
        batchRequest.setSmsTemplate(req.getSmsTemplate());

        batchRequest.setGenInvoiceNoMethod(req.getGenInvoiceNoMethod());
        batchRequest.setGenInvoiceNoCharacterSet(req.getGenInvoiceNoCharacterSet());
        batchRequest.setGenInvoiceNoPattern(req.getGenInvoiceNoPattern());

        batchRequest.setGenTransactionTimeMethod(req.getGenTransactionTimeMethod());
        batchRequest.setGenTransactionTimeValue(req.getGenTransactionTimeValue());

        batchRequest.setStatus(ECommonStatus.ACTIVE);
        batchRequest.setProcessStatus(EBatchRequestProcessStatus.PENDING);
        batchRequest.setApprovalStatus(EApprovalStatus.PENDING);

        batchRequest.setTotalRequests(dtos.size());
        batchRequest.setPendingRequests(dtos.size());

        return transactionBatchRequestService.save(batchRequest);
    }

    private boolean verifyCreateTransaction(CreateTransactionBatchRequestReq req,
                                            List<ActionTransactionExcelDTO> dtos) {
        boolean isValid = true;
        if (dtos.isEmpty() || dtos.size() > batchAdjMaxRecord) {
            return false;
        }
        Set<String> storeCodeSet = new HashSet<>();
        Set<String> terminalCodeSet = new HashSet<>();
        Set<Integer> chainIdSet = new HashSet<>();
        for(ActionTransactionExcelDTO dto : dtos) {
            storeCodeSet.add(dto.getStoreCode());
            terminalCodeSet.add(dto.getTerminalCode());
        }
        // Declare map
        Function function = functionService.findByCode(EOpsFunctionCode.POINT_ADJ.getValue());
        Map<String, Integer> reasonCodeMap = reasonCodeService.findByProgramId(req.getProgramId())
                .stream()
                .filter(i -> Objects.nonNull(i.getCode()) && ECommonStatus.ACTIVE.equals(i.getStatus()))
                .collect(Collectors.toMap(ReasonCode::getCode, ReasonCode::getFunctionId, (k1, k2) -> k1));
        Map<String, Integer> poolCodeMap = poolService.findAllByProgramIdAndStatus(req.getProgramId(), ECommonStatus.ACTIVE)
                .stream()
                .collect(Collectors.toMap(Pool::getCode, Pool::getId, (k1, k2) -> k1));
        Map<String, Store> storeMap = storeService.findActiveByProgramIdAndListStoreCode(req.getProgramId(), storeCodeSet)
                .stream()
                .peek(i -> chainIdSet.add(i.getChainId()))
                .collect(Collectors.toMap(Store::getCode, val -> val, (k1, k2) -> k1));
        Set<Integer> programCorporationSet = programCorporationService.findAllByProgramId(req.getProgramId())
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .map(ProgramCorporation::getCorporationId)
                .collect(Collectors.toSet());
        Set<Integer> chainIdActiveSet = chainService.findByIdIn(chainIdSet)
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .map(Chain::getId)
                .collect(Collectors.toSet());
        Map<String, Integer> posActiveMap = posService.findActiveByProgramIdAndListTerminalCode(req.getProgramId(), terminalCodeSet)
                .stream()
                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                .collect(Collectors.toMap(Pos::getCode, Pos::getStoreId, (k1, k2) -> k1));
        Map<String, Integer> invoiceNoMap = new ConcurrentHashMap<>();
        // Verify data
        Business business = businessService.findActive(req.getBusinessId());
        Program program = programService.findByIdAndBusinessId(req.getProgramId(), business.getId());
        Set<String> setCurrencyCode = this.getCurrencyCodeSetForTransaction(req.getBusinessId());
        Set<Boolean> result = dtos
                .parallelStream()
                .map(dto -> verify(req, dto, reasonCodeMap, function, poolCodeMap, program,
                        setCurrencyCode, storeMap, programCorporationSet, chainIdActiveSet, posActiveMap, invoiceNoMap)
                )
                .collect(Collectors.toSet());
        return !result.contains(false);
    }

    private boolean verify(
            CreateTransactionBatchRequestReq req,
            ActionTransactionExcelDTO dto,
            Map<String, Integer> reasonCodeMap,
            Function function,
            Map<String, Integer> poolCodeMap,
            Program program,
            Set<String> setCurrencyCode,
            Map<String, Store> storeMap,
            Set<Integer> programCorporationSet,
            Set<Integer> chainIdActiveSet,
            Map<String, Integer> posActiveMap,
            Map<String, Integer> invoiceNoMap
    ) {
        try{
            dto.setIsValid(true);
            boolean isRevert = false;
            TransactionHistory transactionHistory = null;
            if(ETransactionBatchType.ADJUST.equals(req.getTransactionBatchType())) {
                verifyDuplicateInvoiceNo(dto, invoiceNoMap);
                verifyBaseActionExcel(req, dto);
                AdjustTransactionExcelDTO newDto = (AdjustTransactionExcelDTO) dto;
                Assert.assertTrue(newDto.getAdjustPoint() == null || newDto.getAdjustPoint().compareTo(BigDecimal.ZERO) <= 0, "Adjust point must greater than zero");
                Assert.assertTrue(Objects.isNull(reasonCodeMap.get(newDto.getReasonCode())), "Reason Code not found");
                Integer functionId = reasonCodeMap.get(newDto.getReasonCode());
                Assert.assertTrue(!Objects.equals(functionId, function.getId()), "Reason Code is invalid");
                Assert.assertNotNull(poolCodeMap.get(newDto.getPoolCode()), "Pool code not found");
                Assert.assertNotNull(EAdjustmentType.of(newDto.getAdjustType()), "Invalid adjust type");
            } else {
                if (ETransactionBatchType.REVERT_FULL.equals(req.getTransactionBatchType())) {
                    transactionHistory = verifyRevertFull(program, (RevertFullTransactionExcelDTO) dto);
                    isRevert = true;
                } else {
                    verifyDuplicateInvoiceNo(dto, invoiceNoMap);
                    if (ETransactionBatchType.REVERT_PARTIAL.equals(req.getTransactionBatchType())) {
                        transactionHistory = verifyRevertPartial(req, program, (RevertPartialTransactionExcelDTO) dto);
                        ((RevertPartialTransactionExcelDTO) dto).setGrossAmount(transactionHistory.getGrossAmount().subtract(((RevertPartialTransactionExcelDTO) dto).getRefundAmount()));
                        isRevert = true;
                    } else {
                        EarnBurnSaleTransactionExcelDTO newDto = (EarnBurnSaleTransactionExcelDTO) dto;
                        verifyBaseActionExcel(req, dto);
                        verifyCurrencyCode(newDto, setCurrencyCode);
                        switch (req.getTransactionBatchType()) {
                            case EARN:
                                verifyTransactionByEarn(newDto);
                                break;
                            case BURN:
                                verifyTransactionByBurn(newDto);
                                break;
                            case SALE:
                                verifyTransactionBySale(newDto);
                                break;
                        }
                    }
                }
            }

            if(isRevert) {
                dto.setMemberId(transactionHistory.getMemberId());
                dto.setIdType(nullSafer(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()), EOpsIdType::getValue));
                dto.setIdNo(transactionHistory.getMemberProductAccountCode());
            } else {
                EOpsIdType idType = EOpsIdType.lookup(dto.getIdType());
                Assert.assertNotNull(idType, "Invalid Identification Type");
                CustomerIdentify identify = new CustomerIdentify();
                identify.setIdType(idType.getMapping());
                identify.setId(dto.getIdNo());
                Member member = memberService.findActive(identify, req.getProgramId());
                dto.setMemberId(member.getId());

                // Check Store Code
                if (Objects.nonNull(dto.getStoreCode())) {
                    Assert.assertNotNull(storeMap.get(dto.getStoreCode()),
                            "Store code not found");
                    Store store = storeMap.get(dto.getStoreCode());
                    // Check Program Corporation
                    Assert.assertTrue(!programCorporationSet.contains(store.getCorporationId()),
                            "Program corporation not found");
                    // Check Chain
                    Assert.assertTrue(!chainIdActiveSet.contains(store.getChainId()),
                            "Chain not found");
                    // Check Pos
                    Assert.assertTrue(Objects.isNull(posActiveMap.get(dto.getTerminalCode())) ||
                                    !posActiveMap.get(dto.getTerminalCode()).equals(store.getId()),
                            "Terminal (POS) not found");
                }
            }

            parseTxnTime(dto.getTransactionTime());

        } catch (Exception e) {
            dto.setErrorMessage(e.getMessage());
            dto.setIsValid(false);
            dto.setStatus(OPSConstant.INVALID);
        }
        return dto.getIsValid();
    }

    private <V extends ActionTransactionExcelDTO> void verifyBaseActionExcel(CreateTransactionBatchRequestReq req, V dto) {
        verifyStorePos(dto);
        verifyTxnTime(req.getGenTransactionTimeMethod(), dto);
        verifyInvoiceNo(req.getGenInvoiceNoMethod(), dto);
    }

    private void verifyTransactionByEarn(EarnBurnSaleTransactionExcelDTO newDto) {
        Assert.assertTrue(Objects.isNull(newDto.getGmv()) ||
                BigDecimal.ZERO.compareTo(newDto.getGmv()) >= 0, "Gmv is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getGrossAmount()) ||
                BigDecimal.ZERO.compareTo(newDto.getGrossAmount()) >= 0, "Gross amount is invalid");
        Assert.assertTrue(Objects.nonNull(newDto.getRedeemPoint()), "Redeem point don't need");
    }

    private void verifyTransactionByBurn(EarnBurnSaleTransactionExcelDTO newDto){
        Assert.assertTrue(Objects.isNull(newDto.getRedeemPoint()) ||
                BigDecimal.ZERO.compareTo(newDto.getRedeemPoint()) >= 0, "Redeem point is invalid");
        Assert.assertTrue(Objects.nonNull(newDto.getGmv()), "Gmv don't need");
        Assert.assertTrue(Objects.nonNull(newDto.getGrossAmount()), "Gross amount don't need");
    }

    private void verifyTransactionBySale(EarnBurnSaleTransactionExcelDTO newDto){
        Assert.assertTrue(Objects.isNull(newDto.getGmv()) ||
                BigDecimal.ZERO.compareTo(newDto.getGmv()) >= 0, "Gmv is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getGrossAmount()) ||
                BigDecimal.ZERO.compareTo(newDto.getGrossAmount()) >= 0, "Gross amount is invalid");
        Assert.assertTrue(Objects.isNull(newDto.getRedeemPoint()) ||
                BigDecimal.ZERO.compareTo(newDto.getRedeemPoint()) >= 0, "Redeem point is invalid");
    }

    private <V extends ActionTransactionExcelDTO> void verifyStorePos(V dto) {
        Assert.assertNotNull(dto.getStoreCode(), "Store code must not be null");
        Assert.assertNotNull(dto.getTerminalCode(), "Terminal code must not be null");
    }

    private <V extends ActionTransactionExcelDTO> void verifyTxnTime(EGenerationTransactionTimeMethod method, V dto) {
        Assert.assertTrue(EGenerationTransactionTimeMethod.MANUAL.equals(method) && StringUtils.isBlank(dto.getTransactionTime()),
                "Invalid transaction time");
    }

    private <V extends ActionTransactionExcelDTO> void verifyInvoiceNo(EGenerationInvoiceNoMethod method, V dto) {
        Assert.assertTrue(EGenerationInvoiceNoMethod.MANUAL.equals(method) && StringUtils.isBlank(dto.getInvoiceNo()),
                "Invalid invoice no");
    }

    private <V extends ActionTransactionExcelDTO> void verifyDuplicateInvoiceNo(V dto, Map<String, Integer> invoiceNoMap){
        if(StringUtils.isNotBlank(dto.getInvoiceNo())){
            Integer rowIndex = invoiceNoMap.get(dto.getInvoiceNo());
            Assert.assertTrue(Objects.nonNull(rowIndex), "Duplicate invoice no row " + rowIndex);
            invoiceNoMap.put(dto.getInvoiceNo(), dto.getRowIndex() + 1);
        }
    }

    private <V extends ActionTransactionExcelDTO> void verifyCurrencyCode(EarnBurnSaleTransactionExcelDTO dto, Set<String> setCurrencyCode){
        Assert.assertTrue( StringUtils.isBlank(dto.getCurrencyCode()), "Invalid currency code");
        String currencyCode = dto.getCurrencyCode().trim();
        Assert.assertTrue( !setCurrencyCode.contains(currencyCode), "Invalid currency code");
        dto.setCurrencyCode(currencyCode);
    }

    private static Date parseTxnTime(String txnTime) {
        if (Objects.nonNull(txnTime)) {
            // Check transaction time
            try {
                SimpleDateFormat FORMATTER_dd_MM_YYYY = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                return FORMATTER_dd_MM_YYYY.parse(txnTime);
            } catch (Exception e) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Invalid transaction time", null);
            }
        }
        return null;
    }

    private TransactionHistory verifyRevertPartial(CreateTransactionBatchRequestReq req, Program program, RevertPartialTransactionExcelDTO dto) {
        verifyBaseActionExcel(req, dto);
        Assert.assertNotNull(dto.getRefundAmount(), "Refund amount must not be null");
        Assert.assertNotNull(dto.getRedeemPoint(), "Redeem point must not be null");
        return verifyRevertFull(program, dto);
    }

    private <V extends RevertFullTransactionExcelDTO> TransactionHistory verifyRevertFull(Program program, V dto) {
        Assert.assertNotNull(dto.getTxnRef(), "Transaction id must not be null");
        Assert.assertNotNull(dto.getOriginalInvoiceNo(), "Original invoice no must not be null");
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(dto.getTxnRef()));
        if (transactionHistories.isEmpty()) {
            throw new OpsBusinessException(OpsErrorCode.TRANSACTION_NOT_FOUND, "Transaction not found", null);
        }

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);

        EOpsTransactionType transactionType = getTransactionType(points);
        if (EOpsTransactionType.ADJUSTMENT.equals(transactionType)) {
            throw new UnsupportedOperationException("Unsupported reverting adjustment transaction type!");
        }

        TransactionHistory transaction = transactionHistories.get(0);
        if (!dto.getOriginalInvoiceNo().equals(transaction.getInvoiceNo())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Original invoice no is invalid", null);
        }

        Store store;
        if(Objects.nonNull(dto.getStoreCode())) {
            store = storeService.findActive(dto.getStoreCode(), transaction.getBusinessId());
        } else {
            store = storeService.findActive(transaction.getStoreId());
        }
        dto.setStoreCode(store.getCode());

        Pos terminal;
        if(Objects.nonNull(dto.getTerminalCode())) {
            terminal = posService.findActive(store.getId(), dto.getTerminalCode());
        } else {
            terminal = posService.findActive(transaction.getPosId());
        }
        dto.setTerminalCode(terminal.getCode());

        Currency baseCurrency = getBaseCurrencyFromTransaction(transaction, program, transactionType);
        if (Objects.nonNull(baseCurrency)) {
            dto.setCurrencyCode(baseCurrency.getCode());
        }
        return transaction;
    }

    private String detectObj(ETransactionBatchType type) {
        switch (type) {
            case ADJUST:
                return OPSConstant.VERIFY_TXN_ADJ;
            case REVERT_FULL:
                return OPSConstant.VERIFY_TXN_REVERT_FULL;
            case REVERT_PARTIAL:
                return OPSConstant.VERIFY_TXN_REVERT_PARTIAL;
            default: // SALE | EARN | BURN
                return OPSConstant.VERIFY_TXN_SALE;
        }
    }

    private ResourceDTO exportFileExcel(ETransactionBatchType type, String fileName, List<ActionTransactionExcelDTO> dtos) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.TXN)
                .objectId(detectObj(type))
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    @Override
    public TransactionBatchRequestDetailRes approveBatchRequest(ApprovalReq req) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(req.getId());
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        if(EApprovalStatus.APPROVED.equals(req.getStatus())) {
            validateApproved(batchRequest);

            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.APPROVED);
            batchRequest.setProcessStatus(EBatchRequestProcessStatus.PROCESSING);
            batchRequest = transactionBatchRequestService.save(batchRequest);

            // Call master worker to do transaction request
            try {
                MasterWorkerTransactionRequestFeignReq feignRequest = MasterWorkerTransactionRequestFeignReq
                        .builder()
                        .batchRequestId(batchRequest.getId())
                        .build();

                APIResponse<?> apiResponse = masterWorkerFeignClient.requestTransactions(feignRequest);

                if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
                    Log.info(LogData.createLogData()
                            .append("msg", "Call to master/worker transaction request successfully")
                            .append("Approve transaction batch request ", batchRequest.getId())
                    );
                } else {
                    Log.error(LogData.createLogData()
                            .append("msg", "Error call to master/worker transaction request")
                            .append("batch request id ", batchRequest.getId())
                            .append("error code ", apiResponse.getMeta().getCode())
                            .append("error message ", apiResponse.getMeta().getMessage())
                    );
                }
            } catch (Exception e) {
                Log.error(LogData.createLogData()
                        .append("msg", "Error call to master/worker transaction request")
                        .append("batch request id ", batchRequest.getId())
                        .append("error message ", e.getMessage())
                );
            }
            batchRequest = transactionBatchRequestService.findActive(req.getId());

        } else if(EApprovalStatus.REJECTED.equals(req.getStatus())) {
            validateRejected(batchRequest);
            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.REJECTED);
            batchRequest.setRejectReason(req.getComment());
            batchRequest = transactionBatchRequestService.save(batchRequest);

        } else {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "EApprovalStatus mismatch ", req.getStatus());
        }

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .rejectReason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateApproved(TransactionBatchRequest batchRequest) {
        if(!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if(!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID);
        }
    }

    private void validateRejected(TransactionBatchRequest batchRequest) {
        if(!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if(!EBatchRequestProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID);
        }
    }

    private <I, V> V nullSafer(I value, org.springframework.cglib.core.internal.Function<I, V> executorFunction) {
        return value != null ? executorFunction.apply(value) : null;
    }

    @Override
    public TransactionBatchRequestDetailRes retryBatchRequest(Long id) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService.findActive(id);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());
        validateRetry(batchRequest);

        MasterWorkerTransactionRequestFeignReq feignRequest = MasterWorkerTransactionRequestFeignReq
                .builder()
                .batchRequestId(batchRequest.getId())
                .build();

        APIResponse<?> apiResponse = masterWorkerFeignClient.requestTransactions(feignRequest);

        if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
            Log.info(LogData.createLogData()
                    .append("msg", "Call to master/worker transaction request successfully")
                    .append("Retry transaction batch request ", batchRequest.getId())
            );
        } else {
            throw new BusinessException(apiResponse.getMeta().getCode(), apiResponse.getMeta().getMessage(), null);
        }
        batchRequest = transactionBatchRequestService.findActive(id);

        return TransactionBatchRequestDetailRes
                .builder()
                .batchNo(batchRequest.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchRequestType(batchRequest.getType())
                .transactionBatchType(batchRequest.getTransactionBatchType())
                .batchName(batchRequest.getName())
                .campaignCode(batchRequest.getCampaignCode())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .isReplaceDes(batchRequest.getIsReplaceDes())
                .rejectReason(batchRequest.getRejectReason())
                .smsTemplate(batchRequest.getSmsTemplate())
                .genInvoiceNoMethod(batchRequest.getGenInvoiceNoMethod())
                .genInvoiceNoCharacterSet(batchRequest.getGenInvoiceNoCharacterSet())
                .genInvoiceNoPattern(batchRequest.getGenInvoiceNoPattern())
                .genTransactionTimeMethod(batchRequest.getGenTransactionTimeMethod())
                .genTransactionTimeValue(batchRequest.getGenTransactionTimeValue())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateRetry(TransactionBatchRequest batchRequest) {
        if(!EApprovalStatus.APPROVED.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID);
        }
        if(batchRequest.getSuccessRequests().equals(batchRequest.getTotalRequests())) {
            throw new BusinessException(ErrorCode.TRANSACTION_REQUEST_COMPLETELY_FINISHED);
        }
    }

    @Override
    public String createSapSaleOrder(TransactionSapSaleOrderCreateReq req) {
        TransactionBatchRequest batchRequest = transactionBatchRequestService
                .findActive(req.getBatchRequestId());

        Assert.assertTrue(
                !EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus()),
                ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID
        );

        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(batchRequest.getId(), SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(!sapSaleOrderCalls.isEmpty(), ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER);

        BigDecimal point = batchRequest.getAwardSuccessPoint().add(batchRequest.getRedeemSuccessPoint());

        SAPSaleOrderCreateFeignReq feignRequest = SAPSaleOrderCreateFeignReq
                .builder()
                .customer(req.getSapCustomer())
                .postingDate(FORMATTER_yyyyMMdd.format(req.getPostingDate()))
                .remark(req.getRemark())
                .point(point.toString())
                .build();

        SAPSaleOrderCreateFeignRes apiResponse = sapFeignClient.createSaleOrder(feignRequest);

        SapSaleOrderCall sapSaleOrderCall = new SapSaleOrderCall();

        if (Objects.nonNull(apiResponse) && Objects.nonNull(apiResponse.getData())) {
            if(SAP_API_STATUS_SUCCESS.equals(apiResponse.getData().getStatus())) {
                Log.info(LogData.createLogData()
                        .append("msg", "Call SAP to create sale order successfully")
                        .append("Transaction batch request ", req.getBatchRequestId())
                        .append("Sale order ", apiResponse.getData().getSaleOrder())
                );
                sapSaleOrderCall.setBusinessId(batchRequest.getBusinessId());
                sapSaleOrderCall.setProgramId(batchRequest.getProgramId());
                sapSaleOrderCall.setBatchRequestId(batchRequest.getId());
                sapSaleOrderCall.setSaleOrder(apiResponse.getData().getSaleOrder());
                sapSaleOrderCall.setCustomer(req.getSapCustomer());
                sapSaleOrderCall.setPoint(point);
                sapSaleOrderCall.setRemark(req.getRemark());
                sapSaleOrderCall.setPostingDate(req.getPostingDate());
                sapSaleOrderCall.setCallType(SAP_CALL_TYPE_CREATE);
                sapSaleOrderCall.setCallStatus(SAP_CALL_STATUS_SUCCESS);
                sapSaleOrderCall.setCallResponse(new Gson().toJson(apiResponse.getData()));
                sapSaleOrderCall = sapSaleOrderCallRepository.save(sapSaleOrderCall);
            } else {
                throw new BusinessException(
                        ErrorCode.SAP_SALE_ORDER_DATA_NOT_VALID, null, null,
                        new Object[]{apiResponse.getData().getDescription()}
                );
            }
        } else {
            throw new BusinessException(ErrorCode.SYSTEM_CALL_SAP_ERROR);
        }
        return sapSaleOrderCall.getSaleOrder();
    }

    @Override
    public String updateSapSaleOrder(Long batchRequestId, TransactionSapSaleOrderUpdateReq req) {
        final String DEFAULT_TYPE = "01";

        TransactionBatchRequest batchRequest = transactionBatchRequestService
                .findActive(batchRequestId);

        Assert.assertTrue(
                !EBatchRequestProcessStatus.COMPLETED.equals(batchRequest.getProcessStatus()),
                ErrorCode.TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID
        );

        List<SapSaleOrderCall> sapSaleOrderCallsOfBatchRequest = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(batchRequest.getId(), SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(!sapSaleOrderCallsOfBatchRequest.isEmpty(), ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER);

        //Find newestSapSaleOrderCall
        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(req.getSapSaleOrder(), SAP_CALL_STATUS_SUCCESS);

        sapSaleOrderCalls.forEach(item -> Assert.assertTrue(
                batchRequestId.equals(item.getBatchRequestId()),
                ErrorCode.ALREADY_HAVE_SAP_SALE_ORDER)
        );

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall newestSapSaleOrderCall = sapSaleOrderCalls.get(0);

        BigDecimal point = batchRequest.getAwardSuccessPoint().add(batchRequest.getRedeemSuccessPoint());

        SAPSaleOrderUpdateFeignReq feignRequest = SAPSaleOrderUpdateFeignReq
                .builder()
                .saleOrder(req.getSapSaleOrder())
                .customer(newestSapSaleOrderCall.getCustomer())
                .type(DEFAULT_TYPE)
                .point(point.toString())
                .build();

        SAPSaleOrderUpdateFeignRes apiResponse = sapFeignClient.updateSaleOrder(feignRequest.getSaleOrder(), feignRequest);

        if (Objects.nonNull(apiResponse) && Objects.nonNull(apiResponse.getData())) {
            SapSaleOrderCall sapSaleOrderCall = new SapSaleOrderCall();

            if(SAP_API_STATUS_SUCCESS.equals(apiResponse.getData().getStatus())) {
                Log.info(LogData.createLogData()
                        .append("msg", "Call SAP to update sale order successfully")
                        .append("Transaction batch request ", batchRequestId)
                        .append("Sale order ", apiResponse.getData().getSaleOrder())
                );
                sapSaleOrderCall.setBusinessId(batchRequest.getBusinessId());
                sapSaleOrderCall.setProgramId(batchRequest.getProgramId());
                sapSaleOrderCall.setBatchRequestId(batchRequest.getId());
                sapSaleOrderCall.setSaleOrder(apiResponse.getData().getSaleOrder());
                sapSaleOrderCall.setCustomer(newestSapSaleOrderCall.getCustomer());
                sapSaleOrderCall.setPoint(point);
                sapSaleOrderCall.setRemark(newestSapSaleOrderCall.getRemark());
                sapSaleOrderCall.setPostingDate(newestSapSaleOrderCall.getPostingDate());
                sapSaleOrderCall.setCallType(SAP_CALL_TYPE_UPDATE);
                sapSaleOrderCall.setCallStatus(SAP_CALL_STATUS_SUCCESS);
                sapSaleOrderCall.setCallResponse(new Gson().toJson(apiResponse.getData()));
                sapSaleOrderCallRepository.save(sapSaleOrderCall);
            } else {
                throw new BusinessException(
                        ErrorCode.SAP_SALE_ORDER_DATA_NOT_VALID, null, null,
                        new Object[]{apiResponse.getData().getDescription()}
                );
            }
        } else {
            throw new BusinessException(ErrorCode.SYSTEM_CALL_SAP_ERROR);
        }

        return req.getSapSaleOrder();
    }

    @Override
    public List<SapSaleOrderRes> getSapSaleOrders(String sapCustomer) {

        SAPSaleOrderGetFeignRes apiResponse = sapFeignClient.getSaleOrders(sapCustomer);

        if(Objects.nonNull(apiResponse.getData()) && Objects.nonNull(apiResponse.getData().getSaleOrders())) {
            return apiResponse.getData().getSaleOrders()
                    .stream()
                    .map(item -> SapSaleOrderRes
                            .builder()
                            .sapSaleOrder(item.getSaleOrder())
                            .billing(item.getBilling())
                            .remark(item.getRemark())
                            .point(item.getPoint())
                            .build())
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public SapSaleOrderDetailRes getSapSaleOrder(String sapSaleOrder) {

        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository
                .findOrderByCreatedAtDesc(sapSaleOrder, SAP_CALL_STATUS_SUCCESS);

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall newestSapSaleOrderCall = sapSaleOrderCalls.get(0);

        SapSaleOrderCall oldestSapSaleOrderCall = sapSaleOrderCalls.get(sapSaleOrderCalls.size() - 1);

        SapSaleOrderRes res = getSapSaleOrders(newestSapSaleOrderCall.getCustomer())
                .stream()
                .filter(item -> sapSaleOrder.equals(item.getSapSaleOrder()))
                .findFirst()
                .orElse(null);

        Assert.assertTrue(Objects.isNull(res), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        return SapSaleOrderDetailRes
                .builder()
                .sapCustomer(newestSapSaleOrderCall.getCustomer())
                .sapSaleOrder(newestSapSaleOrderCall.getSaleOrder())
                .point(res.getPoint())
                .status(SAP_CALL_STATUS_SUCCESS)
                .postingDate(newestSapSaleOrderCall.getPostingDate())
                .remark(res.getRemark())
                .type(newestSapSaleOrderCall.getCallType())
                .calledAt(newestSapSaleOrderCall.getCreatedAt())
                .response(new Gson().fromJson(newestSapSaleOrderCall.getCallResponse(), SapSaleOrderDetailRes.Response.class))
                .createdAt(oldestSapSaleOrderCall.getCreatedAt())
                .createdBy(oldestSapSaleOrderCall.getCreatedBy())
                .build();
    }

    @Override
    public SapSaleOrderDetailRes getSapSaleOrder(Long batchRequestId) {
        List<SapSaleOrderCall> sapSaleOrderCalls = sapSaleOrderCallRepository.findByBatchRequestId(batchRequestId);

        Assert.assertTrue(sapSaleOrderCalls.isEmpty(), ErrorCode.SAP_SALE_ORDER_NOT_FOUND);

        SapSaleOrderCall sapSaleOrderCall = sapSaleOrderCalls.get(0);

        return getSapSaleOrder(sapSaleOrderCall.getSaleOrder());
    }

    private TransactionRequestRes buildTransactionAvailable(TransactionBatchRequest batchRequest,
                                                            TransactionRequest request,
                                                            List<TransactionHistory> transactionHistoryList,
                                                            Map<Integer, String> schemeCodeMapById,
                                                            Map<Integer, String> poolCodeMapById,
                                                            Map<Integer, String> corporationCodeMap, Store store, Map<Integer, String> chainCodeMap,
                                                            Map<Long, String> memberCodeMap) {
        TransactionRequestRes result = TransactionRequestRes.builder().build();
        result.setTxnRefNo(request.getTxnRefNo());
        result.setInvoiceNo(request.getInvoiceNo());
        result.setOriginalInvoiceNo(request.getOriginalInvoiceNo());
        result.setCorporationCode(corporationCodeMap.get(store.getCorporationId()));
        result.setChainCode(chainCodeMap.get(store.getChainId()));
        result.setStoreCode(request.getStoreCode());
        result.setTerminalCode(request.getTerminalCode());
        result.setMemberId(request.getMemberId());
        result.setMemberCode(memberCodeMap.get(request.getMemberId()));
        result.setProductAccountType(nullSafer(EOpsIdType.lookup(request.getIdType()), EOpsIdType::getValue));
        result.setProductAccountCode(request.getIdNo());
        result.setTransactionTime(request.getTransactionTime());
        EOpsTransactionType type = getTransactionType(batchRequest.getTransactionBatchType(), transactionHistoryList, request);
        result.setTransactionType(type != null ? type.getValue() : null);
        result.setGmv(request.getGmv());
        result.setGrossAmount(request.getGrossAmount());
        result.setCurrencyCode(request.getCurrencyCode());
        result.setDescription(request.getDescription());
        result.setProcessStatus(request.getProcessStatus());
        result.setSmsErrorCode(request.getSmsErrorCode());
        result.setSmsErrorMessage(request.getSmsErrorMessage());
        result.setErrorCode(request.getErrorCode());
        result.setErrorMessage(request.getErrorMessage());

        if (batchRequest.getTransactionBatchType() == ETransactionBatchType.ADJUST) {
            if (EAdjustmentType.AWARD.equals(request.getAdjustType())) {
                result.setAwardPoint(request.getAdjustPoint());
            } else {
                result.setRedeemPoint(request.getAdjustPoint());
            }
        } else {
            result.setRedeemPoint(request.getRedeemPoint());
        }

        if (CollectionUtils.isNotEmpty(transactionHistoryList)) {
            TransactionHistory history = transactionHistoryList.get(0);
            TransactionInfo txnInfo = getTransactionInfo(transactionHistoryList, schemeCodeMapById, poolCodeMapById);

            result.setPointBalanceBefore(txnInfo.getBalanceBefore());
            result.setPointBalanceAfter(txnInfo.getBalanceAfter());
            result.setNettAmount(txnInfo.getNettAmount());
            result.setAwardPoint(txnInfo.getTotalAwardPoint());
            result.setRedeemPoint(txnInfo.getTotalRedeemPoint());
            result.setAwardPointBeforeLimit(txnInfo.awardBeforeLimit);
            result.setAwardRetentionTime(txnInfo.getAwardRetentionTime());
            result.setCancellation(history.getCancellation() != null);
            result.setCancellationTime(history.getCancellationTime());
            result.setCancellationType(history.getCancellationType());
            result.setReasonCode(history.getReasonCode());
            result.setSchemeCode(txnInfo.getSchemeCodes());
            result.setPoolCode(txnInfo.getPoolCodes());
            result.setChannel(history.getChannel());
            result.setServiceCode(history.getServiceCode());
            result.setSchemeCode(txnInfo.getSchemeCodes());
            result.setPoolCode(txnInfo.getPoolCodes());

            if (result.getProcessStatus().equals(ERequestProcessStatus.SUCCESS)) {
                if (result.getChannel() == null) {
                    result.setChannel(defaultTransactionChannel);
                } else {
                    result.setChannel(request.getChannel());
                }

                if (result.getServiceCode() == null) {
                    result.setServiceCode(defaultTransactionServiceCode);
                } else {
                    result.setServiceCode(request.getServiceCode());
                }
            }
            Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistoryList);
            result.setTransactionType(nullSafer(getTransactionType(points), EOpsTransactionType::getValue));
        } else {
            result.setReasonCode(request.getReasonCode());
            result.setPoolCode(request.getPoolCode());
        }

        return result;
    }

    @Override
    public TransactionDetailRes getTransactionInfoDetail(String txnRefNo) {
        List<TransactionHistory> transactionHistories = transactionHistoryService.getByTnxPointIds(Collections.singletonList(txnRefNo));

        Assert.assertTrue(transactionHistories.isEmpty(), ErrorCode.TRANSACTION_NOT_FOUND);

        TransactionHistory transactionHistory = transactionHistories.get(0);

        Business business = nullSafer(transactionHistory.getBusinessId(),
                businessId -> businessService.find(businessId).orElse(null));
        Program program = nullSafer(transactionHistory.getProgramId(),
                programId -> programService.find(programId).orElse(null));
        Corporation corporation = nullSafer(transactionHistory.getCorporationId(),
                corporationId -> corporationService.find(corporationId).orElse(null));
        Chain chain = nullSafer(transactionHistory.getChainId(),
                chainId -> chainService.find(chainId).orElse(null));
        Store store = nullSafer(transactionHistory.getStoreId(),
                storeId -> storeService.find(storeId));
        Pos terminal = nullSafer(transactionHistory.getPosId(),
                posId -> posService.find(posId).orElse(null));
        Member member = nullSafer(transactionHistory.getMemberId(),
                memberId -> memberService.find(memberId).orElse(null));

        ReasonCode reasonCode = Objects.nonNull(transactionHistory.getReasonCode())
                ? reasonCodeService.findByCode(transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getReasonCode())
                : null;

        TransactionDetailRes.MemberProfile memberProfile = nullSafer(member, this::getMemberProfile);

        // Get transaction attributes with priority logic based on transaction age
        List<TransactionDetailRes.Attribute> attributes = getTransactionAttributesByPriority(txnRefNo, transactionHistory);

        EAdjustmentType adjustmentType = getAdjustmentType(transactionHistory);

        // Get original invoice
        TransactionHistory originalTxnHistory = null;
        if(Objects.nonNull(transactionHistory.getOriginalInvoiceNo())) {
            originalTxnHistory = transactionHistoryService
                    .findByInvoiceNo(transactionHistory.getOriginalInvoiceNo(), transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getStoreId())
                    .stream().findFirst().orElse(null);
        }

        TransactionDetailRes.Invoice originalInvoice = nullSafer(originalTxnHistory, txnHistory -> TransactionDetailRes.Invoice
                        .builder()
                        .invoiceNo(txnHistory.getInvoiceNo())
                        .txnRefNo(txnHistory.getPointTransactionId())
                        .build()
        );

        // Get related invoice
//        TransactionHistory relatedTxnHistory = transactionHistoryService
//                .findByOriginalInvoiceNo(transactionHistory.getInvoiceNo(), transactionHistory.getBusinessId(), transactionHistory.getProgramId(), transactionHistory.getStoreId())
//                .stream().findFirst().orElse(null);

        TransactionHistory relatedTxnHistory = null;

        TransactionDetailRes.Invoice relatedInvoice = nullSafer(relatedTxnHistory, txnHistory -> TransactionDetailRes.Invoice
                .builder()
                .invoiceNo(txnHistory.getInvoiceNo())
                .txnRefNo(txnHistory.getPointTransactionId())
                .build()
        );

        // Get poolMap, schemeMap, currencyMap, currencyIdMapByPoolId
        // To get pointAwardDetails and pointRedeemDetails
        List<Integer> schemeIds = new ArrayList<>();
        List<Integer> poolIds = new ArrayList<>();
        List<Integer> currencyIds = new ArrayList<>();
        Map<Integer, ShortEntityRes> poolMap = new HashMap<>();
        Map<Integer, Integer> currencyIdMapByPoolId = new HashMap<>();

        transactionHistories.forEach(txnHistory -> {
            schemeIds.add(txnHistory.getSchemeId());
            poolIds.add(txnHistory.getPoolId());
        });

        Map<Integer, ShortEntityRes> schemeMap = schemeService.findByIdIn(schemeIds).stream().collect(Collectors.toMap(
                Scheme::getId,
                scheme -> new ShortEntityRes(scheme.getId(), scheme.getName(), scheme.getCode()),
                (k1, k2) -> k2
        ));

        poolService.findByIdIn(poolIds).forEach(pool -> {
            currencyIds.add(pool.getCurrencyId());
            currencyIdMapByPoolId.put(pool.getId(), pool.getCurrencyId());
            poolMap.put(pool.getId(), new ShortEntityRes(pool.getId(), pool.getName(), pool.getCode()));
        });

        Map<Integer, ShortEntityRes> currencyMap = currencyService.findByIdIn(currencyIds).stream().collect(Collectors.toMap(
                Currency::getId,
                currency -> new ShortEntityRes(currency.getId(), currency.getName(), currency.getCode()),
                (k1, k2) -> k2
        ));

        List<TransactionDetailRes.PointAwardDetail> pointAwardDetails = getPointAwardDetails(transactionHistories, poolMap, schemeMap, currencyMap, currencyIdMapByPoolId);

        List<TransactionDetailRes.PointRedeemDetail> pointRedeemDetails = getPointRedeemDetails(transactionHistories, poolMap, schemeMap, currencyMap, currencyIdMapByPoolId);

        Map<ETransactionType, List<BigDecimal>> points = getTransactionPoints(transactionHistories);
        EOpsTransactionType transactionType = getTransactionType(points);

        //Get Transaction Batch Request
        TransactionRequest transactionRequest = transactionRequestService.findByTxnRefNo(txnRefNo);

        TransactionBatchRequest batchRequest = null;

        if(Objects.nonNull(transactionRequest)) {
            batchRequest = transactionBatchRequestService.find(transactionRequest.getBatchRequestId()).orElse(null);
        }

        return TransactionDetailRes
                .builder()
                .id(transactionHistory.getId())
                .txnRefNo(transactionHistory.getPointTransactionId())
                .invoiceNo(transactionHistory.getInvoiceNo())
                .originalInvoice(originalInvoice)
                .business(nullSafer(business, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .program(nullSafer(program, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .corporation(nullSafer(corporation, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .chain(nullSafer(chain, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .store(nullSafer(store, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .terminal(nullSafer(terminal, val -> new ShortEntityRes(val.getId(), val.getName(), val.getCode())))
                .status(transactionHistory.getStatus())
                .transactionTime(transactionHistory.getTransactionTime())
                .productAccountType(EOpsIdType.lookup(transactionHistory.getMemberProductAccountType()))
                .productAccountCode(transactionHistory.getMemberProductAccountCode())
                .memberProfile(memberProfile)
                .transactionType(transactionType)
                .adjustmentType(adjustmentType)
                .description(transactionHistory.getDescription())
                .channel(transactionHistory.getChannel())
                .service(transactionHistory.getServiceCode())
                .cancellation(EBoolean.of(transactionHistory.getCancellation()))
                .cancellationType(transactionHistory.getCancellationType())
                .cancelledAt(transactionHistory.getCancellationTime())
                .relatedInvoice(relatedInvoice)
                .gmv(transactionHistory.getGmv())
                .grossAmount(transactionHistory.getGrossAmount())
                .nettAmount(transactionHistory.getNettAmount())
                .retentionTime(transactionHistory.getAwardRetentionTime())
                .reasonCode(transactionHistory.getReasonCode())
                .reasonName(nullSafer(reasonCode, ReasonCode::getName))
                .createdAt(nullSafer(batchRequest, TransactionBatchRequest::getCreatedAt))
                .createdBy(nullSafer(batchRequest, TransactionBatchRequest::getCreatedBy))
                .approvedAt(nullSafer(batchRequest, TransactionBatchRequest::getApprovedAt))
                .approvedBy(nullSafer(batchRequest, TransactionBatchRequest::getApprovedBy))
                .errorCode(transactionHistory.getCoreErrorCode())
                .errorMessage(transactionHistory.getCoreErrorMessage())
                .attributes(attributes)
                .pointAwardDetails(pointAwardDetails)
                .pointRedeemDetails(pointRedeemDetails)
                .syncWithElastic(getSyncWithElastic(transactionHistories))
                .build();
    }

    private TransactionDetailRes.MemberProfile getMemberProfile(Member member) {
        return TransactionDetailRes.MemberProfile
                .builder()
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .memberName(member.getFullName())
                .status(member.getStatus())
                .phoneNo(member.getPhoneNo())
                .dob(member.getDob())
                .gender(member.getGender())
                .email(member.getEmail())
                .identifyType(member.getIdentifyType())
                .identifyNo(member.getIdentifyNo())
                .address(member.getAddress())
                .build();
    }

    private EAdjustmentType getAdjustmentType(TransactionHistory transactionHistory) {
        if(ETransactionType.ADJUSTMENT.equals(transactionHistory.getType())) {
            return Objects.nonNull(transactionHistory.getAwardPoint())
                    && transactionHistory.getAwardPoint().compareTo(BigDecimal.ZERO) > 0
                    ? EAdjustmentType.AWARD : EAdjustmentType.REDEEM;
        } else {
            return null;
        }
    }

    /**
     * Get transaction attributes from Elasticsearch if database doesn't have them
     *
     * @param txnRefNo Transaction reference number
     * @return List of transaction attributes
     */
    private List<TransactionDetailRes.Attribute> getTransactionAttributesFromElasticsearch(String txnRefNo) {
        List<TransactionDetailRes.Attribute> attributes = new ArrayList<>();
        try {
            // Create query to find document with matching txnRefNo
            Query query = new CriteriaQuery(new Criteria("txn_ref_no.keyword").is(txnRefNo));

            // Search across all transaction history indices
            SearchHits<Map> searchHits = elasticsearchRestTemplate.search(
                query,
                Map.class,
                IndexCoordinates.of(transactionHistoryIndexPattern)
            );

            if (searchHits.hasSearchHits()) {
                Map<String, Object> source = searchHits.getSearchHit(0).getContent();

                // Attributes are stored as a nested structure:
                // "attributes" -> array of objects with "code" and "value" properties
                if (source.containsKey("attributes")) {
                    List<Map<String, Object>> attributesList = (List<Map<String, Object>>) source.get("attributes");

                    if (attributesList != null && !attributesList.isEmpty()) {
                        // Get program ID for looking up attribute metadata
                        Integer programId = source.containsKey("program_id") ?
                            ((Number) source.get("program_id")).intValue() : null;

                        // Extract all attribute codes for bulk lookup
                        List<String> attributeCodes = attributesList.stream()
                            .filter(attr -> attr.containsKey("code"))
                            .map(attr -> (String) attr.get("code"))
                            .collect(Collectors.toList());

                        // Get attribute metadata for all attributes at once (more efficient)
                        Map<String, ProgramTransactionAttribute> attributeMetadataMap =
                            programId != null && !attributeCodes.isEmpty() ?
                                programTransactionAttributeService.findByProgramIdAndAttributeIn(programId, attributeCodes)
                                    .stream()
                                    .collect(Collectors.toMap(
                                        ProgramTransactionAttribute::getAttribute,
                                        item -> item,
                                        (k1, k2) -> k2
                                    )) :
                                new HashMap<>();

                        // Process each attribute
                        for (Map<String, Object> attr : attributesList) {
                            if (attr.containsKey("code") && attr.containsKey("value")) {
                                String code = (String) attr.get("code");
                                String value = (String) attr.get("value");

                                ProgramTransactionAttribute metadata = attributeMetadataMap.get(code);

                                // Use nullSafer to match the original implementation
                                attributes.add(TransactionDetailRes.Attribute
                                    .builder()
                                    .attribute(code)
                                    .value(value)
                                    .dataType(nullSafer(metadata, ProgramTransactionAttribute::getDataType))
                                    .dataTypeDisplay(nullSafer(metadata, ProgramTransactionAttribute::getDataTypeDisplay))
                                    .status(ECommonStatus.ACTIVE)
                                    .build());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            return Collections.emptyList();
        }
        return attributes;
    }

    /**
     * Get transaction attributes based on transaction time priority
     * - For transactions older than 3 months: try Elasticsearch first, then database
     * - For transactions less than 3 months old: try database first, then Elasticsearch
     *
     * @param txnRefNo Transaction reference number
     * @param transaction Transaction history object
     * @return List of transaction attributes
     */
    private List<TransactionDetailRes.Attribute> getTransactionAttributesByPriority(String txnRefNo, TransactionHistory transaction) {
        List<TransactionDetailRes.Attribute> attributes = new ArrayList<>();

        // Calculate date 3 months ago
        Calendar threeMonthsAgo = Calendar.getInstance();
        threeMonthsAgo.add(Calendar.MONTH, -3);
        Date threeMonthsAgoDate = threeMonthsAgo.getTime();

        // Check if transaction is older than 3 months
        boolean isOlderThanThreeMonths = transaction.getTransactionTime().before(threeMonthsAgoDate);

        if (isOlderThanThreeMonths) {
            // For older transactions: try Elasticsearch first, then database
            attributes = getTransactionAttributesFromElasticsearch(txnRefNo);

            if (attributes.isEmpty()) {
                attributes = getTransactionAttributes(txnRefNo, transaction.getProgramId());
            }
        } else {
            // For newer transactions: try database first, then Elasticsearch
            attributes = getTransactionAttributes(txnRefNo, transaction.getProgramId());

            if (attributes.isEmpty()) {
                attributes = getTransactionAttributesFromElasticsearch(txnRefNo);
            }
        }

        return attributes;
    }

    private List<TransactionDetailRes.Attribute> getTransactionAttributes(String txnRefNo, Integer programId) {
        List<TransactionHistoryAttribute> txnHistoryAttributes =
                transactionHistoryAttributeRepository.findAllByTransactionRef(txnRefNo);

        List<String> attributes = txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(TransactionHistoryAttribute::getCode)
                .collect(Collectors.toList());

        Map<String, ProgramTransactionAttribute> attributeMap =
                programTransactionAttributeService.findByProgramIdAndAttributeIn(programId, attributes)
                .stream().collect(Collectors.toMap(
                        ProgramTransactionAttribute::getAttribute,
                        item -> item,
                        (k1, k2) -> k2
                ));

        return txnHistoryAttributes
                .stream()
                .filter(item -> Objects.nonNull(item.getCode()))
                .map(item -> TransactionDetailRes.Attribute
                        .builder()
                        .attribute(item.getCode())
                        .value(item.getValue())
                        .dataType(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataType))
                        .dataTypeDisplay(nullSafer(attributeMap.get(item.getCode()), ProgramTransactionAttribute::getDataTypeDisplay))
                        .status(ECommonStatus.ACTIVE)
                        .build())
                .collect(Collectors.toList());
    }

    private List<TransactionDetailRes.PointAwardDetail> getPointAwardDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> poolMap,
            Map<Integer, ShortEntityRes> schemeMap,
            Map<Integer, ShortEntityRes> currencyMap,
            Map<Integer, Integer> currencyIdMapByPoolId) {
        return transactionHistories.stream()
                .filter(item -> ETransactionType.AWARD.equals(item.getType())
                        || (ETransactionType.ADJUSTMENT.equals(item.getType()) && BigDecimal.ZERO.compareTo(item.getAwardPoint()) < 0))
                .map(item -> TransactionDetailRes.PointAwardDetail
                        .builder()
                        .point(item.getAwardPoint())
                        .balanceBefore(item.getBalanceBefore())
                        .balanceAfter(item.getBalanceAfter())
                        .pool(poolMap.get(item.getPoolId()))
                        .scheme(schemeMap.get(item.getSchemeId()))
                        .currency(currencyMap.get(currencyIdMapByPoolId.get(item.getPoolId())))
                        .awardRetentionTime(item.getAwardRetentionTime())
                        .build()
                ).collect(Collectors.toList());
    }

    private List<TransactionDetailRes.PointRedeemDetail> getPointRedeemDetails(
            List<TransactionHistory> transactionHistories,
            Map<Integer, ShortEntityRes> poolMap,
            Map<Integer, ShortEntityRes> schemeMap,
            Map<Integer, ShortEntityRes> currencyMap,
            Map<Integer, Integer> currencyIdMapByPoolId) {
        return transactionHistories.stream()
                .filter(item -> item.getType() == ETransactionType.REDEEM
                        || (ETransactionType.ADJUSTMENT.equals(item.getType()) && BigDecimal.ZERO.compareTo(item.getRedeemPoint()) < 0))
                .map(item -> TransactionDetailRes.PointRedeemDetail
                        .builder()
                        .point(item.getRedeemPoint())
                        .balanceBefore(item.getBalanceBefore())
                        .balanceAfter(item.getBalanceAfter())
                        .pool(poolMap.get(item.getPoolId()))
                        .scheme(schemeMap.get(item.getSchemeId()))
                        .currency(currencyMap.get(currencyIdMapByPoolId.get(item.getPoolId())))
                        .build()
                ).collect(Collectors.toList());
    }

    private EOpsTransactionType getTransactionType(ETransactionBatchType transactionBatchType, List<TransactionHistory> transactionHistories, TransactionRequest transactionRequest) {
        switch (transactionBatchType) {
            case EARN:
                return EOpsTransactionType.EARN;
            case BURN:
                return EOpsTransactionType.BURN;
            case SALE:
                return EOpsTransactionType.SALE;
            case ADJUST:
                return EOpsTransactionType.ADJUSTMENT;
            case REVERT_FULL:
                return getTransactionType(getTransactionPoints(transactionHistories));
            case REVERT_PARTIAL: {
                int check = transactionRequest.getRedeemPoint().compareTo(BigDecimal.ZERO);
                if (check == 1) {
                    return EOpsTransactionType.SALE;
                } else if (check == 0) {
                    return EOpsTransactionType.EARN;
                }
            }
        }
        return null;
    }

    private EBoolean getSyncWithElastic(List<TransactionHistory> txnHistories) {
        for(TransactionHistory txnHistory : txnHistories) {
            if(Objects.isNull(txnHistory.getSyncWithElastic())
                    || !ESyncWithElastic.YES.equals(txnHistory.getSyncWithElastic())) {
                return EBoolean.NO;
            }
        }
        return EBoolean.YES;
    }
}
