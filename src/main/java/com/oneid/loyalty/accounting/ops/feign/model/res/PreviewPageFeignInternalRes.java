package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class PreviewPageFeignInternalRes {
    private Long id;
    private String requestCode;
    private String requestName;
    private String requestType;
    private Integer version;
    private Date madeDate;
    private Date checkedDate;
    private String status;
    private Integer madeByUserId;
    private String madeByUserName;
    private String madeByUserEmail;
    private Integer checkedByUserId;
    private String checkedByUserName;
    private Object payload;
}
