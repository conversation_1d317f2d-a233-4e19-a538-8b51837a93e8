package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import com.oneid.oneloyalty.common.entity.Corporation;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorporationInfo implements Serializable {

    private static final long serialVersionUID = -3001296817401623009L;

    private String name;
    private String code;
    private ECommonStatus status;
    private Integer id;

    private Integer businessId;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;
    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;
    private String address1;

    public static CorporationInfo valueOf(Corporation corporation) {
        CorporationInfo result = new CorporationInfo();
        result.setBusinessId(corporation.getBusinessId());
        result.setId(corporation.getId());
        result.setName(corporation.getName());
        result.setStatus(corporation.getStatus());
        result.setCode(corporation.getCode());
        result.setStartDate(corporation.getServiceStartDate());
        result.setEndDate(corporation.getServiceEndDate());
        result.setAddress1(corporation.getAddress1());
        return result;
    }
}
