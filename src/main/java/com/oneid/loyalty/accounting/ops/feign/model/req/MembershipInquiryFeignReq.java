package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.dto.CustomerIdentifierDTO;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MembershipInquiryFeignReq {
    private String businessId;
    private String programId;
    CustomerIdentifierDTO customerIdentifier;
    private Boolean profileRequired;
    private Boolean balanceRequired;
    private Boolean vdCardRequired;
}
