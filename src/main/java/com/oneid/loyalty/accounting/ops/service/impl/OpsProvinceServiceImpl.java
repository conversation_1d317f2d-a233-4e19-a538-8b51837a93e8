package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.ProvinceDTO;
import com.oneid.loyalty.accounting.ops.service.OpsProvinceService;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.entity.Province;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CountryRepository;
import com.oneid.oneloyalty.common.repository.ProvinceRepository;
import com.oneid.oneloyalty.common.service.ProvinceService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class OpsProvinceServiceImpl implements OpsProvinceService {

    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private ProvinceService provinceService;

    @Autowired
    private CountryRepository countryRepository;

    @Override
    public ProvinceDTO getOne(Integer id) {
        final Province province = this.provinceRepository
                .findById(id)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.PROVINCE_NOT_FOUND, "Province is not found",
                        LogData.createLogData().append("province", id)));
        return ProvinceDTO.valueOf(province);
    }

    @Override
    public List<Province> getProvinces(Integer countryId) {
        return this.provinceService.getAllByCountry(countryId);
    }

    @Override
    public List<Province> getProvinces(String countryCode) {
        final Optional<Country> countryOpt = this.countryRepository.findByCode(countryCode);
        if (!countryOpt.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        return this.provinceService.getAllByCountry(countryOpt.get().getId());
    }
}
