package com.oneid.loyalty.accounting.ops.component.service;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AttributeValueService {
    Page<AttributeCombobox> getCorporationsAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    List<AttributeCombobox> getListMemberStatusAttributeValue(Integer programId);

    Page<AttributeCombobox> getChainsAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    Page<AttributeCombobox> getStoresAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    Page<AttributeCombobox> getTerminalsAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    Page<AttributeCombobox> getTierAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    List<AttributeCombobox> getListTxnAttributeValue(Integer programId);

    List<AttributeCombobox> getListCardType(Integer programId);

    List<AttributeCombobox> getFunctionCodes(Integer programId);

    Page<AttributeCombobox> getTransactionCodesAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    Page<AttributeCombobox> getPoolCodeAttributeValue(Integer programId, String nameOrCode, Pageable pageable);

    Page<AttributeCombobox> getVoucherCodeAttributeValue(Integer programId, String keyword, Pageable pageable);

    Page<AttributeCombobox> getListServiceCodeAttributeValue(Integer programId, String code, Pageable pageable);
}