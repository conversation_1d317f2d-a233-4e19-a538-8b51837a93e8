package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import com.oneid.oneloyalty.common.entity.Base;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class BaseDetail<T extends Base> {

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("updated_at")
    private Date updatedAt;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("approved_at")
    private Date approvedAt;

    BaseDetail(T entity) {
        this.setCreatedAt(entity.getCreatedAt());
        this.setCreatedBy(entity.getCreatedBy());
        this.setUpdatedAt(entity.getUpdatedAt());
        this.setUpdatedBy(entity.getUpdatedBy());
        this.setApprovedAt(entity.getApprovedAt());
        this.setApprovedBy(entity.getApprovedBy());
    }

    BaseDetail() {
    }
}