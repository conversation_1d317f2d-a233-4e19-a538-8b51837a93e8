package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.service.OpsProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v1/program-transaction-attributes")
public class ProgramTransactionAttributeController extends BaseController {
    @Autowired
    OpsProgramTransactionAttributeService opsProgramTransactionAttributeService;

    @GetMapping("")
    public ResponseEntity<?> getProgramTransactionAttributes(@RequestParam(name = "program_id", required = true) Integer programId) {
        return success(opsProgramTransactionAttributeService.findByProgramId(programId));
    }
}
