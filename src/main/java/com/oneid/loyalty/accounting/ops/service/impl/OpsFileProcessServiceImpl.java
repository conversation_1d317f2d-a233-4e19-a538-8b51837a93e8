package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.feign.RetryFileWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.RetryFileRequestRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.AutoEarningTransactionExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.SearchControlFileHistoryReq;
import com.oneid.loyalty.accounting.ops.model.res.AuditTrailAutoEarningRes;
import com.oneid.loyalty.accounting.ops.model.res.ControlFileHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.DataFileHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberInfo;
import com.oneid.loyalty.accounting.ops.model.res.RetryFileRequestHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.RetryFileRes;
import com.oneid.loyalty.accounting.ops.service.OpsFileProcessService;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.AuditTrailAutoEarning;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.entity.UserProfile;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.RetryFileRequestHistoryDTO;
import com.oneid.oneloyalty.common.repository.AuditTrailAutoEarningRepository;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.MemberRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.UserProfileRepository;
import com.oneid.oneloyalty.common.service.ControlFileHistoryService;
import com.oneid.oneloyalty.common.service.DataFileHistoryService;
import com.oneid.oneloyalty.common.service.RetryFileRequestService;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsFileProcessServiceImpl implements OpsFileProcessService {

    @Value("${app.business.tcb}")
    private String tcbBusinessCode;

    @Value("${app.business.vgc}")
    private String vgcBusinessCode;

    @Value("${app.program.tcb}")
    private String tcbProgramCode;

    @Value("${app.program.vgc}")
    private String vgcProgramCode;

    @Autowired
    private ControlFileHistoryService controlFileHistoryService;

    @Autowired
    private DataFileHistoryService dataFileHistoryService;

    @Autowired
    private AuditTrailAutoEarningRepository auditTrailAutoEarningRepository;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private RetryFileRequestService retryFileRequestService;

    @Autowired
    private RetryFileWorkerFeignClient retryFileWorkerFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Override
    public Page<ControlFileHistoryRes> getControlFileHistoryList(SearchControlFileHistoryReq req, Pageable pageable) {
        // Adjust end dates to include the full day
        if (req.getCreatedAtTo() != null) {
            Date newCreatedAtTo = new Date(req.getCreatedAtTo().getTime() + OPSConstant.TIMES_OF_DAY);
            req.setCreatedAtTo(newCreatedAtTo);
        }

        if (req.getUpdatedAtTo() != null) {
            Date newUpdatedAtTo = new Date(req.getUpdatedAtTo().getTime() + OPSConstant.TIMES_OF_DAY);
            req.setUpdatedAtTo(newUpdatedAtTo);
        }

        // Call the filter method with all parameters
        Page<ControlFileHistory> result = controlFileHistoryService.filter(
                req.getFileName(),
                req.getStatus(),
                req.getFileTypes(),
                req.getCreatedAtFrom(),
                req.getCreatedAtTo(),
                req.getUpdatedAtFrom(),
                req.getUpdatedAtTo(),
                pageable
        );

        List<ControlFileHistoryRes> controlFileHistoryResList = result.getContent().stream()
                .map(this::mapToControlFileHistoryRes)
                .collect(Collectors.toList());

        return new PageImpl<>(controlFileHistoryResList, pageable, result.getTotalElements());
    }

    @Override
    public ControlFileHistoryRes getControlFileHistoryDetail(Long id) {
        ControlFileHistory controlFile = controlFileHistoryService.findById(id);

        if (controlFile == null) {
            throw new BusinessException(ErrorCode.FILE_PROCESSING_NOT_FOUND, "Control File Not Found",
                    LogData.createLogData().append("id", id));
        }

        Long totalTransaction = dataFileHistoryService.sumTotalTransactionByControlFileId(id);
        Long totalFailedTransaction = dataFileHistoryService.sumTotalFailedTransactionByControlFileId(id);

        RetryFileRequest retryFileRequest;
        RetryFileRes retryFileRes = new RetryFileRes();
        if (EFileType.RETRY_AUTO_EARNING.equals(controlFile.getFileType())) {
            retryFileRequest = retryFileRequestService.findById(controlFile.getFileName());
            if (retryFileRequest == null || retryFileRequest.getOriginalControlFileId() == null) {
                throw new BusinessException(ErrorCode.ORIGINAL_FILE_NOT_FOUND, "Original File Not Found",
                        LogData.createLogData().append("id", id));
            }
            ControlFileHistory retryFile = controlFileHistoryService.findById(Long.valueOf(retryFileRequest.getOriginalControlFileId()));
            if (retryFile != null) {
                retryFileRes.setId(Math.toIntExact(retryFile.getId()));
                retryFileRes.setFileName(retryFile.getFileName());
            }
        }

        return ControlFileHistoryRes.builder()
                .id(controlFile.getId())
                .fileName(controlFile.getFileName())
                .status(controlFile.getStatus())
                .isIgnored(controlFile.getIsIgnored())
                .errorMessage(controlFile.getErrorMessage())
                .totalFiles(BigDecimal.valueOf(controlFile.getTotalFiles()))
                .createdAt(controlFile.getCreatedAt())
                .updatedAt(controlFile.getUpdatedAt())
                .totalTransaction(totalTransaction)
                .totalFailedTransaction(totalFailedTransaction)
                .fileType(controlFile.getFileType())
                .retryFile(EFileType.RETRY_AUTO_EARNING.equals(controlFile.getFileType()) ? retryFileRes : null)
                .build();
    }

    @Override
    public Page<DataFileHistoryRes> getDataFileHistoryList(Long id, Pageable pageable) {
        Page<DataFileHistory> dataFileHistories = dataFileHistoryService.findAllByControlFileId(id, pageable);

        List<DataFileHistoryRes> dataFileHistoryResList = dataFileHistories.getContent().stream()
                .map(this::mapToDataFileHistoryRes)
                .collect(Collectors.toList());

        return new PageImpl<>(dataFileHistoryResList, pageable, dataFileHistories.getTotalElements());
    }

    @Override
    public Page<AuditTrailAutoEarningRes> getFailedTransactionList(Long id, Pageable pageable) {
        List<DataFileHistory> dataFileList = dataFileHistoryService.findByControlFileId(id);
        Map<Long, String> idToFileNameMap = mapDataFileByKey(dataFileList);
        Page<AuditTrailAutoEarning> result = auditTrailAutoEarningRepository.findByDataFileIdInAndTcbStatus(idToFileNameMap.keySet(), ETransactionStatus.FAIL, pageable);

        List<AuditTrailAutoEarningRes> dataFileHistoryResList = result.getContent().stream()
                .map(e -> mapToAuditTrailAutoEarningRes(e, idToFileNameMap))
                .collect(Collectors.toList());

        return new PageImpl<>(dataFileHistoryResList, pageable, result.getTotalElements());
    }

    @Override
    public void ignoreControlFile(Long id) {
        ControlFileHistory controlFile = controlFileHistoryService.findById(id);

        if (controlFile == null) {
            Log.warn(LogData.createLogData()
                    .append("msg", "Control file not found")
                    .append("control_file_id", id));

            throw new BusinessException(ErrorCode.FILE_PROCESSING_NOT_FOUND);
        }

        if (EBoolean.YES.equals(controlFile.getIsIgnored()) || EProcessingStatus.SUCCESS.equals(controlFile.getStatus())) {
            Log.warn(LogData.createLogData()
                    .append("msg", "Cannot mark ignored")
                    .append("control_file_id", id)
                    .append("control_file_name", controlFile.getFileName())
                    .append("status", controlFile.getStatus())
                    .append("is_ignored", controlFile.getIsIgnored())
            );
            throw new BusinessException(ErrorCode.CANNOT_MARK_IGNORED);
        }

        controlFile.setIsIgnored(EBoolean.YES);
        controlFileHistoryService.save(controlFile);
    }

    @Override
    public ResourceDTO exportFailedTransaction(Long controlFileId) {
        ControlFileHistory controlFile = controlFileHistoryService.findById(controlFileId);

        if (controlFile == null) {
            Log.warn(LogData.createLogData()
                    .append("msg", "Control file not found")
                    .append("control_file_id", controlFileId));

            throw new BusinessException(ErrorCode.FILE_PROCESSING_NOT_FOUND);
        }

        List<DataFileHistory> dataFileList = dataFileHistoryService.findByControlFileId(controlFileId);
        Map<Long, String> idToFileNameMap = mapDataFileByKey(dataFileList);
        List<AuditTrailAutoEarningRes> result = auditTrailAutoEarningRepository.findByDataFileIdInAndTcbStatus(idToFileNameMap.keySet(), ETransactionStatus.FAIL).stream()
                .map(e -> mapToAuditTrailAutoEarningRes(e, idToFileNameMap))
                .collect(Collectors.toList());

        List<AutoEarningTransactionExcelDTO> dtos = mapToAutoEarningTransactionExcelDTO(result);
        String fileName = String.format(OPSConstant.FILE_NAME_EXPORT_AUTO_EARNING_TRANSACTION, controlFile.getFileName().split("\\.")[0]);
        return this.exportFileExcel(fileName, dtos, OPSConstant.AUTO_EARNING_TRANSACTION);
    }

    @Override
    public List<RetryFileRequestHistoryRes> getRetryHistoryList(Long id) {
        ControlFileHistory controlFile = controlFileHistoryService.findById(id);
        if (controlFile == null) {
            Log.warn(LogData.createLogData()
                    .append("msg", "Control file not found")
                    .append("control_file_id", id));
            throw new BusinessException(ErrorCode.FILE_PROCESSING_NOT_FOUND);
        }
        return retryFileRequestService.getRetryHistoryList(Math.toIntExact(id)).stream()
                .map(dto -> new RetryFileRequestHistoryRes(
                        dto.getId(),
                        dto.getFileName(),
                        dto.getStatus(),
                        dto.getCreatedBy(),
                        dto.getCreatedAt() != null ? dto.getCreatedAt().getTime() / 1000 : null
                )).collect(Collectors.toList());
    }

    @Override
    public void retryControlFile(Long id) {
        ControlFileHistory controlFile = controlFileHistoryService.findById(id);
        if (controlFile == null) {
            Log.error(LogData.createLogData()
                    .append("msg", "Control file not found")
                    .append("control_file_id", id));
            throw new BusinessException(ErrorCode.FILE_PROCESSING_NOT_FOUND);
        }

        // Check if file is SUCCESS
        if (!EProcessingStatus.SUCCESS.equals(controlFile.getStatus())) {
            Log.error(LogData.createLogData()
                    .append("msg", "Control file status must be SUCCESS")
                    .append("control_file_id", id));
            throw new BusinessException(ErrorCode.CONTROL_FILE_STATUS_MUST_BE_SUCCESS);
        }

        // Check if current file has failed records > 0
        Long totalFailedTransaction = dataFileHistoryService.sumTotalFailedTransactionByControlFileId(id);
        if (totalFailedTransaction == 0) {
            Log.error(LogData.createLogData()
                    .append("msg", "Control file doesn't have failed transactions")
                    .append("control_file_id", id));
            throw new BusinessException(ErrorCode.CONTROL_FILE_DOESNT_HAVE_FAILED_TRANSACTIONS);
        }

        // Check if retry file request has PROCESSING record
        // todo check if has building file
        List<RetryFileRequestHistoryDTO> processingRetryFileList = retryFileRequestService.getRetryHistoryList(Math.toIntExact(id)).stream()
                .filter(ele -> EProcessingStatus.PROCESSING.equals(ele.getStatus()))
                .collect(Collectors.toList());
        if (!processingRetryFileList.isEmpty()) {
            Log.error(LogData.createLogData()
                    .append("msg", "This file is processing retry")
                    .append("control_file_id", id));
            throw new BusinessException(ErrorCode.FILE_IS_PROCESSING_RETRY);
        }

        // TODO call retry
        try {
            String createdBy = opsReqPendingValidator.getCurrentUser();
            APIResponse<RetryFileRequestRes> requestRes = retryFileWorkerFeignClient.requestRetry(Math.toIntExact(id), createdBy);
            if (ErrorCode.SUCCESS.getValue() != requestRes.getMeta().getCode()) {
                throw new BusinessException(ErrorCode.RETRY_FILE_FAILED);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private ResourceDTO exportFileExcel(String fileName, List<AutoEarningTransactionExcelDTO> dtos, String objectId) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.FILE_PROCESS)
                .objectId(objectId)
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    private Map<Long, String> mapDataFileByKey(List<DataFileHistory> dataFileHistoryResList) {
        return dataFileHistoryResList.stream()
                .collect(Collectors.toMap(
                        DataFileHistory::getId,
                        DataFileHistory::getFileName
                ));
    }

    private ControlFileHistoryRes mapToControlFileHistoryRes(ControlFileHistory entity) {
        return ControlFileHistoryRes.builder()
                .id(entity.getId())
                .fileName(entity.getFileName())
                .totalFiles(BigDecimal.valueOf(entity.getTotalFiles()))
                .status(entity.getStatus())
                .errorMessage(entity.getErrorMessage())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .fileType(entity.getFileType())
                .build();
    }

    private DataFileHistoryRes mapToDataFileHistoryRes(DataFileHistory entity) {
        return DataFileHistoryRes.builder()
                .id(entity.getId())
                .fileName(entity.getFileName())
                .status(entity.getStatus())
                .totalRecords(entity.getTotalRecord())
                .totalFailedRecords(entity.getTotalFailedRecord())
                .errorCode(entity.getErrorCode())
                .errorMessage(entity.getErrorMessage())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    private AuditTrailAutoEarningRes mapToAuditTrailAutoEarningRes(AuditTrailAutoEarning entity, Map<Long, String> dataFileList) {
        Member member;
        Member userProfile;
        MemberInfo oneuMember = new MemberInfo();
        MemberInfo tcbMember = new MemberInfo();

        if (entity.getOneuId() != null) {
            oneuMember.setUserId(entity.getOneuId());
            member = getMemberByMasterUserId(vgcBusinessCode, vgcProgramCode, entity.getOneuId());
            if (member != null) {
                oneuMember.setMemberId(member.getId());
            }
        }

        if (entity.getLoyaltyCustId() != null) {
            tcbMember.setPartnerCustomerId(entity.getLoyaltyCustId());
            userProfile = getMemberByPartnerCustomerId(tcbBusinessCode, tcbProgramCode, entity.getLoyaltyCustId());
            if (userProfile != null) {
                tcbMember.setMemberId(userProfile.getId());
            }
        }

        return AuditTrailAutoEarningRes.builder()
                .dataFileName(dataFileList.get(entity.getDataFileId()))
                .oneuId(oneuMember)
                .loyaltyCustId(tcbMember)
                .transactionId(entity.getTransactionId())
                .transactionAmount(entity.getTransactionAmount())
                .ftTransactionId(entity.getFtTransactionId())
                .oneuTransactionRef(entity.getOneuTransactionRef())
                .oneuStatus(entity.getOneuStatus())
                .oneuErrorMessage(entity.getOneuErrorMessage())
                .tcbTransactionRef(entity.getTcbTransactionRef())
                .tcbStatus(ETransactionStatus.FAIL.equals(entity.getOneuStatus()) ? null : entity.getTcbStatus())
                .tcbErrorMessage(entity.getTcbErrorMessage())
                .build();
    }

    private List<AutoEarningTransactionExcelDTO> mapToAutoEarningTransactionExcelDTO(List<AuditTrailAutoEarningRes> dtos) {
        return dtos.stream().map(res -> {
            AutoEarningTransactionExcelDTO dto = new AutoEarningTransactionExcelDTO();
            dto.setFileName(res.getDataFileName());
            dto.setOneuId(res.getOneuId().getUserId());
            dto.setLoyaltyCustId(res.getLoyaltyCustId().getPartnerCustomerId());
            dto.setTransactionId(res.getTransactionId());
            dto.setTransactionAmount(res.getTransactionAmount());
            dto.setFtTransactionId(res.getFtTransactionId());
            dto.setOneuTransactionRef(res.getOneuTransactionRef());
            dto.setOneuStatus(res.getOneuStatus());
            dto.setOneuErrorMessage(res.getOneuErrorMessage());
            dto.setTcbTransactionRef(res.getTcbTransactionRef());
            dto.setTcbStatus(res.getTcbStatus());
            dto.setTcbErrorMessage(res.getTcbErrorMessage());
            return dto;
        }).collect(Collectors.toList());
    }

    private Member getMemberByPartnerCustomerId(String businessCode, String programCode, String partnerCustomerId) {
        Business business = getBusiness(businessCode);
        Program program = getProgram(business.getId(), programCode);
        return memberRepository.findByProgramIdAndPartnerCustomerId(program.getId(), partnerCustomerId);
    }

    private Member getMemberByMasterUserId(String businessCode, String programCode, String masterUserId) {
        Business business = getBusiness(businessCode);
        Program program = getProgram(business.getId(), programCode);

        UserProfile userProfile = userProfileRepository.findByMasterUserIdAndBusinessId(masterUserId, business.getId());

        if (userProfile == null) {
            return null;
        }
        return memberRepository.findByUserProfileAndProgramId(userProfile.getId(), program.getId());
    }

    private Business getBusiness(String businessCode) {
        Business business = businessRepository.findByCode(businessCode);
        if (business == null) {
            throw new BusinessException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        return business;
    }

    private Program getProgram(Integer businessId, String programCode) {
        Program program = programRepository.findByBusinessIdAndCode(businessId, programCode).orElse(null);
        if (program == null) {
            throw new BusinessException(ErrorCode.PROGRAM_NOT_FOUND);
        }
        return program;
    }
}
