package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(name = "oneloyalty-rules", url = "${oneloyalty-rules.base-url}", configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface OneloyaltyRuleFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/v1/check-reset")
    APIFeignInternalResponse<?> checkReset(@RequestBody List<ResetRuleReq> req);
}