package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.search.Searchable;
import com.oneid.oneloyalty.common.search.SearchableFieldExtractor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class FilterProgramTierReq extends SearchableFieldExtractor<ProgramTier> {

    @Searchable(key = "businessId")
    private Integer businessId;

    @Searchable(key = "programId")
    private Integer programId;

    @Searchable(key = "tierCode")
    private String code;

    @Searchable(key = "status")
    private ECommonStatus status;
}