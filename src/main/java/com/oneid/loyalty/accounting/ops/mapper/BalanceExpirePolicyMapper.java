package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.BalanceExpirePolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.oneloyalty.common.constant.EBalanceExpirePolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.BalanceExpirePolicy;

public class BalanceExpirePolicyMapper {
    public static BalanceExpirePolicy toBalanceExpirePolicyOneFromPoolCreateReq(PoolCreateReq request) {
        BalanceExpirePolicyReq balanceExpirePolicyReq = request.getBalanceExpirePolicy();

        BalanceExpirePolicy result = new BalanceExpirePolicy();
        result.setBusinessId(request.getBusinessId());
        result.setName(request.getName());
        result.setType(EBalanceExpirePolicyType.of(balanceExpirePolicyReq.getType()));
        result.setPeriodDay(balanceExpirePolicyReq.getPeriod() != null ? balanceExpirePolicyReq.getPeriod() : 0);
        result.setExpiredTimeFixed(balanceExpirePolicyReq.getFixTimeExpire());
        result.setStatus(ECommonStatus.of(request.getStatus()));
        return result;
    }

    public static BalanceExpirePolicy toBalanceExpirePolicyOneFromPoolUpdateReq(BalanceExpirePolicy balanceExpirePolicy, PoolUpdateReq request) {
        BalanceExpirePolicyReq balanceExpirePolicyReq = request.getBalanceExpirePolicy();

        balanceExpirePolicy.setName(request.getName() != null ? request.getName() : balanceExpirePolicy.getName());
        balanceExpirePolicy.setType(balanceExpirePolicyReq.getType() != null ? EBalanceExpirePolicyType.of(balanceExpirePolicyReq.getType()) : balanceExpirePolicy.getType());
        balanceExpirePolicy.setPeriodDay(balanceExpirePolicyReq.getPeriod() != null ? balanceExpirePolicyReq.getPeriod() : balanceExpirePolicy.getPeriodDay());
        balanceExpirePolicy.setExpiredTimeFixed(balanceExpirePolicyReq.getFixTimeExpire() != null ? balanceExpirePolicyReq.getFixTimeExpire() : balanceExpirePolicy.getExpiredTimeFixed());
        balanceExpirePolicy.setStatus(request.getStatus() != null ? ECommonStatus.of(request.getStatus()) : balanceExpirePolicy.getStatus());
        return balanceExpirePolicy;
    }
}
