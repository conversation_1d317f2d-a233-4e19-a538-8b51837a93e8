package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.mapper.TerminalMapper;
import com.oneid.loyalty.accounting.ops.model.req.TerminalCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TerminalUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.TerminalEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.TerminalRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ValidationService;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsTerminalServiceImpl implements OpsTerminalService {
    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    CorporationRepository corporationRepository;

    @Autowired
    ChainRepository chainRepository;

    @Autowired
    StoreRepository storeRepository;

    @Autowired
    PosRepository posRepository;

    @Autowired
    PosService posService;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsCorporationService opsCorporationService;

    @Autowired
    OpsChainService opsChainService;

    @Autowired
    OpsStoreService opsStoreService;
    
    @Autowired
    private ValidationService validationService;

    @Override
    public Page<TerminalRes> filter(Integer businessId, Integer corporationId, Integer chainId, Integer storeId, String terminalName, String terminalCode, String status, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<Pos> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (corporationId != null)
            specificationBuilder.add(new SearchCriteria("corporationId", corporationId, SearchOperation.EQUAL));

        if (chainId != null)
            specificationBuilder.add(new SearchCriteria("chainId", chainId, SearchOperation.EQUAL));

        if (storeId != null)
            specificationBuilder.add(new SearchCriteria("storeId", storeId, SearchOperation.EQUAL));

        if (terminalName != null)
            specificationBuilder.add(new SearchCriteria("name", terminalName, SearchOperation.EQUAL));

        if (terminalCode != null)
            specificationBuilder.add(new SearchCriteria("code", terminalCode, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        Page<Pos> terminals = posService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = terminals.getContent().stream().map(Pos::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> corporationIds = terminals.getContent().stream().map(Pos::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationbyIds = opsCorporationService.getMapById(corporationIds);
        Set<Integer> chainIds = terminals.getContent().stream().map(Pos::getChainId).collect(Collectors.toSet());
        Map<Integer, Chain> chainbyIds = opsChainService.getMapById(chainIds);
        Set<Integer> storeIds = terminals.getContent().stream().map(Pos::getStoreId).collect(Collectors.toSet());
        Map<Integer, Store> storebyIds = opsStoreService.getMapById(storeIds);

        return new PageImpl<TerminalRes>(terminals.getContent()
                .stream().map(it -> TerminalRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        corporationbyIds.get(it.getCorporationId()),
                        chainbyIds.get(it.getChainId()),
                        storebyIds.get(it.getStoreId())
                )).collect(Collectors.toList())
                , pageRequest, terminals.getTotalElements());
    }

    @Override
    public TerminalRes get(Integer id) {
        Optional<Pos> pos = posService.find(id);
        if (!pos.isPresent())
            throw new BusinessException(ErrorCode.POS_NOT_FOUND, "Pos not found in business", LogData.createLogData().append("pos_id", id));

        return TerminalRes.of(
                pos.get(),
                businessRepository.findById(pos.get().getBusinessId()).orElse(null),
                corporationRepository.findById(pos.get().getCorporationId()).orElse(null),
                chainRepository.findById(pos.get().getChainId()).orElse(null),
                storeRepository.findById(pos.get().getStoreId()).orElse(null)
        );
    }

    @Override
    public List<TerminalEnumAll> getEnumAll() {
        return posRepository.findAll().stream().map(TerminalEnumAll::of).collect(Collectors.toList());
    }

    @Override
    public void update(Integer id, TerminalUpdateReq terminalUpdateReq) {
        this.validateUniqueTerminalNameForUpdate(terminalUpdateReq.getName(), id);
        Pos pos = posService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.POS_NOT_FOUND, "Pos not found in business", LogData.createLogData().append("pos_id", id)));
        Pos posUpdate = TerminalMapper.toTerminalOne(pos, terminalUpdateReq);
        posService.update(posUpdate);
    }

    private void validateUniqueTerminalNameForUpdate(String name, Integer id) {
        if (name == null) {
            return;
        }
        List<Pos> poses = this.posRepository.findByName(name);
        for (Pos pos : poses) {
            if (!pos.getId().equals(id))
                throw new BusinessException(ErrorCode.TERMINAL_NAME_EXISTED, "terminal name existed", LogData.createLogData().append("terminal_name", name));
        }
    }

    @Override
    public Map<Integer, Pos> getMapById(Collection<Integer> ids) {
        List<Pos> posList = this.posRepository.findAllByIdIn(ids);
        return posList.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t,
                (value1, value2) -> value2
        ));
    }

    @Override
    public void add(TerminalCreateReq req) {
        validationService.validateBusinessId(req.getBusinessId());
        validationService.validateCorporationId(req.getBusinessId(), req.getCorporationId());
        validationService.validateChainId(req.getCorporationId(), req.getChainId());
        Store store = validationService.validateStoreId(req.getChainId(), req.getStoreId());
        Pos posChecker = posRepository.findByStoreIdAndCode(store.getId(), req.getCode());
        
        if (posChecker != null)
            throw new BusinessException(ErrorCode.POS_EXISTED, "Pos exist in store", null);

        Pos pos = TerminalMapper.toTerminalOne(req);
        
        posRepository.save(pos);
    }
}
