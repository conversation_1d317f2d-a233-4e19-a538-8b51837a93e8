package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.oneloyalty.common.constant.EAdjustmentType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class TransactionDetailRes {
    private Long id;

    private String txnRefNo;

    private String invoiceNo;

    private Invoice originalInvoice;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private ShortEntityRes corporation;

    private ShortEntityRes chain;

    private ShortEntityRes store;

    private ShortEntityRes terminal;

    private Date transactionTime;

    private EOpsIdType productAccountType;

    private String productAccountCode;

    private String channel;

    private String service;

    private EOpsTransactionType transactionType;

    private EAdjustmentType adjustmentType;

    private String description;

    private EBoolean cancellation;

    private String cancellationType;

    private Date cancelledAt;

    private Invoice relatedInvoice;

    private BigDecimal gmv;

    private BigDecimal grossAmount;

    private BigDecimal nettAmount;

    private Date retentionTime;

    private String reasonCode;

    private String reasonName;

    private String errorCode;

    private String errorMessage;

    private Date createdAt;

    private String createdBy;

    private Date approvedAt;

    private String approvedBy;

    private ETransactionStatus status;

    private List<Attribute> attributes;

    private List<PointRedeemDetail> pointRedeemDetails;

    private List<PointAwardDetail> pointAwardDetails;

    private MemberProfile memberProfile;

    private EBoolean syncWithElastic;

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Invoice {
        private String invoiceNo;

        private String txnRefNo;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Attribute {
        private String attribute;

        private String value;

        private String dataType;

        private String dataTypeDisplay;

        private ECommonStatus status;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PointAwardDetail {
        private BigDecimal point;

        private BigDecimal balanceBefore;

        private BigDecimal balanceAfter;

        private ShortEntityRes pool;

        private ShortEntityRes scheme;

        private ShortEntityRes currency;

        private Date awardRetentionTime;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PointRedeemDetail {
        private BigDecimal point;

        private BigDecimal balanceBefore;

        private BigDecimal balanceAfter;

        private ShortEntityRes pool;

        private ShortEntityRes currency;

        private ShortEntityRes scheme;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class MemberProfile {
        private Long memberId;

        private String memberCode;

        private String memberName;

        private String phoneNo;

        private String dob;

        private String gender;

        private String email;

        private EIdentifyType identifyType;

        private String identifyNo;

        private String address;

        private String status;
    }
}