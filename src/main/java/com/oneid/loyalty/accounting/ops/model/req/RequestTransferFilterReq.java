package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@Getter
@Builder
public class RequestTransferFilterReq {
    private Long cardTransferNo;
    private ECardTransferIndicatorStatus indicator;
    private ECardTransferStatus status;
    private Integer programId;
    private Integer fromStoreId;
    private Integer toStoreId;
    private Integer batchNo;
    private Pageable pageRequest;
}