package com.oneid.loyalty.accounting.ops.component.attribute.strategy;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class DateAttributeValueStrategy extends AttributeValueStrategy<String> {

    private static final String DATE_PATTERN_FORMAT = "dd/MM/yyyy";

    private static final String DATE_PATTERN_FORMAT_YYYYMMDD = "yyyyMMdd";

    public DateAttributeValueStrategy(AttributeMasterDataRepository attributeMasterDataRepository) {
        super(attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(EAttributeDataDisplayType type) {
        return EAttributeDataDisplayType.DATE.equals(type);
    }

    @Override
    public String serialize(Object value, Integer... programIds) {
        try {
            Date date = new Date(DateTimes.parse(DATE_PATTERN_FORMAT_YYYYMMDD, value.toString()));
            verifyMasterData(value.toString());
            return DateTimes.formatYYYYMMDD(date);
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.INVALID_ATTRIBUTE_DATE_FORMAT,
                    null,
                    null,
                    new Object[]{this.conditionAttributeDto.getAttribute(), DATE_PATTERN_FORMAT_YYYYMMDD}
            );
        }
    }

    @Override
    public String deserialize(String attribute, String value, Integer... programIds) {
        return value;
    }
}