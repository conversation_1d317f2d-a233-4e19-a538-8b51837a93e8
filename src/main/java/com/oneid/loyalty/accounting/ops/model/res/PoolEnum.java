package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Pool;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
@AllArgsConstructor
public class PoolEnum {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    public static PoolEnum of(Pool pool) {
        return new PoolEnum(
                pool.getId(),
                pool.getCode(),
                pool.getName(),
                pool.getStatus().getValue()
        );
    }
}
