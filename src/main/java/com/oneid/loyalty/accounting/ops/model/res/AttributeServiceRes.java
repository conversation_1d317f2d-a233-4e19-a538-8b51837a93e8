package com.oneid.loyalty.accounting.ops.model.res;

import com.oneid.oneloyalty.common.constant.EServiceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttributeServiceRes {

    private EServiceType serviceType;

    private ShortEntityRes code;

    private String operator;

    private List<String> values;

    private Date startDate;

    private Date endDate;
}
