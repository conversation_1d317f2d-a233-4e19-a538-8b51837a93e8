package com.oneid.loyalty.accounting.ops.support.feign.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.nio.charset.Charset;
import java.util.Base64;

public class SapInterceptor implements RequestInterceptor {
    private String basicAuth;

    public SapInterceptor(String basicAuth){
        this.basicAuth = Base64.getEncoder().encodeToString(basicAuth.getBytes(Charset.forName("US-ASCII")));
    }
    
    @Override
    public void apply(RequestTemplate template) {
        template.header("Authorization", "Basic " + basicAuth);
    }
}
