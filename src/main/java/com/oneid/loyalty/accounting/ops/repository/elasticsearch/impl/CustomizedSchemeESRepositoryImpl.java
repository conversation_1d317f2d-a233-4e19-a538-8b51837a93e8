package com.oneid.loyalty.accounting.ops.repository.elasticsearch.impl;

import com.oneid.loyalty.accounting.ops.model.elasticsearch.SchemeES;
import com.oneid.loyalty.accounting.ops.repository.elasticsearch.CustomizedSchemeESRepository;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CustomizedSchemeESRepositoryImpl implements CustomizedSchemeESRepository {
    @Value("${app.elasticsearch.index.scheme-management}")
    private String indexName;
    
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    
    @Override
    public Page<SchemeES> findPage(Integer businessId, Integer programId, 
            ESchemeType type, String code, ECommonStatus status, 
            OffsetDateTime startDate, OffsetDateTime endDate,
            String corporationCode,
            String chainCode,
            String storeCode,
            String terminalCode,
            Pageable pageable
    ) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        
        if (Objects.nonNull(businessId)) {
            queryBuilder.must(QueryBuilders.matchQuery("businessId", businessId));
        }
        if (Objects.nonNull(programId)) {
            queryBuilder.must(QueryBuilders.matchQuery("programId", programId));
        }
        if (Objects.nonNull(type)) {
            queryBuilder.must(QueryBuilders.matchQuery("schemeType", type.getValue()));
        }
        if (Objects.nonNull(code)) {
            queryBuilder.must(QueryBuilders.matchQuery("code", code));
        }
        if (Objects.nonNull(status)) {
            queryBuilder.must(QueryBuilders.matchQuery("status", status.getValue()));
        }
        if (Objects.nonNull(startDate)) {
            queryBuilder.must(QueryBuilders.rangeQuery("startDate").gte(startDate));
        }
        if (Objects.nonNull(endDate)) {
            queryBuilder.must(QueryBuilders.rangeQuery("endDate").lte(endDate));
        }

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (Objects.nonNull(corporationCode)) {
            BoolQueryBuilder queryCorp = QueryBuilders.boolQuery()
                    .should(QueryBuilders.multiMatchQuery(corporationCode, "includedCorporations"))
                    .should(QueryBuilders.multiMatchQuery(corporationCode, "excludedCorporations"));
            boolQueryBuilder.should(queryCorp);
        }
        
        if (Objects.nonNull(chainCode)) {
            BoolQueryBuilder queryChain = QueryBuilders.boolQuery()
                    .should(QueryBuilders.multiMatchQuery(chainCode, "includedChains"))
                    .should(QueryBuilders.multiMatchQuery(chainCode, "excludedChains"));
            boolQueryBuilder.should(queryChain);
        }
        
        if (Objects.nonNull(storeCode)) {
            BoolQueryBuilder queryStore = QueryBuilders.boolQuery()
                    .should(QueryBuilders.multiMatchQuery(storeCode, "includedStores"))
                    .should(QueryBuilders.multiMatchQuery(storeCode, "excludedStores"));
            boolQueryBuilder.should(queryStore);
        }
        
        if (Objects.nonNull(terminalCode)) {
            BoolQueryBuilder queryTerminal = QueryBuilders.boolQuery()
                    .should(QueryBuilders.multiMatchQuery(terminalCode, "includedTerminals"))
                    .should(QueryBuilders.multiMatchQuery(terminalCode, "excludedTerminals"));
            boolQueryBuilder.should(queryTerminal);
        }
        queryBuilder.must(boolQueryBuilder);

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(queryBuilder)
                .withPageable(pageable)
                .build();
        
        SearchHits<SchemeES> hits = elasticsearchRestTemplate.search(query, 
                SchemeES.class,
                IndexCoordinates.of(indexName));
        
        List<SchemeES> content = hits.getSearchHits().stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        
        return new PageImpl<>(content, pageable, hits.getTotalHits());
    }
    
}
