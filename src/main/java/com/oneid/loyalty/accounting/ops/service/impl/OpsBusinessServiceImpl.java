package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.BusinessDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.BusinessRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.CreateBusinessMCReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.GetApproveBusinessMCService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.UpdateBusinessMCReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsBusinessServiceImpl implements OpsBusinessService {
    private static final String CON_DAO_DISTRICT_CODE = "755";

    @Autowired
    BusinessService businessService;
    
    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private DistrictRepository districtRepository;

    @Autowired
    GetApproveBusinessMCService getApproveBusinessMCService;
    
    @Autowired
    private WardRepository wardRepository;

    @Override
    public List<BusinessDropDownRes> getAll(SpecificationBuilder<Business> specification) {
        return businessService.searchAll(specification)
                .stream().map(BusinessDropDownRes::valueOf).collect(Collectors.toList());
    }

    @Override
    public Page<BusinessRes> search(SpecificationBuilder<Business> specification, Pageable pageable) {
        Page<Business> result = businessRepository.findAll(specification, pageable);

        return new PageImpl<>(
                result.getContent().stream().map(BusinessRes::of).collect(Collectors.toList()),
                pageable,
                result.getTotalElements());
    }

    @Override
    public AbsGetApproveRes getApproveCreate(CreateBusinessMCReq req) {
        Business business = new Business();

        business.setCountryId(req.getPayload().getCountryId());
        business.setProvinceId(req.getPayload().getProvinceId());
        business.setDistrictId(req.getPayload().getDistrictId());
        business.setWardId(req.getPayload().getWardId());
        business.setName(req.getPayload().getName());
        business.setDescription(req.getPayload().getDescription());
        business.setStatus(ECommonStatus.of(req.getPayload().getStatus()));
        business.setContactPerson(req.getPayload().getContactPerson());
        business.setEmail(req.getPayload().getEmail());
        business.setAddress1(req.getPayload().getAddress1());
        business.setAddress2(req.getPayload().getAddress2());
        business.setPostalCode(req.getPayload().getPostalCode());
        business.setPhone(req.getPayload().getPhone());
        business.setWebsite(req.getPayload().getWebsite());
        business.setCountryId(req.getPayload().getCountryId());
        business.setProvinceId(req.getPayload().getProvinceId());
        business.setDistrictId(req.getPayload().getDistrictId());
        business.setWardId(req.getPayload().getWardId());

        validateBusiness(business);

        return getApproveBusinessMCService.getApproveCreate(req);
    }

    @Override
    public AbsGetApproveRes getApproveUpdate(final UpdateBusinessMCReq req) {

        Business business = businessRepository.findById(req.getPayload().getBusinessId()).orElseThrow(
                ()->new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", req)
        );

        business.setCountryId(req.getPayload().getCountryId());
        business.setProvinceId(req.getPayload().getProvinceId());
        business.setDistrictId(req.getPayload().getDistrictId());
        business.setWardId(req.getPayload().getWardId());
        business.setName(req.getPayload().getName());
        business.setDescription(req.getPayload().getDescription());
        business.setStatus(ECommonStatus.of(req.getPayload().getStatus()));
        business.setContactPerson(req.getPayload().getContactPerson());
        business.setEmail(req.getPayload().getEmail());
        business.setAddress1(req.getPayload().getAddress1());
        business.setAddress2(req.getPayload().getAddress2());
        business.setPostalCode(req.getPayload().getPostalCode());
        business.setPhone(req.getPayload().getPhone());
        business.setWebsite(req.getPayload().getWebsite());

        this.validateBusiness(business);

        req.getPayload().setBusinessCode(business.getCode());
        return getApproveBusinessMCService.getApproveUpdate(req);
    }

    @Override
    public BusinessRes getOne(Integer id) {
        Business business = this.businessRepository.findById(id).orElseThrow(() -> new BusinessException(
                ErrorCode.BUSINESS_NOT_FOUND, "Business not found", LogData.createLogData().append("business_id", id)));
        
        BusinessRes result = new BusinessRes();
        
        Integer wardId = null;
        Integer districtId = null;
        
        if(business.getWardId() != null) {
            Ward ward = wardRepository.getOne(business.getWardId());
            
            if(!ward.getStatus().equals(ECommonStatus.ACTIVE) || !ward.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                ward = wardRepository.findByCodeAndStatusAndNewStatus(ward.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
            }
            
            wardId = ward.getId();
            districtId = ward.getDistrictId();
        }
        
        if(districtId == null && business.getDistrictId() != null) {
            District district = districtRepository.getOne(business.getDistrictId());
            
            if(!district.getStatus().equals(ECommonStatus.ACTIVE) || !district.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                district = districtRepository.findByCodeAndStatusAndNewStatus(district.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
            }
            
            districtId = district.getId();
        }
        
        result.setId(business.getId());
        result.setCode(business.getCode());
        result.setName(business.getName());
        result.setDescription(business.getDescription());
        result.setStatus(business.getStatus().getValue());
        result.setStartDate(business.getStartDate());
        result.setEndDate(business.getEndDate());
        result.setContactPerson(business.getContactPerson());
        result.setEmail(business.getEmail());
        result.setAddress1(business.getAddress1());
        result.setAddress2(business.getAddress2());
        result.setCountryId(business.getCountryId());
        result.setProvinceId(business.getProvinceId());
        result.setDistrictId(districtId);
        result.setWardId(wardId);
        result.setPostalCode(business.getPostalCode());
        result.setPhone(business.getPhone());
        result.setWebsite(business.getWebsite());
        result.setCreatedBy(business.getCreatedBy());
        result.setUpdatedBy(business.getUpdatedBy());
        result.setCreatedAt(business.getCreatedAt());
        result.setUpdatedAt(business.getUpdatedAt());

        return result;
    }

    @Override
    public Map<Integer, Business> getMapById(Collection<Integer> ids) {
        List<Business> businesses = this.businessRepository.findAllByIdIn(ids);
        return businesses.stream().collect(Collectors.toMap(
                Business::getId,
                t -> t,
                (value1, value2) -> value2
        ));
    }

    // Validate not null for all location properties
    private void verifyRequiredLocation(Business business) {
        if (business.getCountryId() == null || business.getProvinceId() == null || business.getDistrictId() == null) {
            throw new BusinessException(
                    ErrorCode.BAD_REQUEST, "Location must not be null",
                    LogData.createLogData().append("business_location", business));
        }

        final District district = this.districtRepository
                .findById(business.getDistrictId())
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.DISTRICT_NOT_FOUND, "District is not found",
                        LogData.createLogData().append("district", business.getDistrictId())));

        if (!district.getCode().equals(CON_DAO_DISTRICT_CODE) && business.getWardId() == null) {
            throw new BusinessException(
                    ErrorCode.BAD_REQUEST, "Location must not be null",
                    LogData.createLogData().append("business_location", business));
        }
    }


    private void validateBusiness(Business business) {
        Integer id = business.getId();
        Business businessByCode = this.businessRepository.findByCode(business.getCode());
        if (businessByCode != null && !businessByCode.getId().equals(id)) {
            throw new BusinessException(ErrorCode.BUSINESS_CONFLICT_CODE, "Business code existed in the system", LogData.createLogData().append("business_code", business.getCode()));
        } else {
            List<Business> businessByName = this.businessRepository.findByName(business.getName())
                    .stream().filter((e) -> !e.getId().equals(id)).collect(Collectors.toList());
            if (businessByName.size() > 0) {
                throw new BusinessException(ErrorCode.BUSINESS_CONFLICT_NAME, "Business name existed in the system",
                        LogData.createLogData().append("business_name", business.getCode()));
            } else {
                List<Business> businessByPhone = this.businessRepository.findByPhone(business.getPhone())
                        .stream().filter((e) -> !e.getId().equals(id)).collect(Collectors.toList());
                if (businessByPhone.size() > 0) {
                    throw new BusinessException(ErrorCode.BUSINESS_CONFLICT_PHONE, "Business phone existed in the system", LogData.createLogData().append("business_phone", business.getCode()));
                } else {
                    this.verifyRequiredLocation(business);
                }
            }
        }
    }
}
