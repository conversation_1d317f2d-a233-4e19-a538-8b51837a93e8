package com.oneid.loyalty.accounting.ops.support;

import java.util.Optional;

import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

public class AuditorAwareImpl implements AuditorAware<OPSAuthenticatedPrincipal> {
    @Override
    public Optional<OPSAuthenticatedPrincipal> getCurrentAuditor() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        return Optional.of((OPSAuthenticatedPrincipal) authentication.getPrincipal());
    }
}