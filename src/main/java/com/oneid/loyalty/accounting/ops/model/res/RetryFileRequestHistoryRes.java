package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RetryFileRequestHistoryRes {
    private Integer id;

    private String fileName;

    private EProcessingStatus status;

    private String createdBy;

    private Long createdAt;
}
