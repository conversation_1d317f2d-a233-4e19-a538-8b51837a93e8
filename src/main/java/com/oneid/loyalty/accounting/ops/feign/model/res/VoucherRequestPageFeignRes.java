package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VoucherRequestPageFeignRes {
    private Integer number;

    private Integer size;

    private Integer totalElements;

    private Integer totalPages;

    private List<SaleRecordFeignRes> content;

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class SaleRecordFeignRes {
        private String code;

        private String name;
    }
}