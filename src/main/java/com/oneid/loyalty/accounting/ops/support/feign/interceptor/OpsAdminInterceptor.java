package com.oneid.loyalty.accounting.ops.support.feign.interceptor;

import java.nio.charset.Charset;
import java.util.Base64;

import feign.RequestInterceptor;
import feign.RequestTemplate;

public class OpsAdminInterceptor implements RequestInterceptor {
    private String basicAuth;
    
    public OpsAdminInterceptor(String basicAuth){
        this.basicAuth = Base64.getEncoder().encodeToString(basicAuth.getBytes(Charset.forName("US-ASCII")));
    }
    
    @Override
    public void apply(RequestTemplate template) {
        template.header("Authorization", "Basic " + basicAuth);
    }
}
