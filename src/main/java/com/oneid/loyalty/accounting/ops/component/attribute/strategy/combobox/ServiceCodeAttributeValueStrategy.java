package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.AttributeServiceCode;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.service.AttributeServiceCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ServiceCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    private final static Logger LOGGER = LoggerFactory.getLogger(ServiceCodeAttributeValueStrategy.class);
    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;
    @Autowired
    private AttributeServiceCodeService attributeServiceCodeService;

    public ServiceCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getServiceCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, Integer... programIds) {
        AttributeServiceCode attributeServiceCode = attributeServiceCodeService.findByCodeAndProgramIdAndStatus(value, programIds[0], ECommonStatus.ACTIVE);

        if (Objects.isNull(attributeServiceCode)) {
            LOGGER.warn("Service code {} not found", value);
            throw new IllegalArgumentException();
        }

        return AttributeCombobox.builder()
                .name(attributeServiceCode.getCode())
                .value(attributeServiceCode.getValue())
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> serviceCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return attributeServiceCodeService.findByProgramIdAndCodeIn(programIds[0], serviceCodes)
                    .stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(s.getCode())
                            .value(s.getValue())
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}