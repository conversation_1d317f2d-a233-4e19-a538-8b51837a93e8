package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
public class HomeController extends BaseController {

    @GetMapping("health")
    @ResponseBody
    public ResponseEntity<?> health() {
        return success(null);
    }
}