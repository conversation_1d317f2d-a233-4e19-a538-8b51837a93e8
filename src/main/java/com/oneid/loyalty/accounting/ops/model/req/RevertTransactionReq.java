package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
public class RevertTransactionReq {
    @NotNull
    @NotEmpty
    @JsonProperty("transaction_id")
    private String transactionId;

    @NotNull
    @NotEmpty
    @JsonProperty("original_invoice_no")
    private String originalInvoiceNo;

    @NotNull
    @JsonProperty("is_partial")
    private Boolean isPartial;

    @Valid
    @JsonProperty("new_txn_info")
    private NewTxnInfo newTxnInfo;

    @Getter
    @Setter
    public static class NewTxnInfo {
        @JsonProperty("store_id")
        private Integer storeId;

        @JsonProperty("terminal_id")
        private Integer terminalId;

        @JsonProperty("invoice_no")
        private String invoiceNo;

        @Min(0)
        @JsonProperty("refund_amount")
        private BigDecimal refundAmount;

        @Min(0)
        @JsonProperty("redeem_point")
        private BigDecimal redeemPoint;

        @JsonProperty("currency_code")
        private String currencyCode;

        @JsonProperty("transaction_time")
        private Long transactionTime;

        @JsonProperty("description")
        private String description;
    }
}
