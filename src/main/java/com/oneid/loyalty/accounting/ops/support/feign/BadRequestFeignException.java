package com.oneid.loyalty.accounting.ops.support.feign;

import feign.FeignException;
import lombok.Getter;

@Getter
public class BadRequestFeignException extends FeignException {
    private static final long serialVersionUID = 4665217289023454912L;
    
    protected BadRequestFeignException(int status, String message) {
        super(status, message);
    }
    
    public BadRequestFeignException(int status, String request, int errorCode, String errorMessage) {
        super(status, errorMessage);
        this.request = request;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    private String request;
    private int errorCode;
    private String errorMessage;
}
