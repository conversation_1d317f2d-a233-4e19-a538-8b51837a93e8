package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = RejectOperatorRequest.RejectOperatorRequestBuilder.class)
public class RejectOperatorRequest {

    private String reason;
    private Integer requestId;

    @JsonPOJOBuilder(withPrefix = "")
    public static class RejectOperatorRequestBuilder {
    }
}
