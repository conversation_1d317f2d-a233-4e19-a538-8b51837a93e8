package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentProcessStatus;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = TierAdjustmentRequestRes.TierAdjustmentRequestResBuilder.class)
public class TierAdjustmentRequestRes {
    private Integer requestId;
    private Integer reviewId;
    private String programName;
    private Long memberId;
    private String fullName;
    private String phoneNo;
    private String policyName;
    private ShortEntityRes nextTier;
    private ShortEntityRes currentTier;
    private String reasonDescription;
    private String note;
    private ECommonStatus status;
    private EApprovalStatus approvalStatus;
    private String businessName;
    private Date createdAt;
    private String createdBy;
    private Date updatedAt;
    private String updatedBy;
    private Date approvedAt;
    private String approvedBy;
    private String reason;
    private String idType;
    private String idNo;
    private String description;
    private ETierAdjustmentProcessStatus processStatus;
    private String errorMessage;
    private String memberCode;

    @JsonPOJOBuilder(withPrefix = "")
    public static class TierAdjustmentRequestResBuilder {
    }
}
