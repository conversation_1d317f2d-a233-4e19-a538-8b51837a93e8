package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.service.outbound.model.data.AbstractRes;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AbsGetApproveRes extends AbstractRes {

    @JsonProperty("data")
    private Data data;

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("id")
        private Long changeRequestId;

        @JsonProperty("maker_id")
        private Long makerId;

        @JsonProperty("maker_name")
        private String makerName;

        @JsonProperty("status")
        private String status;

        @JsonProperty("result")
        private String result;
    }
}
