package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Builder
@JsonDeserialize(builder = PayloadOperatorReq.PayloadOperatorReqBuilder.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PayloadOperatorReq {
    @Valid
    @NotEmpty(message = "operator_corps must not be empty")
    private List<OperatorCorp> operatorCorps;

    @JsonPOJOBuilder(withPrefix = "")
    public static class PayloadOperatorReqBuilder {
    }

    @Getter
    @Builder
    @JsonDeserialize(builder = OperatorCorp.OperatorCorpBuilder.class)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class OperatorCorp {
        @NotNull(message = "id must not be null (corporation)")
        private Integer id;

        @Valid
        @NotEmpty(message = "operator_chains must not be empty")
        private List<OperatorChain> operatorChains;

        @JsonPOJOBuilder(withPrefix = "")
        public static class OperatorCorpBuilder {
        }

        @Getter
        @Builder
        @JsonDeserialize(builder = OperatorChain.OperatorChainBuilder.class)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class OperatorChain {
            @NotNull(message = "id must not be null (chain)")
            private Integer id;

            @Valid
            @NotEmpty(message = "operator_stores must not be empty")
            private List<OperatorStore> operatorStores;

            @JsonPOJOBuilder(withPrefix = "")
            public static class OperatorChainBuilder {
            }

            @Getter
            @Builder
            @JsonDeserialize(builder = OperatorStore.OperatorStoreBuilder.class)
            @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
            public static class OperatorStore {
                @NotNull(message = "id must not be null (store)")
                private Integer id;

                @Valid
                @NotEmpty(message = "operator_terminals must not be empty")
                private List<OperatorTerminal> operatorTerminals;

                @JsonPOJOBuilder(withPrefix = "")
                public static class OperatorStoreBuilder {
                }

                @Getter
                @Builder
                @JsonDeserialize(builder = OperatorTerminal.OperatorTerminalBuilder.class)
                @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
                public static class OperatorTerminal {
                    @NotNull(message = "id must not be null (terminal)")
                    private Integer id;

                    @JsonPOJOBuilder(withPrefix = "")
                    public static class OperatorTerminalBuilder {
                    }
                }
            }
        }
    }
}
