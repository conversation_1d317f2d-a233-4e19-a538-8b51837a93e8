package com.oneid.loyalty.accounting.ops.model.res;


import com.oneid.oneloyalty.common.constant.ECommonStatus;

import com.oneid.oneloyalty.common.constant.ECounterLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class CounterStatisticRes {
    private Integer counterStatusHeader;

    private Statistic statistic;

    private List<CounterHistory> counterHistories;

    private Integer totalHistories;

    @Getter
    @Setter
    public class Statistic {
        private BigDecimal totalCounted;
        private String typeCounted;

        private Integer totalLevel;
        private String typeLevel;

        private Integer totalPeriod;
        private String typePeriod;
    }

    @Getter
    @Setter
    @Builder
    public static class CounterHistory{
        private String code;
        private ECounterLevel level;
        private Date startedAt;
        private Date endedAt;
        private BigDecimal totalCounted;
        private String unit;
        private ECommonStatus status;
    }
}
