package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EBoolean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor()
public class CreateGCTFeignReq {

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("recipient_code")
    private String recipientCode;

    @JsonProperty("transfer_id")
    private Integer transferId;

    @JsonProperty("transfer_no")
    private String transferNo;

    @JsonProperty("selected_batches")
    private List<SelectedBatch> selectedBatches = new ArrayList<>();

    @Data
    @NoArgsConstructor
    public static class SelectedBatch {
        @JsonProperty("batch_id")
        private Integer batchId;
        @JsonProperty("cpr_batch_no")
        private Long cprBatchNo;
        @JsonProperty("convert_xlsx_flag")
        private EBoolean convertXlsxFlag;
        @JsonProperty("store_id")
        private Integer storeId;
    }
}
