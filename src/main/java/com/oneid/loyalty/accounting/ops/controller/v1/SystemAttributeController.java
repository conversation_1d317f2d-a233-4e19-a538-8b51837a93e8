package com.oneid.loyalty.accounting.ops.controller.v1;

import java.time.LocalDate;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.SystemAttributeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.SystemAttributeRes;
import com.oneid.loyalty.accounting.ops.service.OpsSystemAttributeService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/attribute/system")
@Validated
public class SystemAttributeController extends BaseController {
    
    @Autowired
    private OpsSystemAttributeService opsSystemAttributeService;
    
    @GetMapping("request/available")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getRequestPage(
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {
        Pageable pageable = new OffsetBasedPageRequest(offset, limit, null);
        Page<SystemAttributeRes> page = opsSystemAttributeService.getRequestPage(code, name, status, approvalStatus, pageable);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("request/available/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getRequestById(@PathVariable("id") Integer requestId) {
        return success(opsSystemAttributeService.getRequestById(requestId));
    }
    
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    @GetMapping("request/available/{id}/changeable")
    public ResponseEntity<?> getChangeableRequestById(@PathVariable("id") int id) {
        return success(opsSystemAttributeService.getChangeableRequestById(id));
    }
    
    @PostMapping("request/{id}/change")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestChange(@PathVariable("id") Integer requestId,
            @RequestBody @Valid SystemAttributeUpdateReq req
    ) {
        return success(opsSystemAttributeService.requestChange(requestId, req));
    }
    
    @GetMapping("request/in-review")
    @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewPage(
            @RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(name = "from_date", required = false) LocalDate fromDate,
            @RequestParam(name = "to_date", required = false) LocalDate toDate,
            @MakerCheckerOffsetPageable Pageable pageable
    ) {
        Page<SystemAttributeRes> page = opsSystemAttributeService.getInReviewPage(approvalStatus, fromDate, toDate, pageable);
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("request/in-review/{reviewId}")
    @Authorize(role = AccessRole.SYSTEM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewById(@PathVariable("reviewId") Integer reviewId) {
        return success(opsSystemAttributeService.getInReviewById(reviewId));
    }
    
}
