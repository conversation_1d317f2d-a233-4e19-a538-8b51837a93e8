package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.RoundingRule;
import com.oneid.oneloyalty.common.entity.BaseMakerChecker;
import com.oneid.oneloyalty.common.entity.Scheme;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@EqualsAndHashCode
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class SchemeCombineRes extends BaseMakerCheckerDetail {

    @JsonProperty("scheme_id")
    private Integer schemeId;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("scheme_code")
    private String schemeCode;

    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @JsonProperty("scheme_name")
    private String schemeName;

    private String description;

    @JsonProperty("scheme_type")
    private ESchemeType schemeType;

    @JsonProperty("pool_id")
    private Integer poolId;

    private ECommonStatus status;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("rounding_rule")
    private RoundingRule roundingRule;

    @JsonProperty("min_amount")
    private Long minAmount;

    @JsonProperty("max_amount")
    private Long maxAmount;

    @JsonProperty("rule_list")
    private List<RuleRecordRes> ruleList;

    @JsonProperty("formula_group")
    private FormulaGroupRes formulaGroup;

    @JsonProperty("is_vat")
    private EBoolean isVAT;

    @JsonProperty("edit_key")
    private String editKey;

    SchemeCombineRes(BaseMakerChecker entity) {
        super(entity);
    }

    public SchemeCombineRes() {
    }

    public static SchemeCombineRes valueOf(Scheme scheme) {
        SchemeCombineRes res = new SchemeCombineRes(scheme);
        res.setSchemeId(scheme.getId());
        res.setSchemeName(scheme.getName());
        res.setDescription(scheme.getDescription());
        res.setRuleLogic(scheme.getRuleLogic());
        res.setSchemeCode(scheme.getCode());
        res.setSchemeType(scheme.getSchemeType());
        res.setStatus(scheme.getStatus());
        res.setMinAmount(scheme.getMinPoint());
        res.setMaxAmount(scheme.getMaxPoint());
        res.setEndDate(DateTimes.toEpochSecond(scheme.getEndDate()));
        res.setStartDate(DateTimes.toEpochSecond(scheme.getStartDate()));
        res.setProgramId(scheme.getProgramId());
        res.setPoolId(scheme.getPoolId());
        res.setRoundingRule(scheme.getRoundingRule());
        res.setIsVAT(scheme.getIsVAT());

        return res;
    }
}