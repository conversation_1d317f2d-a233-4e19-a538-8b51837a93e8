package com.oneid.loyalty.accounting.ops.model.dto;

import com.poiji.annotation.ExcelCellName;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;

@Data
@Validated
public class RevertPartialTransactionExcelDTO extends RevertFullTransactionExcelDTO {

    @ExcelCellName("REFUND_AMOUNT")
    private BigDecimal refundAmount;

    @ExcelCellName("REDEEM_POINT")
    private BigDecimal redeemPoint;

    private BigDecimal grossAmount;
}
