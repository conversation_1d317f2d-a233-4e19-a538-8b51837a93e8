package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.*;
import lombok.Getter;
import lombok.experimental.SuperBuilder;


@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierMatchingConfigRequestDetailRes {

    private Integer id;

    private String code;

    private String name;

    private ECommonStatus status;

}