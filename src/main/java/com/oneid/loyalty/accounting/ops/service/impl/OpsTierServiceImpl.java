package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.req.TierPrivilegeReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPrivilegeRes;
import com.oneid.loyalty.accounting.ops.model.res.TierRes;
import com.oneid.loyalty.accounting.ops.publisher.MessagePublisher;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsTierService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.EProgramTierPeriodType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.PartnerSupplier;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;
import com.oneid.oneloyalty.common.entity.TierPrivilege;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.PartnerSupplierRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierPolicyService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.TierPrivilegeService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsTierServiceImpl implements OpsTierService {

    @Value("${spring.redis.reset-rule.channel}")
    private String resetRuleTopic;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private OpsRuleRequestService opsRuleRequestService;

    @Autowired
    private ProgramTierPolicyService programTierPolicyService;

    @Autowired
    private PartnerSupplierRepository partnerSupplierRepository;

    @Autowired
    private TierPrivilegeService tierPrivilegeService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    private MessagePublisher messagePublisher;

    @Override
    public void create(ProgramTierReq tierReq) {
        Business business = businessService.findActive(tierReq.getBusinessId());
        Program program = programService.findByIdAndBusinessId(tierReq.getProgramId(), business.getId());
        programTierPolicyService.findById(tierReq.getTierPolicyId());

        // Check duplicate Code
        validateReq(tierReq);

        // Validate Rules
        opsRuleRequestService.validateRules(program.getId(), EServiceType.TIER, tierReq.getRules());

        // Validate Tier privileges
        if (!tierReq.getNonePrivilege() && !CollectionUtils.isEmpty(tierReq.getPrivileges())) {
            validatePrivilege(tierReq.getPrivileges());
        }

        // Call maker
        makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.PROGRAM_TIER, UUID.randomUUID().toString(), tierReq);
    }

    private void validatePrivilege(List<TierPrivilegeReq> privilegeReqs) {
        privilegeReqs.forEach(tierPrivilegeReq -> {
            // Validate partner supplier id
            if (tierPrivilegeReq.getPartnerSupplierId() != null) {
                PartnerSupplier partnerSupplier =
                        partnerSupplierRepository.findById(tierPrivilegeReq.getPartnerSupplierId()).orElse(null);
                if (partnerSupplier == null) {
                    throw new BusinessException(
                            ErrorCode.PARTNER_SUPPLIER_IS_INVALID,
                            "Partner supplier id is invalid",
                            null,
                            new Object[]{tierPrivilegeReq.getPartnerSupplierId()}
                    );
                }
                tierPrivilegeReq.setPartnerSupplierName(partnerSupplier.getName());
            }
        });
    }

    private void validateReq(ProgramTierReq tierReq) {
        // Check duplicate Code
        if (tierReq.isCreate()) {
            Optional<ProgramTier> tierOpt = programTierService.find(tierReq.getProgramId(), tierReq.getCode());
            if (tierOpt.isPresent()) {
                throw new BusinessException(
                        ErrorCode.TIER_CODE_IS_BEING_USED,
                        "Program tier code is existed",
                        null,
                        new Object[]{tierReq.getCode()}
                );
            }
        }
    }

    @Override
    public Page<TierRes> getAvailableTiers(FilterProgramTierReq filterProgramTierReq, Pageable pageable) {

        Business business = businessService.findById(filterProgramTierReq.getBusinessId());
        SpecificationBuilder<ProgramTier> specification = filterProgramTierReq.getSpecificationBuilder();
        Page<ProgramTier> tierPage = programTierService.searchPaging(specification, pageable);

        Map<Integer, Program> programMap =
                programService.findByBusinessId(business.getId()).stream()
                        .collect(Collectors.toMap(Program::getId, p -> p));

        List<TierRes> tierRes = tierPage.getContent().stream()
                .map(tier -> {
                    Program program = programMap.get(tier.getProgramId());
                    return TierRes.builder()
                            .requestId(tier.getId())
                            .approvalStatus(EApprovalStatus.APPROVED)
                            .version(tier.getVersion())
                            .businessName(business.getName())
                            .programName(program.getName())
                            .programCode(program.getCode())
                            .tierStatus(tier.getStatus())
                            .tierName(tier.getName())
                            .tierCode(tier.getTierCode())
                            .tierRank(tier.getRankNo())
                            .build();
                }).collect(Collectors.toList());

        return new PageImpl<>(tierRes, pageable, tierPage.getTotalElements());
    }

    @Override
    public Page<TierRes> getInReviewTiers(EApprovalStatus approvalStatus,
                                          String fromCreatedAt, String toCreatedAt,
                                          String fromReviewedAt, String toReviewedAt,
                                          String createdBy, String checkedBy,
                                          Integer offset, Integer limit) {


        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.PROGRAM_TIER.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromCreatedAt, toCreatedAt),
                        checkedBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromReviewedAt, toReviewedAt)
                );

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        Map<Integer, Business> businessMap = businessService.findAll()
                .stream()
                .collect(Collectors.toMap(Business::getId, b -> b));

        Map<Integer, Program> programMap = programService.findAll()
                .stream()
                .collect(Collectors.toMap(Program::getId, p -> p));

        List<TierRes> listTierRes = previewRes.getData().stream()
                .map(e -> convertPreview(e, businessMap, programMap))
                .collect(Collectors.toList());

        return new PageImpl<>(listTierRes, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private TierRes convertPreview(
            MakerCheckerInternalDataDetailRes data,
            Map<Integer, Business> businessMap,
            Map<Integer, Program> programMap
    ) {
        ProgramTierReq tierReq = this.jsonMapper.convertValue(data.getPayload(), ProgramTierReq.class);
        Business business = businessMap.get(tierReq.getBusinessId());
        Program program = programMap.get(tierReq.getProgramId());
        return TierRes.builder()
                .requestId(data.getId().intValue())
                .reviewId(data.getId().intValue())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .version(data.getVersion())
                .businessId(business.getId())
                .businessName(business.getName())
                .programName(program.getName())
                .programCode(program.getCode())
                .tierStatus(tierReq.getTierStatus())
                .tierName(tierReq.getName())
                .tierCode(tierReq.getCode())
                .tierRank(tierReq.getRankNo())
                .createdAt(data.getMadeDate() != null ? DateTimes.toEpochSecond(Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant())) : null)
                .createdBy(data.getMadeByUserName())
                .reviewedAt(data.getCheckedDate() != null ? DateTimes.toEpochSecond(Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant())) : null)
                .reviewedBy(data.getCheckedByUserName())
                .build();
    }

    @Override
    public TierRes getAvailableTier(Integer tierId) {
        ProgramTier programTier = programTierService.findById(tierId);
        Program program = programService.findById(programTier.getProgramId());
        Business business = businessService.findById(program.getBusinessId());
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(programTier.getTierPolicyId());

        List<RuleRes> ruleRes = opsRuleRequestService.getRules(program.getId(), EServiceType.TIER, programTier.getTierCode());
        List<TierPrivilegeRes> privilegeRes = getTierPrivilegeRes(programTier.getId());

        return TierRes.builder()
                .id(programTier.getId())
                .requestId(programTier.getId())
                .reviewId(programTier.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .programId(program.getId())
                .programName(program.getName())
                .programCode(program.getCode())
                .tierPolicyId(programTierPolicy.getId())
                .tierPolicyName(programTierPolicy.getName())
                .tierRank(programTier.getRankNo())
                .description(programTier.getDescription())
                .tierCode(programTier.getTierCode())
                .enDescription(programTier.getEnDescription())
                .tierName(programTier.getName())
                .tierEnName(programTier.getEnName())
                .cardBackgroundUrl(programTier.getCardBackgroundUrl())
                .badgeIconUrl(programTier.getBadgeIconUrl())
                .qualifyPoint(programTier.getQualifyPoint())
                .expirePeriodType(programTierPolicy.getCounterPeriodType())
                .expirePeriod(programTierPolicy.getCounterPeriod())
                .tierStatus(programTier.getStatus())
                .approvalStatus(EApprovalStatus.APPROVED)
                .nonePrivilege(EBoolean.YES.equals(programTier.getIsNonePrivilege()))
                .privileges(privilegeRes)
                .rules(ruleRes)
                .version(programTier.getVersion())
                .nextVersion(programTier.getVersion() + 1)
                .editKey(null)
                .lastUpdateAt(programTier.getUpdatedAt())
                .lastUpdateBy(programTier.getUpdatedBy())
                .build();
    }

    private List<TierPrivilegeRes> getTierPrivilegeRes(Integer programTierId) {
        List<TierPrivilege> tierPrivileges =
                tierPrivilegeService.findByProgramTierIdAndStatus(
                        programTierId,
                        ECommonStatus.ACTIVE,
                        Sort.by("id")
                );

        return tierPrivileges.stream()
                .map(p ->
                        TierPrivilegeRes.builder()
                                .id(p.getId())
                                .name(p.getName())
                                .description(p.getDescription())
                                .enDescription(p.getEnDescription())
                                .enName(p.getEnName())
                                .displayOrder(p.getDisplayOrder())
                                .icon(p.getIcon())
                                //.partnerSupplierId(partnerSupplierId)
                                .action(p.getDeeplink())
                                .brand(p.getBrand())
                                .status(p.getStatus())
                                .build()
                )
                .collect(Collectors.toList());
    }

    @Override
    public TierRes getInReviewTier(Integer reviewId) {
        MakerCheckerInternalDataDetailRes detailRes =
                makerCheckerInternalFeignClient.previewDetailDefault(Long.valueOf(reviewId), EMakerCheckerType.PROGRAM_TIER);
        ProgramTierReq tierReq = this.jsonMapper.convertValue(detailRes.getPayload(), ProgramTierReq.class);

        Program program = programService.findById(tierReq.getProgramId());
        Business business = businessService.findById(tierReq.getBusinessId());
        ProgramTierPolicy programTierPolicy = programTierPolicyService.findById(tierReq.getTierPolicyId());

        List<RuleRes> ruleRes = null;
        if (tierReq.getRules() != null) {
            ruleRes = tierReq.getRules().stream()
                    .map(RuleRes::valueOf)
                    .collect(Collectors.toList());
        }

        List<TierPrivilegeRes> privilegeRes = null;
        if (tierReq.getPrivileges() != null) {
            privilegeRes = tierReq.getPrivileges().stream()
                    .map(TierPrivilegeRes::valueOf)
                    .collect(Collectors.toList());
        }

        return TierRes.builder()
                .id(detailRes.getId().intValue())
                .requestId(detailRes.getId().intValue())
                .reviewId(detailRes.getId().intValue())
                .businessId(business.getId())
                .businessName(business.getName())
                .programName(program.getName())
                .programCode(program.getCode())
                .tierPolicyId(programTierPolicy.getId())
                .tierPolicyName(programTierPolicy.getName())
                .tierRank(tierReq.getRankNo())
                .description(tierReq.getDescription())
                .tierCode(tierReq.getCode())
                .enDescription(tierReq.getEnDescription())
                .tierName(tierReq.getName())
                .tierEnName(tierReq.getEnName())
                .cardBackgroundUrl(tierReq.getCardBackgroundUrl())
                .badgeIconUrl(tierReq.getBadgeIconUrl())
                .qualifyPoint(tierReq.getQualifyPoint())
                .expirePeriodType(programTierPolicy.getCounterPeriodType())
                .expirePeriod(programTierPolicy.getCounterPeriod())
                .tierStatus(tierReq.getTierStatus())
                .approvalStatus(EApprovalStatus.of(detailRes.getStatus()))
                .nonePrivilege(tierReq.getNonePrivilege())
                .privileges(privilegeRes)
                .rules(ruleRes)
                .version(detailRes.getVersion())
                .lastUpdateAt(detailRes.getMadeDateToDate())
                .lastUpdateBy(detailRes.getMadeByUserName())
                .createdBy(detailRes.getMadeByUserName())
                .createdAt(detailRes.getMadeDateToTimestamp())
                .approvedBy(detailRes.getCheckedByUserName())
                .approvedAt(detailRes.getCheckedDateToTimestamp())
                .build();
    }

    @Override
    public TierRes getEditTier(Integer tierId) {
        ProgramTier programTier = programTierService.findById(tierId);

        // Validate pending request
        makerCheckerInternalFeignClient.validateRequestPending(
                EMakerCheckerType.PROGRAM_TIER,
                programTier.getRequestCode(),
                ErrorCode.TIER_REQUEST_IS_ALREADY_REQUESTED
        );

        TierRes tierRes = this.getAvailableTier(tierId);
        String editKey = opsReqPendingValidator.generateEditKey(programTier.getRequestCode(), programTier.getVersion());
        tierRes.setEditKey(editKey);

        return tierRes;
    }

    @Override
    public void update(Integer requestId, ProgramTierReq req) {
        ProgramTier programTier = programTierService.findById(requestId);
        Program program = programService.findById(programTier.getProgramId());
        Business business = businessService.findById(program.getBusinessId());
        req.setId(programTier.getId());
        req.setBusinessId(business.getId());
        req.setProgramId(program.getId());
        req.setTierPolicyId(programTier.getTierPolicyId());
        req.setCode(programTier.getTierCode());

        // Validate pending request
        makerCheckerInternalFeignClient.validateRequestPending(
                EMakerCheckerType.PROGRAM_TIER,
                programTier.getRequestCode(),
                ErrorCode.TIER_REQUEST_IS_ALREADY_REQUESTED
        );

        // Validate edit key
        if (req.getEditKey() != null) {
            opsReqPendingValidator.verifyEditKey(req.getEditKey(), programTier.getRequestCode(), programTier.getVersion());
        }

        // Validate Rules
        opsRuleRequestService.validateRules(program.getId(), EServiceType.TIER, req.getRules());

        // Validate Tier privileges
        if (!req.getNonePrivilege() && !CollectionUtils.isEmpty(req.getPrivileges())) {
            validatePrivilege(req.getPrivileges());
        }

        // Call maker
        makerCheckerInternalFeignClient.makerDefault(EMakerCheckerType.PROGRAM_TIER, programTier.getRequestCode(), req);
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.PROGRAM_TIER);
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();

        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                ProgramTierReq tierReq = this.jsonMapper.convertValue(detailResData.getPayload(), ProgramTierReq.class);
                /*
                    Process approve request
                 */
                resetRuleReqs = processApprove(detailResData, tierReq);
            }
        }

        // Check reset rule
        if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
            APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
            if (ruleRes.getMeta().getCode() != 200) {
                throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
            }
        }

        // Call checker
        makerCheckerInternalFeignClient.checkerDefault(req);

        if (EApprovalStatus.APPROVED == req.getStatus() && CollectionUtils.isNotEmpty(resetRuleReqs)) {
            ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                    .id(UUID.randomUUID().toString())
                    .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                    .timeStamp(System.currentTimeMillis())
                    .payload(resetRuleReqs)
                    .build();
            messagePublisher.publish(resetRuleTopic, ruleEvent);
        }
    }


    private List<ResetRuleReq> processApprove(
            MakerCheckerInternalDataDetailRes detailResData,
            ProgramTierReq tierReq
    ) {
        // Validate request
        validateReq(tierReq);

        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();

        ProgramTier programTier;
        String createdBy = null;
        String updatedBy;

        if (!tierReq.isCreate()) {
            programTier = programTierService.findById(tierReq.getId());
            updatedBy = detailResData.getMadeByUserName();
        } else {
            Business business = businessService.findById(tierReq.getBusinessId());
            Program program = programService.findByIdAndBusinessId(tierReq.getProgramId(), business.getId());
            ProgramTierPolicy tierPolicy = programTierPolicyService.findById(tierReq.getTierPolicyId());

            programTier = new ProgramTier();
            programTier.setTierCode(tierReq.getCode());
            programTier.setProgramId(program.getId());
            programTier.setBusinessId(business.getId());
            programTier.setTierPolicyId(tierPolicy.getId());
            programTier.setExpiredPeriod(tierPolicy.getCounterPeriod());
            programTier.setExpiredType(convertType(tierPolicy.getCounterPeriodType()));
            updatedBy = createdBy = detailResData.getMadeByUserName();
        }

        programTier.setName(tierReq.getName());
        programTier.setEnName(tierReq.getEnName());
        programTier.setDescription(tierReq.getDescription());
        programTier.setEnDescription(tierReq.getEnDescription());
        programTier.setStatus(tierReq.getTierStatus());
        programTier.setRankNo(tierReq.getRankNo());
        programTier.setCardBackgroundUrl(tierReq.getCardBackgroundUrl());
        programTier.setCardThumbnailUrl(null);
        programTier.setBadgeIconUrl(tierReq.getBadgeIconUrl());
        programTier.setHotline(null);
        programTier.setIsTierDefault(null);
        programTier.setQualifyPoint(tierReq.getQualifyPoint());
        programTier.setVersion(detailResData.getVersion());
        programTier.setRequestCode(detailResData.getRequestCode());
        if (tierReq.getNonePrivilege()) {
            programTier.setIsNonePrivilege(EBoolean.YES);
        } else {
            programTier.setIsNonePrivilege(EBoolean.NO);
        }
        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        opsReqPendingValidator.updateInfoChecker(programTier, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        programTier = programTierService.save(programTier);

        // Create rules
        if (tierReq.getRules() != null && !tierReq.getRules().isEmpty()) {
            resetRuleReqs =  opsRuleRequestService.cuRules(
                    programTier.getProgramId(),
                    EServiceType.TIER,
                    programTier.getTierCode(),
                    tierReq.getRules()
            );
        }

        // Create privilege
        if (!tierReq.getNonePrivilege() && tierReq.getPrivileges() != null) {
            List<TierPrivilege> tierPrivileges = new ArrayList<>();
            for (TierPrivilegeReq privilegeReq : tierReq.getPrivileges()) {
                TierPrivilege tierPrivilege = cuPrivilege(programTier, privilegeReq);
                tierPrivileges.add(tierPrivilege);
            }
            tierPrivilegeService.saveAll(tierPrivileges);
        }

        return resetRuleReqs;
    }

    private TierPrivilege cuPrivilege(ProgramTier programTier, TierPrivilegeReq req) {
        TierPrivilege tierPrivilege;
        if (req.getId() != null) {
            tierPrivilege = tierPrivilegeService.findById(req.getId());
            if (req.isArchive()) {
                if (ECommonStatus.ACTIVE.equals(tierPrivilege.getStatus())) {
                    tierPrivilege.setStatus(ECommonStatus.INACTIVE);
                    return tierPrivilege;
                } else {
                    return null;
                }
            }
        } else {
            tierPrivilege = new TierPrivilege();
            tierPrivilege.setBusinessId(programTier.getBusinessId());
            tierPrivilege.setProgramId(programTier.getProgramId());
            tierPrivilege.setProgramTierId(programTier.getId());
        }

        tierPrivilege.setDescription(req.getDescription());
        tierPrivilege.setEnDescription(req.getEnDescription());
        tierPrivilege.setName(req.getName());
        tierPrivilege.setEnName(req.getEnName());
        tierPrivilege.setIcon(req.getIcon());
        tierPrivilege.setBrand(req.getBrand());
        tierPrivilege.setDeeplink(req.getAction());
        tierPrivilege.setDisplayOrder(req.getDisplayOrder());
        tierPrivilege.setStatus(ECommonStatus.ACTIVE);

        return tierPrivilege;
    }

    private EProgramTierPeriodType convertType(ECounterPeriod eCounterPeriod) {
        if (eCounterPeriod == null) {
            return null;
        }
        switch (eCounterPeriod) {
            case DAILY:
                return EProgramTierPeriodType.DAY;
            case MONTHLY:
                return EProgramTierPeriodType.MONTH;
            case YEARLY:
                return EProgramTierPeriodType.YEAR;
            default:
                return null;
        }
    }
}
