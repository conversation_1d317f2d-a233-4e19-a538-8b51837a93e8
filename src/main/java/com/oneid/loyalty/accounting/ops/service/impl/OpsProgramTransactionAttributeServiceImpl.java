package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.ProgramTransactionAttributeRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpsProgramTransactionAttributeServiceImpl implements OpsProgramTransactionAttributeService {
    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private ProgramService programService;

    @Override
    public List<ProgramTransactionAttributeRes> findByProgramId(Integer programId) {
        programService.findActive(programId);

        return programTransactionAttributeService
                .listAllByProgramId(programId)
                .stream()
                .map(item -> ProgramTransactionAttributeRes
                        .builder()
                        .id(item.getId())
                        .attribute(item.getAttribute())
                        .description(item.getDescription())
                        .dataType(item.getDataType())
                        .dataTypeDisplay(item.getDataTypeDisplay())
                        .build())
                .collect(Collectors.toList());
    }
}
