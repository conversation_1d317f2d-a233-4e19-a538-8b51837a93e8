package com.oneid.loyalty.accounting.ops.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.config.OpsAdminFeignConfig;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.support.web.security.authentication.JwtIntrospectRequest;

@FeignClient(name = "ops-admin-service", url = "${ops.admin.url}", configuration = OpsAdminFeignConfig.class)
public interface OpsAdminServiceFeignClient {
    @RequestMapping(method = RequestMethod.POST, value = "/token/introspect")
    APIResponse<Object> introspectToken(
            @RequestParam(name = "module") String modules,
            @RequestBody JwtIntrospectRequest request);
}
