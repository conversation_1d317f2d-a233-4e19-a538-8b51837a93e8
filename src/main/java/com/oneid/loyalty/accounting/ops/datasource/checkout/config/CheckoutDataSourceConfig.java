package com.oneid.loyalty.accounting.ops.datasource.checkout.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "checkoutEntityManagerFactory",
        transactionManagerRef = "checkoutTransactionManager",
        basePackages = { "com.oneid.loyalty.accounting.ops.datasource.checkout.repository" }
)
public class CheckoutDataSourceConfig {

    @Bean(name = "checkoutDataSourceProperties")
    @ConfigurationProperties("spring.mysql-datasource.checkout")
    public DataSourceProperties checkoutDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "checkoutDataSource")
    @ConfigurationProperties("spring.mysql-datasource.checkout.hikari")
    public DataSource checkoutDataSource(@Qualifier("checkoutDataSourceProperties") DataSourceProperties checkoutDataSourceProperties) {
        return checkoutDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "checkoutEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean checkoutEntityManagerFactory(
            EntityManagerFactoryBuilder checkoutEntityManagerFactoryBuilder, @Qualifier("checkoutDataSource") DataSource checkoutDataSource) {

        Map<String, String> checkoutJpaProperties = new HashMap<>();
        checkoutJpaProperties.put("hibernate.dialect", "org.hibernate.dialect.MySQL8Dialect");
        checkoutJpaProperties.put("hibernate.ddl-auto", "none");
        checkoutJpaProperties.put("hibernate.hbm2ddl.auto", "none");

        return checkoutEntityManagerFactoryBuilder
                .dataSource(checkoutDataSource)
                .packages("com.oneid.loyalty.accounting.ops.datasource.checkout.entity")
//                .persistenceUnit("mysqlDataSource")
                .properties(checkoutJpaProperties)
                .build();
    }

    @Bean(name = "checkoutTransactionManager")
    public PlatformTransactionManager checkoutTransactionManager(
            @Qualifier("checkoutEntityManagerFactory") EntityManagerFactory checkoutEntityManagerFactory) {

        return new JpaTransactionManager(checkoutEntityManagerFactory);
    }
}
