package com.oneid.loyalty.accounting.ops.model.res.v2.giftcardres;

import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftCardTypeRes extends ShortEntityRes {
    private Double price;

    public GiftCardTypeRes(GiftCardType giftCardType) {
        super(giftCardType.getId(), giftCardType.getCode(), giftCardType.getCode());
        this.price = giftCardType.getPrice();
    }

}
