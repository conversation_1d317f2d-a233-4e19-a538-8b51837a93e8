package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.PositiveOrZero;

@Getter
@Setter
public class VerifyFormulaGroupReq extends FormulaGroupReq {
    @PositiveOrZero
    @JsonProperty("max_amount")
    private Long maxAmount;

    @PositiveOrZero
    @JsonProperty("min_amount")
    private Long minAmount;

    @AssertTrue(message = "Max amount is greater than min amount")
    public boolean isValidMinMaxAmount() {
        if (this.minAmount == null || this.maxAmount == null) return true;
        return this.maxAmount.compareTo(this.minAmount) >= 0;
    }
}