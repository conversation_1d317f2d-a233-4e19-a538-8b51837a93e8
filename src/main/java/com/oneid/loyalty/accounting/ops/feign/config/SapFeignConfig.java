package com.oneid.loyalty.accounting.ops.feign.config;

import com.oneid.loyalty.accounting.ops.support.feign.interceptor.SapInterceptor;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

public class SapFeignConfig {
    @Value("${sap.basic.auth}")
    private String basicAuth;
    
    @Bean
    public RequestInterceptor sapRequestInterceptor() {
      return new SapInterceptor(basicAuth);
    }
}