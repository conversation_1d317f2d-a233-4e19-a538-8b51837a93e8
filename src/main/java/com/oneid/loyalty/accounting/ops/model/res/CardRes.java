package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.feign.model.res.CardMemberFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberProfileDetailFeignRes;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Card;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CardRes {

	@JsonProperty("id")
	private Integer Id;

	@JsonProperty("member_id")
	private Long memberId;
	
	@JsonProperty("member_code")
	private String memberCode;
	
	@JsonProperty("card_no")
	private String cardNo;
	
	@JsonProperty("card_type_id")
	private Integer cardTypeId;

	@JsonProperty("card_type_code")
	private String cardTypeCode;

	@JsonProperty("expiry_date")
	private Long expiryDate;

	@JsonProperty("retention_date")
	private Long retentionDate;

	@JsonProperty("issue_date")
	private Long issueDate;

	@JsonProperty("activation_date")
	private Long activationDate;

	@JsonProperty("card_status")
	private String cardStatus;

	@JsonProperty("created_at")
	private Long createdAt;

	@JsonProperty("business_id")
	private Integer businessId;
	
	@JsonProperty("business_code")
	private String businessCode;
	
	@JsonProperty("business_name")
	private String businessName;

	@JsonProperty("corporation_id")
	private Integer corporationId;
	
	@JsonProperty("corporation_code")
	private String corporationCode;

	@JsonProperty("chain_id")
	private Integer chainId;
	
	@JsonProperty("chain_code")
	private String chainCode;

	@JsonProperty("program_id")
	private Integer programId;
	
	@JsonProperty("program_code")
	private String programCode;
	
	@JsonProperty("program_name")
	private String programName;
	
	@JsonProperty("store_id")
	private Integer storeId;
	
	@JsonProperty("store_code")
	private String storeCode;
	
	@JsonProperty("code_block")
	private String codeBlock;

	@JsonProperty("remark")
	private String remark;

	public static CardRes valueOf(Card card, Business business, Program program, Corporation corporation,
			Map<Long, String> memberCode, Map<Integer, CardType> mapCardType) {
		CardRes res = new CardRes();
		res.setId(card.getId());
		res.setMemberId(card.getMemberId());
		res.setMemberCode(memberCode.get(card.getMemberId()) != null ? memberCode.get(card.getMemberId()) : null);
		res.setCardNo(card.getCardNo());
		res.setCardTypeId(mapCardType.get(card.getCardTypeId()) != null
				? Integer.valueOf(mapCardType.get(card.getCardTypeId()).getId())
				: null);
		res.setCardTypeCode(
				mapCardType.get(card.getCardTypeId()) != null ? mapCardType.get(card.getCardTypeId()).getCardType()
						: null);
		res.setIssueDate(card.getIssueDate() != null ? card.getIssueDate().toInstant().getEpochSecond() : null);
		res.setExpiryDate(card.getExpiryDate() != null ? card.getExpiryDate().toInstant().getEpochSecond() : null);
		res.setRetentionDate(
				card.getRetentionDate() != null ? card.getRetentionDate().toInstant().getEpochSecond() : null);
		res.setActivationDate(
				card.getActivationDate() != null ? card.getActivationDate().toInstant().getEpochSecond() : null);
		res.setCardStatus(card.getCardStatus().getValue());
		res.setBusinessId(business.getId());
		res.setBusinessCode(business.getCode());
		res.setBusinessName(business.getName());
		res.setProgramId(program.getId());
		res.setProgramCode(program.getCode());
		res.setProgramName(program.getName());
		res.setCreatedAt(card.getCreatedAt() != null ? card.getCreatedAt().toInstant().getEpochSecond() : null);
		res.setCorporationCode(corporation.getCode());
		res.setCorporationId(corporation.getId());
		res.setCodeBlock(card.getCodeBlock());
		res.setRemark(card.getRemark());
		return res;
	}
	
	
	
	public static CardRes valueOf(CardMemberFeignRes card, MemberProfileDetailFeignRes memberRes, CardType cardType,Program program, Business business, Chain chain,Corporation corporation, Store store) {
        CardRes res = new CardRes();
        res.setId(card.getId());
        res.setMemberId(memberRes.getId());
        res.setMemberCode(memberRes.getMemberCode());
        res.setCardNo(card.getCardNo());
        res.setCardTypeId(cardType.getId());
        res.setCardTypeCode(cardType.getCardType());
        res.setProgramId(program.getId());
        res.setProgramCode(program.getCode());
        res.setProgramName(program.getName());
        res.setBusinessCode(business.getCode());
        res.setBusinessId(business.getId());
        res.setBusinessName(business.getName());
        res.setChainId(chain.getId());
        res.setChainCode(chain.getCode());
        res.setCorporationCode(corporation.getCode());
        res.setCorporationId(corporation.getId());
        res.setStoreId(store.getId());
        res.setStoreCode(store.getCode());
        res.setIssueDate(card.getIssueDate());
        res.setExpiryDate(card.getExpiryDate());
        res.setRetentionDate(card.getRetentionDate());
        res.setActivationDate(card.getActivationDate());
        res.setCardStatus(card.getCardStatus());
        res.setCreatedAt(card.getCreatedAt());
        res.setCodeBlock(card.getCodeBlock());
        res.setRemark(card.getRemark());
        return res;
    }

}
