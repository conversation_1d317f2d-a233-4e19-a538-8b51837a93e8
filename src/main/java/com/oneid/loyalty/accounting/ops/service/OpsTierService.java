package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.res.TierRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpsTierService {

    Page<TierRes> getAvailableTiers(FilterProgramTierReq req, Pageable pageable);

    TierRes getAvailableTier(Integer requestId);

    TierRes getEditTier(Integer requestId);

    void create(ProgramTierReq req);

    void update(Integer requestId, ProgramTierReq req);

    void approve(ApprovalReq req);

    TierRes getInReviewTier(Integer reviewId);

    Page<TierRes> getInReviewTiers(
            EApprovalStatus approvalStatus,
            String fromCreatedAt, String toCreatedAt,
            String fromReviewedAt, String toReviewedAt,
            String createdBy, String reviewedBy,
            Integer offset,
            Integer limit
    );
}