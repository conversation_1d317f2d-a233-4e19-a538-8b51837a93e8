package com.oneid.loyalty.accounting.ops.validation.search;


import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;

public class TimeFormatValidator implements ConstraintValidator<TimeFormat, String> {

    private String dateFormat;
    private String messageInvalid;

    @Override
    public void initialize(TimeFormat constraintAnnotation) {
        this.messageInvalid = constraintAnnotation.message();
        this.dateFormat = constraintAnnotation.format();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        SimpleDateFormat format = new SimpleDateFormat(this.dateFormat);
        format.setLenient(false);
        try {
            format.parse(value);
        } catch (ParseException e) {
            context.buildConstraintViolationWithTemplate(this.messageInvalid).addConstraintViolation();
            return false;
        }
        return true;
    }
}
