package com.oneid.loyalty.accounting.ops.controller.v1;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberCPRReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/card-production-requests")
@Validated
public class CardProductionRequestController extends BaseController {

    @Autowired
    private OpsCardProductionRequestService opsCardProductionRequestService;

    @Autowired
    private CardServiceFeignClient cardServiceFeignClient;

    @PostMapping("/create")
    @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = { AccessPermission.CREATE })
    public Object create(Authentication authentication, @Valid @RequestBody CreateMemberCPRReq req) throws Exception {
        return success(opsCardProductionRequestService.createCPRRequest((OPSAuthenticatedPrincipal) authentication.getPrincipal(), req));
    }

    @GetMapping
    @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW })
    public Object search(
            @RequestParam(value = "business_id", required = false, defaultValue = "0") Integer businessId,
            @RequestParam(value = "corporation_id", required = false, defaultValue = "0") Integer corporationId,
            @RequestParam(value = "chain_id", required = false, defaultValue = "0") Integer chainId,
            @RequestParam(value = "store_id", required = false, defaultValue = "0") Integer storeId,
            @RequestParam(value = "batch_no", required = false, defaultValue = "0") Long batchNo,
            @RequestParam(value = "status", required = false, defaultValue = "") @Pattern(regexp = "^(A|I|P)?$", message = "'status' only accept A/I/P values") String status,
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit) {
        return cardServiceFeignClient.getCardProductionRequestPage(businessId, corporationId, 
                chainId, storeId, batchNo, status, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getOne(@PathVariable(value = "id") Integer id) {
        return success(this.opsCardProductionRequestService.getById(id));
    }

    @GetMapping("/{id}/export-sftp")
    @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.EXPORT })
    public ResponseEntity<?> exportDetailOne(@PathVariable(value = "id") Integer id) {
        return success(this.opsCardProductionRequestService.exportDetailToSFTP(id));
    }

    @GetMapping("/card-temp")
    @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW })
    public Object getCards( 
            @RequestParam(value = "business_code", required = false, defaultValue = "") String businessCode,
            @RequestParam(value = "program_code", required = false, defaultValue = "") String programCode,
            @RequestParam(value = "store_code", required = false, defaultValue = "") String storeCode,
            @RequestParam(value = "batch_no") Integer batchNo,
            @RequestParam(value = "card_no", required = false, defaultValue = "") String cardNo,
            @RequestParam(value = "card_status", required = false, defaultValue = "") String cardStatus,
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit) {

        return cardServiceFeignClient.getMemberCardTempPage(businessCode, programCode, 
                storeCode, batchNo, cardNo, cardStatus, offset, limit);
    }
}
