package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusInReviewRes {

    private Long id;

    private String code;

    private String name;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private ECommonStatus status;

    private EApprovalStatus approvalStatus;

    private String createdBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    private String reviewedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date reviewedAt;

}
