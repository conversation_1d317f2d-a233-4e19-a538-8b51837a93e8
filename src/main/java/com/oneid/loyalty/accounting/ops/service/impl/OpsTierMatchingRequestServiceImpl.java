package com.oneid.loyalty.accounting.ops.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import com.oneid.loyalty.accounting.ops.model.res.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.base.Objects;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.TierMappingReq;
import com.oneid.loyalty.accounting.ops.model.req.TierMatchingCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TierMatchingUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsTierMatchingRequestService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.TierMappingConfigRequest;
import com.oneid.oneloyalty.common.entity.TierMappingRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import com.oneid.oneloyalty.common.repository.TierMappingConfigRequestRepository;
import com.oneid.oneloyalty.common.repository.TierMappingRequestRepository;

@Service
public class OpsTierMatchingRequestServiceImpl implements OpsTierMatchingRequestService {
    @Value("${maker-checker.module.tier-matching-management}")
    private String moduleId;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private ProgramRepository programRepository;
    
    @Autowired
    private TierMappingRequestRepository tierMappingRequestRepository;
    
    @Autowired
    private TierMappingConfigRequestRepository tierMappingConfigRequestRepository;
    
    @Autowired
    private ProgramTierRepository programTierRepository;
    
    private TransactionTemplate transactionTemplate;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public Page<TierMatchingRequestRes> getAvailableTierMatchingRequests(Integer businessId, Integer programId, String code, String name, ECommonStatus status, EApprovalStatus approvalStatus, Pageable pageable) {
        ECommonStatus requestStatus = null;

        if(approvalStatus != null) {
            switch (approvalStatus) {
                case APPROVED:
                    requestStatus = ECommonStatus.ACTIVE;

                    break;

                case REJECTED:
                    requestStatus = ECommonStatus.INACTIVE;

                    break;

                default:
                    requestStatus = ECommonStatus.PENDING;

                    break;
            }
        }

        Page<Object[]> page = tierMappingRequestRepository.findAvailableRequest(businessId,
                programId,
                code,
                name,
                status,
                requestStatus,
                approvalStatus,
                Sort.Direction.DESC,
                null,
                pageable);

        return new PageImpl<>(maptoDTO(page.getContent(), Collections.emptyMap()), pageable, page.getTotalElements());
    }

    @Override
    public Page<TierMatchingRequestRes> getInReviewTierMatchingRequests(EApprovalStatus approvalStatus, Pageable pageable) {
        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                null,
                null);

        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        List<TierMatchingRequestRes> tierMatchingRequestRes = null;
        if(changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeginResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each, (first, second) -> second));

            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(requestIds)) {
                Map<Integer, TierMatchingRequestRes> mapRes =  maptoDTO(tierMappingRequestRepository.findBy(requestIds), changeRecordFeginResMap)
                        .stream()
                        .collect(Collectors.toMap(TierMatchingRequestRes::getRequestId, each -> each));

                tierMatchingRequestRes = requestIds.stream()
                        .map(id -> mapRes.get(id))
                        .collect(Collectors.toList());
            }

        } else {
            tierMatchingRequestRes = Collections.emptyList();
        }
        return new PageImpl<TierMatchingRequestRes>(tierMatchingRequestRes, pageable, changeRequestPageFeignRes.getTotalRecordCount());

    }
    
    @Override
    public TierMatchingRequestRes requestCreate(TierMatchingCreateReq req) {
        TierMappingRequest reqEntity = transactionTemplate
                .execute(status -> {
                    TierMappingRequest tierMappingRequest = this.createTierMappingRequest(req);
                    
                    updateTierMappingConfigs(tierMappingRequest, req.getTierMappings(), false);
                    
                    return tierMappingRequest;
                });
        
        ChangeRequestFeignReq changeReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(reqEntity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(reqEntity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(changeReq);
        
        return TierMatchingRequestRes.builder()
                .requestId(reqEntity.getId())
                .build();
    }

    @Override
    public TierMatchingRequestDetailRes getAvailableTierMatchingRequestById(Integer requestId) {
        return getDetailTierMatchingRequest(requestId);
    }

    @Override
    public TierMatchingRequestDetailRes getInReviewTierMatchingRequestById(Integer reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> apiResponse = makerCheckerServiceClient.getChangeRequestById(reviewId.toString());
        return getDetailTierMatchingRequest(Integer.parseInt(apiResponse.getData().getObjectId()));
    }

    @Override
    public TierMatchingRequestRes requestUpdate(Integer requestId, TierMatchingUpdateReq req) {
        TierMappingRequest entity = transactionTemplate.execute(status -> {
            TierMappingRequest tierMappingRequest = editTierMappingRequest(requestId, req);
            
            updateTierMappingConfigs(tierMappingRequest, req.getTierMappings(), true);
            
            return tierMappingRequest;
        });
        
        ChangeRequestFeignReq changeReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(entity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(entity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(changeReq);
        
        return TierMatchingRequestRes.builder()
                .requestId(entity.getId())
                .build();
    }
    
    @Override
    public TierMatchingRequestDetailRes getChangeableRequest(Integer requestId) {
        TierMappingRequest request = this.getChangeableRequestById(requestId);
        
        Business business = businessRepository.getOne(request.getBusinessId());
        Program baseProgram = programRepository.getOne(request.getBaseProgramId());
        Program matchedProgram = programRepository.getOne(request.getMatchedProgramId());
        
        List<Object[]> records = tierMappingConfigRequestRepository.findActivatedByRequestIdAlongWithActivatedBaseTier(request.getId(), request.getBaseProgramId());
        
        List<TierMappingConfigRes> mappings = records.stream()
                .map(e -> {
                    TierMappingConfigRequest config = TierMappingConfigRequest.class.cast(e[0]);
                    ProgramTier baseTier = ProgramTier.class.cast(e[1]);
                    ProgramTier matchedTier = ProgramTier.class.cast(e[2]);
                    
                    return TierMappingConfigRes.builder()
                            .id(config == null ? null : config.getId())
                            .status(config == null ? null : config.getStatus())
                            .attributeValue(config == null ? null : config.getAttributeValue())
                            .baseTier(TierMatchingConfigRequestDetailRes.builder()
                                    .id(baseTier.getId())
                                    .code(baseTier.getTierCode())
                                    .name(baseTier.getName())
                                    .status(baseTier.getStatus())
                                    .build())
                            .matchedTier(matchedTier == null ? null : TierMatchingConfigRequestDetailRes.builder()
                                    .id(matchedTier.getId())
                                    .code(matchedTier.getTierCode())
                                    .name(matchedTier.getName())
                                    .status(matchedTier.getStatus())
                                    .build())
                            .build();
                })
                .collect(Collectors.toList());
        
        TierMatchingRequestDetailRes res = TierMatchingRequestDetailRes.builder()
                .requestId(request.getId())
                .businessId(request.getBusinessId())
                .businessName(business.getName())
                .baseProgramId(request.getBaseProgramId())
                .baseProgramName(baseProgram.getName())
                .matchedProgramId(request.getMatchedProgramId())
                .matchedProgramName(matchedProgram.getName())
                .tierMatchingCode(request.getCode())
                .tierMatchingName(request.getName())
                .description(request.getDescription())
                .status(request.getStatus())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .autoMatching(request.getAutoMatching())
                .autoVerify(request.getAutoVerify())
                .expireType(request.getExpireType())
                .durationType(request.getDurationType())
                .verificationType(request.getVerificationType())
                .tierMappings(mappings)
                .approvalStatus(request.getApprovalStatus())
                .approvedBy(request.getApprovedBy())
                .approvedAt(request.getApprovedAt())
                .createdAt(request.getCreatedAt())
                .createdBy(request.getCreatedBy())
                .build();
        
        return res;
    }
    
    private void updateTierMappingConfigs(TierMappingRequest tierMappingRequest, List<TierMappingReq> tierMappings, boolean editMode) {
        Map<Integer, ProgramTier> activatedBaseTierMap = programTierRepository.findActiveByProgramOrderByRankNoDesc(tierMappingRequest.getBaseProgramId())
                .stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
        
        List<ProgramTier> activatedMatchedTiers = programTierRepository.findActiveByProgramOrderByRankNoDesc(tierMappingRequest.getMatchedProgramId());
        
        List<Integer> activatedMatchedTierIds = activatedMatchedTiers.stream()
                .map(e -> e.getId())
                .collect(Collectors.toList());
        
        List<TierMappingConfigRequest> newTierMappingConfigRequests = tierMappings.stream()
                .map(e -> {
                    TierMappingConfigRequest avaiableTierMappingConfigRequest = null;
                    Integer tierMappingConfigId = null;
                    TierMappingConfigRequest.TierMappingConfigRequestBuilder tierMappingConfigRequestBuilder = null;
                    ECommonActionType actionType = ECommonActionType.Create;
                    
                    if(editMode && e.getId() != null) {
                        avaiableTierMappingConfigRequest = tierMappingConfigRequestRepository.findById(e.getId())
                        .orElseThrow(() -> new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_REQUEST_NOT_FOUND, null, null, new Object[] { e.getId() } ));
                        
                        tierMappingConfigId = avaiableTierMappingConfigRequest.getTierMappingConfigId();
                        
                        if(Objects.equal(avaiableTierMappingConfigRequest.getBaseTierId(), e.getBaseTierId()) 
                                && Objects.equal(avaiableTierMappingConfigRequest.getMatchTierId(), e.getMatchTierId())
                                && Objects.equal(avaiableTierMappingConfigRequest.getStatus(), e.getStatus())) {
                            actionType = ECommonActionType.NO_CHANGE;
                            
                            if(!activatedBaseTierMap.containsKey(avaiableTierMappingConfigRequest.getBaseTierId())) {
                                actionType = ECommonActionType.Delete;
                            }
                        } else {
                            actionType = ECommonActionType.Update;
                        }
                    }
                    
                    if(actionType.equals(ECommonActionType.NO_CHANGE) || actionType.equals(ECommonActionType.Delete)) {
                        tierMappingConfigRequestBuilder = TierMappingConfigRequest.builder()
                                .baseTierId(avaiableTierMappingConfigRequest.getBaseTierId())
                                .matchTierId(avaiableTierMappingConfigRequest.getMatchTierId())
                                .attributeValue(avaiableTierMappingConfigRequest.getAttributeValue())
                                .status(avaiableTierMappingConfigRequest.getStatus());
                    } else {
                        if (activatedBaseTierMap.keySet().contains(e.getBaseTierId()) == false) 
                            throw new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_BASE_TIER_ID_IS_INVALID, null, null, new Object[] { e.getBaseTierId() });
                        
                        if (e.getMatchTierId() != null && activatedMatchedTierIds.contains(e.getMatchTierId()) == false)
                            throw new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_MATCHED_TIER_ID_IS_INVALID, null, null, new Object[] { e.getMatchTierId() });
                        
                        if(e.getMatchTierId() == null && !e.getStatus().equals(ECommonStatus.INACTIVE))
                            throw new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_EMPTY_MATCHED_TIER_STATUS_MUST_BE_INACTIVATE, null, null, new Object[] { ECommonStatus.INACTIVE });
                        
                        ProgramTier baseTier = activatedBaseTierMap.get(e.getBaseTierId());
                        
                        tierMappingConfigRequestBuilder = TierMappingConfigRequest.builder()
                                .baseTierId(e.getBaseTierId())
                                .matchTierId(e.getMatchTierId())
                                .attributeValue(tierMappingRequest.getCode() + "-" + baseTier.getTierCode())
                                .status(e.getStatus());
                    }
                    
                    return tierMappingConfigRequestBuilder
                            .tierMappingConfigId(tierMappingConfigId)
                            .tierMappingRequestId(tierMappingRequest.getId())
                            .actionType(actionType).build();
                })
                .collect(Collectors.toList());
        
       Map<Integer, Long> totalBaseTierIds = newTierMappingConfigRequests.stream()
        .collect(Collectors.groupingBy(TierMappingConfigRequest::getBaseTierId, Collectors.counting()));
       
       activatedBaseTierMap.keySet().stream()
       .forEach(id -> {
           Long total = totalBaseTierIds.get(id);
           
           if(total == null)
               throw new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_REQUEST_PROGRAM_TIER_ID_IS_REQUIRED, null, null, new Object[] { id });
           
           if(total > 1)
               throw new BusinessException(ErrorCode.TIER_MAPPING_CONFIG_REQUEST_PROGRAM_TIER_ID_IS_DUPLICATED, null, null, new Object[] { id });
       });
        
        tierMappingConfigRequestRepository.saveAll(newTierMappingConfigRequests);
    }
    
    private TierMappingRequest createTierMappingRequest(TierMatchingCreateReq req) {
        this.getActivatedBusiness(req.getBusinessId());
        this.getActivatedProgram(req.getBusinessId(), req.getBaseProgramId());
        this.getActivatedProgram(req.getBusinessId(), req.getMatchedProgramId());
        
        tierMappingRequestRepository.findPendingVersion(req.getBusinessId() ,req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.TIER_MAPPING_CODE_EXISTED);
                });
        
        tierMappingRequestRepository.findEffectedVersion(req.getBusinessId() ,req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.TIER_MAPPING_CODE_EXISTED);
                });
        
        TierMappingRequest request = TierMappingRequest.builder()
                .businessId(req.getBusinessId())
                .baseProgramId(req.getBaseProgramId())
                .matchedProgramId(req.getMatchedProgramId())
                .code(req.getCode())
                .name(req.getName())
                .description(req.getDescription())
                .startDate(Date.from(req.getStartDate().toInstant()))
                .endDate(Date.from(req.getEndDate().toInstant()))
                .autoMatching(req.getAutoMatching() == true ? EBoolean.YES : EBoolean.NO)
                .autoVerify(req.getAutoVerify() == true ? EBoolean.YES : EBoolean.NO)
                .status(req.getStatus())
                .expireType(req.getExpireType())
                .durationType(req.getDurationType())
                .durationValue(req.getDurationValue())
                .verificationType(req.getVerificationType())
                .approvalStatus(EApprovalStatus.PENDING)
                .requestStatus(ECommonStatus.PENDING)
                .build();
        
        tierMappingRequestRepository.save(request);
        
        return request;
    }
    
    private TierMappingRequest editTierMappingRequest(Integer requestId, TierMatchingUpdateReq req) {
        TierMappingRequest availableRequest = this.getChangeableRequestById(requestId);
        
        Instant currentStartDate = availableRequest.getStartDate().toInstant();
        Instant newStartDate = req.getStartDate().toInstant();
        if (currentStartDate.equals(newStartDate) == false) {
            if (currentStartDate.isBefore(Instant.now()))
                throw new BusinessException(ErrorCode.TIER_MAPPING_START_DATE_CANNOT_BE_EDITED);
            
            if (newStartDate.isBefore(Instant.now()))
                throw new BusinessException(ErrorCode.TIER_MAPPING_INVALID_START_DATE);
        }
        
        TierMappingRequest newTierMappingRequest = TierMappingRequest.builder()
                .businessId(availableRequest.getBusinessId())
                .baseProgramId(availableRequest.getBaseProgramId())
                .matchedProgramId(availableRequest.getMatchedProgramId())
                .code(availableRequest.getCode())
                .name(req.getName())
                .description(req.getDescription())
                .startDate(Date.from(newStartDate))
                .endDate(Date.from(req.getEndDate().toInstant()))
                .autoMatching(req.getAutoMatching() == true ? EBoolean.YES : EBoolean.NO)
                .autoVerify(req.getAutoVerify() == true ? EBoolean.YES : EBoolean.NO)
                .status(req.getStatus())
                .expireType(req.getExpireType())
                .durationType(req.getDurationType())
                .durationValue(req.getDurationValue())
                .verificationType(req.getVerificationType())
                .approvalStatus(EApprovalStatus.PENDING)
                .requestStatus(ECommonStatus.PENDING)
                .build();
        
        tierMappingRequestRepository.save(newTierMappingRequest);
        
        return newTierMappingRequest;
    }
    
    private Business getActivatedBusiness(int id) {
        Business business = businessRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        
        if (business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE);
        
        return business;
    }
    
    private Program getActivatedProgram(int businessId, int programId) {
        Program program = programRepository.findByIdAndBusinessId(programId, businessId)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
        
        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE);
        
        return program;
    }


    private List<TierMatchingRequestRes> maptoDTO(List<Object[]> entities, Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeginResMap){
        List<TierMatchingRequestRes> content = new ArrayList<>();

        String businessName = null;
        String matchedProgramName = null;
        String baseProgramName = null;
        TierMappingRequest tierMappingRequest = null;
        Integer reviewId = null;

        for (Object[] each : entities) {
            tierMappingRequest = TierMappingRequest.class.cast(each[0]);
            businessName = String.class.cast(each[1]);
            matchedProgramName = String.class.cast(each[2]);
            baseProgramName = String.class.cast(each[3]);

            reviewId = changeRecordFeginResMap.containsKey(tierMappingRequest.getId()) ? changeRecordFeginResMap.get(tierMappingRequest.getId()).getChangeRequestId() : null;

            TierMatchingRequestRes mapper = TierMatchingRequestRes.builder()
                    .requestId(tierMappingRequest.getId())
                    .businessId(tierMappingRequest.getBusinessId())
                    .businessName(businessName)
                    .baseProgramId(tierMappingRequest.getBaseProgramId())
                    .baseProgramName(baseProgramName)
                    .matchedProgramId(tierMappingRequest.getMatchedProgramId())
                    .matchedProgramName(matchedProgramName)
                    .tierMatchingCode(tierMappingRequest.getCode())
                    .tierMatchingName(tierMappingRequest.getName())
                    .approvalStatus(tierMappingRequest.getApprovalStatus())
                    .status(tierMappingRequest.getStatus())
                    .startDate(tierMappingRequest.getStartDate())
                    .endDate(tierMappingRequest.getEndDate())
                    .reviewId(reviewId)
                    .createdBy(tierMappingRequest.getCreatedBy())
                    .createdAt(tierMappingRequest.getCreatedAt())
                    .approvedBy(tierMappingRequest.getApprovedBy())
                    .approvedAt(tierMappingRequest.getApprovedAt())
                    .rejectedBy(tierMappingRequest.getRejectedBy())
                    .rejectedAt(tierMappingRequest.getRejectedAt())
                    .build();
            content.add(mapper);
        }

        return content;
    }

    private TierMatchingRequestDetailRes getDetailTierMatchingRequest(Integer requestId) {
        TierMappingRequest tierMappingRequest = tierMappingRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.TIER_MAPPING_REQUEST_NOT_FOUND));

        Business business = businessRepository.getOne(tierMappingRequest.getBusinessId());
        Program baseProgram = programRepository.getOne(tierMappingRequest.getBaseProgramId());
        Program matchedProgram = programRepository.getOne(tierMappingRequest.getMatchedProgramId());

        List<TierMappingConfigRequest> tierMappingConfigRequests = tierMappingConfigRequestRepository.findActivatedByTierMappingRequestId(requestId);
        List<TierMappingConfigRes> tierMappings = new ArrayList<>();
        
        if(CollectionUtils.isNotEmpty(tierMappingConfigRequests)) {
            Set<Integer> tierIds = new HashSet<Integer>();
            
            tierMappingConfigRequests.stream()
            .forEach(each -> {
                tierIds.add(each.getBaseTierId());
                tierIds.add(each.getMatchTierId());
            });
            
            Map<Integer, ProgramTier> availableProgramTiers = programTierRepository.findAllById(tierIds)
            .stream()
            .collect(Collectors.toMap(ProgramTier::getId, each -> each));
            
            tierMappingConfigRequests
            .stream().map( r -> {
                ProgramTier baseProgramTier = availableProgramTiers.get(r.getBaseTierId());
                
                TierMatchingConfigRequestDetailRes baseTier = TierMatchingConfigRequestDetailRes.builder()
                        .id(r.getBaseTierId())
                        .code(baseProgramTier.getTierCode())
                        .name(baseProgramTier.getName())
                        .status(baseProgramTier.getStatus())
                        .build();
                
                TierMatchingConfigRequestDetailRes matchedTier = null;
                if (r.getMatchTierId() != null) {
                    ProgramTier matchedProgramTier = availableProgramTiers.get(r.getMatchTierId());
                    
                    matchedTier = TierMatchingConfigRequestDetailRes.builder()
                            .id(r.getMatchTierId())
                            .code(matchedProgramTier.getTierCode())
                            .name(matchedProgramTier.getName())
                            .status(matchedProgramTier.getStatus())
                            .build();
                }
                
                return tierMappings.add(TierMappingConfigRes.builder()
                        .baseTier(baseTier)
                        .matchedTier(matchedTier)
                        .attributeValue(r.getAttributeValue())
                        .status(r.getStatus())
                        .id(r.getId())
                        .build());
            }).collect(Collectors.toList());
        }

        return TierMatchingRequestDetailRes.builder()
                .requestId(tierMappingRequest.getId())
                .businessId(tierMappingRequest.getBusinessId())
                .businessName(business.getName())
                .baseProgramId(tierMappingRequest.getBaseProgramId())
                .baseProgramName(baseProgram.getName())
                .matchedProgramId(tierMappingRequest.getMatchedProgramId())
                .matchedProgramName(matchedProgram.getName())
                .tierMatchingCode(tierMappingRequest.getCode())
                .tierMatchingName(tierMappingRequest.getName())
                .description(tierMappingRequest.getDescription())
                .startDate(tierMappingRequest.getStartDate())
                .endDate(tierMappingRequest.getEndDate())
                .autoMatching(tierMappingRequest.getAutoMatching())
                .autoVerify(tierMappingRequest.getAutoVerify())
                .expireType(tierMappingRequest.getExpireType())
                .durationType(tierMappingRequest.getDurationType())
                .durationValue(tierMappingRequest.getDurationValue())
                .verificationType(tierMappingRequest.getVerificationType())
                .rejectedReason(tierMappingRequest.getRejectedReason())
                .status(tierMappingRequest.getStatus())
                .approvalStatus(tierMappingRequest.getApprovalStatus())
                .tierMappings(tierMappings)
                .createdBy(tierMappingRequest.getCreatedBy())
                .createdAt(tierMappingRequest.getCreatedAt())
                .approvedBy(tierMappingRequest.getApprovedBy())
                .approvedAt(tierMappingRequest.getApprovedAt())
                .rejectedBy(tierMappingRequest.getRejectedBy())
                .rejectedAt(tierMappingRequest.getRejectedAt())
                .build();
    }
    
    private TierMappingRequest getChangeableRequestById(Integer requestId) {
        TierMappingRequest request = tierMappingRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.TIER_MAPPING_REQUEST_NOT_FOUND));
        
        if (request.getApprovalStatus() == EApprovalStatus.PENDING)
            throw new BusinessException(ErrorCode.TIER_MAPPING_IS_ALREADY_REQUESTED);
        
        if (request.getApprovalStatus() == EApprovalStatus.REJECTED)
            throw new BusinessException(ErrorCode.TIER_MAPPING_REJECTED_CAN_NOT_BE_EDITED);
        
        if (request.getRequestStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.TIER_MAPPING_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED);
        
        tierMappingRequestRepository.findPendingVersion(request.getBusinessId(), request.getCode())
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.TIER_MAPPING_IS_ALREADY_REQUESTED);
                });
        
        return request;
    }
}
