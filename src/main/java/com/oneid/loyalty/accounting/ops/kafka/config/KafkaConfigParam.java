package com.oneid.loyalty.accounting.ops.kafka.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Component
public class KafkaConfigParam {

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.truststore.location}")
    public String truststoreLocation;

    @JsonIgnore
    @Value("${kafka.truststore.password}")
    public String truststorePassword;

    @JsonIgnore
    @Value("${kafka.key.password}")
    public String keyPassword;

    @JsonIgnore
    @Value("${kafka.keystore.password}")
    public String keystorePassword;

    @Value("${kafka.keystore.location}")
    public String keystoreLocation;

    @Value("${kafka.ssl-enable}")
    public Boolean sslEnabled;
}
