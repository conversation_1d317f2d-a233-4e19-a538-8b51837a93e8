package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.service.OpsGenerateTemplateService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.repository.*;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class OpsGenerateTemplateServiceImpl implements OpsGenerateTemplateService {
    @Value("${app.row-access-window.size:100}")
    private int ROW_ACCESS_WINDOW_SIZE;

    @Autowired
    CountryRepository countryRepository;

    @Autowired
    ProvinceRepository provinceRepository;

    @Autowired
    DistrictRepository districtRepository;

    @Autowired
    WardRepository wardRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    ProgramTierRepository programTierRepository;

    @Autowired
    CardTypeRepository cardTypeRepository;

    @Override
    public Path registerMember() throws IOException, OpenXML4JException {
        Path tmpOutputFile = Files.createTempFile(null, null);
        InputStream exportTemplateInputStream = new ClassPathResource("template/template-register-member.xlsx")
                .getInputStream();
        try (XSSFWorkbook wbTmp = new XSSFWorkbook(exportTemplateInputStream)) {
            try (SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(wbTmp, ROW_ACCESS_WINDOW_SIZE)) {
                XSSFWorkbook xssfWorkbook = sxssfWorkbook.getXSSFWorkbook();
                xssfWorkbook.createSheet();
                List<Country> countries = countryRepository.findAll();
                Sheet sheetAddress = xssfWorkbook.getSheetAt(1);
                Cell cell = null;
                Row row = null;
                Row headerAddress = sheetAddress.getRow(1);
                int rowIndexAddress = 1;
                int numOfCellsAddress = headerAddress.getPhysicalNumberOfCells();
                Map<Integer, CellStyle> cellStylesAddress = IntStream.range(0, numOfCellsAddress)
                        .boxed()
                        .collect(Collectors.toMap(index -> index, index -> headerAddress.getCell(index).getCellStyle()));
                for (Country country : countries) {
                    List<Province> provinces = provinceRepository.findByCountryId(country.getId());
                    for (Province province : provinces) {
                        List<District> districts = districtRepository.findByProvinceId(province.getId());
                        for (District district : districts) {
                            List<Ward> wards = wardRepository.findByDistrictId(district.getId());
                            for (Ward ward : wards) {
                                row = sheetAddress.createRow(rowIndexAddress);

                                cell = row.createCell(0);
                                cell.setCellValue(toStringOrBlank(country.getCode()));
                                cell.setCellStyle(cellStylesAddress.get(0));


                                cell = row.createCell(1);
                                cell.setCellValue(toStringOrBlank(country.getName()));
                                cell.setCellStyle(cellStylesAddress.get(1));

                                cell = row.createCell(2);
                                cell.setCellValue(toStringOrBlank(province.getCode()));
                                cell.setCellStyle(cellStylesAddress.get(2));

                                cell = row.createCell(3);
                                cell.setCellValue(toStringOrBlank(province.getName()));
                                cell.setCellStyle(cellStylesAddress.get(3));

                                cell = row.createCell(4);
                                cell.setCellValue(toStringOrBlank(district.getCode()));
                                cell.setCellStyle(cellStylesAddress.get(4));

                                cell = row.createCell(5);
                                cell.setCellValue(toStringOrBlank(district.getName()));
                                cell.setCellStyle(cellStylesAddress.get(5));

                                cell = row.createCell(6);
                                cell.setCellValue(toStringOrBlank(ward.getCode()));
                                cell.setCellStyle(cellStylesAddress.get(6));

                                cell = row.createCell(7);
                                cell.setCellValue(toStringOrBlank(ward.getName()));
                                cell.setCellStyle(cellStylesAddress.get(7));

                                ++rowIndexAddress;
                            }
                        }
                    }
                }

                Sheet sheetTier = xssfWorkbook.getSheetAt(2);
                Row headerTier = sheetAddress.getRow(1);
                int rowIndexTier = 1;
                int numOfCellsTier = headerTier.getPhysicalNumberOfCells();
                Map<Integer, CellStyle> cellStylesTier = IntStream.range(0, numOfCellsAddress)
                        .boxed()
                        .collect(Collectors.toMap(index -> index, index -> headerTier.getCell(index).getCellStyle()));
                List<Program> programs = programRepository.findAll();
                List<ProgramTier> programTiers = programTierRepository.findAll();
                Map<Integer, List<ProgramTier>> mapTierByProgram = new HashMap<>();
                for (ProgramTier programTier : programTiers) {
                    if (mapTierByProgram.containsKey(programTier.getProgramId())) {
                        mapTierByProgram.get(programTier.getProgramId()).add(programTier);
                    } else {
                        List<ProgramTier> listProgramTier = new LinkedList<>();
                        listProgramTier.add(programTier);
                        mapTierByProgram.put(programTier.getProgramId(), listProgramTier);
                    }
                }
                for (Program program : programs) {
                    if (program.getStatus() != ECommonStatus.ACTIVE || mapTierByProgram.get(program.getId()) == null) {
                        continue;
                    }
                    for (ProgramTier programTier : mapTierByProgram.get(program.getId())) {
                        row = sheetTier.createRow(rowIndexTier);

                        cell = row.createCell(0);
                        cell.setCellValue(toStringOrBlank(program.getId()));
                        cell.setCellStyle(cellStylesTier.get(0));

                        cell = row.createCell(1);
                        cell.setCellValue(toStringOrBlank(program.getName()));
                        cell.setCellStyle(cellStylesTier.get(1));


                        cell = row.createCell(2);
                        cell.setCellValue(toStringOrBlank(programTier.getId()));
                        cell.setCellStyle(cellStylesTier.get(2));

                        cell = row.createCell(3);
                        cell.setCellValue(toStringOrBlank(programTier.getId()));
                        cell.setCellStyle(cellStylesTier.get(3));

                        ++rowIndexTier;
                    }
                }

                Sheet sheetCardType = xssfWorkbook.getSheetAt(3);
                Row headerCardType = sheetAddress.getRow(1);
                int rowIndexCardType = 1;
                int numOfCellsCardType = headerTier.getPhysicalNumberOfCells();
                Map<Integer, CellStyle> cellStylesCardType = IntStream.range(0, numOfCellsAddress)
                        .boxed()
                        .collect(Collectors.toMap(index -> index, index -> headerTier.getCell(index).getCellStyle()));
                List<CardType> cardTypes = cardTypeRepository.findAll();
                Map<Integer, List<CardType>> mapCardTypeByProgram = new HashMap<>();
                for (CardType cardType : cardTypes) {
                    if (mapCardTypeByProgram.containsKey(cardType.getProgramId())) {
                        mapCardTypeByProgram.get(cardType.getProgramId()).add(cardType);
                    } else {
                        List<CardType> listCardTypes = new LinkedList<>();
                        listCardTypes.add(cardType);
                        mapCardTypeByProgram.put(cardType.getProgramId(), listCardTypes);
                    }
                }
                for (Program program : programs) {
                    if (program.getStatus() != ECommonStatus.ACTIVE ||
                            mapCardTypeByProgram.get(program.getId()) == null) {
                        continue;
                    }
                    for (CardType cardType : mapCardTypeByProgram.get(program.getId())) {
                        row = sheetCardType.createRow(rowIndexCardType);

                        cell = row.createCell(0);
                        cell.setCellValue(toStringOrBlank(program.getId()));
                        cell.setCellStyle(cellStylesCardType.get(0));

                        cell = row.createCell(1);
                        cell.setCellValue(toStringOrBlank(program.getName()));
                        cell.setCellStyle(cellStylesCardType.get(1));

                        cell = row.createCell(2);
                        cell.setCellValue(toStringOrBlank(cardType.getId()));
                        cell.setCellStyle(cellStylesCardType.get(2));

                        cell = row.createCell(3);
                        cell.setCellValue(toStringOrBlank(cardType.getName()));
                        cell.setCellStyle(cellStylesCardType.get(3));

                        cell = row.createCell(4);
                        cell.setCellValue(toStringOrBlank(cardType.getDescription()));
                        cell.setCellStyle(cellStylesCardType.get(4));

                        ++rowIndexCardType;
                    }
                }
                FileOutputStream out = new FileOutputStream(tmpOutputFile.toFile());
                sxssfWorkbook.write(out);

                out.close();
                sxssfWorkbook.dispose();
            }
        }
        return tmpOutputFile;
    }

    private String toStringOrBlank(Object s) {
        if (s == null) return "";
        return String.valueOf(s);
    }
}