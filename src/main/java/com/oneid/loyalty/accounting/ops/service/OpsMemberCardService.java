package com.oneid.loyalty.accounting.ops.service;

import java.time.LocalDate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.MemberCardChangedStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberCardRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECardStatus;

public interface OpsMemberCardService {
    Page<MemberCardRes> filter(Integer businessId, Integer programId, Integer corporationId,
                                             Integer chainId, Integer storeId, Long batchNo,
                                             String cardNo, LocalDate fromDate, LocalDate toDate,
                                             ECardStatus currentStatus, EApprovalStatus approvalStatus,
                                             Pageable pageRequest);

    Page<MemberCardRes> getInReview(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable);

    MemberCardRes getInReviewDetail(Integer reviewId);

    MemberCardRes getAvailableDetail(Integer programId, String cardNo);
    
    MemberCardRes getEditChangedStatusRequestSetting(
            Integer programId,
            String cardNo);
    
    Integer requestEditingChangedStatusRequest(
            Integer programId,
            String cardNo,
            MemberCardChangedStatusReq req);
}
