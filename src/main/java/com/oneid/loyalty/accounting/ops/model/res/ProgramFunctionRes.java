package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramFunctionRes extends OtherInfo {
    private static final long serialVersionUID = -3863883876688401066L;
    private Integer businessId;
    private String businessCode;
    private String businessName;

    private Integer programId;
    private String programCode;
    private String programName;

    private ECommonStatus status;
    private Boolean havingFunctions;

    private String approvalStatus;

    private Long requestId;

    public void withProgram(Program program) {
        this.programId = program.getId();
        this.programName = program.getName();
        this.programCode = program.getCode();
        this.status = program.getStatus();
    }

    public void withBusiness(Business business) {
        this.businessId = business.getId();
        this.businessName = business.getName();
        this.businessCode = business.getCode();
    }
}