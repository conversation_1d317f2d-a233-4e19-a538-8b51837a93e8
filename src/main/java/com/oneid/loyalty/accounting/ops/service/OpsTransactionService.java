package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionBatchRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionMemberRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchAvailableTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionConfirmReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSapSaleOrderUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SapSaleOrderRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionCheckLogRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionSearchRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionStatisticRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface OpsTransactionService {
    Page<TransactionRes> search(TransactionSearchReq transactionSearchReq, Pageable pageRequest, Boolean getBoolean);

    default Page<TransactionRes> search(TransactionSearchReq transactionSearchReq, Pageable pageRequest) {
        return search(transactionSearchReq, pageRequest, null);
    }

    TransactionRes getTransactionDetails(String pointTnxId);

    TransactionBatchRequestDetailRes getTransactionBatchDetails(Long batchId);

    Page<TransactionSearchRes> searchAvailable(SearchAvailableTransactionReq req, Pageable page);

    APIResponse<?> createTransaction(CreateTransactionReq payload);

    ResourceDTO verifyTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception;

    ResourceDTO createTransactionByBatch(CreateTransactionBatchRequestReq req, MultipartFile file) throws Exception;

    APIResponse<?> revertTransaction(RevertTransactionReq revertTransactionReq);

    List<TransactionCheckLogRes> checkLog(String pointTnxId);
    
    Page<TransactionRes> search(
            Integer businessId,
            Integer programId,
            String userProfileId,
            String transactionType,
            Integer transactionTimeFrom,
            Integer transactionTimeTo,
            Pageable pageable);

    APIResponse<?> confirmTransaction(TransactionConfirmReq txnConfirmReq);

    TransactionBatchRequestDetailRes getTransactionBatchRequestDetail(Long id);

    Page<TransactionRequestRes> getInReviewTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable);

    Page<TransactionRequestRes> getAvailableTransactionRequests(Long batchRequestId, ERequestProcessStatus processStatus, Pageable pageable);

    String randomInvoiceNumber(String pattern, ECharacterSet type);

    MemberTransactionRes findMemberTransaction(Integer businessId, Integer programId, EOpsIdType idType, String idNo);

    Long createTransactionBatchRequestForMember(CreateTransactionMemberRequestReq req);

    Page<TransactionBatchRequestRes> filterTransaction(EApprovalStatus approvalStatus, String createdBy, Date createdStart, Date createdEnd,
                                                       String approvedBy, Date approvedStart, Date approvedEnd, Pageable pageable);

    TransactionStatisticRes getTransactionStatisticById(Long requestId);

    ResourceDTO exportFileInReview(Long batchNo);

    ResourceDTO exportFileAvailable(Long batchNo);

    TransactionBatchRequestDetailRes approveBatchRequest(ApprovalReq req);

    TransactionBatchRequestDetailRes retryBatchRequest(Long id);

    String createSapSaleOrder(TransactionSapSaleOrderCreateReq req);

    String updateSapSaleOrder(Long batchRequestId, TransactionSapSaleOrderUpdateReq req);

    List<SapSaleOrderRes> getSapSaleOrders(String sapCustomer);

    TransactionDetailRes getTransactionInfoDetail(String txnRefNo);

    SapSaleOrderDetailRes getSapSaleOrder(String sapSaleOrder);

    SapSaleOrderDetailRes getSapSaleOrder(Long batchRequestId);

}
