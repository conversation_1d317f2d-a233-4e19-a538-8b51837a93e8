package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = TransactionInfoFeignRes.TransactionInfoFeignResBuilder.class)
public class TransactionInfoFeignRes {
    private String txnRefNo;
    private String invoiceNo;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class TransactionInfoFeignResBuilder {
    }
}
