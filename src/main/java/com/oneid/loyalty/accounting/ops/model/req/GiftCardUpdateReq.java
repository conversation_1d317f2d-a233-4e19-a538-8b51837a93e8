package com.oneid.loyalty.accounting.ops.model.req;

import java.time.OffsetDateTime;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.oneid.oneloyalty.common.constant.EGiftCardStatus;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftCardUpdateReq {
    
    @NotNull(message = "Program Id is missing")
    private Integer programId;

    @NotBlank(message = "Serial is missing")
    private String serial;
    
    private String reason;
    
    private EGiftCardStatus status;
    
    private OffsetDateTime expirationDate;
    
    @AssertTrue(message="Either status or expiration_date has value")
    public boolean isValidReq() {
        return status != null ^ expirationDate != null;
    }
    
    @AssertTrue(message="expiration_date must greater than current time")
    public boolean isValidExpirationDate() {
        if (expirationDate == null) {
            return true;
        }
        return expirationDate.isAfter(OffsetDateTime.now());
    }
    
}
