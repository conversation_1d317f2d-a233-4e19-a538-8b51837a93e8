package com.oneid.loyalty.accounting.ops.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.model.APIPageResponse;
import com.oneid.loyalty.accounting.ops.feign.model.req.EditNRSKUAwareRateFeignReq;
import com.oneid.loyalty.accounting.ops.model.APIResponse;

@FeignClient(name = "ops-integration", url = "${ops-integration-service.url}")
public interface OpsIntegrationFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/v1/new-retails/sku/list")
    APIPageResponse<Object> getSKUList(
            @RequestParam(name = "assigned_award_rate", required = false, defaultValue = "true") Boolean assignedAwardRate,
            @RequestParam(value = "sku_code", required = false) String skuCode,
            @RequestParam(value = "product_category_name", required = false) String productCategoryName,
            @RequestParam(value = "merchant_code", required = false) String merchantCode,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "sort_by", required = false) String sortBy,
            @RequestParam(value = "sort_type", required = false) String sortType,
            @RequestParam(name = "offset") long offset,
            @RequestParam(name = "limit") int limit);
    
    @RequestMapping(method = RequestMethod.GET, value = "/v1/new-retails/sku/{id}/detail")
    APIResponse<Object> getSKUById(@PathVariable("id") Integer id);
    
    @RequestMapping(method = RequestMethod.POST, value = "/v1/new-retails/sku/{id}/edit-award-rate")
    APIResponse<Object> editSKUAwardRate(@PathVariable("id") Integer id, @RequestBody EditNRSKUAwareRateFeignReq req);
}