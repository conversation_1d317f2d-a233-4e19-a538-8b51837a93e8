package com.oneid.loyalty.accounting.ops.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.oneid.loyalty.accounting.ops.support.feign.FeignResponse;
import com.oneid.oneloyalty.common.model.Meta;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(value = Include.NON_NULL)
public class APIResponse<T> implements FeignResponse {
    private Meta meta;
    
    private T data;
}