package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BatchAdjustPartnerTransactionReq {
    @NotNull(message = "'corporation_id' must not be blank")
    private Integer corporationId;
    
    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;
    
    @NotNull(message = "'program_id' must not be null")
    private Integer programId;
    
    @NotBlank(message = "'batch_file_name' must not be blank")
    private String batchFileName;
}
