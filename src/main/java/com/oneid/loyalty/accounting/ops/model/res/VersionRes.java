package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Getter
@Setter
@SuperBuilder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VersionRes {
    private String version;
    private String restoredFromVersion;
    private Integer restoredFromId;
    private EApprovalStatus status;
    private String createdBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    private String reviewedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date reviewedAt;

    private String rejectedReason;

    private Long id;
    private Boolean isCurrent;

}
