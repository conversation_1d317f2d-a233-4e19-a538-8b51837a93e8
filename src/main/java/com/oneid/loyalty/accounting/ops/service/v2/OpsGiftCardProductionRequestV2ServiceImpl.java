package com.oneid.loyalty.accounting.ops.service.v2;


import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.v2.GiftCardProductionRequestDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.v2.giftcardres.*;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class OpsGiftCardProductionRequestV2ServiceImpl implements OpsGiftCardProductionRequestV2Service {
    @Autowired
    private GiftCardRequestRepository giftCardRequestRepository;

    @Autowired
    private GiftCardBinService giftCardBinService;

    @Autowired
    private GiftCardTypeService giftCardTypeService;

    @Autowired
    private CardPolicyService cardPolicyService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private CorporationService corporationService;

    @Override
    public GiftCardProductionRequestDetailRes getDetailAvailable(Integer requestId) {
        GiftCardRequest giftCardRequest = giftCardRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND,
                        "Gift card production request not found",
                        null
                ));
        Business business = businessService.find(giftCardRequest.getBusinessId()).orElse(null);
        Program program = programService.find(giftCardRequest.getProgramId()).orElse(null);
        Store store = storeService.find(giftCardRequest.getStoreId());
        Chain chain = null;
        Corporation corporation = null;
        if (Objects.nonNull(store)) {
            chain = chainService.find(store.getChainId()).orElse(null);
            corporation = corporationService.find(store.getCorporationId()).orElse(null);
        }
        GiftCardBin giftCardBin = giftCardBinService.find(giftCardRequest.getGcBinId()).orElse(null);
        GiftCardType giftCardType = giftCardTypeService.find(giftCardRequest.getGcTypeId()).orElse(null);
        CardPolicy cardPolicy = cardPolicyService.find(giftCardRequest.getGcPolicyId()).orElse(null);
        return GiftCardProductionRequestDetailRes.builder()
                .business(business != null ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .corporation(corporation != null ? new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()) : null)
                .store(store != null ? new ShortEntityRes(store.getId(), store.getName(), store.getCode()) : null)
                .chain(chain != null ? new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()) : null)
                .batchNo(giftCardRequest.getCprBatchNo())
                .version(giftCardRequest.getCprVersion())
                .id(Long.valueOf(requestId))
                .description(giftCardRequest.getDescription())
                .batchType(giftCardRequest.getGcBatchType())
                .noOfCard(giftCardRequest.getNoOfCards())
                .giftCardStatus(giftCardRequest.getInitGcStatus())
                .gcPostfix(giftCardRequest.getSerialSuffix())
                .giftCardBin(giftCardBin != null ? new ShortEntityRes(giftCardBin.getId(), giftCardBin.getName(), giftCardBin.getBinCode()) : null)
                .giftCardType(giftCardType != null ? new GiftCardTypeRes(giftCardType) : null)
                .cardPolicy(cardPolicy != null ? new GiftCardPolicyRes(cardPolicy) : null)
                .generateQr(giftCardRequest.getGenerateQr())
                .generationInd(giftCardRequest.getGenerateInd())
                .generationDate(giftCardRequest.getGenerateDate() != null ? DateTimes.toEpochSecond(giftCardRequest.getGenerateDate()) : null)
                .approvedBy(giftCardRequest.getApprovedBy())
                .approvedAt(giftCardRequest.getApprovedAt())
                .createdBy(giftCardRequest.getCreatedBy())
                .createdAt(giftCardRequest.getCreatedAt())
                .updatedBy(giftCardRequest.getUpdatedBy())
                .updatedAt(giftCardRequest.getUpdatedAt())
                .build();
    }
}
