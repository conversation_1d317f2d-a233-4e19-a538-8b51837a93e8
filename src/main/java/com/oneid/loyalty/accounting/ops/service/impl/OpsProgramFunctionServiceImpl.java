package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.ERequestAgentType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramFunctionReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionAvailableDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionInfo;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramFunctionService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProfileAttribute;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramFunction;
import com.oneid.oneloyalty.common.entity.ProgramFunctionProfileAttribute;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.ProgramFunctionProfileAttributeService;
import com.oneid.oneloyalty.common.service.ProgramFunctionService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class OpsProgramFunctionServiceImpl implements OpsProgramFunctionService {

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    FunctionService functionService;

    @Autowired
    ProgramFunctionService programFunctionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    ProgramFunctionProfileAttributeService programFunctionProfileAttributeService;

    @Override
    public Map<String, List<ProgramFunctionInfo>> getFunctions() {
        return functionService.findAllActiveFunction()
                .stream()
                .peek(ele -> {
                    if (Objects.isNull(ele.getGroupName())) {
                        ele.setGroupName(ERequestAgentType.OTHER.getValue());
                    }
                })
                .map(ProgramFunctionInfo::valueOf)
                .collect(Collectors.groupingBy(ProgramFunctionInfo::getGroupName));
    }

    @Override
    public ProgramDetailRes getEditAttributeRequestSetting(Integer programId) {
        Program program = programService.findActive(programId);
        ProgramDetailRes res = ProgramDetailRes.valueOf(program);
        List<ProgramFunction> programFunctions = programFunctionService.findByProgramId(program.getId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .collect(Collectors.toList());
        if (programFunctions.isEmpty()) {
            throw new BusinessException(ErrorCode.PROGRAM_IS_NOT_CONFIGURED, null, null);
        }
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_FUNCTION.getType(), programFunctions.get(0).getRequestCode());
        List<com.oneid.oneloyalty.common.entity.Function> functions = functionService.findAllActiveFunction();
        List<ProgramFunctionProfileAttribute> profileAttributes = programFunctionProfileAttributeService.findAllByProgramId(programId);
        res.withProgramFunctions(programFunctions, functions, profileAttributes);
        String editKey = opsReqPendingValidator.generateEditKey(programFunctions.get(0).getRequestCode(), programFunctions.get(0).getVersion());
        programFunctions = programFunctions
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(ele -> Objects.nonNull(ele.getCreatedAt()))
                .sorted(Comparator.comparing(ProgramFunction::getCreatedAt))
                .collect(Collectors.toList());
        if (!programFunctions.isEmpty()) {
            res.setCreatedAt(programFunctions.get(0).getCreatedAt());
            res.setCreatedBy(programFunctions.get(0).getCreatedBy());
            res.setUpdatedAt(programFunctions.get(programFunctions.size() - 1).getUpdatedAt());
            res.setUpdatedBy(programFunctions.get(programFunctions.size() - 1).getUpdatedBy());
            res.setApprovedAt(programFunctions.get(programFunctions.size() - 1).getApprovedAt());
            res.setApprovedBy(programFunctions.get(programFunctions.size() - 1).getApprovedBy());
        }
        res.setEditKey(editKey);
        return res;
    }

    @Override
    public MakerCheckerInternalMakerRes requestEditingAttributeRequest(ProgramFunctionReq request) {
        // validate program
        programService.findActive(request.getProgramId());
        // validate function code
        validateFunction(request);
        // validate request pending
        Optional<ProgramFunction> first = programFunctionService.findByProgramId(request.getProgramId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .findFirst();

        if (first.isPresent()) {
            if (Objects.isNull(first.get().getRequestCode())) {
                throw new BusinessException(ErrorCode.REQUEST_CODE_NOT_FOUND, "request code not found",
                        LogData.createLogData()
                                .append("program_id", request.getProgramId()));
            }
            opsReqPendingValidator.verifyEditKey(request.getEditKey(), first.get().getRequestCode(), first.get().getVersion());
            opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_FUNCTION.getType(), first.get().getRequestCode());
        } else {
            throw new BusinessException(ErrorCode.PROGRAM_IS_NOT_CONFIGURED, null, null);
        }

        MakerCheckerInternalMakerReq<ProgramFunctionReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<ProgramFunctionReq>builder()
                .requestCode(first.get().getRequestCode())
                .requestName(EMakerCheckerType.PROGRAM_FUNCTION.getName())
                .requestType(EMakerCheckerType.PROGRAM_FUNCTION.getType())
                .payload(request)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> apiResponse = makerCheckerInternalFeignClient.maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != apiResponse.getMeta().getCode())
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program function - request editing attribute request call checker error: " + apiResponse.getMeta().getMessage(), null);
        return apiResponse.getData();
    }

    private void validateFunction(ProgramFunctionReq request) {
        List<String> functionCodes = request.getProfileAttributes()
                .stream()
                .map(ProgramFunctionInfo.ProfileAttribute::getFunctionCode)
                .distinct()
                .peek(ele -> {
                    if (!request.getFunctions().contains(ele)) {
                        throw new BusinessException(ErrorCode.PROGRAM_FUNCTION_PROFILE_ATTRIBUTE_INVALID, null, null);
                    }
                })
                .collect(Collectors.toList());
        request.getFunctions().forEach(ele -> {
            com.oneid.oneloyalty.common.entity.Function activeByCode = functionService.findActiveByCode(ele);
            if (EBoolean.NO.equals(activeByCode.getProfileAttributeFlag()) && functionCodes.contains(ele)) {
                throw new BusinessException(ErrorCode.PROGRAM_FUNCTION_PROFILE_ATTRIBUTE_INVALID, null, null);
            }
        });
    }

    @Override
    public MakerCheckerInternalMakerRes create(ProgramFunctionReq request) {
        // validate program
        programService.findActive(request.getProgramId());
        // validate whether exist request approved
        List<ProgramFunction> byProgramId = programFunctionService.findByProgramId(request.getProgramId());
        if (!byProgramId.isEmpty()) {
            throw new BusinessException(ErrorCode.PROGRAM_IS_ALREADY_CONFIGURED, null, null);
        }
        validationProgramIdDoesNotExistInOtherReqPending(request.getProgramId());
        // validate function code
        validateFunction(request);

        MakerCheckerInternalMakerReq<ProgramFunctionReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<ProgramFunctionReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.PROGRAM_FUNCTION.getName())
                .requestType(EMakerCheckerType.PROGRAM_FUNCTION.getType())
                .payload(request)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program function call maker error: " + createMakerProgramCorporation.getMeta().getMessage(), null);
        }
        return createMakerProgramCorporation.getData();
    }

    private void validationProgramIdDoesNotExistInOtherReqPending(Integer programId) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_FUNCTION.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        previewRes.getData()
                .stream()
                .filter(ele -> Objects.nonNull(ele.getPayload()))
                .map(data -> this.objectMapper.convertValue(data.getPayload(), ProgramFunctionReq.class))
                .filter(ele -> ele.getProgramId().equals(programId))
                .findAny()
                .ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);
                });
    }

    public PageImpl<ProgramFunctionRes> searchAvailable(Integer businessId, String programCode, ECommonStatus status, Pageable pageable) {
        SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<>();
        if (ObjectUtils.isNotEmpty(businessId))
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));
        if (ObjectUtils.isNotEmpty(programCode))
            specificationBuilder.add(new SearchCriteria("code", programCode, SearchOperation.EQUAL));
        if (ObjectUtils.isNotEmpty(status))
            specificationBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));

        Page<Program> pagePrograms = programService.searchPaging(specificationBuilder, pageable);

        List<ProgramFunctionRes> resList = new ArrayList<>();
        pagePrograms.getContent().stream().forEach(program -> {
            ProgramFunctionRes res = new ProgramFunctionRes();
            res.withProgram(program);
            Optional<Business> businessOpt = businessService.find(program.getBusinessId());
            if (businessOpt.isPresent()) {
                res.withBusiness(businessOpt.get());
            }
            res.setHavingFunctions(!programFunctionService.findByProgramId(program.getId()).isEmpty());
            resList.add(res);
        });

        return new PageImpl<ProgramFunctionRes>(resList, pageable, pagePrograms.getTotalElements());
    }

    @Override
    public PageImpl<ProgramFunctionRes> searchInReview(EApprovalStatus approvalStatus, int offset, int limit) {
        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.PROGRAM_FUNCTION.getType(), null, approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList());
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<ProgramFunctionRes> result = new ArrayList<>();
        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            ProgramFunctionReq req = objectMapper.convertValue(item.getPayload(), ProgramFunctionReq.class);
            ProgramFunctionRes res = new ProgramFunctionRes();
            Optional<Program> pOptional = programService.find(req.getProgramId());
            if (pOptional.isPresent()) {
                res.withProgram(pOptional.get());
                Optional<Business> bOptional = businessService.find(pOptional.get().getBusinessId());
                if (bOptional.isPresent()) {
                    res.withBusiness(bOptional.get());
                }
            }

            res.setHavingFunctions(req.getFunctions().size() > 0);

            res.setCreatedAt(item.getMadeDate() != null ? Date.from(ZonedDateTime.parse(item.getMadeDate()).toInstant()) : null);
            res.setApprovedAt(item.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(item.getCheckedDate()).toInstant()) : null);
            res.setCreatedBy(item.getMadeByUserName());
            res.setApprovedBy(item.getCheckedByUserName());
            res.setApprovalStatus(item.getStatus());
            res.setRequestId(item.getId());
            result.add(res);
        }
        return new PageImpl<>(result, PageRequest.of(offset / limit, limit), previewRes.getMeta().getTotal());
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> detailRes = makerCheckerInternalFeignClient
                .previewDetail(req.getId());
        if (ObjectUtils.isEmpty(detailRes.getData())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }
        MakerCheckerInternalDataDetailRes detailResData = detailRes.getData();
        ProgramFunctionReq payload = this.objectMapper.convertValue(detailResData.getPayload(),
                ProgramFunctionReq.class);
        programService.findActive(payload.getProgramId());
        if (!EApprovalStatus.PENDING.getValue().equals(detailRes.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (req.getStatus().equals(EApprovalStatus.APPROVED)) {
            String updatedBy = detailResData.getMadeByUserName();
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
            Set<String> activeIds = new HashSet<>();
            Set<String> activeProfileAttributes = new HashSet<>();

            Predicate<ProgramFunction> predicate = ele -> {
                if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                    activeIds.add(ele.getFunctionCode());
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            };
            Predicate<ProgramFunctionProfileAttribute> predicateProfileAttribute = ele -> {
                if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                    activeProfileAttributes.add(String.format("%s|%s", ele.getFunction(), ele.getProfileAttribute().getValue()));
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            };

            Map<String, ProgramFunction> mapProgramFunctionInactive = programFunctionService
                    .findByProgramId(payload.getProgramId())
                    .stream()
                    .filter(predicate)
                    .collect(Collectors.toMap(ProgramFunction::getFunctionCode, Function.identity(), (first, last) -> last));
            Map<String, ProgramFunctionProfileAttribute> mapProgramFunctionProfileAttributeInactive = programFunctionProfileAttributeService
                    .findAllByProgramId(payload.getProgramId())
                    .stream()
                    .filter(predicateProfileAttribute)
                    .collect(Collectors
                            .toMap(ele -> String.format("%s|%s", ele.getFunction(), ele.getProfileAttribute().getValue()),
                                    Function.identity(),
                                    (first, last) -> last));
            Map<String, Set<EProfileAttribute>> mapProfileAttribute = payload.getProfileAttributes()
                    .stream()
                    .collect(Collectors
                            .groupingBy(ProgramFunctionInfo.ProfileAttribute::getFunctionCode,
                                    Collectors.mapping(ProgramFunctionInfo.ProfileAttribute::getCode,
                                            Collectors.toSet())));
            // Handle create a program function
            List<ProgramFunction> saveAllFunction = new ArrayList<>();
            List<ProgramFunctionProfileAttribute> saveAllProfileAttribute = new ArrayList<>();
            if (activeIds.isEmpty()) {
                payload.getFunctions().forEach(ele -> {
                    saveAllFunction.add(prepareSaveProgramFunction(detailResData, payload, ele, approvedBy));
                });
            } else {
                // Handle edit a program function
                // Exp: | create: A,B,C | edit: B,C,D | we must create a function with code is
                // D and remove a function A
                ArrayList<String> createIds = new ArrayList<>(
                        CollectionUtils.subtract(payload.getFunctions(), activeIds)); // D
                ArrayList<String> deleteIds = new ArrayList<>(
                        CollectionUtils.subtract(activeIds, payload.getFunctions())); // A
                ArrayList<String> holdIds = new ArrayList<>(payload.getFunctions()); // B, C
                holdIds.retainAll(activeIds);
                if (!createIds.isEmpty()) {
                    for (String functionCode : createIds) {
                        ProgramFunction programFunction;
                        // Set the active status for obsolete record
                        if (mapProgramFunctionInactive.containsKey(functionCode)) {
                            programFunction = mapProgramFunctionInactive.get(functionCode);
                            programFunction.setStatus(ECommonStatus.ACTIVE);
                            programFunction.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunction, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                        } else {
                            // New record
                            programFunction = prepareSaveProgramFunction(detailResData, payload, functionCode, approvedBy);
                        }
                        saveAllFunction.add(programFunction);
                    }
                }
                if (!deleteIds.isEmpty()) {
                    for (String functionCode : deleteIds) {
                        List<ProgramFunction> programFunctions = programFunctionService
                                .find(payload.getProgramId(), functionCode, ECommonStatus.ACTIVE);
                        for (ProgramFunction programFunction : programFunctions) {
                            programFunction.setStatus(ECommonStatus.INACTIVE);
                            programFunction.setRequestCode(detailResData.getRequestCode());
                            programFunction.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunction, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAllFunction.add(programFunction);
                        }
                    }
                }
                if (!holdIds.isEmpty()) {
                    for (String functionCode : holdIds) {
                        List<ProgramFunction> programFunctions = programFunctionService
                                .find(payload.getProgramId(), functionCode, ECommonStatus.ACTIVE);
                        for (ProgramFunction programFunction : programFunctions) {
                            programFunction.setRequestCode(detailResData.getRequestCode());
                            programFunction.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunction, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAllFunction.add(programFunction);
                        }
                    }
                }
            }

            // Handle create a program function profile attribute
            if (activeProfileAttributes.isEmpty()) {
                for (Map.Entry<String, Set<EProfileAttribute>> ele : mapProfileAttribute.entrySet()) {
                    ele.getValue().forEach(profileAttribute -> saveAllProfileAttribute.add(prepareSaveProgramFunctionProfileAttribute(detailResData, payload, ele.getKey(), profileAttribute, approvedBy)));
                }
            } else {
                List<String> collect = payload.getProfileAttributes()
                        .stream()
                        .map(ele -> String.format("%s|%s", ele.getFunctionCode(), ele.getCode().getValue()))
                        .collect(Collectors.toList());

                ArrayList<String> createIds = new ArrayList<>(
                        CollectionUtils.subtract(collect, activeProfileAttributes));
                ArrayList<String> deleteIds = new ArrayList<>(
                        CollectionUtils.subtract(activeProfileAttributes, collect));
                ArrayList<String> holdIds = new ArrayList<>(collect);
                holdIds.retainAll(activeProfileAttributes);

                if (!createIds.isEmpty()) {
                    for (String key : createIds) {
                        ProgramFunctionProfileAttribute programFunctionProfileAttribute;
                        // Set the active status for obsolete record
                        if (mapProgramFunctionProfileAttributeInactive.containsKey(key)) {
                            programFunctionProfileAttribute = mapProgramFunctionProfileAttributeInactive.get(key);
                            programFunctionProfileAttribute.setStatus(ECommonStatus.ACTIVE);
                            programFunctionProfileAttribute.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunctionProfileAttribute, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                        } else {
                            // New record
                            String[] arr = split(key);
                            programFunctionProfileAttribute = prepareSaveProgramFunctionProfileAttribute(detailResData, payload, arr[0], EProfileAttribute.of(arr[1]), approvedBy);
                        }
                        saveAllProfileAttribute.add(programFunctionProfileAttribute);
                    }
                }
                if (!deleteIds.isEmpty()) {
                    for (String key : deleteIds) {
                        String[] arr = split(key);
                        List<ProgramFunctionProfileAttribute> programFunctionProfileAttributes = programFunctionProfileAttributeService
                                .find(payload.getProgramId(), arr[0], EProfileAttribute.of(arr[1]), ECommonStatus.ACTIVE);
                        for (ProgramFunctionProfileAttribute programFunctionProfileAttribute : programFunctionProfileAttributes) {
                            programFunctionProfileAttribute.setStatus(ECommonStatus.INACTIVE);
                            programFunctionProfileAttribute.setRequestCode(detailResData.getRequestCode());
                            programFunctionProfileAttribute.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunctionProfileAttribute, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAllProfileAttribute.add(programFunctionProfileAttribute);
                        }
                    }
                }
                if (!holdIds.isEmpty()) {
                    for (String key : holdIds) {
                        String[] arr = split(key);
                        List<ProgramFunctionProfileAttribute> programFunctionProfileAttributes = programFunctionProfileAttributeService
                                .find(payload.getProgramId(), arr[0], EProfileAttribute.of(arr[1]), ECommonStatus.ACTIVE);
                        for (ProgramFunctionProfileAttribute programFunctionProfileAttribute : programFunctionProfileAttributes) {
                            programFunctionProfileAttribute.setRequestCode(detailResData.getRequestCode());
                            programFunctionProfileAttribute.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programFunctionProfileAttribute, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAllProfileAttribute.add(programFunctionProfileAttribute);
                        }
                    }
                }
            }
            programFunctionService.saveAll(saveAllFunction);
            programFunctionProfileAttributeService.saveAll(saveAllProfileAttribute);
        }

        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes = makerCheckerInternalFeignClient
                .checker(checkerReq);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program function call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
    }

    private String[] split(String key) {
        return key.split("\\|");
    }

    private ProgramFunctionProfileAttribute prepareSaveProgramFunctionProfileAttribute(
            MakerCheckerInternalDataDetailRes detailResData,
            ProgramFunctionReq payload, String functionCode,
            EProfileAttribute profileAttribute, String approvedBy) {
        String createdBy;
        String updatedBy;

        ProgramFunctionProfileAttribute programFunctionProfileAttribute = new ProgramFunctionProfileAttribute();
        programFunctionProfileAttribute.setProgramId(payload.getProgramId());
        programFunctionProfileAttribute.setFunction(functionCode);
        programFunctionProfileAttribute.setProfileAttribute(profileAttribute);
        programFunctionProfileAttribute.setStatus(ECommonStatus.ACTIVE);
        programFunctionProfileAttribute.setRequestCode(detailResData.getRequestCode());
        programFunctionProfileAttribute.setVersion(detailResData.getVersion());
        updatedBy = createdBy = detailResData.getMadeByUserName();
        opsReqPendingValidator.updateInfoChecker(programFunctionProfileAttribute, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);

        return programFunctionProfileAttribute;
    }

    private ProgramFunction prepareSaveProgramFunction(
            MakerCheckerInternalDataDetailRes detailResData,
            ProgramFunctionReq payload,
            String functionCode, String approvedBy) {
        String createdBy;
        String updatedBy;

        ProgramFunction programFunction = new ProgramFunction();
        programFunction.setProgramId(payload.getProgramId());
        programFunction.setFunctionCode(functionCode);
        programFunction.setStatus(ECommonStatus.ACTIVE);
        programFunction.setRequestCode(detailResData.getRequestCode());
        programFunction.setVersion(detailResData.getVersion());
        updatedBy = createdBy = detailResData.getMadeByUserName();
        opsReqPendingValidator.updateInfoChecker(programFunction, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);

        return programFunction;
    }

    @Override
    public ProgramFunctionAvailableDetailRes getAvailableDetail(Integer programId) {
        // Find all function of program 
        Program program = programService.find(programId).orElseThrow(
                () -> new BusinessException(
                        ErrorCode.PROGRAM_NOT_FOUND,
                        "Program not found", null));
        Business business = businessService.findActive(program.getBusinessId());
        ProgramFunctionAvailableDetailRes res = new ProgramFunctionAvailableDetailRes();

        List<ProgramFunction> programFunctions = programFunctionService.findByProgramId(program.getId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .collect(Collectors.toList());
        List<com.oneid.oneloyalty.common.entity.Function> functions = functionService.findAllActiveFunction();
        List<ProgramFunctionProfileAttribute> profileAttributes = programFunctionProfileAttributeService.findAllByProgramId(programId);
        res.setFunction(ProgramDetailRes.collectFunctionInfo(programFunctions, functions, profileAttributes));

        res.setBusinessId(business.getId());
        res.setBusinessCode(business.getCode());
        res.setBusinessName(business.getName());
        res.setProgramId(program.getId());
        res.setProgramCode(program.getCode());
        res.setProgramName(program.getName());
        res.setProgramStatus(program.getStatus());
        programFunctions = programFunctions
                .stream()
                .filter(ele -> Objects.nonNull(ele.getCreatedAt()))
                .sorted(Comparator.comparing(ProgramFunction::getCreatedAt))
                .collect(Collectors.toList());
        if (!programFunctions.isEmpty()) {
            res.setCreatedAt(programFunctions.get(0).getCreatedAt());
            res.setCreatedBy(programFunctions.get(0).getCreatedBy());
            res.setUpdatedAt(programFunctions.get(programFunctions.size() - 1).getUpdatedAt());
            res.setUpdatedBy(programFunctions.get(programFunctions.size() - 1).getUpdatedBy());
            res.setApprovedAt(programFunctions.get(programFunctions.size() - 1).getApprovedAt());
            res.setApprovedBy(programFunctions.get(programFunctions.size() - 1).getApprovedBy());
        }
        return res;
    }

    @Override
    public ProgramFunctionAvailableDetailRes getInReviewDetail(Integer id) {
        // TODO Auto-generated method stub
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes = makerCheckerInternalFeignClient.previewDetail(Long.valueOf(id));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Program Function - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.PROGRAM_FUNCTION.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Program Function - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        ProgramFunctionReq payload = objectMapper.convertValue(previewDetailRes.getData().getPayload(), ProgramFunctionReq.class);

        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(
                        ErrorCode.PROGRAM_NOT_FOUND,
                        "Program not found", null));

        Business business = businessService.findActive(program.getBusinessId());
        ProgramFunctionAvailableDetailRes res = new ProgramFunctionAvailableDetailRes();
        List<com.oneid.oneloyalty.common.entity.Function> functions = functionService.findAllActiveFunction();
        res.setFunction(payload.getProfileAttributes()
                .stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(ProgramFunctionInfo.ProfileAttribute::getFunctionCode,
                                Collectors.mapping(ProgramFunctionInfo.ProfileAttribute::getCode,
                                        Collectors.toSet())), map ->
                                functions.stream()
                                        .map(ele -> ProgramFunctionInfo.valueOf(ele, payload.getFunctions(), map))
                                        .collect(Collectors.groupingBy(ProgramFunctionInfo::getGroupName))
                )));
        res.setBusinessId(business.getId());
        res.setBusinessCode(business.getCode());
        res.setBusinessName(business.getName());
        res.setProgramId(program.getId());
        res.setProgramCode(program.getCode());
        res.setProgramName(program.getName());
        res.setApprovalStatus(previewDetailRes.getData().getStatus());
        res.setCreatedAt(previewDetailRes.getData().getMadeDate() != null ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getMadeDate()).toInstant()) : null);
        res.setCreatedBy(previewDetailRes.getData().getMadeByUserName());
        res.setApprovedAt(previewDetailRes.getData().getCheckedDate() != null ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getCheckedDate()).toInstant()) : null);
        res.setApprovedBy(previewDetailRes.getData().getCheckedByUserName());
        return res;
    }

    @Override
    public List<ProgramDropDownRes> getProgramByBusinessExcludeExist(Integer businessId) {
        List<Integer> existIds = programFunctionService.groupByProgram();
        Predicate<Program> predicate = program -> {
            if (!existIds.isEmpty() && existIds.contains(program.getId())) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        };
        return programService.findByBusinessId(businessId)
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(predicate)
                .map(ProgramDropDownRes::valueOf)
                .collect(Collectors.toList());

    }

}
