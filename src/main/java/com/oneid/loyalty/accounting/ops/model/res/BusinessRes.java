package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import com.oneid.oneloyalty.common.entity.Business;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class BusinessRes {
    private Integer id;
    private String code;
    private String name;
    private String description;
    private String status;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("start_date")
    private Date startDate;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("end_date")
    private Date endDate;

    @JsonProperty("contact_person")
    private String contactPerson;

    private String email;

    @JsonProperty("address_1")
    private String address1;

    @JsonProperty("address_2")
    private String address2;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("postal_code")
    private String postalCode;

    private String phone;
    private String website;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("created_at")
    private Date createdAt;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("updated_at")
    private Date updatedAt;

    public static BusinessRes of(Business business) {
        BusinessRes result = new BusinessRes();
        result.setId(business.getId());
        result.setCode(business.getCode());
        result.setName(business.getName());
        result.setDescription(business.getDescription());
        result.setStatus(business.getStatus().getValue());
        result.setStartDate(business.getStartDate());
        result.setEndDate(business.getEndDate());
        result.setContactPerson(business.getContactPerson());
        result.setEmail(business.getEmail());
        result.setAddress1(business.getAddress1());
        result.setAddress2(business.getAddress2());
        result.setCountryId(business.getCountryId());
        result.setProvinceId(business.getProvinceId());
        result.setDistrictId(business.getDistrictId());
        result.setWardId(business.getWardId());
        result.setPostalCode(business.getPostalCode());
        result.setPhone(business.getPhone());
        result.setWebsite(business.getWebsite());
        result.setCreatedBy(business.getCreatedBy());
        result.setUpdatedBy(business.getUpdatedBy());
        result.setCreatedAt(business.getCreatedAt());
        result.setUpdatedAt(business.getUpdatedAt());

        return result;
    }
}
