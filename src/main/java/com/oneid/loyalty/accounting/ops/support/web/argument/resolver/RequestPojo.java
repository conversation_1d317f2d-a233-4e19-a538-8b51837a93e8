package com.oneid.loyalty.accounting.ops.support.web.argument.resolver;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.core.annotation.AliasFor;

import com.oneid.oneloyalty.common.constant.ErrorCode;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface RequestPojo {
    @AliasFor("name")
    String value() default "";
    
    @AliasFor("value")
    String name() default "";
    
    ErrorCode errorCode() default ErrorCode.BAD_REQUEST;
    
    boolean required() default true;
}
