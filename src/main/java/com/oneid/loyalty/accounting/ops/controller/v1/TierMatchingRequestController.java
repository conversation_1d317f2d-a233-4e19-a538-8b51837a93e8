package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.TierMatchingCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TierMatchingUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.TierMatchingRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierMatchingRequestService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/tier-matching")
public class TierMatchingRequestController extends BaseController {
    
    @Autowired
    private OpsTierMatchingRequestService opsTierMatchingRequestService;
    
    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> searchTierMatchingRequestAvailable(@RequestParam(name = "business_id") Integer businessId,
                                                                @RequestParam(name = "matched_program_id", required = false) Integer matchedProgramId,
                                                                @RequestParam(name = "code", required = false) String code,
                                                                @RequestParam(name = "name", required = false) String name,
                                                                @RequestParam(name = "status", required = false) ECommonStatus status,
                                                                @RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
                                                                @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                                                @Valid @RequestParam(name = "limit", defaultValue = "10") @Max(100) @Min(1) Integer limit) {
        Page<TierMatchingRequestRes> page = opsTierMatchingRequestService.getAvailableTierMatchingRequests(businessId, matchedProgramId, code, name, status, approvalStatus, new OffsetBasedPageRequest(offset, limit, null));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> searchTierMatchingRequestInReview(@RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
                                                               @MakerCheckerOffsetPageable Pageable pageable) {
        Page<TierMatchingRequestRes> page = opsTierMatchingRequestService.getInReviewTierMatchingRequests(approvalStatus, pageable);

        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}/view")
    @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableAttributeRequestById(@PathVariable("id") Integer requestId){
        return success(opsTierMatchingRequestService.getAvailableTierMatchingRequestById(requestId));
    }

    @GetMapping("/requests/in-review/{id}/view")
    @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewTierRequestById(@PathVariable("id") Integer reviewId){
        return success(opsTierMatchingRequestService.getInReviewTierMatchingRequestById(reviewId));
    }
    
    @PostMapping("request/create")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestCreate(@RequestBody @Valid TierMatchingCreateReq req) {
        return success(opsTierMatchingRequestService.requestCreate(req));
    }
    
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    @GetMapping("request/{id}/changeable")
    public ResponseEntity<?> getChangeableRequestById(@PathVariable("id") Integer requestId) {
        return success(opsTierMatchingRequestService.getChangeableRequest(requestId));
    }
    
    @PostMapping("request/{request_id}/change")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER_MATCHING_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestUpdate(
            @PathVariable("request_id") Integer requestId, 
            @RequestBody @Valid TierMatchingUpdateReq req) {
        return success(opsTierMatchingRequestService.requestUpdate(requestId, req));
    }

    
}
