package com.oneid.loyalty.accounting.ops.util.excel;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;

public class GiftCardFileFormatUtil {

    public static final boolean HEADER_STYLE_BOLD = true;

    public static final int BATCH_SIZE_FLUSH = 100;

    public static final int PAGE_SIZE = 100;

    public static final String[] COLUMN = {"Business Code", "Program Code", "Store Code", "Batch No", "Point Amount", "Serial", "Status", "Created At", "Expired At", "Used At", "Member Code"};


    public static CellStyle getCellStyleHeader(Workbook workbook) {
        Font headerFont = workbook.createFont();
        headerFont.setBold(HEADER_STYLE_BOLD);
        CellStyle cellStyleHeader = workbook.createCellStyle();
        cellStyleHeader.setFont(headerFont);
        return cellStyleHeader;
    }
}
