package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.oneid.oneloyalty.common.validation.Phone;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreateMemberReq extends MemberReq {
    @NotBlank(message = "phoneNo must not be blank")
    @Phone(message = "Phone no invalid")
    private String phoneNo;
    
    @NotBlank(message = "businessCode must not be blank")
    private String businessCode;
    
    @NotBlank(message = "programCode must not be blank")
    private String programCode;

    @NotBlank(message = "Store code must not be blank")
    private String storeCode;

    @Valid
    @NotNull(message = "Card Info is required")
    private AddCardWithMemberRegistrationReq card;
}