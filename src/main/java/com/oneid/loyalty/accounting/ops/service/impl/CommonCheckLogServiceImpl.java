package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.CheckLogSourceConfigPram;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogCardMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogMemberReq;
import com.oneid.loyalty.accounting.ops.model.res.DataAuditTrailDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.DataAuditTrailRes;
import com.oneid.loyalty.accounting.ops.service.CommonCheckLogService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.EEntityAuditTrail;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.DataAuditTrail;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.DataAuditTrailRepository;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CommonCheckLogServiceImpl implements CommonCheckLogService {

    @Autowired
    private CheckLogSourceConfigPram checkLogSourceConfigPram;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    DataAuditTrailRepository dataAuditTrailRepository;

    @Override
    public List<DataAuditTrailRes> filterCheckLogMember(Long memberId, FilterCheckLogMemberReq req) {
        return dataAuditTrailRepository
                .findByIdRefAndEntityRef(String.valueOf(memberId), EEntityAuditTrail.MEMBER,
                        convertTimeFrom(req.getTimeFrom()),
                        convertTimeTo(req.getTimeTo()))
                .stream()
                .map(en -> DataAuditTrailRes.builder()
                        .id(en.getId())
                        .idRef(en.getIdRef())
                        .payload(en.getPayload())
                        .createdAt(DateTimes.toEpochSecond(en.getCreatedAt()))
                        .eventType(en.getEventType())
                        .userName(en.getUserId())
                        .source(convertSourceToResponse(en.getSource()))
                        .additionalSourceInfo(en.getAdditionalSourceInfo())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public DataAuditTrailDetailRes getDetailCheckLogMemberById(final Long memberId, final Long checkLogId) {

        DataAuditTrail entityCurrent = dataAuditTrailRepository.findById(checkLogId).orElseThrow(
                () -> new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check log not found",
                        LogData.createLogData().append("check_log_id", checkLogId))
        );
        if (entityCurrent.getEntityRef() != EEntityAuditTrail.MEMBER
                || !String.valueOf(memberId).equals(entityCurrent.getIdRef())) {
            throw new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check Log has entity type not equal member",
                    LogData.createLogData().append("check_log_id", checkLogId));
        }

        DataAuditTrail entityBefore = dataAuditTrailRepository
                .findBeforeByIdAndIdRefAndEntityRef(
                        entityCurrent.getId(),
                        entityCurrent.getIdRef(),
                        entityCurrent.getEntityRef()
                );

        return buildCheckLogDetailsFromEntityCurrent(entityCurrent, entityBefore);
    }

    @Override
    public DataAuditTrailDetailRes getDetailCheckLogCardMemberById(Long memberId, String cardNo, Long checkLogId) {

        DataAuditTrail entityCurrent = dataAuditTrailRepository.findById(checkLogId).orElseThrow(
                () -> new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check log not found",
                        LogData.createLogData().append("check_log_id", checkLogId))
        );
        if (entityCurrent.getEntityRef() != EEntityAuditTrail.MEMBER_CARD
                || !String.valueOf(memberId).equals(entityCurrent.getIdRef())) {
            throw new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check log has entity type not equal member",
                    LogData.createLogData().append("check_log_id" +
                            "", checkLogId));
        }
        try {
            Map<String, String> payload = (HashMap<String, String>) entityCurrent.getPayload();
            String payloadCardNo = payload.get("card_no");
            if (!cardNo.equals(payloadCardNo)) {
                throw new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check log not found",
                        LogData.createLogData().append("check_log_id", checkLogId));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ErrorCode.CHECKLOG_NOT_FOUND, "Check log not found",
                    LogData.createLogData().append("check_log_id", checkLogId));
        }

        DataAuditTrail entityBefore = dataAuditTrailRepository
                .findBeforeMemberCardNoByIdAndIdRef(
                        entityCurrent.getId(),
                        entityCurrent.getIdRef(),
                        cardNo
                );

        return buildCheckLogDetailsFromEntityCurrent(entityCurrent, entityBefore);
    }

    @Override
    public List<DataAuditTrailRes> filterCheckLogCardMember(Long memberId, FilterCheckLogCardMemberReq req) {
        List<DataAuditTrail> dataAuditTrails = dataAuditTrailRepository.filterByMemberIdAndCardNo(memberId,
                req.getCardNo(),
                convertTimeFrom(req.getTimeFrom()),
                convertTimeTo(req.getTimeTo())
        );
        return dataAuditTrails.stream().map(
                en -> DataAuditTrailRes.builder()
                        .id(en.getId())
                        .idRef(en.getIdRef())
                        .payload(en.getPayload())
                        .createdAt(DateTimes.toEpochSecond(en.getCreatedAt()))
                        .eventType(en.getEventType())
                        .userName(en.getUserId())
                        .source(convertSourceToResponse(en.getSource()))
                        .additionalSourceInfo(en.getAdditionalSourceInfo())
                        .build()
        ).collect(Collectors.toList());
    }

    private Date convertTimeTo(Long timeTo) {
        return timeTo != null ? DateTimes.toDate(timeTo) : new Date();
    }

    private Date convertTimeFrom(Long timeFrom) {
        return timeFrom != null ? DateTimes.toDate(timeFrom) : DateTimes.toDate(0L);
    }

    private DataAuditTrailDetailRes buildCheckLogDetailsFromEntityCurrent(DataAuditTrail entityCurrent,
                                                                          DataAuditTrail entityBefore) {

        DataAuditTrailRes current = DataAuditTrailRes.builder()
                .id(entityCurrent.getId())
                .idRef(entityCurrent.getIdRef())
                .payload(entityCurrent.getPayload())
                .createdAt(DateTimes.toEpochSecond(entityCurrent.getCreatedAt()))
                .eventType(entityCurrent.getEventType())
                .userName(entityCurrent.getUserId())
                .source(convertSourceToResponse(entityCurrent.getSource()))
                .additionalSourceInfo(entityCurrent.getAdditionalSourceInfo())
                .build();

        DataAuditTrailRes before = entityBefore != null ? DataAuditTrailRes.builder()
                .id(entityBefore.getId())
                .idRef(entityBefore.getIdRef())
                .payload(entityBefore.getPayload())
                .createdAt(DateTimes.toEpochSecond(entityBefore.getCreatedAt()))
                .eventType(entityBefore.getEventType())
                .userName(entityBefore.getUserId())
                .source(convertSourceToResponse(entityBefore.getSource()))
                .additionalSourceInfo(entityBefore.getAdditionalSourceInfo())
                .build() : null;

        return DataAuditTrailDetailRes.builder()
                .current(current)
                .before(before)
                .build();
    }

    private String convertSourceToResponse(String source) {
        if (checkLogSourceConfigPram.SOURCE_API_WRAPPER_VALUE.equals(source)) {
            return checkLogSourceConfigPram.SOURCE_MOBILE_VALUE;
        } else {
            return source;
        }
    }
}