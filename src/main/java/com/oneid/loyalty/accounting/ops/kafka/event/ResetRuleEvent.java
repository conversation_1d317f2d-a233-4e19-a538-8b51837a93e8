package com.oneid.loyalty.accounting.ops.kafka.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

@Getter
@Builder
public class ResetRuleEvent<T> implements Serializable {

    public static final String RESET_RULE_EVENT_TYPE = "RESET_RULE_EVENT";

    @JsonProperty("event_id")
    private String id;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("timestamp")
    private Long timeStamp;

    @JsonProperty("payload")
    private T payload;
}