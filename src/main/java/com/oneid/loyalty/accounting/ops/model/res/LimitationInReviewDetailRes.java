package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Builder
public class LimitationInReviewDetailRes {
    private Long requestId;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private String createdBy;

    private String status;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private EApprovalStatus approvalStatus;

    private String code;

    private String name;

    private CounterShortInformationRes counter;

    private String reason;

    private String description;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;

    private EBoolean allowResetCounter;

    private BigDecimal threshold;

    private BigDecimal warningThreshold;

    private EBoolean allowWithRemainingValue;

    private List<RuleRes> rules;

    private String requestType;
}
