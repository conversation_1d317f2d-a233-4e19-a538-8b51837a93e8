package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.WardDTO;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.List;

public interface OpsWardService {

    /**
     * Get one ward by id.
     *
     * @throws BusinessException if entity is not found
     */
    WardDTO getOne(Integer id);

    /**
     * Get all by district ID
     */
    List<Ward> getByDistrict(Integer districtId);

    /**
     * Get all by country code, province code and district code.
     */
    List<Ward> getByDistrict(String countryCode, String provinceCode, String districtCode);
}
