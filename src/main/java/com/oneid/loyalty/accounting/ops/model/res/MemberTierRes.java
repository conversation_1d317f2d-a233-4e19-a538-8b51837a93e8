package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

import java.util.Collection;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = MemberTierRes.MemberTierResBuilder.class)
public class MemberTierRes {
    private Long memberId;
    private String memberName;
    private String memberPhoneNo;
    private Integer policyId;
    private String policyName;
    private String policyCode;
    private Integer currentTierId;
    private String currentTierName;
    private String currentTierCode;
    private String currentTierIcon;
    private Integer programId;
    private Integer businessId;
    private String avatar;
    private String status;
    private Collection<DropdownRes> tierForUpdates;
}
