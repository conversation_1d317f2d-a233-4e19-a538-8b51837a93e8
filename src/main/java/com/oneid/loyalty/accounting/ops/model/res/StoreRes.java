package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Store;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class StoreRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("corporation_name")
    private String corporationName;

    @JsonProperty("chain_id")
    private Integer chainId;

    @JsonProperty("chain_name")
    private String chainName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("en_name")
    private String enName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("contact_person")
    private String contactPerson;

    @JsonProperty("email_address")
    private String emailAddress;

    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("tax_no")
    private String taxNo;

    @JsonProperty("website")
    private String webSite;

    @JsonProperty("address1")
    private String address1;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @JsonProperty("postal_code")
    private String postalCode;

    @JsonProperty("address2")
    private String address2;

    @JsonProperty("card_stock_limit")
    private Integer cardStockLimit;

    public static StoreRes of(
            Store store,
            Business business,
            Corporation corporation,
            Chain chain
    ) {
        StoreRes storeRes = new StoreRes();
        storeRes.setId(store.getId());
        storeRes.setCode(store.getCode());
        storeRes.setBusinessId(store.getBusinessId());
        storeRes.setBusinessCode(business != null ? business.getCode() : null);
        storeRes.setBusinessName(business != null ? business.getName() : null);
        storeRes.setCorporationId(store.getCorporationId());
        storeRes.setCorporationName(corporation != null ? corporation.getName() : null);
        storeRes.setChainId(store.getChainId());
        storeRes.setChainName(chain != null ? chain.getName() : null);
        storeRes.setName(store.getName());
        storeRes.setEnName(store.getEnName());
        storeRes.setDescription(store.getDescription());
        storeRes.setEnDescription(store.getEnDescription());
        storeRes.setStatus(store.getStatus() != null ? store.getStatus().getValue() : null);
        storeRes.setCreatedBy(store.getCreatedBy());
        storeRes.setUpdatedBy(store.getUpdatedBy());
        storeRes.setApprovedBy(store.getApprovedBy());
        storeRes.setCreatedAt(store.getCreatedAt() != null ? store.getCreatedAt().toInstant().getEpochSecond() : null);
        storeRes.setUpdatedAt(store.getUpdatedAt() != null ? store.getUpdatedAt().toInstant().getEpochSecond() : null);
        storeRes.setApprovedAt(store.getApprovedAt() != null ? store.getApprovedAt().toInstant().getEpochSecond() : null);
        storeRes.setCreatedYmd(store.getCreatedYmd());
        storeRes.setContactPerson(store.getContactPerson());
        storeRes.setEmailAddress(store.getEmailAddress());
        storeRes.setPhoneNo(store.getPhoneNo());
        storeRes.setTaxNo(store.getTaxNo());
        storeRes.setWebSite(store.getWebSite());
        storeRes.setAddress1(store.getAddress1());
        storeRes.setCountryId(store.getCountryId());
        storeRes.setProvinceId(store.getProvinceId());
        storeRes.setDistrictId(store.getDistrictId());
        storeRes.setWardId(store.getWardId());
        storeRes.setServiceStartDate(store.getServiceStartDate() != null ? store.getServiceStartDate().toInstant().getEpochSecond() : null);
        storeRes.setServiceEndDate(store.getServiceEndDate() != null ? store.getServiceEndDate().toInstant().getEpochSecond() : null);
        storeRes.setPostalCode(store.getPostalCode());
        storeRes.setAddress2(store.getAddress2());
        storeRes.setCardStockLimit(store.getCardStockLimit());
        return storeRes;
    }
}
