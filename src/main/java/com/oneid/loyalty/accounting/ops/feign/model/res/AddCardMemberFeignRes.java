package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AddCardMemberFeignRes implements Serializable {

    private static final long serialVersionUID = -2343242;
    @JsonProperty("card_no")
    private String cardNo;
    @JsonProperty("card_code")
    private String cardCode;
    @JsonProperty("status")
    private ECardStatus cardStatus;
    @JsonProperty("card_name")
    private String cardName;
    @JsonProperty("expired_date")
    private Long expiredDate;
    @JsonProperty("issue_date")
    private Long issueDate;
    @JsonProperty("activation_date")
    private Long activationDate;
    @JsonProperty("code_block")
    private String codeBlock;
}
