package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EConditionType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RuleListRes {

    @JsonProperty("scheme_id")
    private Integer schemeId;

    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @JsonProperty("rule_list")
    private List<RuleRecordRes> listRule;
}
