package com.oneid.loyalty.accounting.ops.exception;


import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;

public class OpsBusinessException extends RuntimeException {
    private static final long serialVersionUID = -7745501204619729837L;

    private OpsErrorCode code;

    private int errCode;

    private Object requestData;

    private Object[] messageParams;

    public OpsBusinessException(OpsErrorCode code, String message, Object requestData) {
        super(message);
        this.code = code;
        this.requestData = requestData;
        this.errCode = code.getValue();
    }

    public OpsBusinessException(int errCode, String message, Object requestData) {
        super(message);
        this.errCode = errCode;
        this.requestData = requestData;
    }

    public OpsBusinessException(OpsErrorCode code, String message, Object requestData, Object[] messageParams) {
        super(message);
        this.code = code;
        this.requestData = requestData;
        this.messageParams = messageParams;
    }

    public Object[] getMessageParams() {
        return messageParams;
    }

    public OpsErrorCode getCode() {
        return code;
    }

    public Object getRequestData() {
        return requestData;
    }

    public int getErrCode() {
        return errCode;
    }
}
