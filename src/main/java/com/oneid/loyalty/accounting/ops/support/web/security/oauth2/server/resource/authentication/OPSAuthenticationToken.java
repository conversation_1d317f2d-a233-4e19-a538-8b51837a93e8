package com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.authentication;

import java.util.Collection;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

public class OPSAuthenticationToken extends JwtAuthenticationToken {
    private static final long serialVersionUID = -1662465824565784006L;

    private OPSAuthenticatedPrincipal principal;

    public OPSAuthenticationToken(Jwt jwt, Collection<? extends GrantedAuthority> authorities) {
        super(jwt, authorities);
    }

    public OPSAuthenticationToken(Jwt jwt, Collection<? extends GrantedAuthority> authorities,
            OPSAuthenticatedPrincipal principal) {
        super(jwt, authorities);
        this.principal = principal;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }
}