package com.oneid.loyalty.accounting.ops.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.support.feign.FeignResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonInclude(value = Include.NON_NULL)
public class APIFeignInternalResponse<T> implements FeignResponse {
    private Meta meta;
    private T data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Meta implements Serializable {
        private static final long serialVersionUID = -645749963423423317L;
        @JsonProperty("code")
        private int code;
        @JsonProperty("message")
        private String message;
        @JsonProperty("page_size")
        private int pageSize;
        @JsonProperty("page_index")
        private int pageIndex;
        @JsonProperty("max_page_index")
        private int maxPageIndex;
        @JsonProperty("total")
        private int total;
    }
}