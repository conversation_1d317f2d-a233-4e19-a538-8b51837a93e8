package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateUpdateReq;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.CurrencyRate;

public class CurrencyRateMapper {

    public static CurrencyRate toCurrencyRateOne(CurrencyRateCreateReq request, String userName) {
        CurrencyRate result = new CurrencyRate();

        result.setCurrencyId(request.getCurrencyId());
        result.setBaseCurrencyId(request.getBaseCurrencyId());
        result.setBuyRate(request.getBuyRate());
        result.setSellRate(request.getSellRate());
        result.setStartDate(null);
        result.setEndDate(null);
        result.setStatus(ECommonStatus.of(request.getStatus()));
        result.setBusinessId(request.getBusinessId());
        result.setCreatedAt(DateTimes.currentDate());
        result.setUpdatedAt(DateTimes.currentDate());
        result.setCreatedBy(userName);
        result.setUpdatedBy(userName);

        return result;
    }

    public static CurrencyRate toCurrencyRateOne(CurrencyRate currencyRate, CurrencyRateUpdateReq request, String userName) {
        currencyRate.setCurrencyId(request.getCurrencyId());
        currencyRate.setBaseCurrencyId(request.getBaseCurrencyId());
        currencyRate.setBuyRate(request.getBuyRate());
        currencyRate.setSellRate(request.getSellRate());
        currencyRate.setStartDate(null);
        currencyRate.setEndDate(null);
        currencyRate.setStatus(ECommonStatus.of(request.getStatus()));
        currencyRate.setBusinessId(request.getBusinessId());
        currencyRate.setUpdatedAt(DateTimes.currentDate());
        currencyRate.setUpdatedBy(userName);

        return currencyRate;
    }
}