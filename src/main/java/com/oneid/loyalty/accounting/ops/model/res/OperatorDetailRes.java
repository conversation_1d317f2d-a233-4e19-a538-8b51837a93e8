package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OperatorDetailRes {
    private Integer id;
    private String operatorId;
    private ShortEntityRes business;
    private PayloadOperatorRes payload;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date createdAt;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date updatedAt;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date approvedAt;
    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    
    private Integer version;
    
    private ECommonStatus status;
    private EApprovalStatus approvalStatus;

    private OperatorDetailRes.LegacyPayload legacyPayload;

    private EBoolean legacy;

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonDeserialize(builder = OperatorDetailRes.LegacyPayload.LegacyPayloadBuilder.class)
    public static class LegacyPayload {
        private List<String> operatorCorps;
        private List<String> operatorChains;
        private List<String> operatorStores;
        private List<String> operatorTerminals;

        @JsonPOJOBuilder(withPrefix = "")
        public static class LegacyPayloadBuilder {
        }
    }
}
