package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@RestController
@RequestMapping("v1/rules")
@Validated
public class RuleController extends BaseController {

    @Autowired
    private OpsRuleService opsRuleService;

    @PostMapping("verify")
    public ResponseEntity<?> create(@Valid @RequestBody VerifyCreateListSchemeRuleReq req) {
//        opsRuleService.verifyListRule(req);
        return success(null);
    }
}