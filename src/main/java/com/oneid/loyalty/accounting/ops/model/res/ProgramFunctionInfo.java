package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.loyalty.accounting.ops.constant.ERequestAgentType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EProfileAttribute;
import com.oneid.oneloyalty.common.entity.Function;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class ProgramFunctionInfo implements Serializable {
    private static final long serialVersionUID = 4420721163065441334L;
    private String functionCode;
    private String functionName;
    private String groupName;
    private EBoolean having;
    private List<ProfileAttribute> profileAttributes = new ArrayList<>();

    @Data
    @NoArgsConstructor
    public static class ProfileAttribute implements Serializable {
        private static final long serialVersionUID = -5000934575092298584L;
        @NotNull(message = "Function code: must not be null")
        private String functionCode;
        @NotNull(message = "Code: must not be null")
        private EProfileAttribute code;
        private String name;
        private String description;
        private EBoolean having;
    }

    public static ProgramFunctionInfo valueOf(Function function) {
        ProgramFunctionInfo programFunctionInfo = new ProgramFunctionInfo();
        programFunctionInfo.setFunctionCode(function.getCode());
        programFunctionInfo.setFunctionName(function.getName());
        programFunctionInfo.setGroupName(function.getGroupName());
        programFunctionInfo.setHaving(EBoolean.NO);
        if (EBoolean.YES.equals(function.getProfileAttributeFlag())) {
            programFunctionInfo.setProfileAttributes(Arrays.stream(EProfileAttribute.values()).map(ele -> {
                ProfileAttribute profileAttribute = new ProfileAttribute();
                profileAttribute.setFunctionCode(function.getCode());
                profileAttribute.setCode(ele);
                profileAttribute.setDescription(ele.getDescription());
                profileAttribute.setName(ele.getDisplayName());
                profileAttribute.setHaving(EBoolean.NO);
                return profileAttribute;
            }).collect(Collectors.toList()));
        }
        return programFunctionInfo;
    }

    public static ProgramFunctionInfo valueOf(Function function, Set<String> functionCodes,
                                              Map<String, Set<EProfileAttribute>> profileAttributeMap) {
        ProgramFunctionInfo programFunctionInfo = new ProgramFunctionInfo();
        programFunctionInfo.setFunctionCode(function.getCode());
        programFunctionInfo.setFunctionName(function.getName());
        programFunctionInfo.setGroupName(Objects.nonNull(function.getGroupName()) ? function.getGroupName() : ERequestAgentType.OTHER.getValue());
        if (functionCodes.contains(function.getCode())) {
            programFunctionInfo.setHaving(EBoolean.YES);
        } else {
            programFunctionInfo.setHaving(EBoolean.NO);
        }
        if (EBoolean.YES.equals(function.getProfileAttributeFlag())) {
            programFunctionInfo.setProfileAttributes(Arrays.stream(EProfileAttribute.values()).map(ele -> {
                ProfileAttribute profileAttribute = new ProfileAttribute();
                profileAttribute.setFunctionCode(function.getCode());
                profileAttribute.setCode(ele);
                profileAttribute.setDescription(ele.getDescription());
                profileAttribute.setName(ele.getDisplayName());
                if (profileAttributeMap.containsKey(function.getCode()) && profileAttributeMap.get(function.getCode()).contains(ele)) {
                    profileAttribute.setHaving(EBoolean.YES);
                } else {
                    profileAttribute.setHaving(EBoolean.NO);
                }
                return profileAttribute;
            }).collect(Collectors.toList()));
        }
        return programFunctionInfo;
    }
}