package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.validation.*;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.*;

@Getter
@Setter
public class StoreUpdateReq {
    @NotNull(message = "'BusinessId' cannot be null")
    @JsonProperty("business_id")
    private Integer businessId;
    
    @NotNull(message = "'CorporationId' cannot be null")
    @JsonProperty("corporation_id")
    private Integer corporationId;
    
    @NotNull(message = "'ChainId' cannot be null")
    @JsonProperty("chain_id")
    private Integer chainId;

    @NotBlank(message = "'Name' cannot be blank")
    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @ValidMeta(message = "'Name' is invalid")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    @ValidMeta(message = "'Description' is invalid")
    private String description;

    @NotNull(message = "'Service start date' cannot be null")
    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @NotNull(message = "'Service end date' cannot be null")
    @JsonProperty("service_end_date")
    private Long serviceEndDate;

//    @NotNull(message = "'Contact person' is required")
    @PersonName(message = "'Contact person' is invalid")
    @JsonProperty("contact_person")
    private String contactPerson;

    @ValidEmail(message = "'Email address' is invalid")
    private String emailAddress;

    @NotNull(message = "'Address 1' is required")
    @Address(message = "'Address 1' is invalid")
    @JsonProperty("address1")
    private String address1;

    @Address(message = "'Address 2' is invalid")
    @JsonProperty("address2")
    private String address2;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @Size(max = 16, message = "'Postal code' cannot exceed 16 characters")
    @Pattern(regexp = "^[0-9]{0,16}$", message = "'Postal code' is invalid")
    @JsonProperty("postal_code")
    private String postalCode;

//    @NotNull(message = "'Phone no' is required")
    @Phone(message = "Phone no is invalid")
    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("website")
    private String website;

    @NotBlank(message = "'Status' cannot be empty")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;

    @Max(value = 10000, message = "'Card stock limit' must be less than or equal to 10000")
    @NotNull(message = "'Card stock limit' cannot be null")
    @JsonProperty("card_stock_limit")
    private Integer cardStockLimit;

    @AssertTrue(message = "Service end date must be greater than service start date")
    public boolean isValidStartAndEndDate() {
        if (this.serviceStartDate == null || this.serviceEndDate == null) {
            return true;
        }
        return this.serviceEndDate > this.serviceStartDate;
    }
}
