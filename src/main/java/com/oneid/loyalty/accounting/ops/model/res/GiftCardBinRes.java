package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.GiftCardBin;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftCardBinRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("bin_code")
    private String binCode;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    public static GiftCardBinRes of(GiftCardBin giftCardBin, Business business, Program program) {
        GiftCardBinRes cardBinRes = new GiftCardBinRes();
        cardBinRes.setId(giftCardBin.getId());
        cardBinRes.setBinCode(giftCardBin.getBinCode());
        cardBinRes.setBusinessId(giftCardBin.getBusinessId());
        cardBinRes.setBusinessName(business != null ? business.getName() : null);
        cardBinRes.setProgramId(giftCardBin.getProgramId());
        cardBinRes.setProgramName(program != null ? program.getName() : null);
        cardBinRes.setName(giftCardBin.getName());
        cardBinRes.setDescription(giftCardBin.getDescription());
        cardBinRes.setStatus(giftCardBin.getStatus() != null ? giftCardBin.getStatus().getValue() : null);
        cardBinRes.setCreatedBy(giftCardBin.getCreatedBy());
        cardBinRes.setUpdatedBy(giftCardBin.getUpdatedBy());
        cardBinRes.setApprovedBy(giftCardBin.getApprovedBy());
        cardBinRes.setCreatedAt(giftCardBin.getCreatedAt() != null ? giftCardBin.getCreatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setUpdatedAt(giftCardBin.getUpdatedAt() != null ? giftCardBin.getUpdatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setApprovedAt(giftCardBin.getApprovedAt() != null ? giftCardBin.getApprovedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setCreatedYmd(giftCardBin.getCreatedYmd());
        return cardBinRes;
    }
}
