package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class ConditionRecordReq {
    @JsonProperty("id")
    private Integer conditionId;

    @JsonProperty("rule_id")
    private Integer ruleId;

    @NotNull(message = "attribute must not be null")
    private String attribute;

    @NotNull(message = "operator must not be null")
    private EAttributeOperator operator;

    @NotNull(message = "value must not be null")
    private Object value;

    @JsonProperty("resource_path")
    private String resourcePath;

    @JsonProperty("support_filter")
    private Boolean supportFilter;
}