package com.oneid.loyalty.accounting.ops.service.outbound.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class AbstractRes implements Serializable {

    private static final long serialVersionUID = 4812120694069481680L;

    @JsonProperty("meta")
    protected Meta meta;

    @Setter
    @Getter
    @ToString
    public static class Meta {
        @JsonProperty("code")
        private Integer code;

        @JsonProperty("message")
        private String message;
    }
}