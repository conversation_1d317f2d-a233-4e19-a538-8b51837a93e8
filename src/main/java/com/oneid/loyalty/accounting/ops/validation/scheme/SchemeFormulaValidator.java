package com.oneid.loyalty.accounting.ops.validation.scheme;

import com.oneid.loyalty.accounting.ops.model.req.FormulaRecordReq;
import com.oneid.oneloyalty.common.constant.EFormulaUnitType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class SchemeFormulaValidator implements ConstraintValidator<SchemeFormulaAnnotation, FormulaRecordReq> {

    int maxPercentValue;
    int minPercentValue;

    @Override
    public void initialize(SchemeFormulaAnnotation constraintAnnotation) {
        this.maxPercentValue = constraintAnnotation.maxPercentValue();
        this.minPercentValue = constraintAnnotation.minPercentValue();
    }

    @Override
    public boolean isValid(FormulaRecordReq formula, ConstraintValidatorContext context) {
        if (formula.getFormulaType() == null) {
            context.buildConstraintViolationWithTemplate("Formula type is not null").addConstraintViolation();
            return false;
        }
        switch (formula.getFormulaType()) {
            case F2:
                return f2IsValid(context, formula);
            case F4:
                return f4IsValid(context, formula);
        }
        return true;
    }

    private boolean f4IsValid(ConstraintValidatorContext context, FormulaRecordReq formula) {
        if (formula.getFormulaUnitType() == null) {
            context.buildConstraintViolationWithTemplate("Formula unit is not null").addConstraintViolation();
            return false;
        }

        if (formula.getFactorList() == null) {
            context.buildConstraintViolationWithTemplate("Factor list unit is not null").addConstraintViolation();
            return false;
        }

        if (formula.getFactorList().size() < 1 || formula.getFactorList().size() > 10) {
            context.buildConstraintViolationWithTemplate("Factor list size is between 1 to 10").addConstraintViolation();
            return false;
        }

        if (EFormulaUnitType.PERCENT.equals(formula.getFormulaUnitType())) {
            boolean ok = formula.getFactorList().stream()
                    .map(r -> (r.getValue() <= maxPercentValue && r.getValue() >= minPercentValue))
                    .reduce(true, (a, b) -> a && b);
            if (!ok) {
                context.buildConstraintViolationWithTemplate
                        (String.format("Value is between [%d, %d] when factor equals percent",
                                minPercentValue, maxPercentValue)).addConstraintViolation();
                return false;
            }
        }

        Long lastValue = null;
        for (FormulaRecordReq.FormulaFactor factor : formula.getFactorList()) {

            if (factor.getAmountFrom() == null || factor.getAmountTo() == null) {
                context.buildConstraintViolationWithTemplate
                        ("Factor list invalid, amount from & amount to must not be null").addConstraintViolation();
                return false;
            }

            if (lastValue != null && factor.getAmountFrom().compareTo(lastValue) < 0) {
                context.buildConstraintViolationWithTemplate
                        ("Factor list invalid").addConstraintViolation();
                return false;
            }
            if (factor.getAmountFrom().compareTo(factor.getAmountTo()) > 0) {
                context.buildConstraintViolationWithTemplate
                        ("Factor list invalid, amount from less than amount to").addConstraintViolation();
                return false;
            }
            lastValue = factor.getAmountTo();
        }

        return true;
    }

    private boolean f2IsValid(ConstraintValidatorContext context, FormulaRecordReq formula) {
        if (formula.getN() == null) {
            context.buildConstraintViolationWithTemplate("Formula is require value of N").addConstraintViolation();
            return false;
        }

        if (formula.getD() == null) {
            context.buildConstraintViolationWithTemplate("Formula is require value of D").addConstraintViolation();
            return false;
        }
        return true;
    }
}
