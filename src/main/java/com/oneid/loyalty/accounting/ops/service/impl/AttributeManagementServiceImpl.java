package com.oneid.loyalty.accounting.ops.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeMappingReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeGroupRes;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeServiceTypeMappingRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeServiceTypeRequestRes;
import com.oneid.loyalty.accounting.ops.service.ProgramAttributeManagementService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceTypeMappingRequest;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceTypeRequest;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.RuleConditionRequest;
import com.oneid.oneloyalty.common.entity.SchemeRuleCondition;
import com.oneid.oneloyalty.common.entity.SystemAttribute;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeMappingRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ProgramTransactionAttributeRepository;
import com.oneid.oneloyalty.common.repository.RuleConditionRequestRepository;
import com.oneid.oneloyalty.common.repository.SchemeRuleConditionRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRepository;

@Service
public class AttributeManagementServiceImpl implements ProgramAttributeManagementService {
    
    @Value("${maker-checker.module.program-attribute-management}")
    private String moduleId;
    
    @Value("${maker-checker.module.scheme}")
    private String schemeModuleId;
    
    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @Autowired
    private ProgramAttributeServiceTypeRequestRepository programAttributeServiceTypeRequestRepository;
    
    @Autowired
    private ProgramAttributeServiceTypeMappingRequestRepository programAttributeServiceTypeMappingRequestRepository;
    
    @Autowired
    private SystemAttributeRepository systemAttributeRepository;
    
    @Autowired
    private ProgramTransactionAttributeRepository programTransactionAttributeRepository;
    
    @Autowired
    private ProgramAttributeRepository programAttributeRepository;
    
    @Autowired
    private ProgramRepository programRepository;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private SchemeRuleConditionRepository schemeRuleConditionRepository;
    
    @Autowired
    private RuleConditionRequestRepository ruleConditionRequestRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    
    private TransactionTemplate transactionTemplate;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }
    
    @Override
    public boolean isAvailable(Integer programId, EServiceType serviceType) {
        this.getActivatedProgram(programId);
        boolean haveEffectedVersion = programAttributeServiceTypeRequestRepository.findEffectedVersion(programId, serviceType).isPresent();
        if (haveEffectedVersion)
            return false;
        
        boolean havePendingVersion = programAttributeServiceTypeRequestRepository.findPendingVersion(programId, serviceType).isPresent();
        if (havePendingVersion)
            return false;
        
        return true;
    }
    
    @Override
    public AttributeGroupRes getAllAttributes(Integer programId) {
        List<SystemAttribute> systemAttributes = systemAttributeRepository.findAll();
        List<AttributeRes> systemAttributesRes = systemAttributes.stream()
                .map(e -> AttributeRes.builder()
                        .code(e.getAttribute())
                        .name(e.getName())
                        .description(e.getDescription())
                        .build())
                .collect(Collectors.toList());
        
        List<ProgramTransactionAttribute> programAttributes = programTransactionAttributeRepository.findByProgram(programId);
        List<AttributeRes> programAttributesRes = programAttributes.stream()
                .map(e -> AttributeRes.builder()
                        .code(e.getAttribute())
                        .name(e.getName())
                        .description(e.getDescription())
                        .build())
                .collect(Collectors.toList());
        
        List<ProgramAttribute> memberAttributes = programAttributeRepository.findByProgramId(programId);
        List<AttributeRes> memberAttributesRes = memberAttributes.stream()
                .map(e -> AttributeRes.builder()
                        .code(e.getCode())
                        .name(e.getName())
                        .description(e.getDescription())
                        .build())
                .collect(Collectors.toList());
        
        return AttributeGroupRes.builder()
                .systemAttributes(systemAttributesRes)
                .programAttributes(programAttributesRes)
                .memberAttributes(memberAttributesRes)
                .build();
    }
    
    @Override
    public ProgramAttributeServiceTypeRequestRes requestCreate(ProgramAttributeServiceTypeCreateReq req) {
        ProgramAttributeServiceTypeRequest reqEntity = transactionTemplate
                .execute(status -> this.insertCreateRequest(req));
        
        ChangeRequestFeignReq changeReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(reqEntity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(reqEntity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(changeReq);
        
        return ProgramAttributeServiceTypeRequestRes.builder()
                .id(reqEntity.getId())
                .build();
    }
    
    private ProgramAttributeServiceTypeRequest insertCreateRequest(ProgramAttributeServiceTypeCreateReq req) {
        Integer programId = req.getProgramId();
        EServiceType serviceType = req.getServiceType();
        
        Program program = this.getActivatedProgram(programId);
        
        programAttributeServiceTypeRequestRepository.findEffectedVersion(programId, serviceType)
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_CONFIGURED, null, null, new Object[] { program.getName() });
                });
        
        programAttributeServiceTypeRequestRepository.findPendingVersion(programId, serviceType)
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_REQUESTED);
                });
        
        ProgramAttributeServiceTypeRequest reqEntity = ProgramAttributeServiceTypeRequest.builder()
                .programId(programId)
                .serviceType(serviceType)
                .requestStatus(ECommonStatus.PENDING)
                .approvalStatus(EApprovalStatus.PENDING)
                .build();
        programAttributeServiceTypeRequestRepository.save(reqEntity);
        
        if (CollectionUtils.isNotEmpty(req.getSystemAttributeCodes())) {
            List<SystemAttribute> attributes = systemAttributeRepository.findByAttributeIn(req.getSystemAttributeCodes());
            Map<String, SystemAttribute> attributeMap = attributes.stream()
                    .collect(Collectors.toMap(e -> e.getAttribute(), e -> e));
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = req.getSystemAttributeCodes().stream()
                    .map(code -> {
                        SystemAttribute attribute = attributeMap.get(code);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_SYSTEM_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { code });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(code)
                                .attributeType(EAttributeType.SYSTEM)
                                .actionType(ECommonActionType.Create)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        
        if (CollectionUtils.isNotEmpty(req.getProgramAttributeCodes())) {
            List<ProgramTransactionAttribute> attributes = programTransactionAttributeRepository.findByProgramIdAndAttributeIn(programId, req.getProgramAttributeCodes());
            Map<String, ProgramTransactionAttribute> attributeMap = attributes.stream()
                    .collect(Collectors.toMap(e -> e.getAttribute(), e -> e));
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = req.getProgramAttributeCodes().stream()
                    .map(code -> {
                        ProgramTransactionAttribute attribute = attributeMap.get(code);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_PROGRAM_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { code });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(code)
                                .attributeType(EAttributeType.PROGRAM_TRANSACTION)
                                .actionType(ECommonActionType.Create)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        
        if (CollectionUtils.isNotEmpty(req.getMemberAttributeCodes())) {
            List<ProgramAttribute> attributes = programAttributeRepository.findByProgramIdAndCodeIn(programId, req.getMemberAttributeCodes());
            Map<String, ProgramAttribute> attributeMap = attributes.stream()
                    .collect(Collectors.toMap(e -> e.getCode(), e -> e));
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = req.getMemberAttributeCodes().stream()
                    .map(code -> {
                        ProgramAttribute attribute = attributeMap.get(code);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_MEMBER_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { code });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(code)
                                .attributeType(EAttributeType.MEMBER)
                                .actionType(ECommonActionType.Create)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        
        return reqEntity;
    }
    
    @Override
    public Page<ProgramAttributeServiceTypeRequestRes> getPage(Integer businessId, Integer programId, 
            List<EServiceType> serviceTypes, EApprovalStatus approvalStatus, Pageable pageable
    ) {
        ECommonStatus requestStatus = null;
        
        if(approvalStatus != null) {
            switch (approvalStatus) {
                case APPROVED:
                    requestStatus = ECommonStatus.ACTIVE;
                    break;
                case REJECTED:
                    requestStatus = ECommonStatus.INACTIVE;
                    break;
                default:
                    requestStatus = ECommonStatus.PENDING;
                    break;
            }
        }
        
        Page<Object[]> page = programAttributeServiceTypeRequestRepository.findPage(businessId, programId, serviceTypes, approvalStatus, requestStatus, pageable);
        List<ProgramAttributeServiceTypeRequestRes> content = this.convertToRes(page.getContent());
        return new PageImpl<>(content, pageable, page.getTotalElements());
    }
    
    @Override
    public ProgramAttributeServiceTypeRequestRes getDetail(Integer id) {
        ProgramAttributeServiceTypeRequest request = programAttributeServiceTypeRequestRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_NOT_FOUND));
        
        Program program = programRepository.getOne(request.getProgramId());
        Business business = businessRepository.getOne(program.getBusinessId());
        
        ProgramAttributeServiceTypeRequestRes res = this.convertToDetailRes(request, program, business);
        this.setMappingsData(res);
        return res;
    }
    
    @Override
    public ProgramAttributeServiceTypeRequestRes getChangeable(Integer id) {
        ProgramAttributeServiceTypeRequest request = programAttributeServiceTypeRequestRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_NOT_FOUND));
        
        Integer programId = request.getProgramId();
        EServiceType serviceType = request.getServiceType();
        
        if (request.getApprovalStatus() == EApprovalStatus.PENDING)
            throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_REQUESTED);
        
        if (request.getApprovalStatus() == EApprovalStatus.REJECTED)
            throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_REJECTED);
        
        if (request.getRequestStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_PREVIOUS_VERSION_CAN_NOT_BE_EDITED);
        
        programAttributeServiceTypeRequestRepository.findPendingVersion(programId, serviceType)
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_REQUESTED);
                });
        
        Program program = programRepository.getOne(request.getProgramId());
        Business business = businessRepository.getOne(program.getBusinessId());
        
        ProgramAttributeServiceTypeRequestRes res = this.convertToDetailRes(request, program, business);
        
        List<ProgramAttributeServiceTypeMappingRequest> mappingList = programAttributeServiceTypeMappingRequestRepository.findEffecteds(request.getId());
        
        Map<EAttributeType, Map<String, ProgramAttributeServiceTypeMappingRequest>> mappingPerCodePerType = mappingList.stream()
                .collect(Collectors.groupingBy(e -> e.getAttributeType(), Collectors.toMap(e -> e.getAttributeCode(), e -> e)));
        
        Set<String> usedAttributes = this.getUsedAttributes(programId, serviceType);
        
        List<SystemAttribute> systemAttributes = systemAttributeRepository.findAll();
        List<ProgramAttributeServiceTypeMappingRequestRes> systemAttributeMappings = systemAttributes.stream()
                .map(e -> {
                    ProgramAttributeServiceTypeMappingRequestRes mappingRes = ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getAttribute())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build();
                    
                    ProgramAttributeServiceTypeMappingRequest mapping = mappingPerCodePerType
                            .getOrDefault(EAttributeType.SYSTEM, Collections.emptyMap()).get(e.getAttribute());
                    
                    if (mapping != null) {
                        mappingRes.setId(mapping.getId());
                        mappingRes.setSelected(true);
                        
                        if (usedAttributes.contains(e.getAttribute())) {
                            mappingRes.setUsed(true);
                        } else {
                            mappingRes.setUsed(false);
                        }
                    } else {
                        mappingRes.setSelected(false);
                    }
                    return mappingRes;
                })
                .collect(Collectors.toList());
        
        List<ProgramTransactionAttribute> programAttributes = programTransactionAttributeRepository.findByProgram(programId);
        List<ProgramAttributeServiceTypeMappingRequestRes> programAttributeMappings = programAttributes.stream()
                .map(e -> {
                    ProgramAttributeServiceTypeMappingRequestRes mappingRes = ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getAttribute())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build();
                    
                    ProgramAttributeServiceTypeMappingRequest mapping = mappingPerCodePerType
                            .getOrDefault(EAttributeType.PROGRAM_TRANSACTION, Collections.emptyMap()).get(e.getAttribute());
                    
                    if (mapping != null) {
                        mappingRes.setId(mapping.getId());
                        mappingRes.setSelected(true);
                        
                        if (usedAttributes.contains(e.getAttribute())) {
                            mappingRes.setUsed(true);
                        } else {
                            mappingRes.setUsed(false);
                        }
                    } else {
                        mappingRes.setSelected(false);
                    }
                    return mappingRes;
                })
                .collect(Collectors.toList());
        
        List<ProgramAttribute> memberAttributes = programAttributeRepository.findByProgramId(programId);
        List<ProgramAttributeServiceTypeMappingRequestRes> memberAttributeMappings = memberAttributes.stream()
                .map(e -> {
                    ProgramAttributeServiceTypeMappingRequestRes mappingRes = ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getCode())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build();
                    
                    ProgramAttributeServiceTypeMappingRequest mapping = mappingPerCodePerType
                            .getOrDefault(EAttributeType.MEMBER, Collections.emptyMap()).get(e.getCode());
                    
                    if (mapping != null) {
                        mappingRes.setId(mapping.getId());
                        mappingRes.setSelected(true);
                        
                        if (usedAttributes.contains(e.getCode())) {
                            mappingRes.setUsed(true);
                        } else {
                            mappingRes.setUsed(false);
                        }
                    } else {
                        mappingRes.setSelected(false);
                    }
                    return mappingRes;
                })
                .collect(Collectors.toList());
        
        res.setSystemAttributeMappings(systemAttributeMappings);
        res.setProgramAttributeMappings(programAttributeMappings);
        res.setMemberAttributeMappings(memberAttributeMappings);
        return res;
    }
    
    @Override
    public ProgramAttributeServiceTypeRequestRes requestChange(ProgramAttributeServiceTypeUpdateReq req) {
        ProgramAttributeServiceTypeRequest reqEntity = transactionTemplate
                .execute(status -> this.insertChangeRequest(req));
        
        ChangeRequestFeignReq changeReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(reqEntity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(reqEntity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(changeReq);
        
        return ProgramAttributeServiceTypeRequestRes.builder()
                .id(reqEntity.getId())
                .build();
    }
    
    private ProgramAttributeServiceTypeRequest insertChangeRequest(ProgramAttributeServiceTypeUpdateReq req) {
        Integer programId = req.getProgramId();
        EServiceType serviceType = req.getServiceType();
        
        this.getActivatedProgram(programId);
        
        programAttributeServiceTypeRequestRepository.findPendingVersion(programId, serviceType)
                .ifPresent(e -> {
                    throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_REQUESTED);
                });
        
        programAttributeServiceTypeRequestRepository.findEffectedVersion(programId, serviceType)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_NOT_CONFIGURED));
        
        Set<String> usedAttributes = this.getUsedAttributes(programId, serviceType);
        
        ProgramAttributeServiceTypeRequest reqEntity = ProgramAttributeServiceTypeRequest.builder()
                .programId(programId)
                .serviceType(serviceType)
                .requestStatus(ECommonStatus.PENDING)
                .approvalStatus(EApprovalStatus.PENDING)
                .build();
        programAttributeServiceTypeRequestRepository.save(reqEntity);
        
        if (CollectionUtils.isNotEmpty(req.getSystemAttributeMappings())) {
            List<ProgramAttributeServiceTypeMappingReq> inputMappings = this.filterValidMapping(req.getSystemAttributeMappings());
            
            List<String> attributeCodes = inputMappings.stream().map(e -> e.getAttributeCode()).collect(Collectors.toList());
            List<SystemAttribute> attributes = systemAttributeRepository.findByAttributeIn(attributeCodes);
            Map<String, SystemAttribute> attributeMap = attributes.stream().collect(Collectors.toMap(e -> e.getAttribute(), e -> e));
            
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = inputMappings.stream()
                    .map(inputMapping -> {
                        String attributeCode = inputMapping.getAttributeCode();
                        ECommonActionType actionType = this.determineActionType(inputMapping);
                        if (actionType == ECommonActionType.Delete) 
                            if (usedAttributes.contains(attributeCode)) 
                                throw new BusinessException(ErrorCode.ATTRIBUTE_CODE_BEING_USED, null, null, new Object[] { attributeCode });
                        
                        SystemAttribute attribute = attributeMap.get(attributeCode);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_SYSTEM_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { attributeCode });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(attributeCode)
                                .attributeType(EAttributeType.SYSTEM)
                                .actionType(actionType)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        
        if (CollectionUtils.isNotEmpty(req.getProgramAttributeMappings())) {
            List<ProgramAttributeServiceTypeMappingReq> inputMappings = this.filterValidMapping(req.getProgramAttributeMappings());
            
            List<String> attributeCodes = inputMappings.stream().map(e -> e.getAttributeCode()).collect(Collectors.toList());
            List<ProgramTransactionAttribute> attributes = programTransactionAttributeRepository.findByProgramIdAndAttributeIn(programId, attributeCodes);
            Map<String, ProgramTransactionAttribute> attributeMap = attributes.stream().collect(Collectors.toMap(e -> e.getAttribute(), e -> e));
            
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = inputMappings.stream()
                    .map(inputMapping -> {
                        String attributeCode = inputMapping.getAttributeCode();
                        ECommonActionType actionType = this.determineActionType(inputMapping);
                        if (actionType == ECommonActionType.Delete) 
                            if (usedAttributes.contains(attributeCode)) 
                                throw new BusinessException(ErrorCode.ATTRIBUTE_CODE_BEING_USED, null, null, new Object[] { attributeCode });
                        
                        ProgramTransactionAttribute attribute = attributeMap.get(attributeCode);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_PROGRAM_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { attributeCode });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(attributeCode)
                                .attributeType(EAttributeType.PROGRAM_TRANSACTION)
                                .actionType(actionType)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        
        if (CollectionUtils.isNotEmpty(req.getMemberAttributeMappings())) {
            List<ProgramAttributeServiceTypeMappingReq> inputMappings = this.filterValidMapping(req.getMemberAttributeMappings());
            List<String> attributeCodes = inputMappings.stream().map(e -> e.getAttributeCode()).collect(Collectors.toList());
            List<ProgramAttribute> attributes = programAttributeRepository.findByProgramIdAndCodeIn(programId, attributeCodes);
            Map<String, ProgramAttribute> attributeMap = attributes.stream().collect(Collectors.toMap(e -> e.getCode(), e -> e));
            
            List<ProgramAttributeServiceTypeMappingRequest> mappingRequests = inputMappings.stream()
                    .map(inputMapping -> {
                        String attributeCode = inputMapping.getAttributeCode();
                        ECommonActionType actionType = this.determineActionType(inputMapping);
                        if (actionType == ECommonActionType.Delete) 
                            if (usedAttributes.contains(attributeCode)) 
                                throw new BusinessException(ErrorCode.ATTRIBUTE_CODE_BEING_USED, null, null, new Object[] { attributeCode });
                        
                        ProgramAttribute attribute = attributeMap.get(attributeCode);
                        if (attribute == null)
                            throw new BusinessException(ErrorCode.M_MEMBER_ATTRIBUTE_CODE_NOT_FOUND, null, null, new Object[] { attributeCode });
                        
                        return ProgramAttributeServiceTypeMappingRequest.builder()
                                .requestId(reqEntity.getId())
                                .attributeCode(attributeCode)
                                .attributeType(EAttributeType.MEMBER)
                                .actionType(actionType)
                                .build();
                    })
                    .collect(Collectors.toList());
            programAttributeServiceTypeMappingRequestRepository.saveAll(mappingRequests);
        }
        return reqEntity;
    }
    
    private List<ProgramAttributeServiceTypeMappingReq> filterValidMapping(List<ProgramAttributeServiceTypeMappingReq> inputMappings) {
        return inputMappings.stream()
                .filter(e -> e.isSelected() == true || e.getId() != null)
                .collect(Collectors.toList());
    }
    
    private ECommonActionType determineActionType(ProgramAttributeServiceTypeMappingReq mapping) {
        if (mapping.isSelected() && mapping.getId() == null)
            return ECommonActionType.Create;
        
        if (mapping.isSelected() == false && mapping.getId() != null)
            return ECommonActionType.Delete;
        
        return null;
    }
    
    private Program getActivatedProgram(Integer programId) {
        Program program = programRepository.findById(programId)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
        
        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE);
        
        return program;
    }
    
    private Set<String> getUsedAttributes(Integer programId, EServiceType serviceType) {
        if (serviceType == EServiceType.SCHEME) {
            List<SchemeRuleCondition> effectedConditions = schemeRuleConditionRepository.findByProgramId(programId);
            Set<String> effectedAttributes = effectedConditions.stream()
                    .map(e -> e.getAttribute())
                    .collect(Collectors.toSet());
            
            APIResponse<ChangeRequestPageFeignRes> mcResponse = makerCheckerServiceClient.getChangeRequests(
                    schemeModuleId,
                    null,
                    null,
                    EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                    1,
                    100, // max page size on maker-checker
                    null,
                    null);
            
            ChangeRequestPageFeignRes mcRequestPage = mcResponse.getData();
            Set<String> pendingAttributes = mcRequestPage.getRecords().stream()
                    .map(ChangeRecordFeginRes::getPayload)
                    .map(e -> objectMapper.convertValue(e, UpdateSchemeReq.class))
                    .flatMap(e -> e.getRuleList().stream())
                    .flatMap(e -> e.getListCondition().stream())
                    .map(e -> e.getAttribute())
                    .collect(Collectors.toSet());
            
            effectedAttributes.addAll(pendingAttributes);
            
            return effectedAttributes;
        } else {
            List<RuleConditionRequest> usedConditions = new ArrayList<>();
            if (serviceType == EServiceType.COUNTER) {
                usedConditions = ruleConditionRequestRepository.findUsedByServiceTypeCounter(programId);
            } else if (serviceType == EServiceType.LIMITATION) {
                usedConditions = ruleConditionRequestRepository.findUsedByServiceTypeLimitation(programId);
            } else if (serviceType == EServiceType.TIER) {
                usedConditions = ruleConditionRequestRepository.findUsedByServiceTypeTier(programId);
            }
            
            Set<String> usedAttributes = usedConditions.stream()
                    .map(e -> e.getAttribute())
                    .collect(Collectors.toSet());
            
            return usedAttributes;
        }
    }
    
    @Override
    public Page<ProgramAttributeServiceTypeRequestRes> getInReviewPage(EApprovalStatus approvalStatus,
            LocalDate fromDate, LocalDate toDate, Pageable pageable
    ) {
        APIResponse<ChangeRequestPageFeignRes> mcResponse = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus == null ? null : approvalStatus.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromDate == null ? null : (int) fromDate.atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond(),
                toDate == null ? null : (int) toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toEpochSecond());
        
        ChangeRequestPageFeignRes mcRequestPage = mcResponse.getData();
        
        if (mcRequestPage.getTotalRecordCount() <= 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
        
        List<Integer> requestIds = mcRequestPage.getRecords().stream()
                .map(ChangeRecordFeginRes::getObjectId)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        
        List<Object[]> requestRecords = programAttributeServiceTypeRequestRepository.findDataByIds(requestIds);
        
        List<ProgramAttributeServiceTypeRequestRes> requests = this.convertToRes(requestRecords);
        
        Map<Integer, ProgramAttributeServiceTypeRequestRes> requestMap = requests.stream()
                .collect(Collectors.toMap(e -> e.getId(), e -> e));
        
        List<ProgramAttributeServiceTypeRequestRes> content = mcRequestPage.getRecords().stream()
                .map(mcRequest -> {
                    Integer reviewId = mcRequest.getChangeRequestId();
                    Integer requestId = Integer.parseInt(mcRequest.getObjectId());
                    
                    if (requestMap.containsKey(requestId) == false) // handle mismatched data with makerchecker
                        return null;
                    
                    ProgramAttributeServiceTypeRequestRes request = requestMap.get(requestId);
                    request.setReviewId(reviewId);
                    return request;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        return new PageImpl<>(content, pageable, mcRequestPage.getTotalRecordCount());
    }
    
    @Override
    public ProgramAttributeServiceTypeRequestRes getInReviewDetail(int reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> mcResponse = makerCheckerServiceClient.getChangeRequestById(String.valueOf(reviewId));
        Integer requestId = Integer.parseInt(mcResponse.getData().getObjectId());
        
        ProgramAttributeServiceTypeRequest request = programAttributeServiceTypeRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_NOT_FOUND));
        
        Program program = programRepository.getOne(request.getProgramId());
        Business business = businessRepository.getOne(program.getBusinessId());
        
        ProgramAttributeServiceTypeRequestRes res = this.convertToDetailRes(request, program, business);
        this.setMappingsData(res);
        return res;
    }
    
    private List<ProgramAttributeServiceTypeRequestRes> convertToRes(List<Object[]> records) {
        return records.stream()
                .map(record -> {
                    ProgramAttributeServiceTypeRequest request = ProgramAttributeServiceTypeRequest.class.cast(record[0]);
                    Program program = Program.class.cast(record[1]);
        
                    ProgramAttributeServiceTypeRequestRes res = this.convertToBaseRes(request, program);
                    return res;
                })
                .collect(Collectors.toList());
    }
    
    private ProgramAttributeServiceTypeRequestRes convertToBaseRes(ProgramAttributeServiceTypeRequest request, Program program) {
        return ProgramAttributeServiceTypeRequestRes.builder()
                .id(request.getId())
                .serviceType(request.getServiceType())
                .programId(request.getProgramId())
                .programName(program.getName())
                .approvalStatus(request.getApprovalStatus())
                .createdBy(request.getCreatedBy())
                .createdAt(request.getCreatedAt().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime())
                .approvedBy(request.getApprovedBy())
                .approvedAt(request.getApprovedAt() == null ? null : request.getApprovedAt().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime())
                .build();
    }
    
    private ProgramAttributeServiceTypeRequestRes convertToDetailRes(ProgramAttributeServiceTypeRequest request, Program program, Business business) {
        ProgramAttributeServiceTypeRequestRes res = this.convertToBaseRes(request, program);
        res.setBusinessId(business.getId());
        res.setBusinessName(business.getName());
        res.setChangedReason(request.getChangedReason());
        res.setRejectedReason(request.getRejectedReason());
        return res;
    }
    
    private void setMappingsData(ProgramAttributeServiceTypeRequestRes res) {
        List<ProgramAttributeServiceTypeMappingRequest> mappingList = programAttributeServiceTypeMappingRequestRepository.findEffecteds(res.getId());
        Map<EAttributeType, List<ProgramAttributeServiceTypeMappingRequest>> mappingsPerType = mappingList.stream()
                .filter(e -> e.getActionType() != ECommonActionType.Delete)
                .collect(Collectors.groupingBy(ProgramAttributeServiceTypeMappingRequest::getAttributeType));
        
        if (mappingsPerType.containsKey(EAttributeType.SYSTEM)) {
            List<ProgramAttributeServiceTypeMappingRequest> mappings = mappingsPerType.get(EAttributeType.SYSTEM);
            List<String> attributeCodes = mappings.stream()
                    .map(e -> e.getAttributeCode())
                    .collect(Collectors.toList());
            
            List<SystemAttribute> attributes = systemAttributeRepository.findByAttributeIn(attributeCodes);
            List<ProgramAttributeServiceTypeMappingRequestRes> mappingsRes = attributes.stream()
                    .map(e -> ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getAttribute())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build())
                    .collect(Collectors.toList());
            
            res.setSystemAttributeMappings(mappingsRes);
        }
        
        if (mappingsPerType.containsKey(EAttributeType.PROGRAM_TRANSACTION)) {
            List<ProgramAttributeServiceTypeMappingRequest> mappings = mappingsPerType.get(EAttributeType.PROGRAM_TRANSACTION);
            List<String> attributeCodes = mappings.stream()
                    .map(e -> e.getAttributeCode())
                    .collect(Collectors.toList());
            
            List<ProgramTransactionAttribute> attributes = programTransactionAttributeRepository.findByProgramIdAndAttributeIn(res.getProgramId(), attributeCodes);
            List<ProgramAttributeServiceTypeMappingRequestRes> mappingsRes = attributes.stream()
                    .map(e -> ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getAttribute())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build())
                    .collect(Collectors.toList());
            
            res.setProgramAttributeMappings(mappingsRes);
        }
        
        if (mappingsPerType.containsKey(EAttributeType.MEMBER)) {
            List<ProgramAttributeServiceTypeMappingRequest> mappings = mappingsPerType.get(EAttributeType.MEMBER);
            List<String> attributeCodes = mappings.stream()
                    .map(e -> e.getAttributeCode())
                    .collect(Collectors.toList());
            
            List<ProgramAttribute> attributes = programAttributeRepository.findByProgramIdAndCodeIn(res.getProgramId(), attributeCodes);
            List<ProgramAttributeServiceTypeMappingRequestRes> mappingsRes = attributes.stream()
                    .map(e -> ProgramAttributeServiceTypeMappingRequestRes.builder()
                            .attributeCode(e.getCode())
                            .attributeName(e.getName())
                            .attributeDescription(e.getDescription())
                            .build())
                    .collect(Collectors.toList());
            
            res.setMemberAttributeMappings(mappingsRes);
        }
    }
    
}
