package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import lombok.Builder;
import lombok.Getter;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@Getter
public class GiftCardProductionRequestRes {
    private String description;
    private ECommonStatus requestStatus;
    private EApprovalStatus approvalStatus;
    private EGiftCardStatus giftCardStatus;
    private Long id;
    private Integer requestId;
    private ShortEntityRes store;
    private ShortEntityRes program;
    private ShortEntityRes gcBin;
    private ShortEntityRes gcType;
    private CardPolicyResponse gcPolicy;
    private Integer noOfCard;
    private String gcSuffix;
    private EGiftCardIndicator generationInd;
    private Long generationDate;
    private Integer reviewId;
    private String createdBy;
    private String updateBy;
    private String approvedBy;
    private Long createdAt;
    private Long updatedAt;
    private Long approvedAt;
    private Long batchNo;
    private Integer version;
    private EGiftCardBatchType batchType;
    private EBoolean generateQr;
    private String reason;
    private ShortEntityRes chain;
    private ShortEntityRes corporation;


    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Builder
    @Getter
    public static class CardPolicyResponse {
        private String name;
        private String code;
        private Integer id;
        private String qrUrl;
    }
}
