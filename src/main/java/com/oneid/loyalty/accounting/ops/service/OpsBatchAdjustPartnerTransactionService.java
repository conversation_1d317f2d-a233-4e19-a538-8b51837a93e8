package com.oneid.loyalty.accounting.ops.service;

import java.net.URL;
import java.time.LocalDate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.BatchAdjustPartnerTransactionReq;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionItemRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionSettingRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionStatisticRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;
import com.oneid.oneloyalty.common.constant.EProgressStatus;

public interface OpsBatchAdjustPartnerTransactionService {
    BatchAdjustPartnerTransactionSettingRes getTcbPartnerSetting();
    
    ResourceDTO exportBatchFileTemplate(Integer corporationId);
    
    Integer requestCreatingBatchRequest(BatchAdjustPartnerTransactionReq req, MultipartFile multipartFile);
    
    URL getDownloadedBatchFileLink(Integer requestId);
    
    Page<BatchAdjustPartnerTransactionRes> getAvailableRequests(
            String batchName,
            Integer batchNo,
            Integer corporationId,
            Integer businessId, 
            Integer programId, 
            EBatchProcessStatusType batchProcessStatusType,
            EApprovalStatus approvalStatus,
            LocalDate fromDate,
            LocalDate toDate,
            Pageable pageable);
    
    BatchAdjustPartnerTransactionRes getAvailableRequestById(Integer requestId);
    
    Page<BatchAdjustPartnerTransactionRes> getInReviewRequests(
            EApprovalStatus approvalStatus, 
            LocalDate fromDate, 
            LocalDate toDate, 
            Pageable pageable);
    
    BatchAdjustPartnerTransactionRes getInReviewRequestById(Integer reviewId);
    
    Page<BatchAdjustPartnerTransactionItemRes> getTransactionsById(Integer requestId, EProgressStatus progressStatus, Pageable pageable);
    
    ResourceDTO exportAllTransactionsByRequestId(Integer requestId);
    
    BatchAdjustPartnerTransactionStatisticRes getTransactionStatisticById(Integer requestId);
}
