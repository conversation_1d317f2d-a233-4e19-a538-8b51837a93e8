package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Date;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MakerCheckerInternalDataDetailRes implements Serializable {
    private static final long serialVersionUID = 1602911822581458949L;

    private Long id;

    private String requestName;

    private String requestCode;

    private String requestType;

    private String status;

    private Integer version;

    private String madeDate;

    private String checkedDate;

    private String madeByUserId;

    private String madeByUserName;

    private String madeByUserEmail;

    private String checkedByUserId;

    private String checkedByUserName;

    private String checkedByUserEmail;

    private Object payload;

    private String comment;

    private Integer restoredFromVersion;

    private Integer restoredFromId;

    public Date getMadeDateToDate() {
        Date res = null;
        if (this.madeDate != null) {
            res = Date.from(ZonedDateTime.parse(this.madeDate).toInstant());
        }

        return res;
    }

    public Long getMadeDateToTimestamp() {
        Long res = null;
        if (this.madeDate != null) {
            Date dateVal = Date.from(ZonedDateTime.parse(this.madeDate).toInstant());
            res = DateTimes.toEpochSecond(dateVal);
        }

        return res;
    }

    public Date getCheckedDateToDate() {
        Date res = null;
        if (this.checkedDate != null) {
            res = Date.from(ZonedDateTime.parse(this.checkedDate).toInstant());
        }

        return res;
    }

    public Long getCheckedDateToTimestamp() {
        Long res = null;
        if (this.checkedDate != null) {
            Date dateVal = Date.from(ZonedDateTime.parse(this.checkedDate).toInstant());
            res = DateTimes.toEpochSecond(dateVal);
        }

        return res;
    }
}