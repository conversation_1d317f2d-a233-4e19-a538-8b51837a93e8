package com.oneid.loyalty.accounting.ops.model.res;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class ShortEntityRes implements Serializable {

    private static final long serialVersionUID = -3009193037263309388L;

    public ShortEntityRes(Integer id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    private Integer id;

    private String name;

    private String code;
}