package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.res.DistrictDTO;
import com.oneid.loyalty.accounting.ops.service.OpsDistrictService;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.entity.District;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "v1/districts")
@Validated
public class DistrictController extends BaseController {

    @Autowired
    private OpsDistrictService opsDistrictService;

    @GetMapping
    public ResponseEntity<?> getDistrict(@RequestParam(value = "province_id", required = false) Integer provinceId,
                                         @RequestParam(value = "country_code", required = false) String countryCode,
                                         @RequestParam(value = "province_code", required = false) String provinceCode) {
        List<District> districts;

        if (provinceId != null) {
            districts = this.opsDistrictService.getByProvince(provinceId);
        } else {
            districts = this.opsDistrictService.getByProvince(countryCode, provinceCode);
        }

        return success(districts.stream().map(DistrictDTO::valueOf).collect(Collectors.toList()));
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getOne(@PathVariable("id") Integer id) {
        return success(this.opsDistrictService.getOne(id));
    }
}
