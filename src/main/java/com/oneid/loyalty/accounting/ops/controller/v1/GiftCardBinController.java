package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardBinRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardBinService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardBinService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping(value = "v1/giftcardbins")
@Validated
public class GiftCardBinController extends BaseController {
    @Autowired
    OpsGiftCardBinService opsGiftCardBinService;

    @GetMapping("")
    @Authorize(role = AccessRole.GIFT_CARD_BIN, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(@RequestParam(name = "business_id", required = false) Integer businessId,
                                              @RequestParam(name = "program_id", required = false) Integer programId,
                                              @RequestParam(name = "bin_code", required = false) String binCode,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<GiftCardBinRes> page = opsGiftCardBinService.filter(businessId, programId, binCode, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.GIFT_CARD_BIN, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getGiftCardBin(@PathVariable(value = "id") Integer id) {
        GiftCardBinRes result = opsGiftCardBinService.get(id);
        return success(result);
    }

    @PostMapping
    @Authorize(role = AccessRole.GIFT_CARD_BIN, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createGiftCardBin(@RequestBody @Valid GiftCardBinCreateReq giftCardBinCreateReq) {
        opsGiftCardBinService.add(giftCardBinCreateReq);
        return success(null);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.GIFT_CARD_BIN, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateGiftCardBin(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid GiftCardBinUpdateReq GiftCardBinUpdateReq) {
        opsGiftCardBinService.update(id, GiftCardBinUpdateReq);
        return success(null);
    }
}
