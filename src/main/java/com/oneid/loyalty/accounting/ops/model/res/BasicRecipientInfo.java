package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ESendVia;
import com.oneid.oneloyalty.common.entity.Recipient;
import lombok.*;

import java.util.Objects;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Data
public class BasicRecipientInfo {
    private Integer id;
    private String code;
    private String name;
    private String sftpIpAddress;
    private String sftpFolderPath;
    private String sftpUsername;
    private String email;
    private EBoolean archiveFolder;
    private String archivePhoneReceive;


    private ESendVia sendVia;
    private EBoolean existSftpPassword;
    private EBoolean encryptPGP;
    private EBoolean existPgpPublicKey;

    public BasicRecipientInfo(Recipient r) {
        this.id = r.getId();
        this.code = r.getCode();
        this.name = r.getName();
        this.sftpIpAddress = r.getSftpIpAddress();
        this.sftpFolderPath = r.getSftpFolderPath();
        this.sftpUsername = r.getSftpUsername();
        this.email = r.getEmail();
        this.archiveFolder = r.getSetFolderPassword();
        this.archivePhoneReceive = r.getReceivePasswordPhoneNo();

        this.encryptPGP = r.getEncryptPGP();

        this.setSendVia(r.getSendVia());

        this.existSftpPassword = Objects.nonNull(r.getSftpPassword()) ? EBoolean.YES : EBoolean.NO;
        this.existPgpPublicKey = Objects.nonNull(r.getPgpPublicKey()) ? EBoolean.YES : EBoolean.NO;

    }


}
