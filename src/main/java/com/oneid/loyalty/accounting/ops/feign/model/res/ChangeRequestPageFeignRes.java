package com.oneid.loyalty.accounting.ops.feign.model.res;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ChangeRequestPageFeignRes {
    private Integer page;
    private Integer pageSize;
    private Integer totalRecordCount;
    private List<ChangeRecordFeginRes> records;
    
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ChangeRecordFeginRes {
        @JsonProperty("id")
        private Integer changeRequestId;
        private Long makerId;
        private String makerName;
        private String module;
        private String actionType;
        private String objectId;
        private Object payload;
    }
}
