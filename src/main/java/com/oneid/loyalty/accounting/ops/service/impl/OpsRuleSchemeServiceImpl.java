package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.SchemeRuleMapper;
import com.oneid.loyalty.accounting.ops.model.req.RuleRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionSchemeService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleSchemeService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.SchemeRuleService;
import com.oneid.oneloyalty.common.service.SchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsRuleSchemeServiceImpl implements OpsRuleSchemeService {

    @Autowired
    SchemeRuleService schemeRuleService;

    @Autowired
    SchemeService schemeService;

    @Autowired
    OpsConditionSchemeService opsConditionService;

    @Transactional
    public RuleRecordRes createRule(RuleRecordReq req) {

        RuleRecordRes result = new RuleRecordRes();
        SchemeRule schemeRule = SchemeRuleMapper.toSchemeRule(req);
        schemeRule.setStatus(ECommonStatus.ACTIVE); // default

        final SchemeRule finalSchemeRule = schemeRuleService.create(schemeRule);

        result.setSchemeId(finalSchemeRule.getSchemeId());
        result.setRuleId(finalSchemeRule.getId());
        result.setStatus(finalSchemeRule.getStatus());
        result.setSeqNo(finalSchemeRule.getSeqNo());
        result.setConditionLogic(finalSchemeRule.getConditionLogic());
        result.setRuleName(req.getName());
        result.setRuleDescription(req.getDescription());
        result.setListCondition(opsConditionService.createAllInOneSchemeRule(finalSchemeRule, req.getListCondition()));

        return result;
    }

    @Override
    @Transactional
    public List<RuleRecordRes> createRules(final Scheme scheme, List<RuleRecordReq> ruleList) {
        return Optional.ofNullable(ruleList).orElse(Collections.emptyList())
                .stream().map(r -> {
                    r.setSchemeId(scheme.getId());
                    return createRule(r);
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<RuleRecordRes> updateRules(final Scheme scheme, List<RuleRecordReq> ruleListUpdate) {
        if (ruleListUpdate == null) {
            return null;
        }
        List<RuleRecordRes> result;

        final List<SchemeRule> rulesOld = schemeRuleService.getAllBySchemeId(scheme.getId());
        final Map<Integer, SchemeRule> mapIdToRuleOld = rulesOld.stream().collect(Collectors.toMap(
                SchemeRule::getId,
                r -> r
        ));

        List<RuleRecordReq> rulesWillCreateNew = ruleListUpdate.stream().filter(r -> r.getRuleId() == null)
                .peek(f -> f.setSchemeId(scheme.getId()))
                .collect(Collectors.toList());

        long checker = ruleListUpdate.stream().filter(r -> r.getRuleId() != null &&
                !mapIdToRuleOld.containsKey(r.getRuleId())).count();
        if (checker > 0) {
            throw new BusinessException(ErrorCode.SCHEME_RULE_NOT_FOUND, "Scheme rule not found", ruleListUpdate);
        }

        rulesWillCreateNew.forEach(
                r -> {
                    if (Optional.ofNullable(r.getListCondition()).orElse(Collections.emptyList())
                            .stream().anyMatch(c -> c.getConditionId() != null)) {
                        throw new BusinessException(ErrorCode.CONDITION_NOT_IN_RULE,
                                "Condition not in rul", ruleListUpdate);
                    }
                }
        );

        List<RuleRecordReq> rulesWillUpdate = ruleListUpdate.stream().filter(r -> r.getRuleId() != null &&
                        mapIdToRuleOld.containsKey(r.getRuleId()))
                .peek(f -> f.setSchemeId(scheme.getId()))
                .collect(Collectors.toList());

        final Map<Integer, RuleRecordReq> mapRuleIdToUpdate = rulesWillUpdate.stream()
                .collect(Collectors.toMap(
                        RuleRecordReq::getRuleId,
                        u -> u
                ));

        /* Remove old */
        rulesOld.forEach(r -> {
                    if (!mapRuleIdToUpdate.containsKey(r.getId())) {
                        remove(r);
                    }
                }
        );

        result = new ArrayList<>(createRules(scheme, rulesWillCreateNew));

        rulesWillUpdate.forEach(ruleRecordReq -> {
            SchemeRule update = mapIdToRuleOld.get(ruleRecordReq.getRuleId());
            update.setStatus(ECommonStatus.ACTIVE);
            update.setSeqNo(ruleRecordReq.getSeqNo());
            update.setConditionLogic(ruleRecordReq.getConditionLogic());
            update.setDescription(ruleRecordReq.getDescription());
            update.setName(ruleRecordReq.getName());
            update = schemeRuleService.save(update);
            RuleRecordRes updated = RuleRecordRes.valueOf(update);
            updated.setListCondition(opsConditionService.updateAllInOneSchemeRule(update,
                    ruleRecordReq.getListCondition()));
            result.add(updated);
        });

        return result;
    }

    private void remove(SchemeRule r) {
        r.setStatus(ECommonStatus.INACTIVE);
        schemeRuleService.save(r);
    }
}