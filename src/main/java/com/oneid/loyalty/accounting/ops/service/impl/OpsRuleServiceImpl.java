package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.req.RuleConditionReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleConditionRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.DestinationMemberStatus;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.RuleConditionRepository;
import com.oneid.oneloyalty.common.repository.RuleRepository;
import com.oneid.oneloyalty.common.service.MemberStatusService;
import com.oneid.oneloyalty.common.service.RuleService;
import com.oneid.oneloyalty.common.service.SchemeRuleService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.util.DateTimes;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpsRuleServiceImpl implements OpsRuleService {

    @Autowired
    SchemeRuleService schemeRuleService;

    @Autowired
    SchemeService schemeService;

    @Autowired
    OpsConditionService opsConditionService;

    @Autowired
    RuleService ruleService;

    @Autowired
    RuleRepository ruleRepository;

    @Autowired
    RuleConditionRepository ruleConditionRepository;

    @Autowired
    private AttributeValueFactory attributeValueFactory;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private MemberStatusService memberStatusService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public void verifyListRule(final Scheme scheme, VerifyCreateListSchemeRuleReq req) {
        req.getRuleList().forEach(
                ruleReq -> {
                    opsConditionService.verifyConditions(scheme, ruleReq.getListCondition());
                }
        );
    }

    @Override
    public List<RuleRecordRes> findAllBySchemeId(Integer schemeId) {
        List<SchemeRule> schemeRules = Optional.ofNullable(schemeRuleService.getAllBySchemeId(schemeId))
                .orElse(Collections.emptyList());

        List<RuleRecordRes> resultSet = new ArrayList<>();

        schemeRules.forEach(schemeRule -> {
            RuleRecordRes ruleRes = RuleRecordRes.valueOf(schemeRule);
            ruleRes.setListCondition(opsConditionService.getAllByRule(schemeRule));
            resultSet.add(ruleRes);
        });
        return resultSet;
    }

    @Override
    public List<RuleRes> getRule(String serviceCode, Integer programId, EServiceType serviceType) {
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, ConditionAttributeDto> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> conditionAttributeDto
        ));


        //find all rule reply on counter code which is mapped by service_code into table rule
        List<Rule> lstRule = ruleService.findByProgramIdAndServiceTypeAndServiceCode(programId, serviceType, serviceCode);

        return lstRule
                .stream()
                .filter(ele -> ele.getStatus().equals(ECommonStatus.ACTIVE))
                .map(rule -> {
                    List<RuleConditionRes> conditions = ruleConditionRepository.findByRuleId(rule.getId())
                            .stream()
                            .filter(ele -> ele.getStatus().equals(ECommonStatus.ACTIVE))
                            .map(condition -> {
                                EAttributeOperator operator = EAttributeOperator.lookup(condition.getOperator());

                                ConditionAttributeDto conditionAttributeDto = map.get(condition.getAttribute());

                                AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(conditionAttributeDto);

                                Object value = attributeValueStrategy.getReadValue(condition.getAttribute(), operator, condition.getValue(), programId);

                                return RuleConditionRes.builder()
                                        .id(condition.getId())
                                        .attribute(condition.getAttribute())
                                        .operator(operator)
                                        .dataTypeDisplay(conditionAttributeDto.getDataTypeDisplay())
                                        .value(value)
                                        .build();
                            }).collect(Collectors.toList());

                    return RuleRes.builder()
                            .id(rule.getId())
                            .conditionLogic(rule.getRuleLogic())
                            .conditions(conditions)
                            .name(rule.getName())
                            .startDate(rule.getStartDate())
                            .endDate(rule.getEndDate())
                            .description(rule.getDescription())
                            .code(rule.getCode())
                            .build();
                }).collect(Collectors.toList());
    }

    @Override
    public List<RuleRes> getRuleInReview(Integer programId, List<RuleReq> ruleReqs, String serviceCode) {
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);
        final Map<String, ConditionAttributeDto> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> conditionAttributeDto
        ));
        return ruleReqs
                .stream()
                .filter(ele -> !ele.isArchive())
                .map(rule -> {
                    List<RuleConditionRes> conditions = rule.getConditions()
                            .stream()
                            .filter(ele -> Objects.isNull(ele.getArchive()) || !ele.getArchive())
                            .map(condition -> {
                                EAttributeOperator operator = condition.getOperator();
                                ConditionAttributeDto conditionAttributeDto = map.get(condition.getAttribute());
                                return RuleConditionRes.builder()
                                        .id(condition.getId())
                                        .attribute(condition.getAttribute())
                                        .operator(operator)
                                        .dataTypeDisplay(conditionAttributeDto.getDataTypeDisplay())
                                        .value(condition.getValue())
                                        .build();
                            }).collect(Collectors.toList());
                    return RuleRes.builder()
                            .id(rule.getId())
                            .conditionLogic(rule.getConditionLogic())
                            .conditions(conditions)
                            .name(rule.getName())
                            .startDate(rule.getStartDate())
                            .endDate(rule.getEndDate())
                            .description(rule.getDescription())
                            .code(serviceCode) // TODO define code
                            .build();
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<ResetRuleReq> createRule(Counter counter, List<RuleReq> ruleReqs, MakerCheckerInternalDataDetailRes data, String approvedBy) {
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(counter.getProgramId());
        final Map<String, AttributeValueStrategy<?>> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
        ));

        final Map<String, ConditionAttributeDto> attributesMap =
                availableProgramAttributes.stream().collect(Collectors.toMap(ConditionAttributeDto::getAttribute, c -> c));

        ruleReqs.forEach(ele -> {
            if (CollectionUtils.isEmpty(ele.getConditions())) {
                throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);
            }
            String createdBy;
            String updatedBy;
            updatedBy = createdBy = data.getMadeByUserName();
            Rule rule = new Rule();
            rule.setId(ele.getId());
            rule.setProgramId(counter.getProgramId());
            rule.setCode(String.format("%s_%s", counter.getCode(), UUID.randomUUID()));
            rule.setName(ele.getName());
            rule.setRuleLogic(ele.getConditionLogic());
            rule.setServiceType(EServiceType.COUNTER);
            rule.setServiceCode(counter.getCode());
            rule.setStatus(ECommonStatus.ACTIVE);
            rule.setStartDate(ele.getStartDate());
            rule.setEndDate(ele.getEndDate());
            opsReqPendingValidator.updateInfoChecker(rule, data.getMadeDate(), createdBy, updatedBy, approvedBy);
            Rule ruleSaved = ruleRepository.save(rule);
                    ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(ruleSaved);

            List<RuleCondition> ruleConditions = ele.getConditions()
                    .stream()
                    .map(condition -> {
                        if (!map.containsKey(condition.getAttribute())) {
                            throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});
                        }
                        if (condition.getValue() == null) {
                            throw new BusinessException(OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(), null, null);
                        }
                        String value = map.get(condition.getAttribute())
                                .getWriteValue(
                                        condition.getAttribute(),
                                        condition.getOperator(),
                                        condition.getValue(),
                                        counter.getProgramId()
                                );
                        ConditionAttributeDto attribute = attributesMap.get(condition.getAttribute());
                        RuleCondition ruleCondition = new RuleCondition();
                        ruleCondition.setId(condition.getId());
                        ruleCondition.setRuleId(ruleSaved.getId());
                        ruleCondition.setAttribute(condition.getAttribute());
                        ruleCondition.setOperator(condition.getOperator().getExpression());
                        ruleCondition.setValue(value);
                        ruleCondition.setDataType(attribute.getDataType());
                        ruleCondition.setStatus(ECommonStatus.ACTIVE);
                        opsReqPendingValidator.updateInfoChecker(ruleCondition, data.getMadeDate(), createdBy, updatedBy, approvedBy);
                        return ruleCondition;
                    })
                    .collect(Collectors.toList());

                    List<RuleCondition> ruleConditionList = ruleConditionRepository.saveAll(ruleConditions);
                    resetRuleReq.setConditions(ruleConditionList.stream().map(ResetRuleReq::buildConditionReq).collect(Collectors.toList()));
                    resetRuleReqs.add(resetRuleReq);
                }
        );
        return resetRuleReqs;
    }

    @Override
    @Transactional
    public List<ResetRuleReq> editRule(Counter counter, List<RuleReq> ruleReqs, MakerCheckerInternalDataDetailRes data, String approvedBy) {
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(counter.getProgramId());

        final Map<String, AttributeValueStrategy<?>> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
        ));

        final Map<String, ConditionAttributeDto> attributesMap =
                availableProgramAttributes.stream().collect(Collectors.toMap(ConditionAttributeDto::getAttribute, c -> c));

        Map<Integer, Rule> availableRules = ruleRepository.findByProgramIdAndServiceTypeAndServiceCode(counter.getProgramId(), EServiceType.COUNTER, counter.getCode())
                .stream()
                .collect(Collectors.toMap(Rule::getId, Function.identity()));

        String createdBy;
        String updatedBy;
        updatedBy = createdBy = data.getMadeByUserName();

        ruleReqs.forEach(ruleReq -> {
            Rule newRule = new Rule();
            newRule.setCode(String.format("%s_%s", counter.getCode(), UUID.randomUUID()));
            newRule.setProgramId(counter.getProgramId());
            newRule.setRuleLogic(ruleReq.getConditionLogic());
            newRule.setServiceType(EServiceType.COUNTER);
            newRule.setServiceCode(counter.getCode());
            newRule.setStatus(ECommonStatus.ACTIVE);

            Map<Integer, RuleCondition> conditionRequests = new HashMap<>();

            if (ruleReq.getId() != null) {
                Rule availableRule = availableRules.get(ruleReq.getId());

                if (availableRule == null) {
                    throw new BusinessException(ErrorCode.RULE_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});
                }

                newRule.setId(availableRule.getId());
                newRule.setCreatedAt(availableRule.getCreatedAt());
                newRule.setCreatedBy(availableRule.getCreatedBy());

                if (ruleReq.isArchive()) {
                    newRule.setStatus(ECommonStatus.INACTIVE);
                    List<RuleCondition> byRuleId = ruleConditionRepository.findByRuleId(ruleReq.getId());
                    for (RuleCondition ruleCondition : byRuleId) {
                        ruleCondition.setStatus(ECommonStatus.INACTIVE);
                        opsReqPendingValidator.updateInfoChecker(ruleCondition, data.getMadeDate(), null, updatedBy, approvedBy);
                        ruleConditionRepository.save(ruleCondition);
                    }
                } else {
                    newRule.setName(ruleReq.getName());
                    newRule.setStartDate(ruleReq.getStartDate());
                    newRule.setEndDate(ruleReq.getEndDate());
                }

                conditionRequests.putAll(ruleConditionRepository.findByRuleId(ruleReq.getId())
                        .stream()
                        .collect(Collectors.toMap(RuleCondition::getId, Function.identity())));
            } else {
                newRule.setName(ruleReq.getName());
                newRule.setStartDate(ruleReq.getStartDate());
                newRule.setEndDate(ruleReq.getEndDate());
            }

            if (!ruleReq.isArchive() && CollectionUtils.isEmpty(ruleReq.getConditions())) {
                throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);
            }

            opsReqPendingValidator.updateInfoChecker(newRule, data.getMadeDate(), createdBy, updatedBy, approvedBy);
            Rule rule = ruleRepository.save(newRule);
            // Build reset rule
            ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(rule);

            if (!ruleReq.isArchive()) {
                List<RuleCondition> ruleConditions = ruleReq.getConditions()
                        .stream()
                        .map(condition -> {
                            if (!map.containsKey(condition.getAttribute())) {
                                throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});
                            }

                            RuleCondition newCondition = new RuleCondition();

                            String value = map.get(condition.getAttribute()).getWriteValue(
                                    condition.getAttribute(),
                                    condition.getOperator(),
                                    condition.getValue(),
                                    counter.getProgramId()
                            );
                            ConditionAttributeDto attribute = attributesMap.get(condition.getAttribute());
                            newCondition.setRuleId(newRule.getId());
                            newCondition.setAttribute(condition.getAttribute());
                            newCondition.setOperator(condition.getOperator().getExpression());
                            newCondition.setValue(value);
                            newCondition.setDataType(attribute.getDataType());
                            newCondition.setStatus(ECommonStatus.ACTIVE);

                            if (condition.getId() != null) {
                                RuleCondition availableCondition = conditionRequests.get(condition.getId());

                                if (availableCondition == null) {
                                    throw new BusinessException(ErrorCode.RULE_CONDITION_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});
                                }

                                newCondition.setId(availableCondition.getId());
                                newCondition.setCreatedAt(availableCondition.getCreatedAt());
                                newCondition.setCreatedBy(availableCondition.getCreatedBy());

                                if (condition.getArchive()) {
                                    newCondition.setStatus(ECommonStatus.INACTIVE);
                                    newCondition.setValue(availableCondition.getValue());
                                }
                            }

                            opsReqPendingValidator.updateInfoChecker(newCondition, data.getMadeDate(), createdBy, updatedBy, approvedBy);

                            return newCondition;
                        })
                        .collect(Collectors.toList());

                List<RuleCondition> ruleConditionList = ruleConditionRepository.saveAll(ruleConditions);
                resetRuleReq.setConditions(
                        ruleConditionList.stream()
                                .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                                .map(ResetRuleReq::buildConditionReq)
                                .collect(Collectors.toList())
                );
                resetRuleReqs.add(resetRuleReq);
            }
        });
        return resetRuleReqs;
    }

    @Override
    public void validateRules(Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs) {
        Map<String, ConditionAttributeDto> attributeMap = getAttributeMap(programId);

        // Validate rules request
        for (RuleReq ruleReq : ruleReqs) {
            validateRule(programId, serviceType, attributeMap, ruleReq);
        }
    }

    private Map<String, ConditionAttributeDto> getAttributeMap(Integer programId) {
        Collection<ConditionAttributeDto> attributes =
                opsConditionService.conditionAttributeDtos(programId);
        return attributes.stream()
                .collect(
                        Collectors.toMap(
                                ConditionAttributeDto::getAttribute,
                                conditionAttributeDto -> conditionAttributeDto
                        ));
    }

    private void validateRule(
            Integer programId,
            EServiceType serviceType,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleReq ruleReq
    ) {
        if (!ruleReq.isArchive() && CollectionUtils.isEmpty(ruleReq.getConditions())) {
            throw new BusinessException(
                    OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(),
                    "Rule condition must not empty",
                    null
            );
        }

        // Validate rule code in case create new
        if (ruleReq.getCode() != null && ruleReq.getId() == null) {
            Rule rule = ruleService.findByProgramIdAndCodeAndServiceType(
                    programId,
                    serviceType.getValue(),
                    ruleReq.getCode()
            );
            if (rule != null) {
                throw new BusinessException(
                        ErrorCode.DUPLICATE_RULE_CODE,
                        "Rule code is duplicate",
                        null,
                        new Object[]{ruleReq.getCode()}
                );
            }
        }

        // Validate rule condition
        ruleReq.getConditions().forEach(e -> validateRuleCondition(programId, attributeMap, e));
    }

    private void validateRuleCondition(
            Integer programId,
            Map<String, ConditionAttributeDto> attributeMap,
            RuleConditionReq ruleConditionReq
    ) {
        if (!ruleConditionReq.getArchive()) {
            ConditionAttributeDto conditionAttribute = attributeMap.get(ruleConditionReq.getAttribute());
            if (conditionAttribute == null) {
                throw new BusinessException(
                        ErrorCode.RULE_ATTRIBUTE_NOT_FOUND,
                        "Rule condition attribute is not found",
                        null,
                        new Object[]{ruleConditionReq.getAttribute()}
                );
            }
            ruleConditionReq.setDataTypeDisplay(conditionAttribute.getDataTypeDisplay());

            // Validate rule condition value
            AttributeValueStrategy<?> valueStrategy = attributeValueFactory.lookup(conditionAttribute);
            String conditionValue = valueStrategy.getWriteValue(
                    ruleConditionReq.getAttribute(),
                    ruleConditionReq.getOperator(),
                    ruleConditionReq.getValue(),
                    programId
            );
            if (conditionValue == null) {
                throw new BusinessException(
                        OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(),
                        "Rule condition value must not empty",
                        null
                );
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<ResetRuleReq> createRule(DestinationMemberStatus destinationMemberStatus, List<RuleReq> ruleReqs, String approvedBy, Integer programId, String madeDate) {
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, AttributeValueStrategy<?>> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
        ));

        final Map<String, ConditionAttributeDto> attributesMap =
                availableProgramAttributes.stream().collect(Collectors.toMap(ConditionAttributeDto::getAttribute, c -> c));

        ruleReqs.forEach(ruleReq -> {
            if (CollectionUtils.isEmpty(ruleReq.getConditions())) {
                throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);
            }
            Rule rule = new Rule();
            rule.setProgramId(programId);
            rule.setCode(String.valueOf(UUID.randomUUID()));
            rule.setName(ruleReq.getName());
            rule.setDescription(ruleReq.getDescription());
            rule.setRuleLogic(ruleReq.getConditionLogic());
            rule.setServiceType(EServiceType.MEMBER_STATUS);
            rule.setServiceCode(destinationMemberStatus.getCode());
            rule.setStatus(ECommonStatus.ACTIVE);
            rule.setStartDate(DateTimes.currentTime());
            rule.setEndDate(DateTimes.addYearOfCurrentDate(9999));
            opsReqPendingValidator.updateInfoChecker(rule, madeDate, destinationMemberStatus.getCreatedBy(), destinationMemberStatus.getUpdatedBy(), approvedBy);
            Rule ruleSaved = ruleRepository.save(rule);
                    ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(ruleSaved);

            List<RuleCondition> ruleConditions = ruleReq.getConditions()
                    .stream()
                    .map(condition -> {
                        if (!map.containsKey(condition.getAttribute())) {
                            throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});
                        }
                        if (condition.getValue() == null) {
                            throw new BusinessException(OpsErrorCode.RULE_CONDITION_VALUE_IS_REQUIRED.getValue(), null, null);
                        }
                        String value = map.get(condition.getAttribute())
                                .getWriteValue(
                                        condition.getAttribute(),
                                        condition.getOperator(),
                                        condition.getValue(),
                                        programId
                                );
                        ConditionAttributeDto attribute = attributesMap.get(condition.getAttribute());
                        RuleCondition ruleCondition = new RuleCondition();
                        ruleCondition.setId(condition.getId());
                        ruleCondition.setRuleId(ruleSaved.getId());
                        ruleCondition.setAttribute(condition.getAttribute());
                        ruleCondition.setOperator(condition.getOperator().getExpression());
                        ruleCondition.setValue(value);
                        ruleCondition.setDataType(attribute.getDataType());
                        ruleCondition.setStatus(ECommonStatus.ACTIVE);
                        opsReqPendingValidator.updateInfoChecker(ruleCondition, madeDate, destinationMemberStatus.getCreatedBy(), destinationMemberStatus.getUpdatedBy(), approvedBy);
                        return ruleCondition;
                    })
                    .collect(Collectors.toList());

                    List<RuleCondition> ruleConditionList = ruleConditionRepository.saveAll(ruleConditions);
                    resetRuleReq.setConditions(ruleConditionList.stream().map(ResetRuleReq::buildConditionReq).collect(Collectors.toList()));
                    resetRuleReqs.add(resetRuleReq);
                }
        );
        return resetRuleReqs;
    }

    @Override
    @Transactional
    public List<ResetRuleReq> editRule(DestinationMemberStatus destinationMemberStatus, List<RuleReq> ruleReqs, String approvedBy, Integer programId, String madeDate) {
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        Collection<ConditionAttributeDto> availableProgramAttributes = opsConditionService.conditionAttributeDtos(programId);

        final Map<String, AttributeValueStrategy<?>> map = availableProgramAttributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
        ));

        final Map<String, ConditionAttributeDto> attributesMap =
                availableProgramAttributes.stream().collect(Collectors.toMap(ConditionAttributeDto::getAttribute, c -> c));


        Map<Integer, Rule> availableRules = ruleRepository.findByProgramIdAndServiceTypeAndServiceCode(programId, EServiceType.MEMBER_STATUS, destinationMemberStatus.getCode())
                .stream()
                .collect(Collectors.toMap(Rule::getId, Function.identity()));

        String createdBy;
        String updatedBy;
        updatedBy = createdBy = destinationMemberStatus.getUpdatedBy();

        ruleReqs.forEach(ruleReq -> {
                    Rule newRule = new Rule();
                    newRule.setCode(String.valueOf(UUID.randomUUID()));
                    newRule.setProgramId(programId);
                    newRule.setRuleLogic(ruleReq.getConditionLogic());
                    newRule.setServiceType(EServiceType.MEMBER_STATUS);
                    newRule.setServiceCode(destinationMemberStatus.getCode());
                    newRule.setStatus(ECommonStatus.ACTIVE);

                    Map<Integer, RuleCondition> conditionRequests = new HashMap<>();

                    if (ruleReq.getId() != null) {
                        Rule availableRule = availableRules.get(ruleReq.getId());

                        if (availableRule == null) {
                            throw new BusinessException(ErrorCode.RULE_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});
                        }

                        newRule.setId(availableRule.getId());
                        newRule.setCreatedAt(availableRule.getCreatedAt());
                        newRule.setCreatedBy(availableRule.getCreatedBy());

                        if (ruleReq.isArchive()) {
                            newRule.setStatus(ECommonStatus.INACTIVE);
                            List<RuleCondition> byRuleId = ruleConditionRepository.findByRuleId(ruleReq.getId());
                            for (RuleCondition ruleCondition : byRuleId) {
                                ruleCondition.setStatus(ECommonStatus.INACTIVE);
                                opsReqPendingValidator.updateInfoChecker(ruleCondition, madeDate, null, updatedBy, approvedBy);
                                ruleConditionRepository.save(ruleCondition);
                            }

                        } else {
                            newRule.setName(ruleReq.getName());
                            newRule.setDescription(ruleReq.getDescription());
                            newRule.setStartDate(DateTimes.currentDate());
                            newRule.setEndDate(DateTimes.addYearOfCurrentDate(9999));
                        }

                        conditionRequests.putAll(ruleConditionRepository.findByRuleId(ruleReq.getId())
                                .stream()
                                .collect(Collectors.toMap(RuleCondition::getId, Function.identity())));
                    } else {
                        newRule.setName(ruleReq.getName());
                        newRule.setDescription(ruleReq.getDescription());
                        newRule.setStartDate(DateTimes.currentDate());
                        newRule.setEndDate(DateTimes.addYearOfCurrentDate(9999));
                    }

                    if (!ruleReq.isArchive() && CollectionUtils.isEmpty(ruleReq.getConditions())) {
                        throw new BusinessException(OpsErrorCode.RULE_CONDITION_IS_NOT_EMPTY.getValue(), null, null);
                    }
                    opsReqPendingValidator.updateInfoChecker(newRule, madeDate, createdBy, updatedBy, approvedBy);
            Rule rule = ruleRepository.save(newRule);
            // Build reset rule
            ResetRuleReq resetRuleReq = ResetRuleReq.buildRuleReq(rule);

                    if (!ruleReq.isArchive()) {
                        List<RuleCondition> ruleConditions = ruleReq.getConditions()
                                .stream()
                                .map(condition -> {
                                    if (!map.containsKey(condition.getAttribute())) {
                                        throw new BusinessException(ErrorCode.RULE_ATTRIBUTE_NOT_FOUND, null, null, new Object[]{condition.getAttribute()});
                                    }

                                    RuleCondition newCondition = new RuleCondition();

                                    String value = map.get(condition.getAttribute()).getWriteValue(
                                            condition.getAttribute(),
                                            condition.getOperator(),
                                            condition.getValue(),
                                            programId
                                    );
                                    ConditionAttributeDto attribute = attributesMap.get(condition.getAttribute());
                                    newCondition.setRuleId(newRule.getId());
                                    newCondition.setAttribute(condition.getAttribute());
                                    newCondition.setOperator(condition.getOperator().getExpression());
                                    newCondition.setValue(value);
                                    newCondition.setDataType(attribute.getDataType());
                                    newCondition.setStatus(ECommonStatus.ACTIVE);

                                    if (condition.getId() != null) {
                                        RuleCondition availableCondition = conditionRequests.get(condition.getId());

                                        if (availableCondition == null) {
                                            throw new BusinessException(ErrorCode.RULE_CONDITION_NOT_FOUND, null, null, new Object[]{ruleReq.getId()});
                                        }

                                        newCondition.setId(availableCondition.getId());
                                        newCondition.setCreatedAt(availableCondition.getCreatedAt());
                                        newCondition.setCreatedBy(availableCondition.getCreatedBy());

                                        if (condition.getArchive()) {
                                            newCondition.setStatus(ECommonStatus.INACTIVE);
                                            newCondition.setValue(availableCondition.getValue());
                                        }
                                    }

                                    opsReqPendingValidator.updateInfoChecker(newCondition, madeDate, createdBy, updatedBy, approvedBy);
                                    return newCondition;
                                })
                                .collect(Collectors.toList());

                        List<RuleCondition> ruleConditionList = ruleConditionRepository.saveAll(ruleConditions);
                        resetRuleReq.setConditions(
                                ruleConditionList.stream()
                                        .filter(i -> ECommonStatus.ACTIVE.equals(i.getStatus()))
                                        .map(ResetRuleReq::buildConditionReq)
                                        .collect(Collectors.toList())
                        );
                        resetRuleReqs.add(resetRuleReq);
                    }
                }
        );
        return resetRuleReqs;
    }
}