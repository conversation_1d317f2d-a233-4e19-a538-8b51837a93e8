package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Province;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ProvinceDTO implements Serializable {
    private Integer id;

    @JsonProperty("country_id")
    private Integer countryId;

    private String code;
    private String name;

    @JsonProperty("en_name")
    private String enName;

    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    public static ProvinceDTO valueOf(Province entity) {
        ProvinceDTO result = new ProvinceDTO();
        result.setCountryId(entity.getCountryId());
        result.setId(entity.getId());
        result.setCode(entity.getCode());
        result.setName(entity.getName());
        result.setEnName(entity.getEnName());
        result.setDescription(entity.getDescription());
        result.setEnDescription(entity.getEnDescription());
        result.setStatus(entity.getStatus().getValue());

        return result;
    }
}
