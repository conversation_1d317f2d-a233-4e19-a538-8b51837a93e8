package com.oneid.loyalty.accounting.ops.feign.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.ObjectFactory;
import com.oneid.loyalty.accounting.ops.support.feign.interceptor.Oauth2Interceptor;

import feign.RequestInterceptor;
import feign.form.FormEncoder;

public class MakerCheckerFeignConfig {
    @Bean
    public RequestInterceptor basicAuthRequestInterceptor() {
      return new Oauth2Interceptor();
    }
    
    @Autowired
    private ObjectFactory<HttpMessageConverters> messageConverters;
    
    @Bean
    public FormEncoder feignFormEncoder() {
          return new FormEncoder(new SpringEncoder(this.messageConverters));
     }
}