package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ResetSchemeRuleReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "oneloyalty-scheme", url = "${oneloyalty-scheme.base-url}", configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface OneloyaltySchemeFeignClient {
    @RequestMapping(method = RequestMethod.POST, value = "/v1/check-reset")
    APIFeignInternalResponse<?> checkReset(@RequestBody ResetSchemeRuleReq req);
}