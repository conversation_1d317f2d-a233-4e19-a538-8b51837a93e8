package com.oneid.loyalty.accounting.ops.constant;

import java.util.stream.Stream;

import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EAdjustmentType {
    AWARD("AWD"),
    REDEEM("RED");
    
    @JsonValue
    private String value;
    
    public static EAdjustmentType lookup(String value) {
        if (StringUtils.isEmpty(value))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    public static class RequestQueryConverter implements Converter<String, EAdjustmentType> {
        @Override
        public EAdjustmentType convert(String source) {
            return EAdjustmentType.lookup(source);
        }
    }
}
