package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.feign.model.req.CardTransferringPayloadChangeRequestReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateChangeRequestReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.SearchChangeRequestsRes;
import com.oneid.loyalty.accounting.ops.model.req.RejectReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferFilterReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.ListCardTransfer;
import com.oneid.loyalty.accounting.ops.model.res.RequestTransferDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.RequestTransferRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardTransferService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.authentication.OPSAuthenticationToken;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.*;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OpsCardTransferServiceImpl implements OpsCardTransferService {

    @Autowired
    SequenceService sequenceService;

    @Autowired
    BusinessService businessService;

    @Autowired
    ProgramService programService;

    @Autowired
    CorporationService corporationService;

    @Autowired
    ChainService chainService;

    @Autowired
    StoreService storeService;

    @Autowired
    CardTransferringService cardTransferringService;

    @Autowired
    CardTMPService cardTMPService;

    @Autowired
    CardTypeService cardTypeService;

    @Autowired
    CardTransferringHistoryService cardTransferringHistoryService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    MakerCheckerFeignClient makerCheckerServiceClient;

    @Override
    public Integer createRequestCardTransfer(final RequestTransferReq req) {
        verifyData(req);
        CardTransferring cardTransferring = createCardTransferringAndLockCardIsTransfer(req);
        CreateChangeRequestReq<CardTransferringPayloadChangeRequestReq> changeRequestReq = CreateChangeRequestReq.<CardTransferringPayloadChangeRequestReq>builder()
                .actionType(makerCheckerConfigParam.ACTION_TYPE_CREATE)
                .module(makerCheckerConfigParam.CARD_TRANSFER_MODULE)
                .objectId(String.valueOf(cardTransferring.getId()))
                .payload(
                        CardTransferringPayloadChangeRequestReq
                                .builder()
                                .cardTransferId(cardTransferring.getId())
                                .build()
                )
                .build();
        makerCheckerServiceClient.createRequestCardTransferring(changeRequestReq);
        return cardTransferring.getId();
    }

    @Transactional
    protected CardTransferring createCardTransferringAndLockCardIsTransfer(final RequestTransferReq req) {
        CardTransferring cardTransferring = new CardTransferring();
        cardTransferring.setBusinessId(req.getBusinessId());
        cardTransferring.setProgramId(req.getProgramId());
        cardTransferring.setCardQuantity(req.getCardNoList().size());
        cardTransferring.setTransferIndicator(ECardTransferIndicatorStatus.PENDING);
        cardTransferring.setDescription(req.getDescription());
        cardTransferring.setFromStore(req.getFromStoreId());
        cardTransferring.setToStore(req.getToStoreId());
        cardTransferring.setStatus(ECardTransferStatus.PENDING);
        cardTransferring.setCprBatchNo(req.getBatchNo());
        cardTransferring.setCardTransferNo(sequenceService.getNextSeq(
                EConfSeq.CARD_TRANSFER_NO.getSeqId(req.getBusinessId(), req.getProgramId()))
        );

        List<CardTMP> cardTMPs = req.getCardNoList()
                .stream().map(s -> {
                    CardTMP cardTMP = cardTMPService.findPending(s, req.getBusinessId(),
                            req.getProgramId(), req.getFromStoreId());
                    checkCardIsTransfer(cardTMP, req.getBatchNo());
                    return cardTMP;
                }).collect(Collectors.toList());

        cardTransferringService.save(cardTransferring);
        lockCardIsTransfer(cardTMPs, cardTransferring.getCardTransferNo());
        return cardTransferring;
    }

    @Override
    public RequestTransferDetailRes getDetails(Integer id) {
        CardTransferring cardTransferring = getCardTransferringById(id);

        Business business = businessService.find(cardTransferring.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found",
                        LogData.createLogData().append("business_id", cardTransferring.getBusinessId()))
        );
        Program program = programService.find(cardTransferring.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found",
                        LogData.createLogData().append("program_id", cardTransferring.getProgramId()))
        );
        Store storeFrom = storeService.find(cardTransferring.getFromStore());
        Store storeTo = storeService.find(cardTransferring.getToStore());

        if (!storeFrom.getChainId().equals(storeTo.getChainId())) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Conflict data store from and store to has chain not same",
                    LogData.createLogData().append("store_from", storeFrom).append("store_to", storeTo));
        }
        Corporation corporation = corporationService.find(storeTo.getCorporationId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Corporation not found",
                        LogData.createLogData().append("corporation_id", storeTo.getCorporationId()))
        );

        Chain chain = chainService.find(storeTo.getChainId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Chain not found",
                        LogData.createLogData().append("chain_id", storeTo.getChainId()))
        );

        RequestTransferDetailRes res = RequestTransferDetailRes.builder()
                .id(cardTransferring.getId())
                .cardTransferNo(cardTransferring.getCardTransferNo())
                .batchNo(cardTransferring.getCprBatchNo())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .corporation(new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()))
                .chain(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()))
                .storeFrom(new ShortEntityRes(storeFrom.getId(), storeFrom.getName(), storeFrom.getCode()))
                .storeTo(new ShortEntityRes(storeTo.getId(), storeTo.getName(), storeTo.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .cardQuantity(cardTransferring.getCardQuantity())
                .indicator(cardTransferring.getTransferIndicator())
                .status(cardTransferring.getStatus())
                .description(cardTransferring.getDescription())
                .createdAt(cardTransferring.getCreatedAt())
                .updatedAt(cardTransferring.getUpdatedAt())
                .approvedAt(cardTransferring.getApprovedAt())
                .createdBy(cardTransferring.getCreatedBy())
                .updatedBy(cardTransferring.getUpdatedBy())
                .approvedBy(cardTransferring.getApprovedBy())
                .deniedReason(cardTransferring.getDeniedReason())
                .build();

        return res;
    }

    @Override
    public Page<RequestTransferRes> filter(RequestTransferFilterReq req) {
        SpecificationBuilder<CardTransferring> searchBuilder = new SpecificationBuilder<>();
        int totalElement = -1;
        if (req.getIndicator() == ECardTransferIndicatorStatus.PENDING) {
            APIResponse<SearchChangeRequestsRes<CardTransferringPayloadChangeRequestReq>> mcResponse = makerCheckerServiceClient
                    .getChangeRequests(
                            makerCheckerConfigParam.CARD_TRANSFER_MODULE,
                            null,
                            "PENDING",
                            req.getPageRequest().getPageNumber(),
                            req.getPageRequest().getPageSize(),
                            null, null);

            totalElement = mcResponse.getData().getTotalRecordCount();

            List<Integer> cardTransferIds = mcResponse.getData().getRecords().stream()
                    .map(record -> record.getPayload().getCardTransferId())
                    .collect(Collectors.toList());

            searchBuilder.add(new SearchCriteria("id", cardTransferIds, SearchOperation.IN));
        } else {
            searchBuilder.add(new SearchCriteria("transferIndicator", req.getIndicator(), SearchOperation.EQUAL));
            if (req.getCardTransferNo() != null) {
                searchBuilder.add(new SearchCriteria("cardTransferNo", req.getCardTransferNo(), SearchOperation.EQUAL));
            }
            if (req.getStatus() != null) {
                searchBuilder.add(new SearchCriteria("status", req.getStatus(), SearchOperation.EQUAL));
            }
            if (req.getProgramId() != null) {
                searchBuilder.add(new SearchCriteria("programId", req.getProgramId(), SearchOperation.EQUAL));
            }
            if (req.getFromStoreId() != null) {
                searchBuilder.add(new SearchCriteria("fromStore", req.getFromStoreId(), SearchOperation.EQUAL));
            }
            if (req.getToStoreId() != null) {
                searchBuilder.add(new SearchCriteria("toStore", req.getToStoreId(), SearchOperation.EQUAL));
            }
        }

        Page<CardTransferring> cardTransferringPage = cardTransferringService
                .findAll(searchBuilder, req.getPageRequest());

        Set<Integer> storeIds = new HashSet<>();

        for (CardTransferring cardTransferring : cardTransferringPage.getContent()) {
            storeIds.add(cardTransferring.getFromStore());
            storeIds.add(cardTransferring.getToStore());
        }
        Set<Integer> businessIds = cardTransferringPage.getContent().stream()
                .map(CardTransferring::getBusinessId).collect(Collectors.toSet());

        Set<Integer> programIds = cardTransferringPage.getContent().stream()
                .map(CardTransferring::getProgramId).collect(Collectors.toSet());

        SpecificationBuilder<Store> storeSpecificationBuilder = new SpecificationBuilder<>();
        storeSpecificationBuilder.add(new SearchCriteria("id", storeIds, SearchOperation.IN));
        Map<Integer, ShortEntityRes> storeMap = storeService.find(storeSpecificationBuilder).stream()
                .collect(Collectors.toMap(
                        Store::getId,
                        store -> new ShortEntityRes(store.getId(), store.getName(), store.getCode())
                ));

        SpecificationBuilder<Business> businessSpecificationBuilder = new SpecificationBuilder<>();
        businessSpecificationBuilder.add(new SearchCriteria("id", businessIds, SearchOperation.IN));
        storeSpecificationBuilder.add(new SearchCriteria("id", storeIds, SearchOperation.IN));
        Map<Integer, ShortEntityRes> businessMap = businessService.searchAll(businessSpecificationBuilder).stream()
                .collect(Collectors.toMap(
                        Business::getId,
                        business -> new ShortEntityRes(business.getId(), business.getName(), business.getCode())
                ));

        SpecificationBuilder<Program> programSpecificationBuilder = new SpecificationBuilder<>();
        storeSpecificationBuilder.add(new SearchCriteria("id", programIds, SearchOperation.IN));
        Map<Integer, ShortEntityRes> programMap = programService.searchAll(programSpecificationBuilder).stream()
                .collect(Collectors.toMap(
                        Program::getId,
                        program -> new ShortEntityRes(program.getId(), program.getName(), program.getCode())
                ));

        totalElement = totalElement != -1 ? totalElement : (int) cardTransferringPage.getTotalElements();

        return new PageImpl<>(
                cardTransferringPage.getContent().stream().map(
                        cardTransferring -> RequestTransferRes.builder()
                                .id(cardTransferring.getId())
                                .business(businessMap.get(cardTransferring.getBusinessId()))
                                .program(programMap.get(cardTransferring.getProgramId()))
                                .status(cardTransferring.getStatus())
                                .cardTransferNo(cardTransferring.getCardTransferNo())
                                .batchNo(cardTransferring.getCprBatchNo())
                                .cardQuantity(cardTransferring.getCardQuantity())
                                .transferIndicator(cardTransferring.getTransferIndicator())
                                .indicator(cardTransferring.getIndicator())
                                .description(cardTransferring.getDescription())
                                .deniedReason(cardTransferring.getDeniedReason())
                                .toStore(storeMap.get(cardTransferring.getToStore()))
                                .fromStore(storeMap.get(cardTransferring.getFromStore()))
                                .build()
                ).collect(Collectors.toList()),
                cardTransferringPage.getPageable(), totalElement);
    }

    @Override
    public Object reject(Integer id, RejectReq req) {
        Long requestId = getChangeRequestIdByTransferId(id);
        return makerCheckerServiceClient.reject(requestId, RejectReq.builder().reason(req.getReason()).build()
        ).getData();
    }

    @Override
    public Object approve(Integer id) {
        Long requestId = getChangeRequestIdByTransferId(id);
        return makerCheckerServiceClient.approve(requestId).getData();
    }

    @Override
    public ListCardTransfer getListCardByTransferId(Integer cardTransferId, Pageable pageRequest) {
        CardTransferring cardTransferring = getCardTransferringById(cardTransferId);

        Page<CardTransferringHistory> cardTransferPage = getTransferHistoryByCardTransferring(
                cardTransferring, pageRequest
        );

        ShortEntityRes cardType = null;
        String description = null;
        ECardStatus status = null;
        if (cardTransferPage.getContent().size() > 0) {
            String firstCardNo = cardTransferPage.getContent().get(0).getCardNo();
            CardTMP firstCardTmp = cardTMPService
                    .find(firstCardNo, cardTransferring.getBusinessId(), cardTransferring.getProgramId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.CARD_TMP_NOT_FOUND,
                            "Card tmp not found, because database conflict",
                            LogData.createLogData().append("card_no", firstCardNo)));
            CardType type = cardTypeService.find(firstCardTmp.getCardTypeId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.CARD_TYPE_NOT_FOUND,
                            "Card type not found, because database conflict",
                            LogData.createLogData().append("card_type_id", firstCardTmp.getCardTypeId())));
            cardType = new ShortEntityRes(type.getId(), type.getName(), type.getCardType());
            description = firstCardTmp.getDescription();
            status = firstCardTmp.getCardStatus();
        }
        return ListCardTransfer.builder()
                .cardType(cardType)
                .description(description)
                .status(status)
                .offset(cardTransferPage.getPageable().getOffset())
                .limit(cardTransferPage.getPageable().getPageSize())
                .total(cardTransferPage.getTotalElements())
                .cardNos(cardTransferPage.getContent().stream()
                        .map(CardTransferringHistory::getCardNo)
                        .collect(Collectors.toList())
                )
                .build();
    }


    private void lockCardIsTransfer(List<CardTMP> cardTMPs, final Long cardTransferNo) {
        cardTMPs.forEach(cardTMP -> {
            cardTMP.setCardTransferNo(cardTransferNo);
            cardTMP.setIsTransferring(true);
        });

        cardTMPService.saveAll(cardTMPs);

        List<CardTransferringHistory> cardTransferringHistories = cardTMPs.stream()
                .map(cardTMP -> {
                    CardTransferringHistory cardTransferringHistory = new CardTransferringHistory();
                    cardTransferringHistory.setBusinessId(cardTMP.getBusinessId());
                    cardTransferringHistory.setProgramId(cardTMP.getProgramId());
                    cardTransferringHistory.setStatus(ECardTransferHistoryStatus.PENDING);
                    cardTransferringHistory.setCardTransferNo(cardTransferNo);
                    cardTransferringHistory.setCardNo(cardTMP.getCardNo());
                    cardTransferringHistory.setBatchNo(cardTMP.getCprBatchNo());

                    return cardTransferringHistory;
                }).collect(Collectors.toList());

        cardTransferringHistoryService.saveAll(cardTransferringHistories);
    }

    private CardTransferring getCardTransferringById(final Integer cardTransferringID) {
        return cardTransferringService.findById(cardTransferringID).orElseThrow(
                () -> new BusinessException(ErrorCode.CARD_TRANSFER_NOT_FOUND, "Card transferring not found",
                        LogData.createLogData().append("card_transfer_id", cardTransferringID))
        );
    }

    private Long getChangeRequestIdByTransferId(Integer transferId) {
        APIResponse<SearchChangeRequestsRes<CardTransferringPayloadChangeRequestReq>> changeRequests = makerCheckerServiceClient
                .getChangeRequests(
                        makerCheckerConfigParam.CARD_TRANSFER_MODULE,
                        Long.valueOf(transferId), null, null, null, null, null);

        if (changeRequests.getData().getRecords().size() == 0) {
            throw new BusinessException(ErrorCode.SERVER_ERROR, "Cannot found request id card transferring",
                    LogData.createLogData().append("transfer_id", transferId));
        }
        if (changeRequests.getData().getRecords().size() == 2) {
            throw new BusinessException(ErrorCode.SERVER_ERROR, "Conflict data request, object_id is duplicate",
                    LogData.createLogData().append("transfer_id", transferId));
        }
        return changeRequests.getData().getRecords().get(0).getId();
    }

    private Page<CardTransferringHistory> getTransferHistoryByCardTransferring(CardTransferring cardTransferring,
                                                                               Pageable pageRequest) {

        SpecificationBuilder<CardTransferringHistory> specificationBuilder = new SpecificationBuilder<>();

        specificationBuilder.add(new SearchCriteria("cardTransferNo", cardTransferring.getCardTransferNo(),
                SearchOperation.EQUAL));
        specificationBuilder.add(new SearchCriteria("programId", cardTransferring.getProgramId(),
                SearchOperation.EQUAL));
        specificationBuilder.add(new SearchCriteria("businessId", cardTransferring.getBusinessId(),
                SearchOperation.EQUAL));

        return cardTransferringHistoryService.findAll(specificationBuilder, pageRequest);

    }

    private void checkCardIsTransfer(CardTMP cardTMP, Long batchNo) {
        if (batchNo != null && cardTMP.getCprBatchNo() != batchNo) {
            throw new BusinessException(ErrorCode.CARD_TMP_NOT_FOUND, "Card not found in batch no",
                    LogData.createLogData()
                            .append("batch_no", batchNo)
                            .append("card_no", cardTMP.getCardNo()));
        }
        if (Boolean.TRUE.equals(cardTMP.getIsTransferring())) {
            throw new BusinessException(
                    ErrorCode.CARD_IS_TRANSFERRING,
                    String.format("card_no %s is transferring", cardTMP.getCardNo()),
                    LogData.createLogData()
                            .append("card_no", cardTMP.getCardNo())
            );
        }
    }

    private void verifyData(RequestTransferReq req) {
        businessService.findActive(req.getBusinessId());
        programService.findActive(req.getProgramId());
        storeService.findActive(req.getFromStoreId());
        storeService.findActive(req.getToStoreId());
    }
}