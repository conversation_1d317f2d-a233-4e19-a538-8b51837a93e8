package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.CardTempRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardTmpService;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.repository.CardTMPRepository;
import com.oneid.oneloyalty.common.repository.CardTypeRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OpsCardTmpServiceImpl implements OpsCardTmpService {

    @Autowired
    CardTMPRepository cardTMPRepository;

    @Autowired
    BusinessService businessService;

    @Autowired
    ProgramService programService;

    @Autowired
    StoreService storeService;

    @Autowired
    CardTypeRepository cardTypeRepository;

    @Autowired
    ChainService chainService;

    @Autowired
    CorporationService corporationService;

    @Override
    public Page<CardTempRes> filter(@NotNull Integer businessId, @NotNull Integer programId, @NotNull Integer storeId,
                                    @NotNull Integer batchNo, ECardStatus status, String cardNo, Pageable pageRequest) {

        SpecificationBuilder<CardTMP> specification = new SpecificationBuilder<>();

        Business business = this.businessService.find(businessId).orElse(null);
        Program program = this.programService.find(programId).orElse(null);
        Store store = this.storeService.find(storeId);

        if (business == null || program == null || store == null || !store.getBusinessId().equals(businessId)) {
            return new PageImpl(Collections.EMPTY_LIST, pageRequest, 0);
        }

        specification.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("storeId", storeId, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("cprBatchNo", batchNo, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("isTransferring", Boolean.FALSE, SearchOperation.EQUAL));

        if (status != null) {
            specification.add(new SearchCriteria("cardStatus", status, SearchOperation.EQUAL));
        }
        if (cardNo != null) {
            specification.add(new SearchCriteria("cardNo", cardNo, SearchOperation.EQUAL));
        }

        final Map<Integer, ShortEntityRes> mapCardTypeIdToName = cardTypeRepository.findAll().stream()
                .collect(Collectors.toMap(
                        CardType::getId,
                        entity -> new ShortEntityRes(entity.getId(), entity.getName(), entity.getCardType())
                ));

        Page<CardTMP> cardTMPPage = cardTMPRepository.findAll(specification, pageRequest);

        return new PageImpl<>(
                cardTMPPage.getContent().stream()
                        .map(entity ->
                                CardTempRes.builder()
                                        .id(entity.getId())
                                        .cardNo(entity.getCardNo())
                                        .batchNo(entity.getCprBatchNo())
                                        .issueDate(entity.getIssueDate())
                                        .nameOnCard(entity.getNameOnCard())
                                        .description(entity.getDescription())
                                        .cardStatus(entity.getCardStatus())
                                        .status(entity.getStatus())
                                        .isTransferring(entity.getIsTransferring())
                                        .cardType(mapCardTypeIdToName.get(entity.getCardTypeId()))
                                        .build()
                        ).collect(Collectors.toList()),
                pageRequest, cardTMPPage.getTotalElements());
    }
}