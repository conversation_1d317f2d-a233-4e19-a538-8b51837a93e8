package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CorporationUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESettlementMode;
import com.oneid.oneloyalty.common.entity.Corporation;

import java.util.Date;

public class CorporationMapper {

    public static Corporation toCorporationOne(Corporation corporation, CorporationCreateReq request) {
        corporation.setBusinessId(request.getBusinessId());
        corporation.setName(request.getName());
        corporation.setDescription(request.getDescription());
        corporation.setServiceStartDate(new Date(request.getServiceStartDate() * 1000));
        corporation.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        corporation.setServiceRegistrationNo(request.getServiceRegistrationNo());
        corporation.setSettlementMode(ESettlementMode.of(request.getSettlementMode()));
        corporation.setCardLimitInStock(request.getCardLimitInStock());
        corporation.setTaxIdentificationNumber(request.getTaxIdentificationNumber());
        corporation.setContactPerson(request.getContactPerson());
        corporation.setEmailAddress(request.getEmailAddress());
        corporation.setAddress1(request.getAddress1());
        corporation.setAddress2(request.getAddress2());
        corporation.setCountryId(request.getCountryId());
        corporation.setProvinceId(request.getProvinceId());
        corporation.setDistrictId(request.getDistrictId());
        corporation.setWardId(request.getWardId());
        corporation.setPostalCode(request.getPostalCode());
        corporation.setPhoneNo(request.getPhoneNo());
        corporation.setWebSite(request.getWebsite());
        corporation.setStatus(ECommonStatus.of(request.getStatus()));
        return corporation;
    }

    public static Corporation toCorporationOne(Corporation corporation, CorporationUpdateReq request) {
        corporation.setBusinessId(request.getBusinessId());
        corporation.setName(request.getName());
        corporation.setDescription(request.getDescription());
        corporation.setServiceStartDate(new Date(request.getServiceStartDate() * 1000));
        corporation.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        corporation.setServiceRegistrationNo(request.getServiceRegistrationNo());
        corporation.setSettlementMode(ESettlementMode.of(request.getSettlementMode()));
        corporation.setCardLimitInStock(request.getCardLimitInStock());
        corporation.setTaxIdentificationNumber(request.getTaxIdentificationNumber());
        corporation.setContactPerson(request.getContactPerson());
        corporation.setEmailAddress(request.getEmailAddress());
        corporation.setAddress1(request.getAddress1());
        corporation.setAddress2(request.getAddress2());
        corporation.setCountryId(request.getCountryId());
        corporation.setProvinceId(request.getProvinceId());
        corporation.setDistrictId(request.getDistrictId());
        corporation.setWardId(request.getWardId());
        corporation.setPostalCode(request.getPostalCode());
        corporation.setPhoneNo(request.getPhoneNo());
        corporation.setWebSite(request.getWebsite());
        corporation.setStatus(ECommonStatus.of(request.getStatus()));
        return corporation;
    }
}

