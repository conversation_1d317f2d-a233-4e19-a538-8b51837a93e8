package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = DropdownSearchMultipleParentReq.DropdownSearchMultipleParentReqBuilder.class)
public class DropdownSearchMultipleParentReq {
    private List<Integer> parentIds;
    private String nameOrCode;
    @JsonPOJOBuilder(withPrefix = "")
    public static class DropdownSearchMultipleParentReqBuilder {
    }
}
