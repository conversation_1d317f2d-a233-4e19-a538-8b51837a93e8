package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateCPRFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.MakerCheckerChangeRequestResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardProductionRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardSearchReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardProductionRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConfSeq;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardFileExt;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.GiftCardBin;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardPolicyService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.GiftCardBinService;
import com.oneid.oneloyalty.common.service.GiftCardTypeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.SequenceService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.util.DateUtil;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class OpsGiftCardProductionRequestServiceImpl implements OpsGiftCardProductionRequestService {

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ChainService chainService;

    @Autowired
    private GiftCardBinService giftCardBinService;

    @Autowired
    private GiftCardTypeService giftCardTypeService;

    @Autowired
    private CardPolicyService cardPolicyService;

    @Autowired
    private GiftCardRequestRepository giftCardRequestRepository;

    @Autowired
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @Autowired
    private CardServiceFeignClient cardServiceFeignClient;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public Long createNewChangeRequest(CreateGiftCardRequestReq req) {
        if (req.getInitialGcStatus() != EGiftCardStatus.ACTIVE && req.getInitialGcStatus() != EGiftCardStatus.PENDING) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Cannot create gift card production request with status not in ACTIVE/PENDING", null);
        }

        Business business = businessService.findActive(req.getBusinessId());
        Program program = programService.findActive(req.getProgramId());
        Store store = storeService.findActive(req.getStoreId());

        if (req.getInitialGcStatus() != EGiftCardStatus.PENDING && req.getInitialGcStatus() != EGiftCardStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Cannot create gift card request with initial gift card not in [Pending, Active]", null);
        }

        if (!business.getId().equals(program.getBusinessId()) || !req.getBusinessId().equals(store.getBusinessId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Store/Program not match with business", req);
        }

        giftCardBinService.findActive(req.getGiftCardBinId());
        GiftCardType giftCardType = giftCardTypeService.findActive(req.getGiftCardTypeId());
        CardPolicy cardPolicy = cardPolicyService.findActive(giftCardType.getPolicyId());

        if (req.getGenerateQr() == EBoolean.YES && cardPolicy.getQrUrl() == null) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Can not find  qr url from card policy", null);
        }
        if (!ECardPolicyType.GIFT_CARD.equals(cardPolicy.getPolicyType())) {
            throw new BusinessException(ErrorCode.CARD_POLICY_NOT_MATCHED, "Card policy not matched ", req);
        }

        if ((cardPolicy.getMaxCardCpr() != null) && (cardPolicy.getMaxCardCpr() < req.getNoOfCard())) {
            throw new BusinessException(ErrorCode.CPR_OVER_MAX_CARD, "Over max card per request", cardPolicy.getMaxCardCpr());
        }

        Long batchNo = sequenceService.getNextSeq(
                EConfSeq.GIFT_CARD_BATCH_NO.getSeqId(req.getBusinessId(), req.getProgramId())
        );

        GiftCardRequest giftCardRequest = new GiftCardRequest();
        giftCardRequest.setBusinessId(req.getBusinessId());
        giftCardRequest.setProgramId(req.getProgramId());
        giftCardRequest.setStoreId(req.getStoreId());
        giftCardRequest.setGcBinId(req.getGiftCardBinId());
        giftCardRequest.setGcTypeId(req.getGiftCardTypeId());
        giftCardRequest.setGcPolicyId(cardPolicy.getId());
        giftCardRequest.setSerialSuffix(req.getSerialSuffix());
        giftCardRequest.setCprBatchNo(batchNo);
        giftCardRequest.setNoOfCards(req.getNoOfCard());
        giftCardRequest.setDescription(req.getDescription());
        giftCardRequest.setInitGcStatus(req.getInitialGcStatus() != null ? req.getInitialGcStatus() : EGiftCardStatus.PENDING);
        giftCardRequest.setCreatedYmd(DateUtil.formatYYYYMMDD(new Date()));
        giftCardRequest.setGenerateInd(EGiftCardIndicator.NO);
        giftCardRequest.setStatus(ECommonStatus.PENDING);
        giftCardRequest.setApprovalStatus(EApprovalStatus.PENDING);
        giftCardRequest.setCprVersion(1);
        giftCardRequest.setRequestStatus(ECommonStatus.PENDING);
        giftCardRequest.setGenerateQr(req.getGenerateQr());
        giftCardRequest.setGcBatchType(req.getBatchType());
        giftCardRequest.setGcFileExt(EGiftCardFileExt.TXT);

        giftCardRequest = saveToDatabaseGiftCardRequest(giftCardRequest);
//
        ChangeRequestFeignReq changeRequest = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(makerCheckerConfigParam.GIFT_CARD_PRODUCTION_REQUEST)
                .payload(Collections.singletonMap("request_id", giftCardRequest.getId()))
                .objectId(String.valueOf(giftCardRequest.getId()))
                .build();
//
        APIResponse<MakerCheckerChangeRequestResponse> apiResponse = makerCheckerFeignClient.changes(changeRequest);
        return apiResponse.getData().getId();
    }

    @Override
    public Page<GiftCardProductionRequestRes> searchInReview(EApprovalStatus approvalStatus, Integer fromDate, Integer toDate, Pageable pageable) {
        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerFeignClient.getChangeRequests(
                makerCheckerConfigParam.GIFT_CARD_PRODUCTION_REQUEST,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase()
                        : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromDate,
                toDate);
        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        if (changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());

            List<GiftCardRequest> giftCardRequests = giftCardRequestRepository.findAllById(requestIds);

            Map<Integer, Integer> mapObjectIdToReview = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(
                            iter -> Integer.parseInt(iter.getObjectId()),
                            ChangeRequestPageFeignRes.ChangeRecordFeginRes::getChangeRequestId)
                    );

            Map<Integer, GiftCardBin> mapGCBin = giftCardBinService.findByIdIn(
                    giftCardRequests.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(GiftCardBin::getId, iter -> iter));

            Map<Integer, GiftCardType> mapGCType = giftCardTypeService.findByIdIn(
                    giftCardRequests.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(GiftCardType::getId, iter -> iter));

            Map<Integer, CardPolicy> mapPolicy = cardPolicyService.findByIdIn(
                    giftCardRequests.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(CardPolicy::getId, iter -> iter));

            Map<Integer, Program> mapProgram = programService.findByIdIn(
                    giftCardRequests.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(Program::getId, iter -> iter));

            Map<Integer, Store> mapStore = storeService.findByIdIn(
                    giftCardRequests.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toList())
            ).stream().collect(Collectors.toMap(Store::getId, iter -> iter));

            List<GiftCardProductionRequestRes> content = giftCardRequests
                    .stream()
                    .map(iter -> mapObjectIdToReview.containsKey(iter.getId()) ?
                            transform(
                                    iter,
                                    mapGCBin.getOrDefault(iter.getGcBinId(), null),
                                    mapGCType.getOrDefault(iter.getGcTypeId(), null),
                                    mapPolicy.getOrDefault(iter.getGcPolicyId(), null),
                                    mapProgram.getOrDefault(iter.getProgramId(), null),
                                    mapStore.getOrDefault(iter.getStoreId(), null),
                                    mapObjectIdToReview.get(iter.getId()))
                            : null)
                    .sorted((o1, o2) -> {
                        if (o1 == null) {
                            return -1;
                        } else if (o2 == null) {
                            return 1;
                        }
                        return o2.getRequestId().compareTo(o1.getRequestId());
                    })
                    .collect(Collectors.toList());

            return new PageImpl<>(content, pageable, changeRequestPageFeignRes.getTotalRecordCount());
        } else {
            return Page.empty(pageable);
        }
    }

    @Override
    public GiftCardProductionRequestRes getDetailInReview(Integer reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> apiResponse = makerCheckerFeignClient
                .getChangeRequestById(reviewId.toString());
        GiftCardRequest giftCardRequest = giftCardRequestRepository
                .findById(Integer.parseInt(apiResponse.getData().getObjectId())).orElseThrow(
                        () -> new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND, null, null)
                );

        return transform(
                giftCardRequest,
                giftCardBinService.find(giftCardRequest.getGcBinId()).orElse(null),
                giftCardTypeService.find(giftCardRequest.getGcTypeId()).orElse(null),
                cardPolicyService.find(giftCardRequest.getGcPolicyId()).orElse(null),
                programService.find(giftCardRequest.getProgramId()).orElse(null),
                storeService.find(giftCardRequest.getStoreId()),
                apiResponse.getData().getChangeRequestId()
        );
    }

    @Override
    public Page<GiftCardProductionRequestRes> searchAvailable(GiftCardSearchReq req, Pageable pageable) {
        ECommonStatus requestStatus = req.getApprovalStatus() == EApprovalStatus.APPROVED ? ECommonStatus.ACTIVE :
                (req.getApprovalStatus() == EApprovalStatus.REJECTED ? ECommonStatus.INACTIVE : ECommonStatus.PENDING);

        Page<Object[]> page = giftCardRequestRepository.filter(
                req.getBusinessId(),
                null,
                req.getCorporationId(),
                req.getChainId(),
                req.getStoreId(),
                req.getCprBatchNo(),
                req.getBatchType(),
                req.getGiftCardStatus(),
                req.getGenerationStatus(),
                req.getApprovalStatus(),
                requestStatus,
                pageable
        );

        List<GiftCardProductionRequestRes> content = page.getContent()
                .stream()
                .map(iter -> {
                    GiftCardRequest giftCardRequest = (GiftCardRequest) iter[0];
                    Program program = (Program) iter[1];
                    Store store = (Store) iter[2];
                    GiftCardBin giftCardBin = (GiftCardBin) iter[3];
                    GiftCardType giftCardType = (GiftCardType) iter[4];
                    CardPolicy cardPolicy = (CardPolicy) iter[5];
                    return transform(
                            giftCardRequest,
                            giftCardBin,
                            giftCardType,
                            cardPolicy,
                            program,
                            store,
                            null);
                })
                .collect(Collectors.toList());

        return new PageImpl<>(content, pageable, page.getTotalElements());
    }

    @Override
    public GiftCardProductionRequestRes getDetailAvailable(Integer requestId) {
        GiftCardRequest giftCardRequest = giftCardRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND,
                        "Gift card production request not found",
                        null
                ));
        return transform(
                giftCardRequest,
                giftCardBinService.find(giftCardRequest.getGcBinId()).orElse(null),
                giftCardTypeService.find(giftCardRequest.getGcTypeId()).orElse(null),
                cardPolicyService.find(giftCardRequest.getGcPolicyId()).orElse(null),
                programService.find(giftCardRequest.getProgramId()).orElse(null),
                storeService.find(giftCardRequest.getStoreId()),
                null
        );
    }

    @Override
    public Long update(Integer requestId, UpdateGiftCardRequestReq req) {
        GiftCardRequest old = giftCardRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND,
                        "Gift card production request not found", null));

        if (req.getStatus() != EGiftCardStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Cannot update gift card status difference active", null);
        }

        if (old.getInitGcStatus() != EGiftCardStatus.PENDING) {
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_PENDING,
                    "Gift card production request not pending", null);
        }

        if (old.getApprovalStatus() == EApprovalStatus.PENDING) {
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_IS_ALREADY_REQUESTED, null, null);
        }

        if (old.getApprovalStatus().equals(EApprovalStatus.REJECTED))
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_CAN_NOT_BE_EDITED, null, null);

        if (!old.getRequestStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, null, null);

        GiftCardRequest alreadyRequestedChecker = giftCardRequestRepository
                .findByCprBatchNoAndBusinessIdAndProgramId(old.getCprBatchNo(), old.getBusinessId(), old.getProgramId(), ECommonStatus.PENDING);

        if (alreadyRequestedChecker != null) {
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_IS_ALREADY_REQUESTED, null, null);
        }

        GiftCardRequest giftCardRequest = new GiftCardRequest();
        giftCardRequest.setInitGcStatus(req.getStatus());

        giftCardRequest.setBusinessId(old.getBusinessId());
        giftCardRequest.setProgramId(old.getProgramId());
        giftCardRequest.setStoreId(old.getStoreId());
        giftCardRequest.setGcBinId(old.getGcBinId());
        giftCardRequest.setGcTypeId(old.getGcTypeId());
        giftCardRequest.setGcPolicyId(old.getGcPolicyId());
        giftCardRequest.setSerialSuffix(old.getSerialSuffix());
        giftCardRequest.setCprBatchNo(old.getCprBatchNo());
        giftCardRequest.setNoOfCards(old.getNoOfCards());
        giftCardRequest.setDescription(old.getDescription());
        giftCardRequest.setCreatedYmd(DateUtil.formatYYYYMMDD(new Date()));
        giftCardRequest.setGenerateInd(old.getGenerateInd());
        giftCardRequest.setStatus(ECommonStatus.PENDING);
        giftCardRequest.setApprovalStatus(EApprovalStatus.PENDING);
        giftCardRequest.setCprVersion(old.getCprVersion() + 1);
        giftCardRequest.setRequestStatus(ECommonStatus.PENDING);
        giftCardRequest.setGenerateQr(old.getGenerateQr());
        giftCardRequest.setGcBatchType(old.getGcBatchType());
        giftCardRequest = saveToDatabaseGiftCardRequest(giftCardRequest);

        ChangeRequestFeignReq changeRequest = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.UPDATE)
                .module(makerCheckerConfigParam.GIFT_CARD_PRODUCTION_REQUEST)
                .payload(Collections.singletonMap("request_id", giftCardRequest.getId()))
                .objectId(String.valueOf(giftCardRequest.getId()))
                .build();

        APIResponse<MakerCheckerChangeRequestResponse> apiResponse = makerCheckerFeignClient.changes(changeRequest);
        return apiResponse.getData().getId();
    }

    @Transactional
    public GiftCardRequest saveToDatabaseGiftCardRequest(GiftCardRequest entity) {
        return giftCardRequestRepository.save(entity);
    }

    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.GIFT_CARD_PRODUCTION_REQUEST);
        GiftCardProductionRequestReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), GiftCardProductionRequestReq.class);
        if (Objects.isNull(payload)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "payload must not be null",
                    LogData.createLogData());
        }
        if (!EApprovalStatus.PENDING.getValue().equals(detailResData.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                //validate
                businessService.findActive(payload.getBusinessId());
                programService.findActive(payload.getProgramId());
                Store s = storeService.findStatusActive(payload.getStoreId());
                corporationService.findActive(s.getCorporationId());
                chainService.findActive(s.getChainId());
                GiftCardType gct = giftCardTypeService.findActive(payload.getGiftCardTypeId());
                giftCardBinService.findActive(payload.getGiftCardBinId());
                CardPolicy cardPolicy = cardPolicyService.findActive(gct.getPolicyId());
                if (cardPolicy.getMaxCardCpr() < payload.getNoOfCard()) {
                    throw new BusinessException(ErrorCode.CARD_OVER_STOCK_LIMIT, "Over stock limit",
                            LogData.createLogData().append("request", payload)
                                    .append("stock_limit", cardPolicy.getMaxCardCpr()));
                }
                processCreate(detailResData, payload, gct.getPolicyId());
            }
        }
        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes = makerCheckerInternalFeignClient
                .checker(checkerReq);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Card production request call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
        return;
    }

    private void processCreate(
            MakerCheckerInternalDataDetailRes detailResData,
            GiftCardProductionRequestReq payload, Integer policyId
    ) {
        String createdBy;
        String updatedBy;

        Date madeDate = detailResData.getMadeDate() != null ? Date.from(ZonedDateTime.parse(detailResData.getMadeDate()).toInstant()) : null;
        String currentUser = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        GiftCardRequest ent = new GiftCardRequest();
        ent.setBusinessId(payload.getBusinessId());
        ent.setCprBatchNo(payload.getBatchNo());
        ent.setNoOfCards(payload.getNoOfCard());
        ent.setStoreId(payload.getStoreId());
        ent.setDescription(payload.getDescription());
        ent.setGenerateInd(EGiftCardIndicator.NO);
        ent.setGcBinId(payload.getGiftCardBinId());
        ent.setGcTypeId(payload.getGiftCardTypeId());
        ent.setGcPolicyId(policyId);
        ent.setProgramId(payload.getProgramId());
        ent.setGcFileExt(EGiftCardFileExt.TXT);
        ent.setStatus(ECommonStatus.ACTIVE);
        ent.setInitGcStatus(EGiftCardStatus.of(payload.getInitialGcStatus()));
        ent.setSerialSuffix(payload.getSerialSuffix());
        ent.setGenerateQr(EBoolean.of(payload.getGenerateQr()));
        ent.setGcBatchType(EGiftCardBatchType.of(payload.getBatchType()));
        ent.setCreatedAt(madeDate);
        ent.setCreatedBy(detailResData.getMadeByUserName());
        ent.setRequestCode(detailResData.getRequestCode());
        ent.setVersion(detailResData.getVersion());
        ent.setApprovalStatus(EApprovalStatus.APPROVED);
        ent.setCprVersion(detailResData.getVersion());
        ent.setRequestStatus(ECommonStatus.ACTIVE);
        updatedBy = createdBy = detailResData.getMadeByUserName();
        opsReqPendingValidator.updateInfoChecker(ent, detailResData.getMadeDate(), createdBy, updatedBy, currentUser);
        ent = giftCardRequestRepository.save(ent);

        CreateCPRFeignReq req = new CreateCPRFeignReq(ent.getId());

        cardServiceFeignClient.createCPR(req);

        return;
    }

    private GiftCardProductionRequestRes transform(GiftCardRequest giftCardRequest, GiftCardBin bin, GiftCardType type,
                                                   CardPolicy policy, Program program, Store store, Integer reviewId) {

        GiftCardProductionRequestRes.CardPolicyResponse policyResponse = policy == null ? null :
                GiftCardProductionRequestRes.CardPolicyResponse
                        .builder()
                        .id(policy.getId())
                        .name(policy.getName())
                        .code(policy.getCode())
                        .qrUrl(policy.getQrUrl())
                        .build();

        return GiftCardProductionRequestRes.builder()
                .requestId(giftCardRequest.getId())
                .store(store != null ? new ShortEntityRes(store.getId(), store.getName(), store.getCode()) : null)
                .gcBin(bin != null ? new ShortEntityRes(bin.getId(), bin.getName(), bin.getBinCode()) : null)
                .gcPolicy(policyResponse)
                .gcType(type != null ? new ShortEntityRes(type.getId(), type.getDescription(), type.getCode()) : null)
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .requestStatus(giftCardRequest.getStatus())
                .giftCardStatus(giftCardRequest.getInitGcStatus())
                .approvalStatus(giftCardRequest.getApprovalStatus())
                .noOfCard(giftCardRequest.getNoOfCards())
                .description(giftCardRequest.getDescription())
                .gcSuffix(giftCardRequest.getSerialSuffix())
                .generationInd(giftCardRequest.getGenerateInd())
                .generationDate(DateTimes.toEpochSecond(giftCardRequest.getGenerateDate()))
                .batchNo(giftCardRequest.getCprBatchNo())
                .createdBy(giftCardRequest.getCreatedBy())
                .createdAt(DateTimes.toEpochSecond(giftCardRequest.getCreatedAt()))
                .updateBy(giftCardRequest.getUpdatedBy())
                .updatedAt(DateTimes.toEpochSecond(giftCardRequest.getUpdatedAt()))
                .approvedAt(DateTimes.toEpochSecond(giftCardRequest.getApprovedAt()))
                .approvedBy(giftCardRequest.getApprovedBy())
                .reviewId(reviewId)
                .version(giftCardRequest.getCprVersion())
                .batchType(giftCardRequest.getGcBatchType())
                .generateQr(giftCardRequest.getGenerateQr())
                //.reason(giftCardRequest.getRejectedReason())
                .build();
    }

    @Override
    public GiftCardProductionRequestRes verifyGiftCardTransferring(int businessId, int programId, long batchNo) {
        GiftCardRequest giftCardRequest = giftCardRequestRepository.findByCprBatchNoAndBusinessIdAndProgramId(
                batchNo, businessId, programId);

        if (ObjectUtils.isEmpty(giftCardRequest))
            throw new BusinessException(
                    ErrorCode.GIFT_CARD_BATCH_NO_NOT_FOUND,
                    "This batch does not exist",
                    null
            );

        if (!giftCardRequest.getGenerateInd().equals(EGiftCardIndicator.YES))
            throw new BusinessException(
                    ErrorCode.GIFT_CARD_PRODUCTION_IN_PROGRESS,
                    "This batch has not been generated yet",
                    null
            );

        GiftCardType giftCardType = giftCardTypeService.find(giftCardRequest.getGcTypeId()).orElse(null);
        Store store = storeService.find(giftCardRequest.getStoreId());
        Chain chain = null;
        Corporation corporation = null;
        if (Objects.nonNull(store)) {
            chain = chainService.find(store.getChainId()).orElse(null);
            corporation = corporationService.find(store.getCorporationId()).orElse(null);
        }
        return GiftCardProductionRequestRes.builder()
                .batchNo(giftCardRequest.getCprBatchNo())
                .batchType(giftCardRequest.getGcBatchType())
                .description(giftCardRequest.getDescription())
                .gcType(giftCardType != null ? new ShortEntityRes(giftCardType.getId(), giftCardType.getCode(), giftCardType.getCode()) : null)
                .noOfCard(giftCardRequest.getNoOfCards())
                .store(store != null ? new ShortEntityRes(store.getId(), store.getName(), store.getCode()) : null)
                .chain(chain != null ? new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()) : null)
                .corporation(corporation != null ? new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()) : null)
                .id(Long.valueOf(giftCardRequest.getId()))
                .build();
    }
}
