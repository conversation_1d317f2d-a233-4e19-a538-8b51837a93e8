package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.mapper.CorporationMapper;
import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CorporationUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.CorporationEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.CorporationInfo;
import com.oneid.loyalty.accounting.ops.model.res.CorporationRes;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CountryService;
import com.oneid.oneloyalty.common.service.DistrictService;
import com.oneid.oneloyalty.common.service.ProvinceService;
import com.oneid.oneloyalty.common.service.WardService;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsCorporationServiceImpl implements OpsCorporationService {

    @Autowired
    private CountryService countryService;

    @Autowired
    private ProvinceService provinceService;

    @Autowired
    private DistrictService districtService;

    @Autowired
    private WardService wardService;

    @Autowired
    CorporationRepository corporationRepository;

    @Autowired
    CorporationService corporationService;

    @Autowired
    BusinessRepository businessRepository;
    
    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private DistrictRepository districtRepository;
    
    @Autowired
    private WardRepository wardRepository;

    final Integer ZERO_BUSINESS_ID = 0;

    @Override
    public List<CorporationInfo> searchListCorporation(SearchCorporationReq req) {
        SpecificationBuilder<Corporation> searchBuilder = new SpecificationBuilder<>();
        if (req.getBusinessId() != null) {
            searchBuilder.add(new SearchCriteria("businessId", req.getBusinessId(), SearchOperation.EQUAL));
        }
        if (req.getStatus() != null) {
            searchBuilder.add(new SearchCriteria("status", req.getStatus(), SearchOperation.EQUAL));
        }
        return corporationRepository.findAll(searchBuilder)
                .stream()
                .map(CorporationInfo::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public List<CorporationInfo> listCorporationOfBusiness(Integer businessId) {
        return corporationRepository.findByBusinessId(businessId).stream()
                .map(CorporationInfo::valueOf).collect(Collectors.toList());
    }

    @Override
    public List<CorporationEnumAll> getEnumAll() {
        return corporationRepository.findAll().stream().map(CorporationEnumAll::of).collect(Collectors.toList());
    }

    @Override
    public CorporationRes check(String code) {
        Corporation corporation = corporationRepository.findByBusinessId(0, code);
        if (corporation == null)
            throw new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found",
                    LogData.createLogData().append("corporation_code", code));

        Map<Integer, String> businessIdToName = this.getBusinessIdToName();
        return CorporationRes.of(corporation, businessIdToName);
    }

    @Override
    public void update(Integer id, CorporationUpdateReq corporationUpdateReq) {
        validateUniqueCorporationNameForUpdate(corporationUpdateReq.getName(), id);
        validateUniqueCorporationPhoneForUpdate(corporationUpdateReq.getPhoneNo(), id);
        validateUniqueTaxIdentificationNumberForUpdate(corporationUpdateReq.getTaxIdentificationNumber(), id);
        validateLocation(corporationUpdateReq.getCountryId(),
                corporationUpdateReq.getProvinceId(),
                corporationUpdateReq.getDistrictId(),
                corporationUpdateReq.getWardId());
        Corporation corporation = corporationService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found in business", LogData.createLogData().append("corporation_id", id)));
        Corporation corporationUpdate = CorporationMapper.toCorporationOne(corporation, corporationUpdateReq);
        corporationService.update(corporationUpdate);
    }

    @Override
    public Map<Integer, Corporation> getMapById(Collection<Integer> ids) {
        List<Corporation> corporations = this.corporationRepository.findAllByIdIn(ids);
        return corporations.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t,
                (value1, value2) -> value2
        ));
    }

    @Override
    public Page<CorporationRes> filter(Integer businessId, String corporationName, String corporationCode, String status, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<Corporation> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));
        else
            specificationBuilder.add(new SearchCriteria("businessId", ZERO_BUSINESS_ID, SearchOperation.NOT_EQUAL));

        if (corporationName != null)
            specificationBuilder.add(new SearchCriteria("name", corporationName, SearchOperation.EQUAL));

        if (corporationCode != null)
            specificationBuilder.add(new SearchCriteria("code", corporationCode, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        Map<Integer, String> businessIdToName = this.getBusinessIdToName();

        Page<Corporation> corporations = corporationService.find(specificationBuilder, pageRequest);

        return new PageImpl<CorporationRes>(corporations.getContent()
                .stream().map(it -> CorporationRes.of(it, businessIdToName)).collect(Collectors.toList())
                , pageRequest, corporations.getTotalElements());
    }

    @Override
    public CorporationRes get(Integer id) {
        Corporation corporation = corporationService.find(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found", null));
        
        Map<Integer, String> businessIdToName = this.getBusinessIdToName();
        
        Integer wardId = null;
        Integer districtId = null;

        if(corporation.getWardId() != null) {
            Optional<Ward> ward = wardRepository.findById(corporation.getWardId());

            if (ward.isPresent()){
                Ward existedWard = ward.get();
                if(!existedWard.getStatus().equals(ECommonStatus.ACTIVE) || !existedWard.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedWard = wardRepository.findByCodeAndStatusAndNewStatus(existedWard.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                wardId = existedWard.getId();
                districtId = existedWard.getDistrictId();
            }
        }

        if(districtId == null && corporation.getDistrictId() != null) {
            Optional<District> district = districtRepository.findById(corporation.getDistrictId());

            if(district.isPresent()) {
                District existedDistrict = district.get();
                if (!existedDistrict.getStatus().equals(ECommonStatus.ACTIVE) || !existedDistrict.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedDistrict = districtRepository.findByCodeAndStatusAndNewStatus(existedDistrict.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                districtId = existedDistrict.getId();
            }
        }

        CorporationRes corporationRes = new CorporationRes();
        corporationRes.setId(corporation.getId());
        corporationRes.setCode(corporation.getCode());
        corporationRes.setBusinessId(corporation.getBusinessId());
        corporationRes.setBusinessName(businessIdToName != null ? businessIdToName.get(corporation.getBusinessId()) : null);
        corporationRes.setName(corporation.getName());
        corporationRes.setEnName(corporation.getEnName());
        corporationRes.setDescription(corporation.getDescription());
        corporationRes.setEnDescription(corporation.getEnDescription());
        corporationRes.setStatus(corporation.getStatus() != null ? corporation.getStatus().getValue() : null);
        corporationRes.setCreatedBy(corporation.getCreatedBy());
        corporationRes.setUpdatedBy(corporation.getUpdatedBy());
        corporationRes.setApprovedBy(corporation.getApprovedBy());
        corporationRes.setCreatedAt(corporation.getCreatedAt() != null ? corporation.getCreatedAt().toInstant().getEpochSecond() : null);
        corporationRes.setUpdatedAt(corporation.getUpdatedAt() != null ? corporation.getUpdatedAt().toInstant().getEpochSecond() : null);
        corporationRes.setApprovedAt(corporation.getApprovedAt() != null ? corporation.getApprovedAt().toInstant().getEpochSecond() : null);
        corporationRes.setCreatedYmd(corporation.getCreatedYmd());
        corporationRes.setContactPerson(corporation.getContactPerson());
        corporationRes.setEmailAddress(corporation.getEmailAddress());
        corporationRes.setPhoneNo(corporation.getPhoneNo());
        corporationRes.setTaxNo(corporation.getTaxNo());
        corporationRes.setWebSite(corporation.getWebSite());
        corporationRes.setAddress1(corporation.getAddress1());
        corporationRes.setCountryId(corporation.getCountryId());
        corporationRes.setProvinceId(corporation.getProvinceId());
        corporationRes.setDistrictId(districtId);
        corporationRes.setWardId(wardId);
        corporationRes.setServiceStartDate(corporation.getServiceStartDate() != null ? corporation.getServiceStartDate().toInstant().getEpochSecond() : null);
        corporationRes.setServiceEndDate(corporation.getServiceEndDate() != null ? corporation.getServiceEndDate().toInstant().getEpochSecond() : null);
        corporationRes.setServiceRegistrationNo(corporation.getServiceRegistrationNo());
        corporationRes.setSettlementMode(corporation.getSettlementMode() != null ? corporation.getSettlementMode().getValue() : null);
        corporationRes.setCardLimitInStock(corporation.getCardLimitInStock());
        corporationRes.setTaxIdentificationNumber(corporation.getTaxIdentificationNumber());
        corporationRes.setPostalCode(corporation.getPostalCode());
        corporationRes.setAddress2(corporation.getAddress2());
        
        return corporationRes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CorporationRes add(CorporationCreateReq corporationCreateReq) {
        Business business = businessService.findActive(corporationCreateReq.getBusinessId());
        
        corporationService.findByBusinessAndCorporationCode(business.getCode(), corporationCreateReq.getCode())
        .ifPresent(entity -> {
            throw new BusinessException(ErrorCode.CORPORATION_EXISTED, "Corporation exist in business", null);
        });
        
        Corporation corporation = corporationRepository.findByBusinessId(ZERO_BUSINESS_ID, corporationCreateReq.getCode());
        
        if (corporation == null){
            corporation = new Corporation();
            corporation.setCode(corporationCreateReq.getCode());
        }
        
        if(corporationRepository.getByName(corporationCreateReq.getName())
                .stream()
                .filter(entity -> entity.getBusinessId().equals(business.getId()))
                .count() > 0) {
            throw new BusinessException(OpsErrorCode.CORPORATION_NAME_EXIST.getValue(), "corporation name exist", null);
        }
        
        if(corporationCreateReq.getPhoneNo() != null && corporationRepository.getByPhoneNo(corporationCreateReq.getPhoneNo())
                .stream()
                .filter(entity -> entity.getBusinessId().equals(business.getId()))
                .count() > 0) {
            throw new BusinessException(OpsErrorCode.CORPORATION_PHONE_EXIST.getValue(), "corporation phone exist", null);
        }
        
        if(corporationCreateReq.getTaxIdentificationNumber() != null) {
            if(corporationRepository.findAllByTaxIdentificationNumber(corporationCreateReq.getTaxIdentificationNumber())
            .stream()
            .filter(entity -> entity.getBusinessId().equals(business.getId()))
            .count() > 0) {
                throw new BusinessException(OpsErrorCode.TAX_IDENTIFICATION_NUMBER_EXIST.getValue(), "tax identification number exist", null);
            }
        }
        
        validateLocation(corporationCreateReq.getCountryId(),
                corporationCreateReq.getProvinceId(),
                corporationCreateReq.getDistrictId(),
                corporationCreateReq.getWardId());
        
        corporation = CorporationMapper.toCorporationOne(corporation, corporationCreateReq);
        
        corporationRepository.save(corporation);
        
        return CorporationRes.valueOf(corporation, business);
    }

    private void validateUniqueCorporationNameForUpdate(String corporationName, Integer id) {
        if (corporationName == null) return;
        List<Corporation> corporations = corporationRepository.getByName(corporationName);
        for (Corporation corporation : corporations) {
            if (!corporation.getId().equals(id)) {
                throw new OpsBusinessException(OpsErrorCode.CORPORATION_NAME_EXIST, "corporation name exist", LogData.createLogData().append("corporation_name", corporationName));
            }
        }
    }

    private void validateUniqueCorporationPhoneForUpdate(String corporationPhone, Integer id) {
        if (corporationPhone == null) return;
        List<Corporation> corporations = corporationRepository.getByPhoneNo(corporationPhone);
        for (Corporation corporation : corporations) {
            if (!corporation.getId().equals(id)) {
                throw new OpsBusinessException(OpsErrorCode.CORPORATION_PHONE_EXIST, "corporation phone exist", LogData.createLogData().append("corporation_phone", corporationPhone));
            }
        }
    }

    private void validateUniqueTaxIdentificationNumberForUpdate(Integer taxIdentificationNumber, Integer id) {
        if (taxIdentificationNumber == null) return;
        List<Corporation> corporations = corporationRepository.findAllByTaxIdentificationNumber(taxIdentificationNumber);
        for (Corporation corporation : corporations) {
            if (!corporation.getId().equals(id)) {
                throw new OpsBusinessException(OpsErrorCode.TAX_IDENTIFICATION_NUMBER_EXIST, "tax identification number exist", LogData.createLogData().append("taxIdentificationNumber", taxIdentificationNumber));
            }
        }
    }

    private void validateLocation(Integer countryId, Integer provinceId, Integer districtId, Integer wardId) {
        if (countryId != null) {
            this.countryService.getById(countryId).orElseThrow(() -> new BusinessException(
                    ErrorCode.COUNTRY_NOT_FOUND, "Country is not found", LogData.createLogData().append("country", countryId)));
        }

        if (provinceId != null && !this.provinceService.isValid(provinceId, countryId)) {
            throw new BusinessException(
                    ErrorCode.PROVINCE_NOT_FOUND, "Province is not found", LogData.createLogData().append("province", provinceId));
        }

        if (districtId != null && !this.districtService.isValid(districtId, provinceId)) {
            throw new BusinessException(
                    ErrorCode.DISTRICT_NOT_FOUND, "District is not found", LogData.createLogData().append("district", districtId));
        }

        if (wardId != null && !this.wardService.isValid(wardId, districtId)) {
            throw new BusinessException(
                    ErrorCode.WARD_NOT_FOUND, "Ward is not found", LogData.createLogData().append("ward", wardId));
        }
    }

    private Map<Integer, String> getBusinessIdToName() {
        List<Business> businesses = businessRepository.findAll();
        return businesses.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t.getName()
        ));
    }
}