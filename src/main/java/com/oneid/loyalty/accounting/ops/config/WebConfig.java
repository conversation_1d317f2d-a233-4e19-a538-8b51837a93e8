package com.oneid.loyalty.accounting.ops.config;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.web.SortHandlerMethodArgumentResolver;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EAdjustmentType;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.constant.MemberSortingField;
import com.oneid.loyalty.accounting.ops.support.data.databind.OffsetSetting;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageRequestArgumentResolver;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojoArgumentResolver;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.EProgressStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private List<Locale> locales = Arrays.asList(new Locale("vi"), new Locale("en"));
    
    @Autowired
    private OffsetSetting offsetSetting;
    
    @Autowired
    private SortHandlerMethodArgumentResolver sortResolver;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Bean("localeResolver")
    public LocaleResolver acceptHeaderLocaleResolver() {
        return new AcceptHeaderLocaleResolver() {
            @Override
            public Locale resolveLocale(HttpServletRequest request) {
                String headerLang = request.getHeader("Accept-Language");
                return headerLang == null || headerLang.isEmpty() ? locales.get(0)
                        : Locale.lookup(Locale.LanguageRange.parse(headerLang), locales);
            }
        };
    }

    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource rs = new ResourceBundleMessageSource();
        rs.setBasename("messages");
        rs.setDefaultEncoding("UTF-8");
        rs.setUseCodeAsDefaultMessage(true);
        return rs;
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new ECommonStatus.RequestQueryConverter());
        registry.addConverter(new ESchemeType.RequestQueryConverter());
        registry.addConverter(new MemberSortingField.UserSortingFieldConverter());
        registry.addConverter(new EGiftCardIndicator.RequestQueryConverter());
        registry.addConverter(new EGiftCardStatus.RequestQueryConverter());
        registry.addConverter(new EOpsCancellationType.RequestQueryConverter());
        registry.addConverter(new EAdjustmentType.RequestQueryConverter());
        registry.addConverter(new EOpsTransactionType.RequestQueryConverter());
        registry.addConverter(new EOpsIdType.RequestQueryConverter());
        registry.addConverter(new Converter<String, ECardStatus>() {
            @Override
            public ECardStatus convert(String source) {
                ECardStatus res = ECardStatus.of(source);
                if (res == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "card status type mismatch ", source);
                }
                return res;
            }
        });
        registry.addConverter(new Converter<String, ECardTransferStatus>() {
            @Override
            public ECardTransferStatus convert(String source) {
                ECardTransferStatus res = ECardTransferStatus.of(source);
                if (res == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "card transfer status type mismatch ", source);
                }
                return res;
            }
        });
        registry.addConverter(new Converter<String, ECardTransferIndicatorStatus>() {
            @Override
            public ECardTransferIndicatorStatus convert(String source) {
                ECardTransferIndicatorStatus res = ECardTransferIndicatorStatus.of(source);
                if (res == null) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "card transfer indicator status type mismatch ", source);
                }
                return res;
            }
        });
        
        registry.addConverter(new EApprovalStatus.RequestQueryConverter());
        registry.addConverter(new EApprovalStatus.RequestQueryConverter());
        
        registry.addConverter(new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String source) {
                return LocalDate.parse(source, DateTimeFormatter.ISO_LOCAL_DATE);
            }
        });
        
        registry.addConverter(new EBatchProcessStatusType.RequestQueryConverter());
        registry.addConverter(new EProgressStatus.RequestQueryConverter());
        registry.addConverter(new ESchemeSortingField.ESchemeSortingFieldConverter());
        registry.addConverter(new ETransactionStatus.RequestQueryConverter());
    }
    
    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new MakerCheckerOffsetPageRequestArgumentResolver(sortResolver, offsetSetting));
        resolvers.add(new RequestPojoArgumentResolver(objectMapper));
    }
}
