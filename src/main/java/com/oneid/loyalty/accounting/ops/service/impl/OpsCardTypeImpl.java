package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.CardBinMapper;
import com.oneid.loyalty.accounting.ops.mapper.CardTypeMapper;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import com.oneid.loyalty.accounting.ops.model.res.CardTypeRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsCardTypeService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardBin;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CardPolicyRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OpsCardTypeImpl implements OpsCardTypeService {
    @Autowired
    CardTypeService cardTypeService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    CardPolicyRepository cardPolicyRepository;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsProgramService opsProgramService;

    @Override
    public Page<CardTypeRes> filter(Integer businessId, Integer programId, String cardType, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.Direction.DESC, "createdAt");

        SpecificationBuilder<CardType> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (cardType != null)
            specificationBuilder.add(new SearchCriteria("cardType", cardType, SearchOperation.EQUAL));

        Page<CardType> cardTypes = cardTypeService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = cardTypes.getContent().stream().map(CardType::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> programIds = cardTypes.getContent().stream().map(CardType::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programbyIds = opsProgramService.getMapById(programIds);

        return new PageImpl<CardTypeRes>(cardTypes.getContent()
                .stream().map(it -> CardTypeRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        programbyIds.get(it.getProgramId())
                )).collect(Collectors.toList()), pageRequest, cardTypes.getTotalElements());
    }

    @Override
    public CardTypeRes get(Integer id) {
        Optional<CardType> cardType = cardTypeService.find(id);
        if (!cardType.isPresent())
            throw new BusinessException(ErrorCode.CARD_TYPE_NOT_FOUND, "Card type not found in business", LogData.createLogData().append("card_type_id", id));

        return CardTypeRes.of(
                cardType.get(),
                businessRepository.findById(cardType.get().getBusinessId()).orElse(null),
                programRepository.findById(cardType.get().getProgramId()).orElse(null)
        );
    }

    @Override
    public void add(CardTypeCreateReq cardTypeCreateReq) {
        cardTypeService.create(CardTypeMapper.toCardTypeOne(cardTypeCreateReq));
    }

    @Override
    public void update(Integer id, CardTypeUpdateReq cardTypeUpdateReq) {
        CardType cardType = cardTypeService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CARD_TYPE_NOT_FOUND, "Card type not found in business", LogData.createLogData().append("card_type_id", id)));
        cardTypeService.update(CardTypeMapper.toCardTypeOne(cardType, cardTypeUpdateReq));
    }
}
