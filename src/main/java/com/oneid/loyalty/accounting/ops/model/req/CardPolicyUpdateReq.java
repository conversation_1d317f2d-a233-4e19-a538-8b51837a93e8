package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mapping.model.SnakeCaseFieldNamingStrategy;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CardPolicyUpdateReq {
    @JsonIgnore
    private Integer id;

    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @NotBlank(message = "'Name' cannot be empty")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    private String description;

    private ECommonStatus status;

    private String qrUrl;
}