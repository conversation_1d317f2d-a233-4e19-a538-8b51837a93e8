package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Getter
@Setter
public class CurrencyRateUpdateReq {
    @JsonProperty("base_currency_id")
    private Integer baseCurrencyId;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @NotNull(message = "'BuyRate' is invalid")
    @JsonProperty("buy_rate")
    @PositiveOrZero
    private Double buyRate;

    @NotNull(message = "'SellRate' is invalid")
    @JsonProperty("sell_rate")
    @PositiveOrZero
    private Double sellRate;

    @JsonProperty("status")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' is invalid")
    private String status;

    @JsonProperty("business_id")
    private Integer businessId;
}
