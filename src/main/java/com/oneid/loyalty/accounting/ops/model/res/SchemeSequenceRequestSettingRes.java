package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SchemeSequenceRequestSettingRes {
    private Integer id;
    private String businessName;
    private String programName;
    private Integer businessId;
    private Integer programId;
    private Integer version;
    private ESchemeType schemeType;
    private Integer pendingRequestId;
    private Integer changeRequestId;
    private Integer nextVersion;
    private ECommonStatus status;
    private EApprovalStatus approvalStatus;
    
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date createdAt;
    
    private List<SchemeRes> schemes;
}
