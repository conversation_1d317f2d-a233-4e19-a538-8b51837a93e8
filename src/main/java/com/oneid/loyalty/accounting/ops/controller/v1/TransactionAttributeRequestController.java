package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.model.req.CreateAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.EditAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeRequestService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/attribute/transaction")
@Validated
public class TransactionAttributeRequestController extends BaseController {
    @Autowired
    OpsAttributeRequestService opsAttributeRequestService;
    
    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.CREATE }),
    })
    public ResponseEntity<?> requestCreatingTierRequest(@Valid @RequestBody CreateAttributeRequestReq req){
        req.setAttributeType(EAttributeType.PROGRAM_TRANSACTION);
        return success(opsAttributeRequestService.requestCreatingAttributeRequest(req));
    }
    
    @GetMapping("/requests/available")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(value = "business_id", required = true) Integer businessId,
            @RequestParam(value = "program_id", required = true) Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit){

        Page<AttributeRequestRes> page = opsAttributeRequestService.getAvailableAttributeRequests(EAttributeType.PROGRAM_TRANSACTION, businessId, programId, code, name, status, approvalStatus, new OffsetBasedPageRequest(offset, limit, null));
        
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/in-review")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getInReviewTierRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "fromDate", required = false) Integer fromDate,
            @RequestParam(value = "toDate", required = false) Integer toDate,
            @MakerCheckerOffsetPageable Pageable pageable){
        Page<AttributeRequestRes> page = opsAttributeRequestService.getInReviewAttributeRequests(EAttributeType.PROGRAM_TRANSACTION, approvalStatus, fromDate, toDate, pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}/view")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getAvailableAttributeRequestById(@PathVariable("id") Integer requestId){
        return success(opsAttributeRequestService.getAvailableAttributeRequestById(requestId, EAttributeType.PROGRAM_TRANSACTION));
    }
    
    @GetMapping("/requests/in-review/{id}/view")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getInReviewTierRequestById(@PathVariable("id") Integer reviewId){
        return success(opsAttributeRequestService.getInReviewAttributeRequestById(reviewId, EAttributeType.PROGRAM_TRANSACTION));
    }
    
    @GetMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> getEditTierRequestSetting(@PathVariable("id") Integer requestId){
        return success(opsAttributeRequestService.getEditAttributeRequestSetting(requestId));
    }

    @PostMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestEditingAttributeRequest(@PathVariable("id") Integer requestId, @Valid @RequestBody EditAttributeRequestReq req){
        return success(opsAttributeRequestService.requestEditingAttributeRequest(requestId, req, EAttributeType.PROGRAM_TRANSACTION));
    }

    @GetMapping("/data-type-display")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getDataType(){
        return success(opsAttributeRequestService.getDataTypeDisplay());
    }

}
