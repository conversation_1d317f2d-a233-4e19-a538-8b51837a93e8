package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class CreateProgramReq extends UpdateProgramReq{

    @NotBlank(message = "Program code: must not be blank")
    @JsonProperty("program_code")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "Program code only letters and numbers")
    @Length(max = 16)
    private String programCode;

    @NotNull(message = "Business id: must not be null")
    @JsonProperty("business_id")
    private Integer businessId;
}
