package com.oneid.loyalty.accounting.ops.component.attribute.strategy;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class NumberAttributeValueStrategy extends AttributeValueStrategy<Long> {

    public NumberAttributeValueStrategy(AttributeMasterDataRepository attributeMasterDataRepository) {
        super(attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(EAttributeDataDisplayType type) {
        return Objects.equals(EAttributeDataDisplayType.NUMBER, type);
    }

    @Override
    public Long serialize(Object value, final Integer... programIds) {
        try {
            verifyMasterData(value.toString());
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            throw new BusinessException(ErrorCode.INVALID_ATTRIBUTE_NUMBER_FORMAT,
                    null,
                    null,
                    new Object[]{this.conditionAttributeDto.getAttribute()}
            );
        }
    }

    @Override
    public Long deserialize(String attribute, String value, final Integer... programIds) {
        return Long.parseLong(value);
    }
}