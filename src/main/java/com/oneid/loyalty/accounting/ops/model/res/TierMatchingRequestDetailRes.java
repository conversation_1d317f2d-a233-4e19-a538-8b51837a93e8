package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.*;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierMatchingRequestDetailRes extends VersioningRes {

    private Integer requestId;

    private Integer reviewId;
    
    private Integer businessId;

    private String businessName;

    private Integer baseProgramId;

    private String baseProgramName;

    private Integer matchedProgramId;

    private String matchedProgramName;

    private String tierMatchingCode;

    private String tierMatchingName;

    private String description;

    @JsonSerialize(using =  DateSerializer.class)
    private Date startDate;

    @JsonSerialize(using =  DateSerializer.class)
    private Date endDate;

    private EBoolean autoMatching;

    private EBoolean autoVerify;

    private ETierMappingExpireType expireType;

    private EDurationType durationType;

    private Integer durationValue;

    private List<EVerificationType> verificationType;

    private String rejectedReason;

    private ECommonStatus status;
    
    private EApprovalStatus approvalStatus;

    private List<TierMappingConfigRes> tierMappings;

}
