package com.oneid.loyalty.accounting.ops.constant;

import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ERequestAgentType {
    OPS("OPS"),
    MCS("MCS"),
    POS("POS"),
    OTHER("Other");
    
    private String value;
    
    public static ERequestAgentType lookup(String value) {
        if (StringUtils.isEmpty(value)) return null;
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ERequestAgentType> {
        @Override
        public ERequestAgentType convert(String value) {
            return ERequestAgentType.lookup(value);
        }
    }
    
}
