package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SearchAvailableTransactionReq {
    private Integer businessId;

    private Integer programId;

    private Long batchNo;

    private String batchName;

    private EBatchRequestType batchRequestType;

    private ETransactionBatchType transactionBatchType;

    private EBoolean enableSMS;

    private EBoolean failedRecords;

    private Date createdStart;

    private Date createdEnd;

    private EBatchRequestProcessStatus status;
}
