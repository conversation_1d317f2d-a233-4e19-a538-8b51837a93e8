package com.oneid.loyalty.accounting.ops.model.req;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Convert;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EBoolean;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@Builder
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = EditCounterReq.EditCounterReqBuilder.class)
public class EditCounterReq {
    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    private String name;

    @NotBlank(message = "'edit_key' must not be blank")
    @Length(max = 255)
    private String editKey;

    private String description;

    @NotNull(message = "'enable_revert' must not be null")
    private EBoolean enableRevert;

    @NotNull(message = "'end_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
//    @JsonSerialize(using = DateSerializer.class)
    private Date endDate;

    @NotBlank(message = "counter_status must not blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("counter_status")
    private String counterStatus;

    private EServiceType serviceType;
    
    @Valid
    @NotNull(message = "'rules' must not be null")
    @Size(min = 1, max = 100, message = "'rules' size between 1, 100")
    private List<RuleReq> rules;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class EditCounterReqBuilder {
    }
}
