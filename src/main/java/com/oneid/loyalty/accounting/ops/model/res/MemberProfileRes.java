package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.util.SimpleDateSerializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProductAccountType;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemberProfileRes {
    private Long memberId;
    private String phoneNo;
    private String fullName;
    private String gender;
    private String identifyNo;
    private String status;
    private Integer businessId;
    private Integer programId;
    private String businessCode;
    private String memberCode;
    private String tierName;
    
    @JsonSerialize(using = SimpleDateSerializer.class)
    private Date registrationDate;
    
    EProductAccountType productAccountType;
}

