package com.oneid.loyalty.accounting.ops.model.elasticsearch;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import java.time.OffsetDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@Document(
        indexName = "#{@environment.getProperty('app.elasticsearch.index.scheme-management')}",
        createIndex = false
)
public class SchemeES {

    @Id
    private Integer id;

    private String code;

    private String name;

    private String description;

    private String schemeType;

    private String ruleLogic;

    private Integer poolId;

    private String poolName;

    private String status;

    private Integer programId;

    private String programName;

    private Integer businessId;

    private String businessName;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private OffsetDateTime startDate;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private OffsetDateTime endDate;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private OffsetDateTime createdAt;

    private boolean anyCorporation;

    private boolean anyChain;

    private boolean anyStore;

    private boolean anyTerminal;

    List<?> includedCorporations;

    List<?> excludedCorporations;

    List<?> includedChains;

    List<?> excludedChains;

    List<?> includedStores;

    List<?> excludedStores;

    List<?> includedTerminals;

    List<?> excludedTerminals;
}
