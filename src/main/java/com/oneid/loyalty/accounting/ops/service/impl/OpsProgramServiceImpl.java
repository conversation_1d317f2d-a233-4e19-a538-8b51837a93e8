package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.mapper.ProgramMapper;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramGetAllReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramReq;
import com.oneid.loyalty.accounting.ops.model.res.CorporationInfo;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramListRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramProductRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.entity.ProgramFunction;
import com.oneid.oneloyalty.common.entity.ProgramFunctionProfileAttribute;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import com.oneid.oneloyalty.common.entity.ProgramProduct;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.ProgramCorporationService;
import com.oneid.oneloyalty.common.service.ProgramFunctionProfileAttributeService;
import com.oneid.oneloyalty.common.service.ProgramFunctionService;
import com.oneid.oneloyalty.common.service.ProgramLevelService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpsProgramServiceImpl implements OpsProgramService {

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    ProgramCorporationService programCorporationService;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    ProgramLevelService programLevelService;

    @Autowired
    ProgramFunctionService programFunctionService;

    @Autowired
    FunctionService functionService;

    @Autowired
    ProgramFunctionProfileAttributeService programFunctionProfileAttributeService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ProgramProductService programProductService;

    @Override
    public ProgramRes getOne(Integer programId) {

        ProgramRes result = ProgramRes.valueOfWithVersionDetails(getProgram(programId));

        List<ProgramCorporation> programCorporations = programCorporationService.findByProgramId(programId);
        result = setCorporationsInfo(result,
                programCorporations.stream().map(t -> t.getCorporationId()).collect(Collectors.toSet())
        );
        setBusinessNameFromProgramRes(result);
        return result;
    }

    @Override
    @Transactional
    public ProgramRes create(CreateProgramReq programReq) {
        if (programReq.getListCorporationId() == null) {
            programReq.setListCorporationId(new HashSet<>());
        }
        Program program = ProgramMapper.toProgram(programReq);
        validationProgramCodeDoesNotExist(programReq.getBusinessId(), programReq.getProgramCode());
        program = programService.create(program);
        createCorporationForProgram(program.getId(),
                programReq.getListCorporationId());

        ProgramRes result = ProgramRes.valueOf(program);

        result = setCorporationsInfo(result, programReq.getListCorporationId());
        setBusinessNameFromProgramRes(result);
        return result;
    }

    @Override
    public MakerCheckerInternalMakerRes createMaker(CreateProgramReq request) {
        businessService.findActive(request.getBusinessId());
        validationProgramCodeDoesNotExist(request.getBusinessId(), request.getProgramCode());
        validationProgramCodeDoesNotExistInOtherReqPending(request.getBusinessId(), request.getProgramCode());
        validationProgramRefNotSameBusiness(request.getBusinessId(), request.getProgramRef());
        MakerCheckerInternalMakerReq<CreateProgramReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateProgramReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.PROGRAM.getName())
                .requestType(EMakerCheckerType.PROGRAM.getType())
                .payload(request)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program call maker error: " + createMakerProgramCorporation.getMeta().getMessage(), null);
        }
        return createMakerProgramCorporation.getData();
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> detailRes = makerCheckerInternalFeignClient.previewDetail(req.getId());
        if (ObjectUtils.isEmpty(detailRes.getData())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }

        MakerCheckerInternalDataDetailRes detailResData = detailRes.getData();

        if (!EApprovalStatus.PENDING.getValue().equals(detailRes.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (req.getStatus().equals(EApprovalStatus.APPROVED)) {
            if (ObjectUtils.isNotEmpty(detailResData) && ObjectUtils.isNotEmpty(detailResData.getPayload())) {
                CreateProgramReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateProgramReq.class);
                String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
                String createdBy = null;
                String updatedBy;
                if (Objects.nonNull(payload.getProgramId())) {
                    updatedBy = detailResData.getMadeByUserName();
                    Program programForUpdate = getProgram(payload.getProgramId());
                    programForUpdate.setName(payload.getProgramName());
                    programForUpdate.setEnName(payload.getProgramNameEn());
                    programForUpdate.setDescription(payload.getDescription());
                    programForUpdate.setEnDescription(payload.getDescriptionEn());
                    programForUpdate.setStatus(ECommonStatus.of(payload.getStatus()));
                    programForUpdate.setWebsite(payload.getWebsite());
                    programForUpdate.setHotline(payload.getHotline());
                    programForUpdate.setLogoUrl(payload.getLogoUrl());
                    programForUpdate.setProgramRef(payload.getProgramRef());
                    programForUpdate.setAutoRegister(Objects.nonNull(payload.getProgramRef()) ? EBoolean.YES : EBoolean.NO);
                    programForUpdate.setRequestCode(detailResData.getRequestCode());
                    programForUpdate.setVersion(detailResData.getVersion());
                    opsReqPendingValidator.updateInfoChecker(programForUpdate, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
                    programService.update(programForUpdate);
                } else {
                    validationProgramCodeDoesNotExist(payload.getBusinessId(), payload.getProgramCode());
                    Program program = ProgramMapper.toProgram(payload);
                    program.setRequestCode(detailResData.getRequestCode());
                    program.setVersion(detailResData.getVersion());
                    updatedBy = createdBy = detailResData.getMadeByUserName();
                    opsReqPendingValidator.updateInfoChecker(program, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
                    programService.create(program);
                }
            }
        }

        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes = makerCheckerInternalFeignClient.checker(checkerReq);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program corporation call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
    }

    @Override
    public ProgramListRes getEditAttributeRequestSetting(Integer programId) {
        Program program = programService.find(programId).orElseThrow(() ->
                new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", LogData.createLogData().append("program_id", programId))
        );
        Business business = businessService.find(program.getBusinessId()).orElseThrow(() ->
                new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", LogData.createLogData().append("business_id", program.getBusinessId()))
        );
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM.getType(), program.getRequestCode());
        String editKey = opsReqPendingValidator.generateEditKey(program.getRequestCode(), program.getVersion());

        ProgramListRes programListRes = ProgramListRes.valueOf(getProgram(programId));
        programListRes.setBusinessCode(business.getCode());
        programListRes.setBusinessName(business.getName());
        programListRes.setEditKey(editKey);
        if (Objects.nonNull(program.getProgramRef())) {
            Program programRef = programService.find(program.getProgramRef()).orElseThrow(() ->
                    new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program refer not found", LogData.createLogData().append("program_id", program.getProgramRef()))
            );
            programListRes.setProgramRefCode(programRef.getCode());
            programListRes.setProgramRefName(programRef.getName());
        }
        return programListRes;
    }

    @Override
    public MakerCheckerInternalMakerRes requestEditingAttributeRequest(UpdateProgramReq request) {
        // validate program
        Program program = programService.find(request.getProgramId()).orElseThrow(() ->
                new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", LogData.createLogData().append("program_id", request.getProgramId()))
        );
        if (Objects.isNull(program.getRequestCode())) {
            throw new BusinessException(ErrorCode.REQUEST_CODE_NOT_FOUND, "request code not found",
                    LogData.createLogData()
                            .append("program_id", program.getId()));
        }
        opsReqPendingValidator.verifyEditKey(request.getEditKey(), program.getRequestCode(), program.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM.getType(), program.getRequestCode());
        if (Objects.nonNull(request.getProgramRef())) {
            if (program.getId().equals(request.getProgramRef())) {
                throw new BusinessException(ErrorCode.PROGRAM_REF_INVALID, null, null);
            }
            if (!request.getProgramRef().equals(program.getProgramRef())) {
                validationProgramRefNotSameBusiness(program.getBusinessId(), request.getProgramRef());
            }
        }
        MakerCheckerInternalMakerReq<UpdateProgramReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<UpdateProgramReq>builder()
                .requestCode(Objects.nonNull(program.getRequestCode()) ? program.getRequestCode() : UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.PROGRAM.getName())
                .requestType(EMakerCheckerType.PROGRAM.getType())
                .payload(request)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> apiResponse = makerCheckerInternalFeignClient.maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != apiResponse.getMeta().getCode())
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program - request editing attribute request call checker error: " + apiResponse.getMeta().getMessage(), null);
        return apiResponse.getData();
    }

    @Override
    @Transactional
    public ProgramRes update(UpdateProgramReq programReq) {

        Program programForUpdate = getProgram(programReq.getProgramId());

        programForUpdate.setName(programReq.getProgramName());

        if (programReq.getStatus() != null)
            programForUpdate.setStatus(ECommonStatus.of(programReq.getStatus()));

        if (programReq.getDescription() != null)
            programForUpdate.setDescription(programReq.getDescription());

        if (programReq.getListCorporationId() != null) {

            // remove old and add new corporantion
            final Integer old = -1, neww = 1, nothing = 0;

            Map<Integer, Integer> mapCorporationId = new HashMap<>();
            Map<Integer, ProgramCorporation> mapCorporationIdToProgramCorporation = new HashMap<>();
            for (Integer corporationId : programReq.getListCorporationId()) {
                mapCorporationId.put(corporationId, neww);
            }
            List<ProgramCorporation> listProgramCorporation = programCorporationService.
                    findByProgramId(programReq.getProgramId());
            for (ProgramCorporation programCorporation : listProgramCorporation) {
                mapCorporationId.put(programCorporation.getCorporationId(),
                        (mapCorporationId.containsKey(programCorporation.getCorporationId()) ? neww : 0) + old);
                mapCorporationIdToProgramCorporation.put(programCorporation.getCorporationId(), programCorporation);
            }
            List<ProgramCorporation> programCorporationsAddNew = new ArrayList<>();
            List<ProgramCorporation> programCorporationsRemoveOld = new ArrayList<>();
            for (Map.Entry<Integer, Integer> entry : mapCorporationId.entrySet()) {
                if (neww.equals(entry.getValue())) {
                    ProgramCorporation programCorporation = new ProgramCorporation();

                    programCorporation.setProgramId(programReq.getProgramId());
                    programCorporation.setCorporationId(entry.getKey());
                    programCorporation.setStatus(ECommonStatus.ACTIVE);

                    programCorporationsAddNew.add(programCorporation);
                } else if (old.equals(entry.getValue())) {
                    programCorporationsRemoveOld.add(mapCorporationIdToProgramCorporation.get(entry.getKey()));
                } else {
                    // nothing, maybe todo update for version control
                }

            }
            if (programCorporationsAddNew.size() > 0)
                programCorporationService.createAllInOneProgram(programCorporationsAddNew);
            if (programCorporationsRemoveOld.size() > 0)
                programCorporationService.softDeleteAllInOneProgram(programCorporationsRemoveOld);
        }

        ProgramRes result = ProgramRes.valueOf(programService.update(programForUpdate));

        result = setCorporationsInfo(result, programReq.getListCorporationId());

        return result;
    }

    @Override
    public Page<ProgramRes> search(SearchProgramReq searchProgramReq, Pageable pageRequest) {

        SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<>();

        if (searchProgramReq.getStatus() != null) {
            specificationBuilder.add(new SearchCriteria("status", searchProgramReq.getStatus(),
                    SearchOperation.EQUAL));
        }

        if (searchProgramReq.getBusinessId() != null) {
            specificationBuilder.add(new SearchCriteria("businessId", searchProgramReq.getBusinessId(),
                    SearchOperation.EQUAL));
        }

        if (searchProgramReq.getProgramCode() != null) {
            specificationBuilder.add(new SearchCriteria("code", searchProgramReq.getProgramCode(),
                    SearchOperation.EQUAL));
        }

        if (searchProgramReq.getProgramId() != null) {
            specificationBuilder.add(new SearchCriteria("id", searchProgramReq.getProgramId(),
                    SearchOperation.EQUAL));
        }

        if (searchProgramReq.getProgramName() != null) {
            specificationBuilder.add(new SearchCriteria("name", searchProgramReq.getProgramName(),
                    SearchOperation.EQUAL));
        }

        Page<Program> programPage = programService.searchPaging(specificationBuilder, pageRequest);

        return new PageImpl<>(setBusinessNameFromListProgramRes(
                programPage.getContent().stream().map(ProgramRes::valueOf).collect(Collectors.toList()))
                , pageRequest, programPage.getTotalElements());
    }

    @Override
    public Page<ProgramListRes> getAvailableProgramRequests(SearchProgramReq searchProgramReq, Pageable pageRequest) {
        SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<>();
        if (Objects.nonNull(searchProgramReq.getBusinessId())) {
            specificationBuilder.add(new SearchCriteria("businessId", searchProgramReq.getBusinessId(),
                    SearchOperation.EQUAL));
        }
        if (Objects.nonNull(searchProgramReq.getProgramCode())) {
            specificationBuilder.add(new SearchCriteria("code", searchProgramReq.getProgramCode(),
                    SearchOperation.MATCH));
        }
        if (Objects.nonNull(searchProgramReq.getStatus())) {
            specificationBuilder.add(new SearchCriteria("status", searchProgramReq.getStatus(),
                    SearchOperation.EQUAL));
        }
        Page<Program> programPage = programService.searchPaging(specificationBuilder, pageRequest);
        return new PageImpl<>(setBusinessNameFromListProgramListRes(
                programPage.getContent()
                        .stream()
                        .map(ele -> {
                            ProgramListRes programRes = ProgramListRes.valueOf(ele);
                            if (Objects.nonNull(ele.getProgramRef())) {
                                programService.find(ele.getProgramRef()).ifPresent(program -> {
                                    programRes.setProgramRefCode(program.getCode());
                                    programRes.setProgramRefName(program.getName());
                                });
                            }
                            return programRes;
                        }).collect(Collectors.toList()))
                , pageRequest, programPage.getTotalElements());
    }

    private List<ProgramListRes> setBusinessNameFromListProgramListRes(List<ProgramListRes> listProgramRes) {
        if (listProgramRes.isEmpty()) return Collections.emptyList();
        List<Business> businesses = businessService.searchAll(new SpecificationBuilder<Business>());
        final Map<Integer, Business> businessMap = businesses.stream()
                .collect(Collectors.toMap(Business::getId, Function.identity()));
        return listProgramRes.stream().peek(t -> businessMap.computeIfPresent(t.getBusinessId(), (k, v) -> {
            t.setBusinessName(v.getName());
            t.setBusinessCode(v.getCode());
            return v;
        })).collect(Collectors.toList());
    }

    @Override
    public Page<ProgramListRes> getInReviewProgramRequests(EApprovalStatus eApprovalStatus, Integer offset, Integer limit) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM.getType())
                .status(Collections.singletonList(eApprovalStatus.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<ProgramListRes> programRes = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());

        return new PageImpl<>(programRes, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    @Override
    public ProgramListRes getInReviewProgramDetailRequests(Integer id) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(id));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.PROGRAM.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        return this.convertPreview(previewDetailRes.getData());
    }

    private ProgramListRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateProgramReq programCreateReq = this.jsonMapper.convertValue(data.getPayload(), CreateProgramReq.class);
        Business business;
        Program program = null;
        Program programRef = null;
        // TODO improve
        if (Objects.nonNull(programCreateReq.getBusinessId())) {
            business = businessService.find(programCreateReq.getBusinessId()).orElseThrow(() ->
                    new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", LogData.createLogData().append("business_id", programCreateReq.getBusinessId()))
            );
        } else {
            program = programService.find(programCreateReq.getProgramId()).orElseThrow(() ->
                    new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", LogData.createLogData().append("program_id", programCreateReq.getProgramRef()))
            );
            business = businessService.find(program.getBusinessId()).orElseThrow(() ->
                    new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", LogData.createLogData().append("business_id", programCreateReq.getBusinessId()))
            );
        }

        if (Objects.nonNull(programCreateReq.getProgramRef())) {
            programRef = programService.find(programCreateReq.getProgramRef()).orElseThrow(() ->
                    new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program refer not found", LogData.createLogData().append("program_id", programCreateReq.getProgramRef()))
            );
        }
        ProgramListRes result = ProgramListRes.builder()
                .id(data.getId())
                .programId(programCreateReq.getProgramId())
                .programCode(Objects.nonNull(program) ? program.getCode() : programCreateReq.getProgramCode())
                .programName(programCreateReq.getProgramName())
                .programNameEn(programCreateReq.getProgramNameEn())
                .businessId(business.getId())
                .businessCode(business.getCode())
                .businessName(business.getName())
                .logoUrl(programCreateReq.getLogoUrl())
                .website(programCreateReq.getWebsite())
                .hotline(programCreateReq.getHotline())
                .description(programCreateReq.getDescription())
                .descriptionEn(programCreateReq.getDescriptionEn())
                .programRef(programCreateReq.getProgramRef())
                .programRefCode(Objects.nonNull(programRef) ? programRef.getCode() : null)
                .programRefName(Objects.nonNull(programRef) ? programRef.getName() : null)
                .status(ECommonStatus.of(programCreateReq.getStatus()))
                .approvalStatus(data.getStatus())
                .build();
        result.setCreatedAt(data.getMadeDate() != null ? Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant()) : null);
        result.setCreatedBy(data.getMadeByUserName());
        result.setApprovedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null);
        result.setApprovedBy(data.getCheckedByUserName());
        return result;
    }

    @Override
    public List<ProgramDropDownRes> filter(ProgramGetAllReq programGetAllReq) {
        List<Program> programs = null;

        if (StringUtils.isNoneBlank(programGetAllReq.getBusinessCode())) {
            programs = programService.findByBusinessCodeAndStatus(programGetAllReq.getBusinessCode(), programGetAllReq.getStatus());
        } else {
            SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<Program>();

            if (programGetAllReq.getStatus() != null) {
                specificationBuilder.add(new SearchCriteria("status", programGetAllReq.getStatus(),
                        SearchOperation.EQUAL));
            }

            if (programGetAllReq.getBusinessId() != null) {
                specificationBuilder.add(new SearchCriteria("businessId", programGetAllReq.getBusinessId(),
                        SearchOperation.EQUAL));
            }

            programs = programService.searchAll(specificationBuilder);

        }

        return Optional.ofNullable(programs).orElse(Collections.emptyList())
                .stream().map(ProgramDropDownRes::valueOf).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Program> getMapById(Collection<Integer> ids) {
        List<Program> programs = this.programRepository.findAllByIdIn(ids);
        return programs.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t
        ));
    }

    private List<ProgramCorporation> createCorporationForProgram(Integer programId, Set<Integer> listCorporationId) {
        final Integer finalProgramId = programId;
        List<ProgramCorporation> listProgramCorporation = listCorporationId.stream()
                .map(t -> {
                    ProgramCorporation programCorporation = new ProgramCorporation();
                    programCorporation.setProgramId(finalProgramId);
                    programCorporation.setCorporationId(t);
                    programCorporation.setStatus(ECommonStatus.ACTIVE);
                    return programCorporation;
                }).collect(Collectors.toList());
        return programCorporationService.createAllInOneProgram(listProgramCorporation);
    }

    private Program getProgram(Integer programId) {
        Program program = programService.find(programId).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", programId)
        );
        return program;
    }

    private ProgramRes setBusinessNameFromProgramRes(ProgramRes programRes) {
        Business business = businessService.findActive(programRes.getBusinessId());

        programRes.setBusinessName(business.getName());

        return programRes;
    }

    private List<ProgramRes> setBusinessNameFromListProgramRes(List<ProgramRes> listProgramRes) {
        if (listProgramRes.isEmpty()) return Collections.emptyList();
        List<Business> businesses = businessService.searchAll(new SpecificationBuilder<Business>());
        final Map<Integer, Business> businessMap = businesses.stream()
                .collect(Collectors.toMap(Business::getId, Function.identity()));
        return listProgramRes.stream().peek(t -> businessMap.computeIfPresent(t.getBusinessId(), (k, v) -> {
            t.setBusinessName(v.getName());
            t.setBusinessCode(v.getCode());
            return v;
        })).collect(Collectors.toList());
    }

    private void validationProgramCodeDoesNotExist(Integer businessId, String programCode) {
        Program checker = programService.find(businessId, programCode).orElse(null);
        if (checker != null) {
            throw new BusinessException(ErrorCode.PROGRAM_CODE_EXISTED,
                    "[VALIDATION PROGRAM CODE] program code existed", programCode);
        }
    }

    private void validationProgramCodeDoesNotExistInOtherReqPending(Integer businessId, String programCode) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        Optional<CreateProgramReq> any = previewRes.getData().stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), CreateProgramReq.class))
                .filter(ele -> Objects.nonNull(ele.getProgramCode()))
                .filter(ele -> Objects.nonNull(ele.getBusinessId()))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramCode().equals(programCode))
                .findAny();
        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_CODE_EXISTED,
                    "[VALIDATION PROGRAM CODE] program code existed in other requests pending", programCode);
        }
    }

    private void validationProgramRefNotSameBusiness(Integer businessId, Integer programRefId) {
        if (Objects.nonNull(businessId) && Objects.nonNull(programRefId)) {
            Program checker = programService.findActive(programRefId);
            if (!Objects.equals(checker.getBusinessId(), businessId)) {
                throw new BusinessException(ErrorCode.PROGRAM_REF_INVALID,
                        "[VALIDATION PROGRAM REFER] program refer not the same business", programRefId);
            }
        }
    }

    private ProgramRes setCorporationsInfo(ProgramRes programRes, Set<Integer> corporationIds) {
        List<Corporation> corporations = corporationService.findByBusinessId(programRes.getBusinessId());
        Set<CorporationInfo> corporationInfos = corporations.stream()
                .filter(corporation -> corporationIds.contains(corporation.getId()))
                .map(CorporationInfo::valueOf)
                .collect(Collectors.toSet());
        programRes.setCorporationsInfo(corporationInfos);
        return programRes;
    }

    @Override
    public ProgramDetailRes detail(Integer programId) {
        Program program = getProgram(programId);
        Business business = businessService.findActive(program.getBusinessId());
        List<ProgramCorporation> programCorporations = programCorporationService.findByProgramId(program.getId());
        programCorporations = programCorporations.stream().filter(item -> item.getStatus() == ECommonStatus.ACTIVE).collect(Collectors.toList());
        List<Corporation> corporations = new ArrayList<>();
        programCorporations.stream().forEach(item -> {
            Optional<Corporation> corOption = corporationService.find(item.getCorporationId());
            if (corOption.isPresent()) {
                corporations.add(corOption.get());
            }
        });

        List<ProgramLevel> programLevels = programLevelService.findAllByProgramId(program.getId());
        List<ProgramFunction> programFunctions = programFunctionService.findByProgramId(program.getId());
        List<com.oneid.oneloyalty.common.entity.Function> functions = functionService.findAllActiveFunction();
        List<ProgramFunctionProfileAttribute> profileAttributes = programFunctionProfileAttributeService.findAllByProgramId(programId);

        ProgramDetailRes res = ProgramDetailRes.valueOf(program);
        res.withProgramCorporations(programCorporations, corporations);
        res.withProgramLevels(programLevels);
        res.withProgramFunctions(programFunctions, functions, profileAttributes);
        res.withBusiness(business);
        if (program.getProgramRef() != null) {
            Program referentProgram = getProgram(program.getProgramRef());
            res.withRefProgram(referentProgram);
        }
        return res;
    }

    @Override
    public List<ProgramProductRes> getListProgramProductByProgramId(Integer programId) {
        List<ProgramProductRes> response = new ArrayList<>();
        List<ProgramProduct> programProducts = programProductService.getAllActiveByProgramId(programId);

        Map<String, ProgramProduct> mapProgramProduct = new HashMap<>();
        for (ProgramProduct programProduct : programProducts) {
            if (mapProgramProduct.get(programProduct.getIdType().getValue()) == null) {
                mapProgramProduct.put(programProduct.getIdType().getValue(), programProduct);
            }
        }

        ProgramProductRes productProgramUserId = ProgramProductRes.builder()
                .name(EOpsIdType.USER_ID.getValue())
                .idType(EOpsIdType.USER_ID.getValue())
                .build();
        ProgramProductRes productProgramMemberCode = ProgramProductRes.builder()
                .name(EOpsIdType.MEMBER_CODE.getValue())
                .idType(EOpsIdType.MEMBER_CODE.getValue())
                .build();
        ProgramProductRes productProgramIdentify = ProgramProductRes.builder()
                .name(EOpsIdType.IDENTIFY.getValue())
                .idType(EOpsIdType.IDENTIFY.getValue())
                .build();
        response.add(productProgramUserId);
        response.add(productProgramMemberCode);
        response.add(productProgramIdentify);

        for (String key : mapProgramProduct.keySet()) {
            ProgramProduct p = mapProgramProduct.get(key);
            EOpsIdType idType = EOpsIdType.lookup(p.getIdType().getValue());
            ProgramProductRes programProductRes = ProgramProductRes.builder()
                    .name(idType.getValue())
                    .idType(idType.getValue())
                    .build();
            response.add(programProductRes);
        }
        return response;
    }
}