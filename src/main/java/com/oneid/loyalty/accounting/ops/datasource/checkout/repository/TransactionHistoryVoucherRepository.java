package com.oneid.loyalty.accounting.ops.datasource.checkout.repository;

import com.oneid.loyalty.accounting.ops.datasource.checkout.entity.TransactionHistoryVoucher;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TransactionHistoryVoucherRepository extends JpaRepository<TransactionHistoryVoucher, Long> {
    Optional<TransactionHistoryVoucher> findByTransactionRef(String transactionRef);
}
