package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MakerCheckerInternalPreviewReq {

    private String requestType;

    private String requestCode;

    private List<String> status = new ArrayList<>();

    private String madeByUserName;

    private RangeDateReq madeDate;

    private String checkedByUserName;

    private RangeDateReq checkedDate;

    public MakerCheckerInternalPreviewReq(String requestType, String requestCode, List<String> status) {
        this.requestType = requestType;
        this.requestCode = requestCode;
        this.status = status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RangeDateReq {
        String fromDate;
        String toDate;

        public static RangeDateReq valueOf(String fromDate, String toDate) {
            RangeDateReq rangeDateReq = new RangeDateReq();
            rangeDateReq.setFromDate(fromDate);
            rangeDateReq.setToDate(toDate);
            return rangeDateReq;
        }
    }
}