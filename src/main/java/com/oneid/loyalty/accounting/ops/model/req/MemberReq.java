package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;

import com.oneid.oneloyalty.common.validation.PersonName;
import com.oneid.oneloyalty.common.validation.Phone;
import com.oneid.oneloyalty.common.validation.ValidEmail;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public abstract class MemberReq {
    @NotBlank(message = "First name must not be blank")
    @PersonName(message = "First name invalid", minWord = 1)
    private String firstName;

    @NotBlank(message = "Last name must not be blank")
    @PersonName(message = "Last name invalid", minWord = 1)
    private String lastName;

    @NotBlank(message = "Full name must not be blank")
    @PersonName(message = "Full name invalid")
    private String fullName;
    
    @PersonName(message = "Mid name invalid", minWord = 0)
    private String midName;

    @NotNull(message = "Identify type must not be null")
    private EIdentifyType identifyType;

    @NotBlank(message = "Identify no must not be blank")
    @Pattern(regexp = "^(?!^0+$)[a-zA-Z0-9]{3,20}$", message = "Identify no must match ^(?!^0+$)[a-zA-Z0-9]{3,20}$")
    private String identifyNo;

    @Pattern(regexp = "^(M|F|U){1}$", message = "Gender must be (M|F|U)")
    private String gender;

    private String dob;

    @ValidEmail
    private String email;

    private String houseNumber;

    private String homePhone;

    private String officePhone;

    @Phone(message = "Referral code invalid") // same phone no
    private String referralCode;

    private String countryCode;

    private String provinceCode;

    private String districtCode;

    private String wardCode;

    private String address;

    private String street;

    private String job;
    
    private Integer tierId;

    private String registrationDate;

    private String status;
}