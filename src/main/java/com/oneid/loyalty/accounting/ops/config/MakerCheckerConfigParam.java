package com.oneid.loyalty.accounting.ops.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class MakerCheckerConfigParam {
    @Value("${maker-checker.module.gift-card-production-request:gcpr}")
    public String GIFT_CARD_PRODUCTION_REQUEST;

    @Value("${maker-checker.module.card-transfer}")
    public String CARD_TRANSFER_MODULE;

    @Value("${maker-checker.module.scheme}")
    public String SCHEME_MODULE;

    @Value("${maker-checker.module.operator:operator}")
    public String OPERATOR_MODULE;

    @Value("${maker-checker.action.create}")
    public String ACTION_TYPE_CREATE;

    @Value("${maker-checker.action.update}")
    public String ACTION_TYPE_UPDATE;
}
