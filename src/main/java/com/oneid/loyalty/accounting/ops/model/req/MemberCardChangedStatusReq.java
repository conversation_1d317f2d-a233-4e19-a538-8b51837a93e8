package com.oneid.loyalty.accounting.ops.model.req;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.sun.istack.NotNull;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = MemberCardChangedStatusReq.MemberCardChangedStatusReqBuilder.class)
public class MemberCardChangedStatusReq {
    @NotNull
    private ECardStatus status;
    
    @Length(max = 255)
    private String reason;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class MemberCardChangedStatusReqBuilder {
    }
}