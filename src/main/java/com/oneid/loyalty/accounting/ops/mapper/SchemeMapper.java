package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifySchemeInfoReq;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.entity.Scheme;

public class SchemeMapper {

    public static Scheme toSchemeCreate(CreateSchemeReq req) {
        Scheme scheme = new Scheme();
        return buildSchemeFromReqCreate(scheme, req);
    }

    public static void setValuesFromReqUpdate(Scheme scheme, CreateSchemeReq req) {
        scheme.setStatus(req.getStatus());
        scheme.setIsVAT(req.getIsVAT() != null ? req.getIsVAT() : scheme.getIsVAT());
        String schemeCode = scheme.getCode();
        buildSchemeFromReqCreate(scheme,req);
        scheme.setCode(schemeCode); // cannot update scheme code
    }

    private static void buildSchemeFromReqVerify(Scheme scheme, VerifySchemeInfoReq req) {
        scheme.setProgramId(req.getProgramId());
        scheme.setPoolId(req.getPoolId());
        scheme.setCode(req.getSchemeCode());
        scheme.setName(req.getSchemeName());
        scheme.setDescription(req.getDescription());
        scheme.setSchemeType(req.getSchemeType());
        scheme.setStartDate(DateTimes.toDate(req.getStartDate()));
        scheme.setEndDate(DateTimes.toDate(req.getEndDate()));
    }

    private static Scheme buildSchemeFromReqCreate(Scheme scheme, CreateSchemeReq req) {
        buildSchemeFromReqVerify(scheme, req);
        scheme.setRuleLogic(req.getRuleLogic());
        scheme.setMaxPoint(req.getMaxAmount());
        scheme.setMinPoint(req.getMinAmount());
        scheme.setRoundingRule(req.getRoundingRule());
        scheme.setIsVAT(req.getIsVAT());
        return scheme;
    }
}
