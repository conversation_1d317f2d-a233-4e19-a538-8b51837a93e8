package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.EOpsCardExpireUnit;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.util.DateTimes;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.*;

@Getter
@Setter
@Valid
public class CardPolicyCreateReq {

    @OpsName(message = "name invalid character")
    @Length(max = 255, message = "'Name' cannot exceed 255 characters")
    @NotBlank(message = "'Name' cannot be empty")
    private String name;

    @OpsName(message = "description invalid character")
    @Length(max = 255, message = "'Description' cannot exceed 255 characters")
    private String description;

    @NotNull(message = "Card policy type must not be null")
    private ECardPolicyType type;

    @NotNull(message = "business_id must not be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull(message = "Length must not be null")
    @Max(value = 24, message = "Length must not be greater than {value}")
    @Min(value = 16, message = "Length must not be less than {value}")
    private Integer length;

    @Positive
    @Max(value = 10000, message = "max_card_per_cpr max is 10000")
    @NotNull(message = "max_card_per_cpr must not be null")
    @JsonProperty("max_card_per_cpr")
    private Integer maxCardPerCPR;

    @NotNull(message = "card_expiration_type must not be null")
    @JsonProperty("card_expiration_type")
    private EOpsCardExpireUnit cardExpirationType;

    @JsonProperty("card_expiration_value")
    private Long cardExpirationValue;

    @NotNull(message = "card_expiration_trigger must not be null")
    @JsonProperty("card_expiration_trigger")
    private ECardPolicyTrigger cardExpirationTrigger;

    @NotNull(message = "card_retention_type must not be null")
    @JsonProperty("card_retention_type")
    private ERetensionType cardRetentionType;

    @JsonProperty("card_retention_value")
    private Long cardRetentionValue;

    @NotNull(message = "card_retention_trigger must not be null")
    @JsonProperty("card_retention_trigger")
    private ECardPolicyTrigger cardRetentionTrigger;

    @NotNull(message = "status must not be null")
    private ECommonStatus status;

    @JsonProperty("has_checksum")
    private EHasChecksum hasChecksum;

    @JsonProperty("qr_url")
    private String qrUrl;

    @AssertTrue(message = "card_expiration_value must not be null")
    public boolean isCardExpirationValueNotNul() {
        return !(
                EOpsCardExpireUnit.DAY.equals(this.cardExpirationType)
                        ||  EOpsCardExpireUnit.MONTH.equals(this.cardExpirationType)
                        ||  EOpsCardExpireUnit.YEAR.equals(this.cardExpirationType)
                        ||  EOpsCardExpireUnit.FIXED_TIME.equals(this.cardExpirationType)
        ) ||  this.cardExpirationValue != null;
    }

    @AssertTrue(message = "card_expiration_value max value of year, month, day equal 99")
    public boolean isCardExpirationValueMaxValueYMD() {
        return !(
                EOpsCardExpireUnit.DAY.equals(this.cardExpirationType)
                        ||  EOpsCardExpireUnit.MONTH.equals(this.cardExpirationType)
                        ||  EOpsCardExpireUnit.YEAR.equals(this.cardExpirationType)
        ) ||  this.cardExpirationValue.compareTo(100l) < 0;
    }

    @AssertTrue(message = "card_expiration_value max value of type fixed time equal 31/12/2999")
    public boolean isCardExpirationValueMaxValueFIXED_TIME() {
        return !(
                EOpsCardExpireUnit.FIXED_TIME.equals(this.cardExpirationType)
        ) ||  this.cardExpirationValue.compareTo(DateTimes.toEpochSecond(DateTimes.timeUnlimited())) < 0;
    }

    @AssertTrue(message = "card_retention_value must not be null")
    public boolean isCardRetentionValueNotNul() {
        return ERetensionType.END_DAY.equals(this.cardRetentionType) ||  this.cardRetentionValue != null;
    }

    @AssertTrue(message = "status invalid")
    public boolean isValidStatus() {
        return ECommonStatus.ACTIVE.equals(status) || ECommonStatus.INACTIVE.equals(status);
    }
}
