package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.req.UpdateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveReq;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

@Getter
@Setter
public class UpdateBusinessMCReq extends AbsGetApproveReq {
    @JsonProperty("payload")
    private UpdateBusinessReq payload;

    @Override
    public HttpEntity build(HttpHeaders headers) {
        return new HttpEntity<>(this, headers);
    }
}