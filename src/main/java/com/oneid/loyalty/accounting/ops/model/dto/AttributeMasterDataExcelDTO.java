package com.oneid.loyalty.accounting.ops.model.dto;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;
import lombok.Data;

@Data
public class AttributeMasterDataExcelDTO {
    @ExcelRow
    private int rowIndex;

    @ExcelCellName("VALUE")
    private String value;

    @ExcelCellName("DESCRIPTION")
    private String description;

    @ExcelCellName("STATUS")
    private String attributeStatus;

    private Long memberId;

    private Boolean isValid;

    private String status = OPSConstant.VALID;

    private String errorMessage;

    private String format;
}
