package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SearchControlFileHistoryReq {
    private String fileName;

    private EProcessingStatus status;

    private List<EFileType> fileTypes;

    private Date createdAtFrom;

    private Date createdAtTo;

    private Date updatedAtFrom;

    private Date updatedAtTo;

    private Integer offSet;

    private Integer limit;
}
