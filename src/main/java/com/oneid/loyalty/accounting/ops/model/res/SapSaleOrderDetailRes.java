package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class SapSaleOrderDetailRes {
    private String sapCustomer;

    private String sapSaleOrder;

    private String point;

    private String status;

    private Date postingDate;

    private String remark;

    private String type;

    private Date calledAt;

    private Response response;

    private String createdBy;

    private Date createdAt;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public class Response {
        @JsonProperty("SALESORDER")
        private String saleOrder;

        @JsonProperty("SALESORDER_REF")
        private String saleOrderRef;

        @JsonProperty("STATUS")
        private String status;

        @JsonProperty("DESCRIPTION")
        private String description;
    }
}
