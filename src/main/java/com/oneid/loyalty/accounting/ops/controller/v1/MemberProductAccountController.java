package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.service.OpsMemberProductAccountService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v1/member-product-account")
public class MemberProductAccountController extends BaseController {

    @Autowired
    OpsMemberProductAccountService opsMemberProductAccountService;

    @Authorize(role = AccessRole.MEMBER_PRODUCT_ACCOUNT, permissions = {AccessPermission.VIEW })
    @GetMapping("member/{member_id}")
    public ResponseEntity<?> listMemberProductAccount(@PathVariable("member_id") Long memberId) {
        return success(opsMemberProductAccountService.getListMemberProductAccount(memberId));
    }
}