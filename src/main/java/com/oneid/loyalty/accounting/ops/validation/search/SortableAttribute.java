package com.oneid.loyalty.accounting.ops.validation.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Sort;

import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public abstract class SortableAttribute {

    protected static Map<String, String> mapPropsToAttribute = new HashMap<>();

    abstract protected void initMapPropsToAttribute();
    abstract protected String getSortByDefault();

    @JsonProperty("sort_by")
    protected String sortBy;

    @JsonProperty(value = "sort_direction")
    protected Sort.Direction sortDirection = Sort.Direction.ASC;

    public final Sort getSort() {
        initMapPropsToAttribute();
        validationSortByAttribute();
        String sortBy = (getSortBy() == null ? getSortByDefault() : mapPropsToAttribute.get(getSortBy()));
        return Sort.by(getSortDirection(),sortBy);
    }

    private void validationSortByAttribute() {
        if(sortBy != null && !mapPropsToAttribute.containsKey(sortBy))
            throw new ConstraintViolationException(String.format("Cannot sort by %s", sortBy), null);
    }
}