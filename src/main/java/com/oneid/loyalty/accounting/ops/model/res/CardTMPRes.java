package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CardTMPRes {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("card_no")
    private String cardNo;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("cpr_batch_no")
    private long cprBatchNo;

    @JsonProperty("issue_date")
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date issueDate;

    @JsonProperty("name_on_card")
    private String nameOnCard;

    @JsonProperty("description")
    private String description;

    @JsonProperty("card_status")
    private String cardStatus;

    @JsonProperty("card_type_id")
    private Integer cardTypeId;

    @JsonProperty("status")
    private String status;
}
