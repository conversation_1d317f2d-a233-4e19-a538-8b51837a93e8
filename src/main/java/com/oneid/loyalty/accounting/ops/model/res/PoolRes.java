package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Id;
import java.util.Map;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class PoolRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @JsonProperty("currency_name")
    private String currencyName;

    @JsonProperty("balance_retention_policy_id")
    private Integer retentionPolicyId;

    @JsonProperty("balance_expire_policy_id")
    private Integer balanceExpiredPolicyId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("negative_balance")
    private String negativeBalance;

    @JsonProperty("allow_refund_remaining_balance")
    private String allowRefundRemainingBalance;

    public static PoolRes of(Pool pool, Business business, Program program, Currency currency) {
        return new PoolRes(
                pool.getId(),
                pool.getCode(),
                pool.getBusinessId(),
                business != null ? business.getName() : null,
                pool.getCurrencyId(),
                currency != null ? currency.getName() : null,
                pool.getRetentionPolicyId(),
                pool.getBalanceExpiredPolicyId(),
                pool.getName(),
                pool.getDescription(),
                pool.getStatus().getValue(),
                pool.getCreatedBy(),
                pool.getUpdatedBy(),
                pool.getApprovedBy(),
                pool.getCreatedAt() != null ? pool.getCreatedAt().toInstant().getEpochSecond() : null,
                pool.getUpdatedAt() != null ? pool.getUpdatedAt().toInstant().getEpochSecond() : null,
                pool.getApprovedAt() != null ? pool.getApprovedAt().toInstant().getEpochSecond() : null,
                pool.getCreatedYmd(),
                pool.getProgramId(),
                program != null ? program.getName() : null,
                pool.getNegativeBalance() != null ? pool.getNegativeBalance().getValue() : EBoolean.NO.getValue(),
                pool.getAllowRefundRemainingBalance() != null ? pool.getAllowRefundRemainingBalance().getValue() : EBoolean.NO.getValue()
        );
    }
}
