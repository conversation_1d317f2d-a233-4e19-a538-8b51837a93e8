package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.model.req.*;
import com.oneid.loyalty.accounting.ops.feign.model.res.GCTExistFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.GCTExportFeignRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.res.CardMemberFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;

@FeignClient(name = "oneloyalty-card-service", url = "${oneloyalty.card-service.url}",
        configuration = OpsCommonOneLoyaltyFeignConfig.class)
public interface CardServiceFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/member-card/detail")
    APIResponse<CardMemberFeignRes> getMemberCardDetail(@RequestParam(name = "id") Integer id);

    @RequestMapping(method = RequestMethod.POST, value = "/member/add-card")
    APIResponse<?> addMemberCard(AddCardMemberFeignReq req);

    @RequestMapping(method = RequestMethod.PUT, value = "/member-card/update")
    APIResponse<?> updateMemberCard(UpdateCardFeignReq req);

    @RequestMapping(method = RequestMethod.GET, value = "/gift-card/detail")
    APIResponse<?> getGiftCardDetail(
            @RequestParam(name = "id") Long id,
            @RequestParam(name = "program_code") String programCode,
            @RequestParam(name = "business_code") String businessCode,
            @RequestParam(name = "store_code") String storeCode);

    @RequestMapping(method = RequestMethod.POST, value = "/gift-card/gcpr/approve")
    APIResponse<?> createCPR(@RequestBody CreateCPRFeignReq req);

    @RequestMapping(method = RequestMethod.POST, value = "/gift-card/gct/approve")
    APIResponse<?> createGCT(@RequestBody CreateGCTFeignReq req);

    @RequestMapping(method = RequestMethod.GET, value = "/gift-card/gct/export")
    APIResponse<GCTExportFeignRes> exportFeature(@RequestParam(name = "path") String path);

    @RequestMapping(method = RequestMethod.POST, value = "/gift-card/gct/verify")
    APIResponse<GCTExistFeignRes> verifyGCR(@RequestBody CreateGCTFeignReq req);

    @RequestMapping(method = RequestMethod.POST, value = "member/add-card/validate")
    APIResponse<?> verifyMemberCard(VerifyMemberCardReq req);
    
    @RequestMapping(method = RequestMethod.POST, value = "member-card/cpr/verify")
    APIResponse<?> verifyCardProductionRequest(CreateMemberCPRFeignReq request);
    
    @RequestMapping(method = RequestMethod.GET, value = "member-card/cpr")
    Object getCardProductionRequestPage(
            @RequestParam(name = "business_id") Integer businessId, 
            @RequestParam(name = "corporation_id") Integer corporationId, 
            @RequestParam(name = "chain_id") Integer chainId,
            @RequestParam(name = "store_id") Integer storeId, 
            @RequestParam(name = "batch_no") Long batchNo, 
            @RequestParam(name = "status") String status, 
            @RequestParam(name = "offset") int offset, 
            @RequestParam(name = "limit") int limit);
    
    @RequestMapping(method = RequestMethod.GET, value = "member-card/temp-cards")
    Object getMemberCardTempPage(
            @RequestParam(name = "business_code") String businessCode, 
            @RequestParam(name = "program_code") String programCode, 
            @RequestParam(name = "store_code") String storeCode, 
            @RequestParam(name = "batch_no") Integer batchNo, 
            @RequestParam(name = "card_no") String cardNo, 
            @RequestParam(name = "status") String status,
            @RequestParam(name = "offset") Integer offset, 
            @RequestParam(name = "limit") Integer limit);
    
}
