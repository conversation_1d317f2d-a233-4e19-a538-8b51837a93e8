package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransactionSapSaleOrderUpdateReq {
    @NotBlank(message = "'sap_sale_order' cannot be blank")
    private String sapSaleOrder;
}
