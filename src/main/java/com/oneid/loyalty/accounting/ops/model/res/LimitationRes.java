package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LimitationRes {
    private Integer id;
    private Integer requestId;
    private Integer reviewId;
    private Integer limitationId;
    private ShortEntityRes business;
    private Integer counterId;
    private ShortEntityRes program;
    private Integer version;
    private Integer nextVersion;
    private String name;
    private String description;
    private String code;
    private String rejectedReason;
    private ECommonStatus status;
    private ECommonStatus requestStatus;
    private BigDecimal threshold;
    private BigDecimal warningThreshold;
    private EBoolean allowWithRemainingValue;
    private EBoolean allowResetCounter;

    private Integer numberDayCounterExpire;
    private String counterName;
    private CounterShortInformationRes counter;

    @JsonSerialize(using =  DateSerializer.class)
    private Date startDate;

    @JsonSerialize(using =  DateSerializer.class)
    private Date endDate;

    private EApprovalStatus approvalStatus;

    private List<RuleRes> rules;

    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    private Long createdAt;
    private Long updatedAt;
    private Long approvedAt;
}
