package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.oneloyalty.common.constant.EBatchRequestProcessStatus;
import com.oneid.oneloyalty.common.constant.EBatchRequestType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransactionSearchRes extends OtherInfo implements Serializable {

    private static final long serialVersionUID = -5800921566604521162L;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private String campaignCode;

    private Long batchNo;

    private String batchName;

    private EBatchRequestType batchRequestType;

    private ETransactionBatchType transactionBatchType;

    private EBoolean enableSMS;

    private Integer failedRecords;

//    private Date createdAt;

    private String description;

    private Date generateDate;

    private EBatchRequestProcessStatus processStatus;

    private String referenceCode;

    private EBoolean isReplaceDes;

    private EGenerationInvoiceNoMethod genInvoiceNoMethod;

    private ECharacterSet genInvoiceNoCharacterSet;

    private String genInvoiceNoPattern;

    private EGenerationTransactionTimeMethod genTransactionTimeMethod;

    @JsonDeserialize(using = DateDeserializer.class)
    private Date genTransactionTimeValue;

    private String smsTemplate;

}
