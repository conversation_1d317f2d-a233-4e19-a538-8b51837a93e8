package com.oneid.loyalty.accounting.ops.support.feign.decoder;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;

import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.http.HttpStatus;

import com.oneid.loyalty.accounting.ops.support.feign.FeignResponse;
import com.oneid.oneloyalty.common.exception.BusinessException;

import feign.FeignException;
import feign.FeignException.FeignClientException;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;

@SuppressWarnings({"rawtypes", "unchecked"})
public class ResponseErrorHandlingDecoder extends ResponseEntityDecoder implements ErrorDecoder {
    private List<ResponseErrorHandlingVerifier> errorHandlingVerifiers;

    public ResponseErrorHandlingDecoder(Decoder decoder,  List<ResponseErrorHandlingVerifier> errorHandlingVerifiers) {
        super(decoder);
        this.errorHandlingVerifiers = errorHandlingVerifiers;
    }
    
    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        Object obj = super.decode(response, type);
        
        errorHandlingVerifiers.stream()
        .filter(verifier -> verifier.isApplicable(type))
        .findFirst()
        .ifPresent(verifier -> verifier.verify(response.request(), (FeignResponse) obj));
        
        return obj;
    }

    @Override
    public Exception decode(String methodKey, Response response) {
        if (response.status() == HttpStatus.NOT_FOUND.value()) {
            return new BusinessException(HttpStatus.NOT_FOUND.value(), null, null);
        }
        return FeignClientException.errorStatus(methodKey, response);
    }

}
