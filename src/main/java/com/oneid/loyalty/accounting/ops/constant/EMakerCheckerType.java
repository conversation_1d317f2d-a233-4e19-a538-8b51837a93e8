package com.oneid.loyalty.accounting.ops.constant;

public enum EMakerCheckerType {
    PROGRAM("Program", "Program", "Program"),
    PROGRAM_FUNCTION("ProgramFunction", "ProgramFunction", "ProgramFunction"),
    PROGRAM_CORPORATION("ProgramCorporation", "ProgramCorporation", "ProgramCorporation"),
    PROGRAM_LEVEL("ProgramLevel", "ProgramLevel", "ProgramLevel"),
    COUNTER("Counter", "Counter", "Counter"),
    GIFT_CARD_PRODUCTION_REQUEST("GiftCardProductionRequest", "GiftCardProductionRequest", "GiftCardProductionRequest"),
    GIFT_CARD_TRANSFER("GiftCardTransfer", "GiftCardTransfer", "GiftCardTransfer"),
    PROGRAM_TIER_POLICY("ProgramTierPolicy", "ProgramTierPolicy", "ProgramTierPolicy"),
    PROGRAM_TIER("ProgramTier", "ProgramTier", "ProgramTier"),
    LIMITATION("Limitation", "Limitation", "Limitation"),
    MEMBER_ATTRIBUTE("MemberAttribute", "MemberAttribute", "MemberAttribute"),
    PROGRAM_TRANSACTION_ATTRIBUTE("ProgramTransactionAttribute", "ProgramTransactionAttribute", "ProgramTransactionAttribute"),
    MEMBER_STATUS("MemberStatus", "MemberStatus", "MemberStatus"),
    MEMBER_STATUS_TRANSITION("MemberStatusTransition", "MemberStatusTransition", "MemberStatusTransition"),
    SCHEME("Scheme", "Scheme", "Scheme"),
    ;

    private final String code;

    private final String name;

    private final String type;

    EMakerCheckerType(String code, String name, String type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }
}
