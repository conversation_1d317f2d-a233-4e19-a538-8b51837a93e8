package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class TierPolicyRes {
    private Integer requestId;
    private Integer id;
    private Integer tierPolicyId;
    private String businessName;
    private Integer businessId;
    private String programCode;
    private Integer programId;
    private String programName;
    private String name;
    private String code;
    private String description;
    private List<TierPolicyCounter> counters;
    private List<String> qualificationEvent;
    private List<Event> events;
    private ECounterPeriod periodCycle;
    private Integer periodCycleValue;
    private List<String> expirationDates;
    private ECommonStatus status;
    private EApprovalStatus approvalStatus;
    private String editKey;
    private Integer tierDefaultId;
    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    private String reviewedBy;
    private Long reviewedAt;
    private Long createdAt;
    private Long updatedAt;
    private Long approvedAt;
    private Long startDate;
    private Long endDate;

    @Getter
    @Builder
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TierPolicyCounter {
        private Integer counterId;
        private String counterCode;
        private Integer numberDayCounterExpired;
        private String counterName;
    }

    @Getter
    @Builder
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Event {
        private String name;
        private String code;
    }
}
