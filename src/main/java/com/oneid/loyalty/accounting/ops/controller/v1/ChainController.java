package com.oneid.loyalty.accounting.ops.controller.v1;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateChainReq;
import com.oneid.loyalty.accounting.ops.model.res.ChainEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.ChainRes;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/chains")
public class ChainController extends BaseController {
    @Autowired
    OpsChainService opsChainService;

    @GetMapping("enum-all")
    public ResponseEntity<?> getEnumAll() {
        List<ChainEnumAll> result = opsChainService.getEnumAll();
        return success(result);
    }

    @GetMapping("")
    @Authorize(role = AccessRole.CHAIN, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterChains(@RequestParam(name = "business_id", required = false) Integer businessId,
                                          @RequestParam(name = "corporation_id", required = false) Integer corporationId,
                                          @RequestParam(name = "chain_name", required = false) String chainName,
                                          @RequestParam(name = "chain_code", required = false) String chainCode,
                                          @RequestParam(name = "status", required = false)
                                          @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                          @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                          @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<ChainRes> page = opsChainService.filter(businessId, corporationId, chainName, chainCode, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.CHAIN, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getChain(@PathVariable(value = "id") Integer id) {
        ChainRes result = opsChainService.get(id);
        return success(result);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.CHAIN, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateChain(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid ChainUpdateReq chainUpdateReq) {
        this.opsChainService.update(id, chainUpdateReq);
        return success(null);
    }
    
    @PostMapping("")
    @Authorize(role = AccessRole.CHAIN, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createChain(@RequestBody @Valid CreateChainReq req) {
        return success(this.opsChainService.create(req));
    }
}