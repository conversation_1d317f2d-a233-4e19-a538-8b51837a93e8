package com.oneid.loyalty.accounting.ops.feign.model.req;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor()
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateCPRFeignReq {

    private Integer cprId;
}
