package com.oneid.loyalty.accounting.ops.util;

import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * author viet.le2
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Assert {

    public static void assertTrue(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, message, null);
        }
    }

    public static void assertTrue(boolean expression, ErrorCode errorCode) {
        if (expression) {
            throw new BusinessException(errorCode);
        }
    }

    public static void assertNotNull(Object obj, String message) {
        assertTrue(Objects.isNull(obj), message);
    }

}
