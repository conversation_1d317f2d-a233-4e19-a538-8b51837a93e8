package com.oneid.loyalty.accounting.ops.feign.model.res;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.util.EpochDateDeserializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberProfileDetailFeignRes {
    private Long id;
    private String businessId;
    private String programId;
    private String phoneNo;
    private String fullName;
    private String gender;
    private String identifyNo;
    private EIdentifyType identifyType;
    private String status;
    private String memberCode;
    private String memberStatus;
    private String address;
    private String dob;
    private String email;
    private String firstName;
    private String lastName;
    private String houseNumber;
    private String street;
    private String countryId;
    private String wardId;
    private String districtId;
    private String provinceId;
    private String homePhone;
    private String officePhone;
    private String referralCode;
    private Integer storeRegisterId;
    private Integer tierId;
    
    @JsonDeserialize(using = EpochDateDeserializer.class)
    private Date registrationDate;
    
    private String job;
}
