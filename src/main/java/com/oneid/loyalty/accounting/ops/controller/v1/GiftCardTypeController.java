package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardBinRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardBinService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardTypeService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping(value = "v1/giftcardtypes")
@Validated
public class GiftCardTypeController  extends BaseController {
    @Autowired
    OpsGiftCardTypeService opsGiftCardTypeService;

    @GetMapping("")
    @Authorize(role = AccessRole.GIFT_CARD_TYPE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(@RequestParam(name = "business_id", required = false) Integer businessId,
                                    @RequestParam(name = "program_id", required = false) Integer programId,
                                    @RequestParam(name = "card_type_code", required = false) String code,
                                    @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                    @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<GiftCardTypeRes> page = opsGiftCardTypeService.filter(businessId, programId, code, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.GIFT_CARD_TYPE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getGiftCardType(@PathVariable(value = "id") Integer id) {
        GiftCardTypeRes result = opsGiftCardTypeService.get(id);
        return success(result);
    }

    @PostMapping
    @Authorize(role = AccessRole.GIFT_CARD_TYPE, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createGiftCardType(@RequestBody @Valid GiftCardTypeCreateReq giftCardTypeCreateReq) {
        opsGiftCardTypeService.add(giftCardTypeCreateReq);
        return success(null);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.GIFT_CARD_TYPE, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateGiftCardType(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid GiftCardTypeUpdateReq giftCardTypeUpdateReq) {
        opsGiftCardTypeService.update(id, giftCardTypeUpdateReq);
        return success(null);
    }
}
