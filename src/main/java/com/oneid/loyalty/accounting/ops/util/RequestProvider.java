package com.oneid.loyalty.accounting.ops.util;

import com.oneid.loyalty.accounting.ops.service.outbound.model.data.AbstractReq;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class RequestProvider {
    private static ThreadLocal<Map<String, Object>> result = new ThreadLocal<Map<String, Object>>();
    public static void reset() {
        result.set(null);
    }
    public static void set(Map<String, Object> value) {
        result.set(value);
    }
    public static Map<String, Object> get() {
        return result.get();
    }
    public static void add(String key, Object value) {
        Map<String, Object> data = result.get();
        if(data == null) {
            data = new ConcurrentHashMap<String, Object>();
        }
        data.put(key, value);
        result.set(data);
    }
    public static String getRequestID() {
        Map map = RequestProvider.get();
        String requestID = "";
        if (map != null) {
            Object o = map.get(AbstractReq.KEY_X_REQUEST_ID);
            requestID = o == null ? "" : o.toString();
        }
        if (StringUtils.isEmpty(requestID)) {
            requestID = UUID.randomUUID().toString();
        }
        return requestID;
    }
}