package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.TimeStamp;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

@Getter
@Setter
public class VerifySchemeInfoReq {

    @Positive
    @NotNull(message = "Program id must not be null")
    @JsonProperty("program_id")
    private Integer programId;

    @NotBlank(message = "Scheme code: must not be blank")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "Scheme code only letters and numbers")
    @Length(max = 16, message = "Scheme code: length must be between 1 and 16")
    @JsonProperty("scheme_code")
    private String schemeCode;

    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @NotBlank(message = "Scheme name: must not be blank")
    @Length(max = 255, message = "Scheme name: length must be between 1 and 255")
    @JsonProperty("scheme_name")
    private String schemeName;

    @Length(max = 255, message = "Scheme description: length must be between 1 and 255")
    private String description;

    @NotNull(message = "Scheme type must not be null")
    @JsonProperty("scheme_type")
    private ESchemeType schemeType;

    @NotNull(message = "Pool must not be null")
    @JsonProperty("pool_id")
    private Integer poolId;


    @TimeStamp
    @NotNull(message = "Start date must not be null")
    @JsonProperty("start_date")
    private Long startDate;

    @TimeStamp
    @NotNull(message = "End date must not be null")
    @JsonProperty("end_date")
    private Long endDate;

    @AssertTrue(message = "End date must be greater than start date")
    public boolean isValidStartAndEndDate() {
        if (this.startDate == null || this.endDate == null) {
            return false;
        }
        return endDate > startDate;
    }
}