package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class GiftCardRequestRes extends BaseMakerCheckerDetail<GiftCardRequest> {

    public GiftCardRequestRes() {
    }

    public GiftCardRequestRes(GiftCardRequest request) {
        super(request);
    }

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("store")
    private DropdownRes store;

    @JsonProperty("program")
    private DropdownRes program;

    @JsonProperty("no_of_card")
    private Integer noOfCard;

    private String description;

    @JsonProperty("gc_bin")
    private DropdownRes giftCardBin;

    @JsonProperty("gc_type")
    private CardTypeRes giftCardType;

    @JsonProperty("gc_policy")
    private DropdownRes giftCardPolicy;

    @JsonProperty("gc_suffix")
    private String giftCardSuffix;

    @JsonProperty("generation_ind")
    private EGiftCardIndicator generationIndicator;

    @JsonSerialize(converter = EpochTimeSerialize.class)
    @JsonProperty("generation_date")
    private Date generationDate;

    @JsonProperty("init_gc_status")
    private EGiftCardStatus initGcStatus;

    @JsonProperty("batch_no")
    private Long batchNo;

    private ECommonStatus status;

    @AllArgsConstructor
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class CardTypeRes {
        private Integer id;
        private String code;
        private String name;
        private Double price;
    }
}