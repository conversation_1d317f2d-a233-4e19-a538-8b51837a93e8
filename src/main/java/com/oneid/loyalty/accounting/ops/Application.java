package com.oneid.loyalty.accounting.ops;

import com.oneid.loyalty.accounting.ops.config.PackageCommonConfig;
import com.oneid.loyalty.accounting.ops.support.data.databind.OffsetSetting;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

@SpringBootApplication(exclude = { RedisAutoConfiguration.class, RedisRepositoriesAutoConfiguration.class })
@Import(PackageCommonConfig.class)
@EnableFeignClients(basePackages = { "com.oneid.loyalty.accounting.ops.feign" })
@EnableConfigurationProperties(value = { OffsetSetting.class })
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}