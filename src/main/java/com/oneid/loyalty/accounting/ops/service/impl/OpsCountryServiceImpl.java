package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.CountryDTO;
import com.oneid.loyalty.accounting.ops.service.OpsCountryService;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.CountryService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpsCountryServiceImpl implements OpsCountryService {

    @Autowired
    private CountryService countryService;

    @Override
    public CountryDTO getOne(Integer id) {
        final Country country = this.countryService.getById(id).orElseThrow(() -> new BusinessException(
                ErrorCode.COUNTRY_NOT_FOUND, "Country is not found", LogData.createLogData().append("country", id))
        );
        return CountryDTO.valueOf(country);
    }
}
