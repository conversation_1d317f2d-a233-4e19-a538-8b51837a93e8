package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardBinService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;


@RestController
@RequestMapping(value = "v1/cardbins")
@Validated
public class CardBinController extends BaseController {
    @Autowired
    OpsCardBinService opsCardBinService;

    @GetMapping("")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_CARD_BIN, permissions = {AccessPermission.VIEW }),
            @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW }),
    }, any = true)
    public ResponseEntity<?> filter(@RequestParam(name = "business_id", required = false) Integer businessId,
                                              @RequestParam(name = "program_id", required = false) Integer programId,
                                              @RequestParam(name = "bin_code", required = false) String binCode,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<CardBinRes> page = opsCardBinService.filter(businessId, programId, binCode, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_BIN, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCardBin(@PathVariable(value = "id") Integer id) {
        CardBinRes result = opsCardBinService.get(id);
        return success(result);
    }

    @PostMapping
    @Authorize(role = AccessRole.MEMBER_CARD_BIN, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createCardBin(@RequestBody @Valid CardBinCreateReq cardBinCreateReq) {
        opsCardBinService.add(cardBinCreateReq);
        return success(null);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_BIN, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCardBin(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid CardBinUpdateReq cardBinUpdateReq) {
        opsCardBinService.update(id, cardBinUpdateReq);
        return success(null);
    }

}
