package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.EditAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.DataTypeDisplayRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeRequestService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.AttributeRequest;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeRequestRepository;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class OpsAttributeRequestServiceImpl implements OpsAttributeRequestService {
    @Value("${maker-checker.module.member-attribute}")
    private String moduleMemberId;

    @Value("${maker-checker.module.transaction-attribute}")
    private String moduleTransactionId;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private AttributeRequestRepository attributeRequestRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private SystemAttributeRequestRepository systemAttributeRequestRepository;

    private TransactionTemplate transactionTemplate;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public Integer requestCreatingAttributeRequest(CreateAttributeRequestReq req) {
        AttributeRequest createRequest = createRequest(req);

        String moduleId = null;

        switch (req.getAttributeType()) {
            case MEMBER:
                moduleId = moduleMemberId;
                break;
            case PROGRAM_TRANSACTION:
                moduleId = moduleTransactionId;
                break;

            default:
                break;
        }

        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(createRequest.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(Integer.parseInt(createRequest.getId().toString()))
                        .build())
                .build();

        makerCheckerServiceClient.changes(feignReq);

        return Integer.parseInt(createRequest.getId().toString());
    }

    @Override
    public Page<AttributeRequestRes> getAvailableAttributeRequests(EAttributeType type,
                                                                   Integer businessId,
                                                                   Integer programId,
                                                                   String code,
                                                                   String name,
                                                                   ECommonStatus status,
                                                                   EApprovalStatus approvalStatus,
                                                                   Pageable pageable) {

        ECommonStatus requestStatus = null;

        if (approvalStatus != null) {
            switch (approvalStatus) {
                case APPROVED:
                    requestStatus = ECommonStatus.ACTIVE;
                    break;

                case REJECTED:
                    requestStatus = ECommonStatus.INACTIVE;
                    break;

                default:
                    requestStatus = ECommonStatus.PENDING;
                    break;
            }
        }

        Page<Object[]> res = attributeRequestRepository.
                findAvailableRequest(
                        type,
                        businessId,
                        programId,
                        code, name, status, requestStatus,
                        approvalStatus,
                        pageable);

        return new PageImpl<AttributeRequestRes>(mapToDTO(res.getContent(), Collections.emptyMap()), pageable, res.getTotalElements());
    }

    @Override
    public Page<AttributeRequestRes> getInReviewAttributeRequests(
            EAttributeType type,
            EApprovalStatus approvalStatus,
            Integer fromDate,
            Integer toDate,
            Pageable pageable) {

        String moduleId = "";
        switch (type) {
            case MEMBER:
                moduleId = moduleMemberId;
                break;
            case PROGRAM_TRANSACTION:
                moduleId = moduleTransactionId;
                break;
        }

        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromDate,
                toDate);
        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        List<AttributeRequestRes> res = null;

        if (changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeginResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each, (first, second) -> second));

            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());
            res = mapToDTO(attributeRequestRepository.findBy(requestIds, type), changeRecordFeginResMap);
        } else {
            res = Collections.emptyList();
        }
        return new PageImpl<AttributeRequestRes>(res, pageable, changeRequestPageFeignRes.getTotalRecordCount());
    }

    @Override
    public AttributeRequestRes getAvailableAttributeRequestById(Integer requestId, EAttributeType type) {
        AttributeRequest res = attributeRequestRepository.findByIdAndAttributeType(requestId, type)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND, null, null));

        Program program = programRepository.findById(res.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        Business business = businessRepository.findById(res.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        return AttributeRequestRes.builder()
                .id(res.getId())
                .code(res.getCode())
                .name(res.getName())
                .approvalStatus(res.getApprovalStatus())
                .attributeType(res.getAttributeType())
                .dataType(res.getDataType())
                .dataTypeDisplay(res.getDataTypeDisplay())
                .programId(res.getProgramId())
                .programName(program.getName())
                .businessId(business.getId())
                .businessName(business.getName())
                .operators(res.getOperators())
                .regexValidation(res.getRegexValidation())
                .status(res.getStatus())
                .lastUpdateAt(res.getApprovedAt())
                .lastUpdateBy(res.getApprovedBy())
                .createdAt(res.getCreatedAt())
                .createdBy(res.getCreatedBy())
                .build();
    }

    @Override
    public AttributeRequestRes getInReviewAttributeRequestById(Integer reviewId, EAttributeType type) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> apiResponse = makerCheckerServiceClient.getChangeRequestById(reviewId.toString());
        AttributeRequest res = attributeRequestRepository.findByIdAndAttributeType(Integer.parseInt(apiResponse.getData().getObjectId()), type)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND, null, null));

        Program program = programRepository.findById(res.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        Business business = businessRepository.findById(res.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        return AttributeRequestRes.builder()
                .id(Integer.parseInt(res.getId().toString()))
                .code(res.getCode())
                .name(res.getName())
                .approvalStatus(res.getApprovalStatus())
                .attributeType(res.getAttributeType())
                .dataType(res.getDataType())
                .dataTypeDisplay(res.getDataTypeDisplay())
                .programId(res.getProgramId())
                .programName(program.getName())
                .businessId(business.getId())
                .businessName(business.getName())
                .operators(res.getOperators())
                .regexValidation(res.getRegexValidation())
                .status(res.getStatus())
                .lastUpdateAt(res.getApprovedAt())
                .lastUpdateBy(res.getApprovedBy())
                .createdAt(res.getCreatedAt())
                .createdBy(res.getCreatedBy())
                .build();
    }

    @Override
    public Integer requestEditingAttributeRequest(Integer requestId, EditAttributeRequestReq req, EAttributeType type) {
        AttributeRequest attributeRequest = transactionTemplate.execute(status -> {
            AttributeRequest entity = editAttributeRequest(requestId, req, type);
            return entity;
        });

        String moduleId = "";
        switch (type) {
            case MEMBER:
                moduleId = moduleMemberId;
                break;
            case PROGRAM_TRANSACTION:
                moduleId = moduleTransactionId;
                break;
        }
        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(attributeRequest.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(Integer.parseInt(attributeRequest.getId().toString()))
                        .build())
                .build();

        makerCheckerServiceClient.changes(feignReq);

        return Integer.parseInt(attributeRequest.getId().toString());
    }

    @Override
    public AttributeRequestRes getEditAttributeRequestSetting(Integer requestId) {
        AttributeRequest availableRequest = attributeRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND, null, null));
        EApprovalStatus approvalStatus = availableRequest.getApprovalStatus();

        if (approvalStatus.equals(EApprovalStatus.PENDING))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);

        if (approvalStatus.equals(EApprovalStatus.REJECTED))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_CAN_NOT_BE_EDITED, null, null);
        if (!availableRequest.getRequestStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, null, null);

        Program program = programRepository.findById(availableRequest.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        Business business = businessRepository.findById(availableRequest.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        if (!attributeRequestRepository.findPendingVersion(availableRequest.getProgramId(),
                availableRequest.getCode(),
                availableRequest.getAttributeType()).isEmpty())
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);

        return AttributeRequestRes.builder()
                .id(Integer.parseInt(availableRequest.getId().toString()))
                .code(availableRequest.getCode())
                .name(availableRequest.getName())
                .approvalStatus(availableRequest.getApprovalStatus())
                .attributeType(availableRequest.getAttributeType())
                .dataType(availableRequest.getDataType())
                .dataTypeDisplay(availableRequest.getDataTypeDisplay())
                .programId(availableRequest.getProgramId())
                .businessId(business.getId())
                .businessName(business.getName())
                .operators(availableRequest.getOperators())
                .regexValidation(availableRequest.getRegexValidation())
                .status(availableRequest.getStatus())
                .lastUpdateAt(availableRequest.getApprovedAt())
                .lastUpdateBy(availableRequest.getApprovedBy())
                .programName(program.getName())
                .createdAt(availableRequest.getCreatedAt())
                .createdBy(availableRequest.getCreatedBy())
                .build();
    }

    private AttributeRequest createRequest(CreateAttributeRequestReq req) {

        Program program = programRepository.findById(req.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, null, null);

        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        if (business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, null, null);

        attributeRequestRepository.findByCodeAndProgramId(req.getCode(), req.getProgramId()).forEach(
                attributeRequest -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_EXISTED, null, null,
                            new Object[]{attributeRequest.getCode(), attributeRequest.getAttributeType()});
                }
        );

        systemAttributeRequestRepository.findExistedAttribute(req.getCode()).forEach(
                attributeRequest -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_EXISTED, null, null,
                            new Object[]{attributeRequest.getCode(), EAttributeType.SYSTEM});
                }
        );

        EAttributeDataTypeDisplay dataTypeDisplay = req.getDataTypeDisplay();

        if (!dataTypeDisplay.hasDataType(req.getDataType()))
            throw new BusinessException(ErrorCode.ATTRIBUTE_DATA_TYPE_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY,
                    null,
                    null,
                    new Object[]{req.getDataType()});

        req.getOperators()
                .stream()
                .filter(operator -> !dataTypeDisplay.hasOperator(operator))
                .findAny()
                .ifPresent(operator -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_OPERATOR_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY,
                            null,
                            null,
                            new Object[]{operator.getExpression()});
                });

        List<String> operators = req.getOperators().stream()
                .map(EAttributeOperator::getExpression)
                .collect(Collectors.toList());

        AttributeRequest request = new AttributeRequest();

        request.setCode(req.getCode());
        request.setName(req.getName());
        request.setRequestStatus(ECommonStatus.PENDING);
        request.setStatus(ECommonStatus.ACTIVE);
        request.setApprovalStatus(EApprovalStatus.PENDING);
        request.setAttributeType(req.getAttributeType());
        request.setDataType(req.getDataType().name());
        request.setDataTypeDisplay(req.getDataTypeDisplay().name());
        request.setDescription(req.getDescription());
        request.setProgramId(req.getProgramId());
        request.setBusinessId(req.getBusinessId());
        request.setOperators(operators);
        request.setRegexValidation(req.getRegexValidation());

        attributeRequestRepository.save(request);

        return request;
    }

    private AttributeRequest editAttributeRequest(Integer requestId, EditAttributeRequestReq req, EAttributeType type) {
        AttributeRequest attributeRequest = attributeRequestRepository.findByIdAndAttributeType(requestId, type)
                .orElseThrow(() -> new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_NOT_FOUND, null, null));
        EApprovalStatus approvalStatus = attributeRequest.getApprovalStatus();

        AttributeRequest createRequest = new AttributeRequest();

        if (approvalStatus.equals(EApprovalStatus.PENDING))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);

        if (approvalStatus.equals(EApprovalStatus.REJECTED))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_CAN_NOT_BE_EDITED, null, null);

        if (!attributeRequest.getRequestStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, null, null);

        if (!attributeRequestRepository.findPendingVersion(attributeRequest.getProgramId(),
                attributeRequest.getCode(),
                attributeRequest.getAttributeType()).isEmpty())
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);

        EAttributeDataTypeDisplay dataTypeDisplay = EAttributeDataTypeDisplay.lookup(attributeRequest.getDataTypeDisplay());
        req.getOperators()
                .stream()
                .filter(operator -> !dataTypeDisplay.hasOperator(operator))
                .findAny()
                .ifPresent(operator -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_OPERATOR_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY,
                            null,
                            null,
                            new Object[]{operator.getExpression()});
                });

        List<String> givenOperators = req.getOperators().stream()
                .map(EAttributeOperator::getExpression)
                .distinct()
                .collect(Collectors.toList());

        attributeRequest.getOperators().stream()
                .filter(s -> !givenOperators.contains(s))
                .findAny()
                .ifPresent(a -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_OPERATOR_CAN_NOT_REMOVE,
                            null,
                            null, new Object[]{a});
                });

        createRequest.setCode(attributeRequest.getCode());
        createRequest.setName(req.getName());
        createRequest.setStatus(ECommonStatus.ACTIVE);
        createRequest.setAttributeType(attributeRequest.getAttributeType());
        createRequest.setDataType(attributeRequest.getDataType());
        createRequest.setDataTypeDisplay(attributeRequest.getDataTypeDisplay());
        createRequest.setDescription(attributeRequest.getDescription());
        createRequest.setProgramId(attributeRequest.getProgramId());
        createRequest.setBusinessId(attributeRequest.getBusinessId());
        createRequest.setRequestStatus(ECommonStatus.PENDING);
        createRequest.setApprovalStatus(EApprovalStatus.PENDING);
        createRequest.setVersion(attributeRequest.getVersion());
        createRequest.setRegexValidation(req.getRegexValidation());
        createRequest.setOperators(givenOperators);

        attributeRequestRepository.save(createRequest);

        return createRequest;

    }

    private List<AttributeRequestRes> mapToDTO(List<Object[]> entities, Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeginResMap) {
        List<AttributeRequestRes> attributeRequestRes = new ArrayList<>();
        Program program = null;
        Business business = null;
        AttributeRequest attributeRequest = null;
        Integer reviewId = null;
        for (Object[] a : entities) {
            attributeRequest = AttributeRequest.class.cast(a[0]);
            program = Program.class.cast(a[1]);
            business = Business.class.cast(a[2]);
            reviewId = changeRecordFeginResMap.containsKey(attributeRequest.getId()) ? changeRecordFeginResMap.get(attributeRequest.getId()).getChangeRequestId() : null;

            attributeRequestRes.add(AttributeRequestRes.builder()
                    .id(attributeRequest.getId())
                    .reviewId(reviewId)
                    .code(attributeRequest.getCode())
                    .name(attributeRequest.getName())
                    .approvalStatus(attributeRequest.getApprovalStatus())
                    .attributeType(attributeRequest.getAttributeType())
                    .dataType(attributeRequest.getDataType())
                    .dataTypeDisplay(attributeRequest.getDataTypeDisplay())
                    .operators(attributeRequest.getOperators())
                    .regexValidation(attributeRequest.getRegexValidation())
                    .status(attributeRequest.getStatus())
                    .lastUpdateAt(attributeRequest.getApprovedAt())
                    .lastUpdateBy(attributeRequest.getApprovedBy())
                    .programName(program.getName())
                    .programId(program.getId())
                    .businessId(business.getId())
                    .businessName(business.getName())
                    .createdAt(attributeRequest.getCreatedAt())
                    .createdBy(attributeRequest.getCreatedBy())
                    .build());
        }
        return attributeRequestRes;
    }

    @Override
    public List<DataTypeDisplayRes> getDataTypeDisplay() {
        return Arrays.stream(EAttributeDataTypeDisplay.values())
                .filter(each -> each.isAvailableForSystemField() == false)
                .map(dataTypeDisplay ->
                        DataTypeDisplayRes.builder()
                                .dataTypeDisplay(dataTypeDisplay)
                                .dataTypes(dataTypeDisplay.getDataTypes())
                                .operators(dataTypeDisplay.getOperators())
                                .build())
                .collect(Collectors.toList());
    }
}