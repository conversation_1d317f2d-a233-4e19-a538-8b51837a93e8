package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ResetLimitationReq {

    @NotNull(message = "Reset Value must not be blank")
    @Min(0)
    private BigDecimal resetValue;

    @NotNull(message = "Reset Type must not be blank")
    private String resetType;

    @JsonIgnore
    private Integer limitationId;

    @Length(max = 512)
    private String newDescription;
}