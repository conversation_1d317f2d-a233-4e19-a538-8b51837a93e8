package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECardIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import lombok.Builder;
import lombok.Getter;

import javax.persistence.*;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RequestTransferRes {
    private Integer id;
    private ShortEntityRes business;
    private ShortEntityRes program;
    private ECardTransferStatus status;
    private Long cardTransferNo;
    private Integer cardQuantity;
    private ECardTransferIndicatorStatus transferIndicator;
    private ECardIndicatorStatus indicator;
    private ShortEntityRes fromStore;
    private ShortEntityRes toStore;
    private String description;
    private String deniedReason;
    private Long batchNo;
}