package com.oneid.loyalty.accounting.ops.support.data.databind;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.databind.util.StdConverter;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;

public class AccessControlConverter extends StdConverter<Map<AccessRole, Collection<Integer>>, Map<AccessRole, Long>> {

    @Override
    public Map<AccessRole, Long> convert(Map<AccessRole, Collection<Integer>> value) {
        Map<AccessRole, Long>  permissions = new HashMap<AccessRole, Long>();
        
        value
        .entrySet()
        .stream()
        .forEach(each -> {
            Long code = each.getValue().stream()
                    .map(AccessPermission::lookup)
                    .filter(Objects::nonNull)
                    .mapToLong(AccessPermission::getCode)
                    .sum(); 
            
            permissions.put(each.getKey(), code);
        });
        
        return permissions;
    }
}