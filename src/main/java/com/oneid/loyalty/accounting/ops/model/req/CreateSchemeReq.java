package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.RoundingRule;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class CreateSchemeReq extends VerifySchemeInfoReq {

    private Integer id;

    @NotNull(message = "rule_logic: must not be null")
    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @Min(value = 0, message = "Min amount must be greater than or equal to 0")
    @Max(value = 999999999999999L, message = "Min amount must be less than or equal 999999999999999")
    @JsonProperty("min_amount")
    private Long minAmount;

    @Min(value = 1, message = "Max amount must be greater than or equal to 1")
    @Max(value = 999999999999999L, message = "Max amount must be less than or equal 999999999999999")
    @JsonProperty("max_amount")
    private Long maxAmount;

    @JsonProperty("rounding_rule")
    private RoundingRule roundingRule;

    @Valid
    @Size(min = 0, max = 100, message = "rule_list size between 1, 100")
    @JsonProperty("rule_list")
    private List<RuleRecordReq> ruleList;

    @Valid
    @JsonProperty("formula_group")
    private FormulaGroupReq formulaGroup;

    @AssertTrue(message = "Formula must not be null")
    public boolean isValidFormulaMustNotBeNull() {
        return  this.formulaGroup != null || !ESchemeType.AWARD.equals(this.getSchemeType());
    }

    @AssertTrue(message = "Rounding rule must not be null")
    public boolean isValidRoundingRuleNotNull() {
        return  this.formulaGroup == null || this.roundingRule != null;
    }

    @AssertTrue(message = "Max amount must be greater than min amount")
    public boolean isValidMinMaxValue() {
        if (this.maxAmount == null || this.minAmount == null) {
            return true;
        }
        return maxAmount.compareTo(minAmount) >= 0;
    }


//    @AssertTrue(message = "Max amount, min amount must not be null")
//    public boolean isValidMinMaxValueNotNullForSchemeAward() {
//        return (this.maxAmount != null && this.minAmount != null) || !ESchemeType.AWARD.equals(this.getSchemeType());
//    }

    // Data details for OPS
    @JsonProperty("status")
    private ECommonStatus status;

    @JsonIgnore
    @JsonProperty("business_id")
    private Integer businessId;

    @JsonIgnore
    @JsonProperty("business_name")
    private String businessName;

    @JsonIgnore
    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("pool_name")
    private String poolName;

    @JsonIgnore
    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonIgnore
    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("request_created_at")
    private Long requestCreatedAt;

    @JsonProperty("is_vat")
    private EBoolean isVAT;
}