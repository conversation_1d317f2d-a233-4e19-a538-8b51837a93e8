//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.entity.WlPartner;
//import com.oneid.loyalty.accounting.ops.service.core.CorePartnerService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service
//public class PartnerServiceImpl implements PartnerService {
//    @Autowired
//    private CorePartnerService corePartnerService;
//
//    @Override
//    public List<WlPartner> getAll() {
//        return corePartnerService.getAll();
//    }
//}