package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
import com.oneid.oneloyalty.common.entity.SchemeRule;

import java.util.List;


public interface OpsConditionSchemeService {
    List<ConditionRecordRes> createAllInOneSchemeRule(SchemeRule schemeRule,
                                                      List<ConditionRecordReq> conditions);

    List<ConditionRecordRes> updateAllInOneSchemeRule(SchemeRule schemeRule,
                                                      List<ConditionRecordReq> conditions);
}