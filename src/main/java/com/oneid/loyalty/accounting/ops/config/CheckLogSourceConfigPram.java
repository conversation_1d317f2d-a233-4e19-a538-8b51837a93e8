package com.oneid.loyalty.accounting.ops.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CheckLogSourceConfigPram {
    @Value("${checklog.source.api-wrapper-value}")
    public String SOURCE_API_WRAPPER_VALUE;

    @Value("${checklog.source.mobile.value}")
    public String SOURCE_MOBILE_VALUE;

    @Value("${checklog.source.mobile.default-store-codes}")
    public List<String> SOURCE_MOBILE_DEFAULT_STORE_CODES;
}