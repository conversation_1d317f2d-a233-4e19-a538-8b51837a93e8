package com.oneid.loyalty.accounting.ops.util;

import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;

@Data
public class Versioning {
    public String currentFormat;
    public String basicFormat;

    public String format(@Required Integer intVersion, boolean isCurrent) {

        if (isCurrent) {
            return String.format(this.currentFormat, intVersion);
        }
        return String.format(this.basicFormat, intVersion);

    }
}
