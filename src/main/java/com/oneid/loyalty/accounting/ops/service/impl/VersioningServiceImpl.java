package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.loyalty.accounting.ops.service.VersioningService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.Versioning;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VersioningServiceImpl implements VersioningService {

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private Versioning versioning;

    @Override
    public Page<VersionRes> getVersions(
            String requestCode,
            Integer currentVersion,
            List<EApprovalStatus> approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String checkedBy,
            Integer offset,
            Integer limit) {

        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.COUNTER.getType(),
                        requestCode,
                        approvalStatus != null ? approvalStatus.stream().map(EApprovalStatus::getValue).collect(Collectors.toList()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        checkedBy,
                        checkedDate
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<VersionRes> content = new ArrayList<>();

        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            boolean isCurrent = (currentVersion == item.getVersion()) && EApprovalStatus.APPROVED.getValue().equals(item.getStatus());
            content.add(VersionRes.builder()
                    .createdAt(item.getMadeDate() != null ? Date.from(ZonedDateTime.parse(item.getMadeDate()).toInstant()) : null)
                    .reviewedAt(item.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(item.getCheckedDate()).toInstant()) : null)
                    .createdBy(item.getMadeByUserName())
                    .reviewedBy(item.getCheckedByUserName())
                    .rejectedReason(item.getComment())
                    .status(EApprovalStatus.of(item.getStatus()))
                    .restoredFromId(item.getRestoredFromId())
                    .restoredFromVersion(item.getRestoredFromVersion() == null ? null : versioning.format(item.getRestoredFromVersion(), false))
                    .version(item.getVersion() == null ? null : versioning.format(item.getVersion(), isCurrent))
                    .id(item.getId())
                    .isCurrent(isCurrent)
                    .build());
        }
        Pageable pageable = Pageable.unpaged();
        if (Objects.nonNull(limit) && limit > 0) {
            pageable = new OffsetBasedPageRequest(offset, limit, null);
        }
        return new PageImpl<>(content, pageable, previewRes.getMeta().getTotal());
    }

}
