package com.oneid.loyalty.accounting.ops.service;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;

public interface OpsGenerateTemplateService {
    Path registerMember() throws IOException, OpenXML4JException, SAXException, ParserConfigurationException;
}
