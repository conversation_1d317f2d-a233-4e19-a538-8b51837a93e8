package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DataAuditTrailRes {
    private Long id;
    private String idRef;
    private Long createdAt;
    private String eventType;
    
    @JsonProperty("user_id")
    private String userName;
    
    private Object payload;
    private String source;
    private String additionalSourceInfo;
}
