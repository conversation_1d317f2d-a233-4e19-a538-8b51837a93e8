package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

@Getter
@Setter
public class GiftCardTypeCreateReq {
    @NotBlank
    @Length(max = 8, message = "'Code' max length is 8")
    @Pattern(regexp = "^[0-9]{1,8}$", message = "'Code' is invalid")
    @JsonProperty("code")
    private String code;

    @Length(max = 255, message = "'Description' max length is 255")
    @JsonProperty("description")
    private String description;

    @NotNull(message = "Business not null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull(message = "Program not null")
    @JsonProperty("program_id")
    private Integer programId;

    @Min(0) @Max(9999999999999999L)
    @NotNull(message = "Price not null")
    @JsonProperty("price")
    private Double price;

    @NotNull(message = "Base currency not null")
    @JsonProperty("base_currency_id")
    private Integer baseCurrencyId;

    @Min(0) @Max(9999999999999999L)
    @NotNull(message = "Point not null")
    @JsonProperty("point")
    private Long point;

    @NotNull(message = "Currency not null")
    @JsonProperty("currency_id")
    private Integer currencyId;

    @NotNull(message = "Gift card policy not null")
    @JsonProperty("giftcard_policy_id")
    private Integer giftCardPolicyId;

    @NotBlank
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;
}
