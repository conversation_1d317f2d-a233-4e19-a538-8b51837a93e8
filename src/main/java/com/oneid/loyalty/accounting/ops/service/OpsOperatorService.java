package com.oneid.loyalty.accounting.ops.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.RejectOperatorRequest;
import com.oneid.loyalty.accounting.ops.model.req.SearchOperatorReq;
import com.oneid.loyalty.accounting.ops.model.res.OperatorDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.OperatorGetApproveRes;
import com.oneid.loyalty.accounting.ops.model.res.OperatorRequestRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

public interface OpsOperatorService {
    OperatorGetApproveRes getApprove(CreateOperatorReq request);

    OperatorGetApproveRes getApproveForUpdate(CreateOperatorReq request);

    void getReject(RejectOperatorRequest request);

    OperatorDetailRes getDetail(Integer requestId);

    Page<OperatorRequestRes> filter(SearchOperatorReq request, Pageable pageable);

    void approve(Integer requestId);
    
    Page<OperatorRequestRes> getAvailableOperatorRequests(
            Integer businessId,
            Integer corporationId,
            Integer chainId,
            Integer storeId,
            Integer terminalId,
            String operatorId,
            EApprovalStatus approvalStatus,
            ECommonStatus operatorStatus,
            Pageable pageable);
}