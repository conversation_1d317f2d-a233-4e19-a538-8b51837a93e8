package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Ward;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class WardDTO implements Serializable {
    private Integer id;
    private String code;
    private String name;

    @JsonProperty("en_name")
    private String enName;
    private String description;

    @JsonProperty("en_description")
    private String enDescription;
    private String status;

    @JsonProperty("district_id")
    private Integer districtId;

    public static WardDTO valueOf(Ward entity) {
        WardDTO result = new WardDTO();
        result.setId(entity.getId());
        result.setCode(entity.getCode());
        result.setName(entity.getName());
        result.setEnName(entity.getEnName());
        result.setDescription(entity.getDescription());
        result.setEnDescription(entity.getEnDescription());
        result.setStatus(entity.getStatus().getValue());

        result.setDistrictId(entity.getDistrictId());

        return result;
    }
}
