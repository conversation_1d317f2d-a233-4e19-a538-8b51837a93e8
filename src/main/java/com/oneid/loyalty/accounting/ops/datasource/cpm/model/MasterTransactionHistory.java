package com.oneid.loyalty.accounting.ops.datasource.cpm.model;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ESyncWithElastic;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "master_transaction_history")
@Setter
@Getter
public class MasterTransactionHistory implements Serializable {


    private static final long serialVersionUID = -8391366807489203076L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "transaction_ref")
    private String transactionRef;

    @Column(name = "cus_id")
    private String cusId;

    @Column(name = "cus_id_type")
    @Convert(converter = EIdType.Converter.class)
    private EIdType cusIdType;

    @Column(name = "service_code")
    private String serviceCode;

    @Column(name = "channel_code")
    private String channelCode;

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Column(name = "business_code")
    private String businessCode;

    @Column(name = "program_code")
    private String programCode;

    @Column(name = "corp_code")
    private String corpCode;

    @Column(name = "store_code")
    private String storeCode;

    @Column(name = "chain_code")
    private String chainCode;

    @Column(name = "pos_code")
    private String posCode;

    @Column(name = "pool_code")
    private String poolCode;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "transaction_time")
    private Date transactionTime;

    @Column(name = "transaction_ymd")
    private Long transactionYmd;

    @Column(name = "description")
    private String description;

    @Column(name = "total_redeem_point")
    private BigDecimal totalRedeemPoint;

    @Column(name = "gross_amount")
    private BigDecimal grossAmount;

    @Column(name = "member_code")
    private String memberCode;

    @Column(name = "cancellation")
    @Convert(converter = EBoolean.Converter.class)
    private EBoolean cancellation;

    @Column(name = "cancellation_time")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date cancellationTime;

    @Column(name = "status")
    @Convert(converter = ETransactionStatus.Converter.class)
    private ETransactionStatus status;

    @Column(name = "link_account_status")
    private String linkAccountStatus;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "hash_value")
    private String hashValue;

    @Column(name = "sync_with_elastic")
    @Convert(converter = ESyncWithElastic.Converter.class)
    private ESyncWithElastic syncWithElastic = ESyncWithElastic.NO;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date createdAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "approved_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date approvedAt;

    @Column(name = "created_ymd")
    private Long createdYmd;
}
