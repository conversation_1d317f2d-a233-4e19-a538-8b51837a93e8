package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.DistrictDTO;
import com.oneid.loyalty.accounting.ops.model.res.ProvinceDTO;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.List;

public interface OpsDistrictService {

    /**
     * Get one district by id.
     *
     * @throws BusinessException if entity is not found
     */
    DistrictDTO getOne(Integer id);

    /**
     * Get all districts by province ID
     *
     * @throws BusinessException both absent or present
     */
    List<District> getByProvince(Integer provinceId);

    /**
     * Get all districts by country code and province code
     */
    List<District> getByProvince(String countryCode, String provinceCode);
}
