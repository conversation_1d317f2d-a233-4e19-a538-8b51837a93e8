package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramGetAllReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchProgramReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramReq;
import com.oneid.loyalty.accounting.ops.model.res.*;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.entity.Program;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface OpsProgramService {
    ProgramRes getOne(Integer programId);

    ProgramRes create(CreateProgramReq programReq);

    MakerCheckerInternalMakerRes createMaker(CreateProgramReq programReq);

    void approve(ApprovalReq req);

    ProgramListRes getEditAttributeRequestSetting(Integer reviewId);

    MakerCheckerInternalMakerRes requestEditingAttributeRequest(UpdateProgramReq request);

    ProgramRes update(UpdateProgramReq programReq);

    Page<ProgramRes> search(SearchProgramReq searchProgramReq, Pageable pageRequest);

    Page<ProgramListRes> getAvailableProgramRequests(SearchProgramReq searchProgramReq, Pageable pageRequest);

    Page<ProgramListRes> getInReviewProgramRequests(EApprovalStatus eApprovalStatus, Integer offset, Integer limit);

    ProgramListRes getInReviewProgramDetailRequests(Integer id);

    List<ProgramDropDownRes> filter(ProgramGetAllReq programGetAllReq);

    Map<Integer, Program> getMapById(Collection<Integer> ids);

    ProgramDetailRes detail(Integer programId);

    List<ProgramProductRes> getListProgramProductByProgramId(Integer programId);
}