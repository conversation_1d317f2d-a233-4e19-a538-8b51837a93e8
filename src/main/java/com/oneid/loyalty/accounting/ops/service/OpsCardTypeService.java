package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardTypeRes;
import org.springframework.data.domain.Page;

public interface OpsCardTypeService {
    Page<CardTypeRes> filter(Integer businessId, Integer programId, String cardType, Integer offset, Integer limit);

    CardTypeRes get(Integer id);

    void add(CardTypeCreateReq cardTypeCreateReq);

    void update(Integer id, CardTypeUpdateReq cardTypeUpdateReq);
}
