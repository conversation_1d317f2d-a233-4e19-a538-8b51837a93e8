package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.res.BaseProgramRes;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.validation.Hotline;
import com.oneid.oneloyalty.common.validation.Website;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Convert;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
public class UpdateProgramReq extends BaseProgramRes implements Serializable {

    private static final long serialVersionUID = 5861559221307971861L;

    @JsonProperty("program_id")
    private Integer programId;

    @NotBlank(message = "Program name: must not be blank")
    @JsonProperty("program_name")
    @Length(max = 255, message = "Program name: length must be between 1 and 255")
    private String programName;

    @JsonProperty("program_name_en")
    private String programNameEn;

    @JsonProperty("description")
    @Length(max = 255, message = "Description: length must be between 1 and 255")
    private String description;

    @JsonProperty("description_en")
    private String descriptionEn;

    @Website(message = "'Website' is invalid")
    private String website;

    @Hotline(message = "'Hotline' is invalid")
    private String hotline;

    @JsonProperty("program_ref")
    private Integer programRef;

    @JsonProperty("logo_url")
    private String logoUrl;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("list_corporation_id")
    private Set<Integer> listCorporationId;

    @NotBlank(message = "Status must not blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;

}
