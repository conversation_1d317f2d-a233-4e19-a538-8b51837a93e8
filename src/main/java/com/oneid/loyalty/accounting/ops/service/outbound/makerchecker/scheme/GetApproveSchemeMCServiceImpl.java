package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.scheme;

import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.HttpEntityService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class GetApproveSchemeMCServiceImpl extends AbsGetApproveService implements GetApproveSchemeMCService {
    @Value("${maker-checker.module.scheme}")
    private String module;

    @Value("${maker-checker.action.create}")
    private String create;

    @Value("${maker-checker.action.update}")
    private String update;

    @Value("${maker-checker.param-keys.scheme-id}")
    private String keySchemeId;

    @Override
    public AbsGetApproveRes create(CreateReq req) {
        req.setModule(module);
        req.setActionType(create);
        req.setObjectId(null);
        req.getPayload().setRequestCreatedAt(DateTimes.toEpochSecond(new Date()));
        return createChange(HttpEntityService.getHttpEntity(req));
    }

    @Override
    public AbsGetApproveRes update(UpdateReq req) {
        req.setModule(module);
        req.setActionType(update);
        req.getRequestParamsCallback().put(keySchemeId, req.getPayload().getSchemeId().toString());
        req.setObjectId(req.getPayload().getSchemeId().toString());
        req.getPayload().setRequestCreatedAt(DateTimes.toEpochSecond(new Date()));
        return createChange(HttpEntityService.getHttpEntity(req));
    }
}