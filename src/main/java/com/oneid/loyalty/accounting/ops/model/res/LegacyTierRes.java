package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProgramTierPeriodType;
import com.oneid.oneloyalty.common.entity.ProgramTier;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(value = Include.NON_NULL)
public class LegacyTierRes extends VersionDetails {
    @JsonProperty("tier_code")
    private String tierCode;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("business_id")
    private Integer businessId;

    private String name;

    private Integer id;

    @JsonProperty("status")
    private ECommonStatus status;

    @JsonProperty("description")
    private String description;

    @JsonProperty("card_background_url")
    private String cardBackgroundUrl;

    @JsonProperty("rank_no")
    private Integer rankNo;

    @JsonProperty("expire_period")
    private Integer expirePeriod;

    @JsonProperty("expire_period_type")
    private EProgramTierPeriodType expirePeriodType;

    public static LegacyTierRes valueOf(ProgramTier programTier) {
        LegacyTierRes tierRes = new LegacyTierRes();
        tierRes.setId(programTier.getId());
        tierRes.setProgramId(programTier.getProgramId());
        tierRes.setTierCode(programTier.getTierCode());
        tierRes.setName(programTier.getName());
        tierRes.setStatus(programTier.getStatus());
        tierRes.setDescription(programTier.getDescription());
        tierRes.setCardBackgroundUrl(programTier.getCardBackgroundUrl());
        tierRes.setRankNo(programTier.getRankNo());
        tierRes.setExpirePeriod(programTier.getExpiredPeriod());
        tierRes.setExpirePeriodType(programTier.getExpiredType());
        return tierRes;
    }

    public static LegacyTierRes valueOfWithVersionDetails(ProgramTier programTier) {
        LegacyTierRes tierRes = valueOf(programTier);
        tierRes.setUpdatedBy(programTier.getUpdatedBy());
        tierRes.setCreatedBy(programTier.getCreatedBy());
        if (programTier.getCreatedAt() != null)
            tierRes.setCreatedAt(programTier.getCreatedAt().getTime());
        if (programTier.getUpdatedAt() != null)
        tierRes.setUpdatedAt(programTier.getUpdatedAt().getTime());
        return tierRes;
    }
}
