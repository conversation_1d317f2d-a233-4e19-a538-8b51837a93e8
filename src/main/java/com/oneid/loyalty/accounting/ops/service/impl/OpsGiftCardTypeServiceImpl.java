package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.GiftCardTypeMapper;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardBinRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsCurrencyService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardTypeService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.GiftCardTypeService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OpsGiftCardTypeServiceImpl implements OpsGiftCardTypeService {
    @Autowired
    GiftCardTypeService giftCardTypeService;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsProgramService opsProgramService;

    @Autowired
    OpsCurrencyService opsCurrencyService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    CurrencyRepository currencyRepository;

    @Override
    public Page<GiftCardTypeRes> filter(Integer businessId, Integer programId, String code, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.Direction.DESC, "id");

        SpecificationBuilder<GiftCardType> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (code != null)
            specificationBuilder.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        Page<GiftCardType> giftCardTypes = giftCardTypeService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = giftCardTypes.getContent().stream().map(GiftCardType::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessByIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> programIds = giftCardTypes.getContent().stream().map(GiftCardType::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programByIds = opsProgramService.getMapById(programIds);
        Set<Integer> baseCurrencyIds = giftCardTypes.getContent().stream().map(GiftCardType::getBaseCurrencyId).collect(Collectors.toSet());
        Map<Integer, Currency> baseCurrencyByIds = opsCurrencyService.getMapById(baseCurrencyIds);
        Set<Integer> currencyIds = giftCardTypes.getContent().stream().map(GiftCardType::getCurrencyId).collect(Collectors.toSet());
        Map<Integer, Currency> currencyByIds = opsCurrencyService.getMapById(currencyIds);

        return new PageImpl<GiftCardTypeRes>(giftCardTypes.getContent()
                .stream().map(it -> GiftCardTypeRes.of(
                        it,
                        businessByIds.get(it.getBusinessId()),
                        programByIds.get(it.getProgramId()),
                        baseCurrencyByIds.get(it.getBaseCurrencyId()),
                        currencyByIds.get(it.getCurrencyId())
                )).collect(Collectors.toList()), pageRequest, giftCardTypes.getTotalElements());
    }

    @Override
    public GiftCardTypeRes get(Integer id) {
        Optional<GiftCardType> giftCardType = giftCardTypeService.find(id);
        if (!giftCardType.isPresent())
            throw new BusinessException(ErrorCode.GIFT_CARD_TYPE_NOT_FOUND, "GiftCard type not found in business", LogData.createLogData().append("id", id));
        ;
        return GiftCardTypeRes.of(
                giftCardType.get(),
                businessRepository.findById(giftCardType.get().getBusinessId()).orElse(null),
                programRepository.findById(giftCardType.get().getProgramId()).orElse(null),
                currencyRepository.findById(giftCardType.get().getBaseCurrencyId()).orElse(null),
                currencyRepository.findById(giftCardType.get().getCurrencyId()).orElse(null)
        );
    }

    @Override
    public void add(GiftCardTypeCreateReq giftCardTypeCreateReq) {
        giftCardTypeService.create(GiftCardTypeMapper.toGiftCardTypeOne(giftCardTypeCreateReq));
    }

    @Override
    public void update(Integer id, GiftCardTypeUpdateReq giftCardTypeUpdateReq) {
        GiftCardType giftCardType = giftCardTypeService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.GIFT_CARD_TYPE_NOT_FOUND, "Giftcard type not found in business", LogData.createLogData().append("id", id)));
        giftCardTypeService.update(GiftCardTypeMapper.toGiftCardTypeOne(giftCardType, giftCardTypeUpdateReq));
    }
}
