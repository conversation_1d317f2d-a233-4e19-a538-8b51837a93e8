package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.res.BaseProgramRes;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class UpdateProgramCorporationReq extends BaseProgramRes implements Serializable {

    private static final long serialVersionUID = -3222117438737121601L;
    @NotEmpty(message = "Corporation: must not be blank")
    @JsonProperty("corporations")
    private Set<Integer> corporations = new HashSet<>();

    @JsonProperty("created_by")
    private String createdBy;
}
