package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.NotificationCenterFeignClient;
import com.oneid.loyalty.accounting.ops.feign.Oauth2FeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateGCTFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SendGiftCardTransferFolderPasswordTemplateData;
import com.oneid.loyalty.accounting.ops.feign.model.req.SendSmsInternalReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.GCTExistFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.GCTExportFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.GiftCardTransferAvailableListRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.Oauth2GetTokenRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SendSmsInternalRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.BasicRecipientInfo;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferInReviewDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferringAvailableRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferringInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.SendSmsPasswordFolderRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardTransferService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConfSeq;
import com.oneid.oneloyalty.common.constant.EProcessStatus;
import com.oneid.oneloyalty.common.constant.ESendVia;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import com.oneid.oneloyalty.common.entity.GiftCardTransfer;
import com.oneid.oneloyalty.common.entity.GiftCardTransferBatch;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Recipient;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.repository.GiftCardTransferBatchRepository;
import com.oneid.oneloyalty.common.repository.GiftCardTransferRepository;
import com.oneid.oneloyalty.common.repository.RecipientRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.GiftCardTypeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.SequenceService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsGiftCardTransferServiceImpl implements OpsGiftCardTransferService {

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ChainRepository chainRepository;

    @Autowired
    private CorporationRepository corporationRepository;

    @Autowired
    private GiftCardTypeService giftCardTypeService;


    @Autowired
    private GiftCardRequestRepository giftCardRequestRepository;


    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private RecipientRepository recipientRepository;

    @Autowired
    private GiftCardTransferRepository giftCardTransferRepository;

    @Autowired
    private CardServiceFeignClient cardServiceFeignClient;

    @Autowired
    private GiftCardTransferBatchRepository giftCardTransferBatchRepository;

    @Autowired
    private NotificationCenterFeignClient notificationCenterFeignClient;

    @Value("${app.gift-card-transfer.default-send-sms-password-time-sec}")
    private Long defaultSendSmsPasswordTimeSec;

    @Autowired
    private Oauth2FeignClient oauth2FeignClient;

    @Value("${oauth2.token}")
    private String oauth2Token;

    @Value("${notification-center.template-id-send-password-folder}")
    private String templateId;

    @Autowired
    private Validator validator;

    @Override
    public MakerCheckerInternalMakerRes createNewChangeRequest(CreateGiftCardTransferReq req) {
        businessService.findActive(req.getBusinessId());
        programService.findActive(req.getProgramId());
        updateRecipient(req);

        long nextSeq = sequenceService.getNextSeq(
                EConfSeq.CARD_TRANSFER_NO.getSeqId(req.getBusinessId(), req.getProgramId()));
        validationGCTransferDoesNotExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), nextSeq);
        verifyGCR(req);

        if (!req.getSelectedBatches().isEmpty()) {
            for (CreateGiftCardTransferReq.SelectedBatch batch : req.getSelectedBatches()) {
                GiftCardRequest giftCardRequest = giftCardRequestRepository.findByCprBatchNoAndBusinessIdAndProgramId(batch.getBatchNo(), req.getBusinessId(), req.getProgramId());
                if (Objects.isNull(giftCardRequest)) {
                    throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND);
                }
            }
        }
        req.setTransferNo(nextSeq);
        String pgpPublicKey = req.getPgpPublicKey();
        req.setPgpPublicKey(StringEscapeUtils.escapeJava(pgpPublicKey));
        MakerCheckerInternalMakerReq<CreateGiftCardTransferReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateGiftCardTransferReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.GIFT_CARD_TRANSFER.getName())
                .requestType(EMakerCheckerType.GIFT_CARD_TRANSFER.getType())
                .payload(req)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Gift card transfer call maker error: " + createMakerProgramCorporation.getMeta().getMessage(),
                    null);
        }
        return createMakerProgramCorporation.getData();
    }

    /**
     * Verify batch file exists in SFTP folder
     *
     * @param req
     */
    private void verifyGCR(CreateGiftCardTransferReq req) {
        CreateGCTFeignReq createGCTFeignReq = new CreateGCTFeignReq();
        getSelectedBatchInfo(req.getSelectedBatches(), req.getProgramId());
        List<Long> batchNo = req.getSelectedBatches().stream().map(CreateGiftCardTransferReq.SelectedBatch::getBatchNo).collect(Collectors.toList());
        List<GiftCardRequest> gcrs = giftCardRequestRepository.findByProgramIdAndBatchNo(req.getProgramId(), batchNo);
        Map<Long, GiftCardRequest> giftCardRequestMap = gcrs.stream().collect(Collectors.toMap(GiftCardRequest::getCprBatchNo, g -> g));
        List<CreateGCTFeignReq.SelectedBatch> collect = req.getSelectedBatches().stream().map(ele -> {
            CreateGCTFeignReq.SelectedBatch selectedBatch = new CreateGCTFeignReq.SelectedBatch();
            GiftCardRequest gcr = giftCardRequestMap.get(ele.getBatchNo());
            selectedBatch.setBatchId(gcr.getId());
            selectedBatch.setBatchId(gcr.getId());
            selectedBatch.setCprBatchNo(ele.getBatchNo());
            selectedBatch.setStoreId(ele.getStore().getId());
            selectedBatch.setConvertXlsxFlag(ele.getConvertXlsxFlag());
            return selectedBatch;
        }).collect(Collectors.toList());
        createGCTFeignReq.setSelectedBatches(collect);
        APIResponse<GCTExistFeignRes> response = cardServiceFeignClient.verifyGCR(createGCTFeignReq);
        if (ErrorCode.SUCCESS.getValue() != response.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Call card service error: " + response.getMeta().getMessage(), null);
        } else {
            if (!response.getData().getBatchNos().isEmpty()) {
                throw new BusinessException(ErrorCode.FILE_NOT_FOUND, null, null, new Object[]{String.join(";", response.getData().getBatchNos())});
            }
        }
    }

    private void updateRecipient(CreateGiftCardTransferReq req) {
        if (Objects.nonNull(req.getRecipientCode())) {
            Optional<Recipient> byCode = recipientRepository.findByCode(req.getRecipientCode());
            if (byCode.isEmpty()) {
                createRecipient(req);
                req.setSftpPassword(null);
            }

        } else {
            throw new BusinessException(ErrorCode.RECIPIENT_NOT_FOUND);
        }
    }

    @Override
    public List<BasicRecipientInfo> findRecipientLikeCode(String code) {
        SpecificationBuilder<Recipient> specificationBuilder = new SpecificationBuilder<>();
        if (null != code) {
            specificationBuilder.add(new SearchCriteria("code", code, SearchOperation.MATCH));
        }
        return recipientRepository.findAll(specificationBuilder)
                .stream()
                .map(BasicRecipientInfo::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<BasicRecipientInfo> findRecipientLikeCodeAndName(String value) {
        SpecificationBuilder<Recipient> specificationBuilder = new SpecificationBuilder<>();
        SpecificationBuilder<Recipient> sb = new SpecificationBuilder<>();
        if (null != value) {
            specificationBuilder.add(new SearchCriteria("code", value, SearchOperation.MATCH));
            sb.add(new SearchCriteria("name", value, SearchOperation.MATCH));
        }
        return recipientRepository.findAll(specificationBuilder.or(sb))
                .stream()
                .map(BasicRecipientInfo::new)
                .collect(Collectors.toList());
    }

    private void validationGCTransferDoesNotExistInOtherReqPending(Integer businessId, Integer programId, long transferNo) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.GIFT_CARD_TRANSFER.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        Optional<CreateGiftCardTransferReq> any = previewRes.getData().stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), CreateGiftCardTransferReq.class))
                .filter(ele -> Objects.nonNull(ele.getProgramId()))
                .filter(ele -> Objects.nonNull(ele.getBusinessId()))
                .filter(ele -> Objects.nonNull(ele.getRecipientCode()))
                .filter(ele -> transferNo == ele.getTransferNo() && ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId))
                .findAny();
        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.RECIPIENT_CODE_EXISTED,
                    "[VALIDATION GIFT CARD TRANSFER] transfer no existed in other requests pending", transferNo);
        }
    }

    private void createRecipient(CreateGiftCardTransferReq req) {
        Set<ConstraintViolation<CreateGiftCardTransferReq>> violations = validator.validate(req);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
        Recipient recipient = new Recipient();
        recipient.setCode(req.getRecipientCode());
        recipient.setName(req.getRecipientName());
        recipient.setSendVia(ESendVia.of(req.getSendVia()));
        recipient.setSftpIpAddress(req.getSftpIPAddress());
        recipient.setSftpFolderPath(req.getSftpFolderPath());
        recipient.setSftpUsername(req.getSftpUsername());
        recipient.setSftpPassword(req.getSftpPassword());
        recipient.setEmail(req.getEmailAddress());
        recipient.setSetFolderPassword(req.getArchiveFolder());
        recipient.setReceivePasswordPhoneNo(req.getArchivePhoneReceive());
        recipient.setPgpPublicKey(StringEscapeUtils.unescapeJava(req.getPgpPublicKey()));
        recipient.setEncryptPGP(req.getEncryptPGP());
        recipient.setStatus(ECommonStatus.ACTIVE);
        recipientRepository.save(recipient);
    }

    @Override
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.GIFT_CARD_TRANSFER);
        CreateGiftCardTransferReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateGiftCardTransferReq.class);
        if (Objects.isNull(payload)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "payload must not be null",
                    LogData.createLogData());
        }
        if (!EApprovalStatus.PENDING.getValue().equals(detailResData.getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData.getPayload() != null) {
                String createdBy = null;
                String updatedBy;
                updatedBy = createdBy = detailResData.getMadeByUserName();
                //validate
                Business business = businessService.findActive(payload.getBusinessId());
                Program program = programService.findActive(payload.getProgramId());
                Recipient recipient = recipientRepository.findByCode(payload.getRecipientCode()).orElseThrow(() -> new BusinessException(ErrorCode.RECIPIENT_NOT_FOUND, null, null));
                String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
                GiftCardTransfer giftCardTransfer = new GiftCardTransfer();
                giftCardTransfer.setBusinessId(payload.getBusinessId());
                giftCardTransfer.setProgramId(payload.getProgramId());
                giftCardTransfer.setTransferNo((int) payload.getTransferNo());
                giftCardTransfer.setArchiveFolder(recipient.getSetFolderPassword());
                giftCardTransfer.setReceivePasswordPhoneNo(recipient.getReceivePasswordPhoneNo());
                if (EBoolean.YES.equals(recipient.getSetFolderPassword())) {
                    giftCardTransfer.setArchivePassword(RandomStringUtils.randomAlphanumeric(8));
                }
                giftCardTransfer.setRecipientId(recipient.getId());
                giftCardTransfer.setStatus(ECommonStatus.ACTIVE);
                giftCardTransfer.setProcessStatus(EProcessStatus.PROCESSING);
                giftCardTransfer.setEncryptPGP(recipient.getEncryptPGP());
                giftCardTransfer.setPgpPublicKey(recipient.getPgpPublicKey());
                opsReqPendingValidator.updateInfoChecker(giftCardTransfer, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
                GiftCardTransfer cardTransfer = giftCardTransferRepository.save(giftCardTransfer);

                CreateGCTFeignReq createGCTFeignReq = new CreateGCTFeignReq();
                createGCTFeignReq.setTransferId(cardTransfer.getId());
                createGCTFeignReq.setTransferNo(String.valueOf(payload.getTransferNo()));
                createGCTFeignReq.setBusinessCode(business.getCode());
                createGCTFeignReq.setProgramCode(program.getCode());
                createGCTFeignReq.setRecipientCode(recipient.getCode());
                getSelectedBatchInfo(payload.getSelectedBatches(), program.getId());
                List<Long> batchNo = payload.getSelectedBatches().stream().map(CreateGiftCardTransferReq.SelectedBatch::getBatchNo).collect(Collectors.toList());
                List<GiftCardRequest> gcrs = giftCardRequestRepository.findByProgramIdAndBatchNo(program.getId(), batchNo);
                Map<Long, GiftCardRequest> giftCardRequestMap = gcrs.stream().collect(Collectors.toMap(GiftCardRequest::getCprBatchNo, g -> g));
                String finalCreatedBy = createdBy;
                List<CreateGCTFeignReq.SelectedBatch> collect = payload.getSelectedBatches().stream().map(ele -> {
                    GiftCardTransferBatch giftCardTransferBatch = new GiftCardTransferBatch();
                    giftCardTransferBatch.setGiftCardTransferId(cardTransfer.getId());
                    giftCardTransferBatch.setBatchNo(Math.toIntExact(ele.getBatchNo()));
                    giftCardTransferBatch.setProgramId(payload.getProgramId());
                    giftCardTransferBatch.setConvertXlsx(ele.getConvertXlsxFlag().getValue());
                    giftCardTransferBatch.setStatus(ECommonStatus.ACTIVE);
                    opsReqPendingValidator.updateInfoChecker(giftCardTransferBatch, detailResData.getMadeDate(), finalCreatedBy, updatedBy, approvedBy);
                    giftCardTransferBatchRepository.save(giftCardTransferBatch);

                    CreateGCTFeignReq.SelectedBatch selectedBatch = new CreateGCTFeignReq.SelectedBatch();
                    GiftCardRequest gcr = giftCardRequestMap.get(ele.getBatchNo());
                    selectedBatch.setBatchId(gcr.getId());
                    selectedBatch.setBatchId(gcr.getId());
                    selectedBatch.setCprBatchNo(ele.getBatchNo());
                    selectedBatch.setStoreId(ele.getStore().getId());
                    selectedBatch.setConvertXlsxFlag(ele.getConvertXlsxFlag());
                    return selectedBatch;
                }).collect(Collectors.toList());
                createGCTFeignReq.setSelectedBatches(collect);
                APIResponse<?> gct = cardServiceFeignClient.createGCT(createGCTFeignReq);
                if (ErrorCode.SUCCESS.getValue() != gct.getMeta().getCode()) {
                    throw new BusinessException(ErrorCode.SERVER_ERROR,
                            "Call card service error: " + gct.getMeta().getMessage(), null);
                }
            }
        }
        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes =
                makerCheckerInternalFeignClient.checkerDefault(req);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Card production request call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
    }

    @Override
    public ResourceDTO exportFeature(int id) {
        Optional<GiftCardTransfer> giftCardTransfer = giftCardTransferRepository.findActiveById(id);
        if (giftCardTransfer.isPresent()) {
            GiftCardTransfer cardTransfer = giftCardTransfer.get();
            if ((EBoolean.YES.equals(cardTransfer.getArchiveFolder()) && Objects.nonNull(cardTransfer.getArchivePassword())
                    && Objects.nonNull(cardTransfer.getFilePath())) || EBoolean.YES.equals(cardTransfer.getEncryptPGP())) {
                APIResponse<GCTExportFeignRes> response = cardServiceFeignClient.exportFeature(cardTransfer.getFilePath());
                if (ErrorCode.SUCCESS.getValue() != response.getMeta().getCode()) {
                    throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                            "Gift card transfer - export feature error: " + response.getMeta().getMessage(), null);
                }
                return ResourceDTO.builder()
                        .filename(cardTransfer.getFilePath().substring(cardTransfer.getFilePath().lastIndexOf("/") + 1))
                        .resource(new InputStreamResource(new ByteArrayInputStream(response.getData().getData()))).build();
            } else {
                throw new BusinessException(ErrorCode.SERVER_ERROR);
            }
        } else {
            throw new BusinessException(ErrorCode.SERVER_ERROR);
        }
    }

    @Override
    public Page<GiftCardTransferringInReviewRes> getInReview(EApprovalStatus approvalStatus,
                                                             String fromCreatedAt,
                                                             String toCreatedAt,
                                                             String fromReviewedAt,
                                                             String toReviewedAt,
                                                             String createdBy,
                                                             String reviewedBy,

                                                             Pageable pageable) {
        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.GIFT_CARD_TRANSFER.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        reviewedBy,
                        checkedDate
                );

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, (int) pageable.getOffset(), pageable.getPageSize());
        if (previewRes.getMeta().getTotal() > 0) {
            List<GiftCardTransferringInReviewRes> collect = previewRes.getData().stream()
                    .map(this::convertPreview)
                    .collect(Collectors.toList());
            return new PageImpl<>(collect, pageable, previewRes.getMeta().getTotal());
        } else {
            return Page.empty(pageable);
        }
    }

    private GiftCardTransferringInReviewRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        CreateGiftCardTransferReq cardTransferReq = this.jsonMapper.convertValue(data.getPayload(), CreateGiftCardTransferReq.class);
        Business business = businessService.find(cardTransferReq.getBusinessId()).orElse(null);
        Program program = programService.find(cardTransferReq.getProgramId()).orElse(null);
        List<Long> batchIds = cardTransferReq.getSelectedBatches().stream().map(CreateGiftCardTransferReq.SelectedBatch::getBatchNo).collect(Collectors.toList());
        int sumCardQuantity = giftCardRequestRepository.findByProgramIdAndBatchNo(cardTransferReq.getProgramId(), batchIds).stream()
                .map(GiftCardRequest::getNoOfCards)
                .filter(noOfCards -> !ObjectUtils.isEmpty(noOfCards)).reduce(0, Integer::sum);
        return GiftCardTransferringInReviewRes.builder()
                .requestId(data.getId())
                .transferNo(cardTransferReq.getTransferNo())
                .business(business != null ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .batchQuantity(cardTransferReq.getSelectedBatches().size())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .cardQuantity(sumCardQuantity)
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .reviewedBy(data.getCheckedByUserName())
                .reviewedAt(data.getCheckedDateToDate())
                .build();
    }

    @Override
    public GiftCardTransferInReviewDetailRes giftCardTransferInReviewDetail(Integer id) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(id));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.GIFT_CARD_TRANSFER.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Program - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        return convertPreviewDetail(previewDetailRes.getData());
    }

    private GiftCardTransferInReviewDetailRes convertPreviewDetail(MakerCheckerInternalDataDetailRes data) {
        CreateGiftCardTransferReq giftcardTransferReq = this.jsonMapper.convertValue(data.getPayload(), CreateGiftCardTransferReq.class);
        Business business = businessService.find(giftcardTransferReq.getBusinessId()).orElse(null);
        Program program = programService.find(giftcardTransferReq.getProgramId()).orElse(null);
        List<Long> batchNos = giftcardTransferReq.getSelectedBatches().stream().map(CreateGiftCardTransferReq.SelectedBatch::getBatchNo).collect(Collectors.toList());
        int sumCardQuantity = giftCardRequestRepository.findByProgramIdAndBatchNo(program.getId(), batchNos).stream()
                .map(GiftCardRequest::getNoOfCards)
                .filter(noOfCards -> !ObjectUtils.isEmpty(noOfCards)).reduce(0, Integer::sum);
        Recipient recipient = recipientRepository.findByCode(giftcardTransferReq.getRecipientCode()).orElseThrow(() -> new BusinessException(ErrorCode.RECIPIENT_NOT_FOUND, null, null));
        getSelectedBatchInfo(giftcardTransferReq.getSelectedBatches(), giftcardTransferReq.getProgramId());
        return GiftCardTransferInReviewDetailRes.builder()
                .requestId(data.getId())
                .transferNo(giftcardTransferReq.getTransferNo())
                .business(business != null ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .batchQuantity(giftcardTransferReq.getSelectedBatches().size())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .cardQuantity(sumCardQuantity)
                .createdBy(data.getMadeByUserName())
                .createdAt(data.getMadeDateToDate())
                .approvedBy(data.getCheckedByUserName())
                .approvedAt(data.getCheckedDateToDate())
                .recipient(new BasicRecipientInfo(recipient))
                .reason(data.getComment())
                .selectedBatchList(giftcardTransferReq.getSelectedBatches())
                .reason(data.getComment())
                .build();
    }

    private void getSelectedBatchInfo(List<CreateGiftCardTransferReq.SelectedBatch> batch, Integer programId) {
        List<Long> batchNo = batch
                .stream()
                .map(CreateGiftCardTransferReq.SelectedBatch::getBatchNo)
                .collect(Collectors.toList());
        List<GiftCardRequest> gcrs = giftCardRequestRepository.findByProgramIdAndBatchNo(programId, batchNo);
        Map<Long, GiftCardRequest> giftCardRequestMap = gcrs
                .stream()
                .collect(Collectors.toMap(GiftCardRequest::getCprBatchNo, g -> g));
        Set<Integer> storeIds = gcrs
                .stream()
                .map(GiftCardRequest::getStoreId)
                .collect(Collectors.toSet());
        Set<Integer> giftCardTypeIds = gcrs
                .stream()
                .map(GiftCardRequest::getGcTypeId)
                .collect(Collectors.toSet());
        Map<Integer, GiftCardType> giftCardTypeMap = giftCardTypeService.findByIdIn(giftCardTypeIds)
                .stream()
                .collect(Collectors.toMap(GiftCardType::getId, g -> g));
        List<Store> stores = storeService.findByIdIn(storeIds);
        Map<Integer, Store> storeMap = stores
                .stream()
                .collect(Collectors.toMap(Store::getId, s -> s));
        Set<Integer> chainIds = stores
                .stream()
                .map(Store::getChainId)
                .collect(Collectors.toSet());
        Set<Integer> corporationIds = stores
                .stream()
                .map(Store::getCorporationId)
                .collect(Collectors.toSet());
        Map<Integer, Chain> chainMap = chainRepository.findAllByIdIn(chainIds)
                .stream()
                .collect(Collectors.toMap(Chain::getId, c -> c));
        Map<Integer, Corporation> corporationMap = corporationRepository.findAllByIdIn(corporationIds)
                .stream()
                .collect(Collectors.toMap(Corporation::getId, c -> c));
        for (CreateGiftCardTransferReq.SelectedBatch selectedBatch : batch) {
            GiftCardRequest gcr = giftCardRequestMap.get(selectedBatch.getBatchNo());
            selectedBatch.setBatchId(gcr.getId());
            selectedBatch.setBatchType(gcr.getGcBatchType().getValue());
            selectedBatch.setDescription(gcr.getDescription());
            GiftCardType giftCardType = giftCardTypeMap.get(gcr.getGcTypeId());
            selectedBatch.setGiftCardType(new ShortEntityRes(giftCardType.getId(), giftCardType.getDescription(), giftCardType.getCode()));
            Store store = storeMap.get(gcr.getStoreId());
            Chain chain = chainMap.get(store.getChainId());
            Corporation corporation = corporationMap.get(store.getCorporationId());
            selectedBatch.setStore(new ShortEntityRes(store.getId(), store.getName(), store.getCode()));
            selectedBatch.setChain(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()));
            selectedBatch.setCorporation(new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()));
            selectedBatch.setNoOfCards(gcr.getNoOfCards());
        }
    }

    @Override
    public BasicRecipientInfo findRecipientCode(String code) {
        Optional<Recipient> byCode = recipientRepository.findByCode(code);
        if (byCode.isEmpty()) {
            throw new BusinessException(ErrorCode.RECIPIENT_NOT_FOUND, null, null);
        }
        return new BasicRecipientInfo(byCode.get());
    }

    @Override
    public Page<GiftCardTransferAvailableListRes> searchGiftCardTransferAvailable(
            Integer businessId,
            Integer programId,
            Integer batchNo,
            Integer transferNo,
            Date createdStart,
            Date createdEnd,
            EProcessStatus processStatus,
            String recipientCode,
            ESendVia sendVia,
            Integer offset,
            Integer limit
    ) {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + (1000 * 60 * 60 * 24)) : null;
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<Object[]> rs = giftCardTransferRepository.searchGCTransferAvailable(businessId, programId, batchNo,
                transferNo, createdStart, createdEnd, processStatus, recipientCode, sendVia, pageRequest);
        List<GiftCardTransferAvailableListRes> data = rs.getContent().stream().map(GiftCardTransferAvailableListRes::new).collect(Collectors.toList());

        return new PageImpl<>(data, rs.getPageable(), rs.getTotalElements());
    }

    @Override
    public GiftCardTransferringAvailableRes giftCardTransferAvailableDetail(Integer id) {
        GiftCardTransfer gct = giftCardTransferRepository.findActiveById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_TRANSFER_NOT_FOUND, null, null));
        Recipient recipient = recipientRepository.findById(gct.getRecipientId()).orElseThrow(() -> new BusinessException(ErrorCode.RECIPIENT_NOT_FOUND, null, null));

        Business b = businessService.findById(gct.getBusinessId());
        Program p = programService.findById(gct.getProgramId());
        List<GiftCardTransferBatch> gctb = giftCardTransferBatchRepository.findByGiftCardTransferNoId(id);
        List<Long> batchNo = gctb.stream().map(g -> g.getBatchNo().longValue()).collect(Collectors.toList());
        List<GiftCardRequest> gcrs = giftCardRequestRepository.findByProgramIdAndBatchNo(gct.getProgramId(), batchNo);
        Map<Long, GiftCardRequest> giftCardRequestMap = gcrs.stream().collect(Collectors.toMap(GiftCardRequest::getCprBatchNo, g -> g));
        int sumCardQuantity = gctb.stream()
                .filter(ele -> Objects.nonNull(ele.getBatchNo()))
                .filter(ele -> Objects.nonNull(giftCardRequestMap.get(ele.getBatchNo().longValue())))
                .map(x -> giftCardRequestMap.get(x.getBatchNo().longValue()).getNoOfCards()).reduce(0, Integer::sum);
        Set<Integer> storeIds = gcrs.stream().map(GiftCardRequest::getStoreId).collect(Collectors.toSet());
        Set<Integer> giftCardTypeIds = gcrs.stream().map(GiftCardRequest::getGcTypeId).collect(Collectors.toSet());
        Map<Integer, GiftCardType> giftCardTypeMap = giftCardTypeService.findByIdIn(giftCardTypeIds).stream().collect(Collectors.toMap(GiftCardType::getId, g -> g));
        List<Store> stores = storeService.findByIdIn(storeIds);
        Map<Integer, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, s -> s));
        Set<Integer> chainIds = stores.stream().map(Store::getChainId).collect(Collectors.toSet());
        Set<Integer> corporationIds = stores.stream().map(Store::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Chain> chainMap = chainRepository.findAllByIdIn(chainIds).stream().collect(Collectors.toMap(Chain::getId, c -> c));
        Map<Integer, Corporation> corporationMap = corporationRepository.findAllByIdIn(corporationIds).stream().collect(Collectors.toMap(Corporation::getId, c -> c));
        List<CreateGiftCardTransferReq.SelectedBatch> selectedBatchList = gctb.stream().map(sb -> {
            GiftCardRequest gcr = giftCardRequestMap.get(sb.getBatchNo().longValue());
            Store store = storeMap.get(gcr.getStoreId());
            Chain chain = chainMap.get(store.getChainId());
            Corporation corporation = corporationMap.get(store.getCorporationId());
            GiftCardType giftCardType = giftCardTypeMap.get(gcr.getGcTypeId());
            return CreateGiftCardTransferReq.SelectedBatch.builder()
                    .batchId(gcr.getId())
                    .batchNo(gcr.getCprBatchNo())
                    .noOfCards(gcr.getNoOfCards())
                    .convertXlsxFlag(EBoolean.of(sb.getConvertXlsx()))
                    .batchType(gcr.getGcBatchType().getValue())
                    .description(gcr.getDescription())
                    .giftCardType(new ShortEntityRes(giftCardType.getId(), giftCardType.getDescription(), giftCardType.getCode()))
                    .store(new ShortEntityRes(store.getId(), store.getName(), store.getCode()))
                    .chain(new ShortEntityRes(chain.getId(), chain.getName(), chain.getCode()))
                    .corporation(new ShortEntityRes(corporation.getId(), corporation.getName(), corporation.getCode()))
                    .build();

        }).collect(Collectors.toList());

        return GiftCardTransferringAvailableRes
                .builder()
                .id(gct.getId().longValue())
                .business(new ShortEntityRes(b.getId(), b.getName(), b.getCode()))
                .program(new ShortEntityRes(p.getId(), p.getName(), p.getCode()))
                .transferNo(gct.getTransferNo().longValue())
                .batchQuantity(gctb.size())
                .cardQuantity(sumCardQuantity)
                .processStatus(gct.getProcessStatus())
                .selectedBatchList(selectedBatchList)
                .archiveFolder(gct.getArchiveFolder())
                .encryptPGP(gct.getEncryptPGP())
                .existArchiveFolderPassword(Objects.nonNull(gct.getArchivePassword()) ? EBoolean.YES : EBoolean.NO)
                .receivePasswordPhoneNo(gct.getReceivePasswordPhoneNo())
                .createdAt(gct.getCreatedAt())
                .createdBy(gct.getCreatedBy())
                .updatedAt(gct.getUpdatedAt())
                .updatedBy(gct.getUpdatedBy())
                .approvedAt(gct.getApprovedAt())
                .approvedBy(gct.getApprovedBy())
                .recipient(new BasicRecipientInfo(recipient))
                .build();
    }

    @Override
    public SendSmsPasswordFolderRes sendSmsPasswordFolder(Integer id) {
        GiftCardTransfer giftCardTransfer = giftCardTransferRepository.findActiveById(id)
                .orElseThrow(() -> {
                    Log.error(
                            LogData.createLogData()
                                    .append("gift_card_transfer_id", id)
                                    .append("error", "gift card transfer does not exist")
                    );
                    return new BusinessException(ErrorCode.GIFT_CARD_TRANSFER_NOT_FOUND);
                });

        SendSmsPasswordFolderRes res = verifySendSmsPassword(giftCardTransfer);

        if (EBoolean.YES.equals(res.getSendSmsSuccess())) {
            sendSmsPassword(giftCardTransfer);
            giftCardTransfer.setLastSentSMSAt(res.getLastSendSmsAt());
            giftCardTransfer.setSendSMSPasswordCount(
                    Objects.isNull(giftCardTransfer.getSendSMSPasswordCount()) ? 1 : giftCardTransfer.getSendSMSPasswordCount() + 1);
            giftCardTransferRepository.save(giftCardTransfer);
        }
        Log.info(
                LogData.createLogData()
                        .append("gift_card_transfer_id", id)
                        .append("res", res)
        );
        return res;
    }

    private void sendSmsPassword(GiftCardTransfer giftCardTransfer) {
        Oauth2GetTokenRes tokenRes = getOauthToken();
        SendSmsInternalRes res = sendSms(giftCardTransfer, tokenRes.getAccessToken());
        if (Objects.isNull(res) || res.getMeta().getCode() != 202) {
            Log.error(
                    LogData.createLogData()
                            .append("gift_card_transfer_id", giftCardTransfer.getId())
                            .append("gift_card_transfer_no", giftCardTransfer.getTransferNo())
                            .append("error", "send sms from service platform failed")
            );
            throw new BusinessException(ErrorCode.SERVER_ERROR, "send sms from service platform failed", null);
        }
    }

    private SendSmsInternalRes sendSms(GiftCardTransfer giftCardTransfer, String accessToken) {
        String token = "Bearer " + accessToken;
        SendGiftCardTransferFolderPasswordTemplateData data = new SendGiftCardTransferFolderPasswordTemplateData();
        data.setGCTransPDW(giftCardTransfer.getArchivePassword());
        List<GiftCardTransferBatch> batch = giftCardTransferBatchRepository.findByGiftCardTransferNoId(giftCardTransfer.getId());
        if (Objects.nonNull(batch) && batch.size() > 0) {
            GiftCardRequest gcr = giftCardRequestRepository.findByCprBatchNoAndBusinessIdAndProgramId(
                    batch.get(0).getBatchNo().longValue(),
                    giftCardTransfer.getBusinessId(),
                    batch.get(0).getProgramId()
            );
            if (Objects.isNull(gcr) || StringUtils.isEmpty(gcr.getDescription())) {
                Log.error(
                        LogData.createLogData()
                                .append("gift_card_transfer_id", giftCardTransfer.getId())
                                .append("gift_card_transfer_no", giftCardTransfer.getTransferNo())
                                .append("error", "card production request missing SO (description field)")
                );
                throw new BusinessException(ErrorCode.MISSING_SELL_ODER_IN_DESCRIPTION, "card production request missing SO (description field)", null);
            }
            data.setGCBatchSO(
                    gcr.getDescription()
                            .substring(0, Math.min(10,
                                            gcr.getDescription().length()
                                    )
                            )
            );
        }
        SendSmsInternalReq req = new SendSmsInternalReq();
        SendSmsInternalReq.Meta meta = new SendSmsInternalReq.Meta();
        //meta.setClientVersionCode("");//todo define?
        req.setMeta(meta);
        SendSmsInternalReq.Data dataBody = new SendSmsInternalReq.Data();
        dataBody.setData(data);
        dataBody.setTemplateId(templateId);
        dataBody.setType("sms");
        dataBody.setRecipients(
                Collections.singletonList(
                        SendSmsInternalReq.Data.Recipients
                                .builder()
                                .identity(giftCardTransfer.getReceivePasswordPhoneNo())
                                .type("phone_number")
                                .build()
                )
        );
        req.setData(Collections.singletonList(dataBody));
        return notificationCenterFeignClient.sendSms(token, req);
    }

    private Oauth2GetTokenRes getOauthToken() {
        Map<String, String> body = new HashMap<>();
        body.put("grant_type", "client_credentials");
        body.put("scope", "sms.internal");
        return oauth2FeignClient.getToken(oauth2Token, body);
    }

    private SendSmsPasswordFolderRes verifySendSmsPassword(GiftCardTransfer giftCardTransfer) {
        Date now = new Date();
        Date last = giftCardTransfer.getLastSentSMSAt();

        if (Objects.isNull(last)) {
            return SendSmsPasswordFolderRes
                    .builder()
                    .DefaultSendSmsTimeSec(defaultSendSmsPasswordTimeSec)
                    .NextSendSmsTimeSec(defaultSendSmsPasswordTimeSec)
                    .SendSmsSuccess(EBoolean.YES)
                    .LastSendSmsAt(now)
                    .build();
        } else {
            Long passed =
                    Duration.between(
                            last.toInstant(),
                            now.toInstant()

                    ).toSeconds();
            if (defaultSendSmsPasswordTimeSec <= passed) {
                return SendSmsPasswordFolderRes
                        .builder()
                        .DefaultSendSmsTimeSec(defaultSendSmsPasswordTimeSec)
                        .NextSendSmsTimeSec(defaultSendSmsPasswordTimeSec)
                        .SendSmsSuccess(EBoolean.YES)
                        .LastSendSmsAt(now)
                        .build();
            }
            return SendSmsPasswordFolderRes
                    .builder()
                    .DefaultSendSmsTimeSec(defaultSendSmsPasswordTimeSec)
                    .NextSendSmsTimeSec(defaultSendSmsPasswordTimeSec - passed)
                    .SendSmsSuccess(EBoolean.NO)
                    .LastSendSmsAt(giftCardTransfer.getLastSentSMSAt())
                    .build();
        }
    }
}