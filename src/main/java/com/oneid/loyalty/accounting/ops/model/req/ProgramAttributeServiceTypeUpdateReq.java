package com.oneid.loyalty.accounting.ops.model.req;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;

import com.oneid.oneloyalty.common.constant.EServiceType;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class ProgramAttributeServiceTypeUpdateReq {
    
    @NotNull(message = "program_id is missing")
    private Integer programId;
    
    @NotNull(message = "service_type is missing")
    private EServiceType serviceType;
    
    private List<ProgramAttributeServiceTypeMappingReq> systemAttributeMappings;
    
    private List<ProgramAttributeServiceTypeMappingReq> programAttributeMappings;
    
    private List<ProgramAttributeServiceTypeMappingReq> memberAttributeMappings;
    
    @AssertTrue(message="Duplicate attribute_code at system_attributes")
    public boolean isValidSystemAttributes() {
        List<ProgramAttributeServiceTypeMappingReq> mappingReqs = this.filterValidMapping(systemAttributeMappings);
        
        Map<String, Long> countMap = mappingReqs.stream()
                .collect(Collectors.groupingBy(e -> e.getAttributeCode(), Collectors.counting()));
        
        for (Entry<String, Long> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                return false;
            }
        }
        
        return true;
    }
    
    @AssertTrue(message="Duplicate attribute_code at program_attributes")
    public boolean isValidProgramAttributes() {
        List<ProgramAttributeServiceTypeMappingReq> mappingReqs = this.filterValidMapping(programAttributeMappings);
        
        Map<String, Long> countMap = mappingReqs.stream()
                .collect(Collectors.groupingBy(e -> e.getAttributeCode(), Collectors.counting()));
        
        for (Entry<String, Long> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                return false;
            }
        }
        
        return true;
    }
    
    @AssertTrue(message="Duplicate attribute_code at member_attributes")
    public boolean isValidMemberAttributes() {
        List<ProgramAttributeServiceTypeMappingReq> mappingReqs = this.filterValidMapping(memberAttributeMappings);
        
        Map<String, Long> countMap = mappingReqs.stream()
                .collect(Collectors.groupingBy(e -> e.getAttributeCode(), Collectors.counting()));
        
        for (Entry<String, Long> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                return false;
            }
        }
        
        return true;
    }
    
    private List<ProgramAttributeServiceTypeMappingReq> filterValidMapping(List<ProgramAttributeServiceTypeMappingReq> inputMappings) {
        return inputMappings.stream()
                .filter(e -> e.isSelected() == true || e.getId() != null)
                .collect(Collectors.toList());
    }
    
}
