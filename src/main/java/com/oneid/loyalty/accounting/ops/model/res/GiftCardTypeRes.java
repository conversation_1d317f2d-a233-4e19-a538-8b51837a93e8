package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Program;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class GiftCardTypeRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("code")
    private String code;

    @JsonProperty("giftcard_policy_id")
    private Integer giftCardPolicyId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("base_currency_id")
    private Integer baseCurrencyId;

    @JsonProperty("base_currency_name")
    private String baseCurrencyName;

    @JsonProperty("price")
    private Double price;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @JsonProperty("currency_name")
    private String currencyName;

    @JsonProperty("point")
    private Long point;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    public static GiftCardTypeRes of(GiftCardType giftCardType, Business business, Program program, Currency baseCurrency, Currency currency) {
        GiftCardTypeRes giftCardTypeRes = new GiftCardTypeRes();
        giftCardTypeRes.setId(giftCardType.getId());
        giftCardTypeRes.setBusinessId(giftCardType.getBusinessId());
        giftCardTypeRes.setBusinessName(business != null ? business.getName() : null);
        giftCardTypeRes.setProgramId(giftCardType.getProgramId());
        giftCardTypeRes.setProgramName(program != null ? program.getName() : null);
        giftCardTypeRes.setCode(giftCardType.getCode());
        giftCardTypeRes.setGiftCardPolicyId(giftCardType.getPolicyId());
        giftCardTypeRes.setDescription(giftCardType.getDescription());
        giftCardTypeRes.setBaseCurrencyId(giftCardType.getBaseCurrencyId());
        giftCardTypeRes.setBaseCurrencyName(baseCurrency != null ? baseCurrency.getName() : null);
        giftCardTypeRes.setPrice(giftCardType.getPrice());
        giftCardTypeRes.setCurrencyId(giftCardType.getCurrencyId());
        giftCardTypeRes.setCurrencyName(currency != null ? currency.getName() : null);
        giftCardTypeRes.setPoint(giftCardType.getPoint());
        giftCardTypeRes.setStatus(giftCardType.getStatus() != null ? giftCardType.getStatus().getValue() : null);
        giftCardTypeRes.setCreatedBy(giftCardType.getCreatedBy());
        giftCardTypeRes.setUpdatedBy(giftCardType.getUpdatedBy());
        giftCardTypeRes.setApprovedBy(giftCardType.getApprovedBy());
        giftCardTypeRes.setCreatedAt(giftCardType.getCreatedAt() != null ? giftCardType.getCreatedAt().toInstant().getEpochSecond() : null);
        giftCardTypeRes.setUpdatedAt(giftCardType.getUpdatedAt() != null ? giftCardType.getUpdatedAt().toInstant().getEpochSecond() : null);
        giftCardTypeRes.setApprovedAt(giftCardType.getApprovedAt() != null ? giftCardType.getApprovedAt().toInstant().getEpochSecond() : null);
        giftCardTypeRes.setCreatedYmd(giftCardType.getCreatedYmd());
        return giftCardTypeRes;
    }
}
