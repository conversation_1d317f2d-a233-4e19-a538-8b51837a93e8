package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.model.req.DropdownSearchMultipleParentReq;
import com.oneid.loyalty.accounting.ops.model.res.DropdownRes;
import com.oneid.loyalty.accounting.ops.service.OpsDropDownService;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import java.util.List;

@RestController
@RequestMapping("v1/list-info")
public class DropDownSupportController extends BaseController {

    @Autowired
    private OpsDropDownService dropDownService;

    @GetMapping("business")
    public ResponseEntity<?> getAll() {
        return success(dropDownService.business(ECommonStatus.ACTIVE));
    }

    @GetMapping("corporation/business/{business_id}")
    public ResponseEntity<?> getAllCorporation(@PathVariable(value = "business_id") Integer businessId) {
        return success(dropDownService.corporation(businessId, ECommonStatus.ACTIVE));
    }

    @GetMapping("corporation/business/{business_id}/search")
    public ResponseEntity<?> getAllCorporation(@PathVariable(value = "business_id") Integer businessId,
                                               @RequestParam(name = "status", defaultValue = "A", required = false) ECommonStatus status,
                                               @RequestParam(name = "keyword", required = false) String keyword,
                                               @Valid @RequestParam(name = "offset", defaultValue = "0", required = false) @Min(0) Integer offset,
                                               @Valid @RequestParam(name = "limit", defaultValue = "20", required = false) @Max(100) Integer limit
    ) {
        Page<DropdownRes> page = dropDownService.filterCorporation(businessId, keyword, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("corporation/program-id/{program_id}")
    public ResponseEntity<?> getActiveCorporations(
            @PathVariable(value = "program_id") Integer programId
    ) {
        return success(dropDownService.getActiveCorporationsByProgramId(programId));
    }

    @GetMapping("chain/corporation/{corporation_id}")
    public ResponseEntity<?> getAllChain(@PathVariable(value = "corporation_id") Integer corporationId) {
        return success(dropDownService.chain(corporationId, ECommonStatus.ACTIVE));
    }

    @GetMapping("chain/corporation/{corporation_id}/search")
    public ResponseEntity<?> getAllChain(@PathVariable(value = "corporation_id") Integer corporationId,
                                         @RequestParam(name = "status", defaultValue = "A", required = false) ECommonStatus status,
                                         @RequestParam(name = "keyword", required = false) String keyword,
                                         @Valid @RequestParam(name = "offset", defaultValue = "0", required = false) @Min(0) Integer offset,
                                         @Valid @RequestParam(name = "limit", defaultValue = "20", required = false) @Max(100) Integer limit) {
        Page<DropdownRes> page = dropDownService.filterChain(corporationId, keyword, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("chain/business/{business_code}/corporation/{corporation_code}")
    public ResponseEntity<?> getAllChain(
            @PathVariable(value = "business_code") String businessCode,
            @PathVariable(value = "corporation_code") String corporationCode) {
        return success(dropDownService.getActivatedChainsByCorporationCode(businessCode, corporationCode, ECommonStatus.ACTIVE));
    }

    @GetMapping("store")
    public ResponseEntity<?> getActiveStores(
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "code_prefix", required = false) String codePrefix
    ) {
        return success(dropDownService.getActiveStoresByChainAndProgram(chainId, programId, codePrefix));
    }

    @GetMapping("store/chain/{chain_id}")
    public ResponseEntity<?> getAllStore(@PathVariable(value = "chain_id") Integer chainId) {
        return success(dropDownService.store(chainId, ECommonStatus.ACTIVE));
    }

    @GetMapping("store/chain/{chain_id}/search")
    public ResponseEntity<?> getAllStore(@PathVariable(value = "chain_id") Integer storeId,
                                         @RequestParam(name = "status", defaultValue = "A", required = false) ECommonStatus status,
                                         @RequestParam(name = "keyword", required = false) String keyword,
                                         @Valid @RequestParam(name = "offset", defaultValue = "0", required = false) @Min(0) Integer offset,
                                         @Valid @RequestParam(name = "limit", defaultValue = "20", required = false) @Max(100) Integer limit) {
        Page<DropdownRes> page = dropDownService.filterStore(storeId, keyword, status, offset, limit);
        return success(page, offset, limit);
    }


    @GetMapping("terminal/store/{store_id}")
    public ResponseEntity<?> getAllTerminal(@PathVariable(value = "store_id") Integer storeId) {
        return success(dropDownService.terminal(storeId, ECommonStatus.ACTIVE));
    }

    @GetMapping("card-policy/business/{business_id}/card-policy-type/{card_policy_type}")
    public ResponseEntity<?> getAllCardPolicy(@PathVariable(value = "business_id") Integer businessId,
                                              @PathVariable(value = "card_policy_type") ECardPolicyType cardPolicyType) {
        return success(dropDownService.cardPolicy(businessId, cardPolicyType));
    }

    @GetMapping("program/business/{business_id}")
    public ResponseEntity<?> getAllProgramByBusiness(@PathVariable(value = "business_id") Integer businessId) {
        return success(dropDownService.program(businessId));
    }

    @GetMapping("programs/{program_id}/corporations")
    public ResponseEntity<?> getCorporationsByProgram(@PathVariable(value = "program_id") Integer programId) {
        return success(dropDownService.corporationByProgram(programId));
    }

    @GetMapping("gift-card-bin")
    public ResponseEntity<?> getAllGCBinByBusiness(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId) {
        return success(dropDownService.gcBinByBusinessAndProgram(businessId, programId));
    }

    @GetMapping("gift-card-type")
    public ResponseEntity<?> getAllGCTypeByBusiness(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId) {
        return success(dropDownService.gcTypeByBusinessAndProgram(businessId, programId));
    }

    @GetMapping("card-type/business/{business_id}/program/{program_id}")
    public ResponseEntity<?> getCardTypes(
            @PathVariable(value = "business_id") Integer businessId,
            @PathVariable(value = "program_id") Integer programId) {
        return success(dropDownService.getCardTypesByBusinessAndProgramId(businessId, programId));
    }

    @GetMapping("currency-for-transaction")
    public ResponseEntity<?> getCurrencyForTransaction(@RequestParam(value = "business_id", required = true) Integer businessId) {
        return success(dropDownService.currencyForTransaction(businessId));
    }

    @GetMapping("base-currency-for-transaction")
    public ResponseEntity<?> getBaseCurrencyForTransaction(@RequestParam(value = "business_id", required = true) Integer businessId) {
        return success(dropDownService.baseCurrencyForTransaction(businessId));
    }

    @GetMapping("/tier/program/{program_id}")
    public ResponseEntity<?> getTiers(@PathVariable(value = "program_id") Integer programId,
                                      @RequestParam(value = "direction", required = false, defaultValue = "ASC") Direction direction
    ) {
        Sort sort = Sort.by(direction, "rankNo");
        return success(dropDownService.getActivatedTiers(programId, sort));
    }

    @GetMapping("/reason-code/business/{businessId}/program/{programId}")
    public ResponseEntity<?> getActiveReasonCodes(
            @PathVariable(value = "businessId") Integer businessId,
            @PathVariable(value = "programId") Integer programId,
            @RequestParam(value = "function_code", required = false) EOpsFunctionCode functionCode) {
        return success(dropDownService.getActiveReasonCodes(businessId, programId, functionCode));
    }

    @PostMapping("chains/search")
    public ResponseEntity<?> searchChain(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchChains(req.getParentIds(), req.getNameOrCode()));
    }

    @PostMapping("stores/search")
    public ResponseEntity<?> searchStore(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchStores(req.getParentIds(), req.getNameOrCode()));
    }

    @PostMapping("terminals/search")
    public ResponseEntity<?> searchTerminal(
            @RequestBody DropdownSearchMultipleParentReq req) {
        return success(dropDownService.searchTerminals(req.getParentIds(), req.getNameOrCode()));
    }

    @GetMapping("/programs/business/{businessId}")
    public ResponseEntity<?> getActivePrograms(
            @PathVariable(value = "businessId") Integer businessId,
            @RequestParam(value = "exclude_program_id", required = false) List<Integer> excludeProgramIds) {
        return success(dropDownService.getActivePrograms(businessId, excludeProgramIds));
    }

    @GetMapping("/tier-policy/business/{businessId}/program/{programId}")
    public ResponseEntity<?> getActiveTierPolicies(
            @PathVariable(value = "businessId") Integer businessId,
            @PathVariable(value = "programId") Integer programId) {
        return success(dropDownService.getActiveTierPolicies(businessId, programId));
    }

    @GetMapping("/events")
    ResponseEntity<?> getMessageEvents() {
        return success(dropDownService.getMessageEvent());
    }

    @GetMapping("/partner-supplier/business/{businessId}")
    public ResponseEntity<?> getActivatedPartnerSuppliers(@PathVariable(value = "businessId") Integer businessId) {
        return success(dropDownService.getActivatedPartnerSuppliers(businessId));
    }

    @GetMapping("/currencies/business/{business_code}")
    public ResponseEntity<?> getActivatedCurrenciesByBusinessCode(@PathVariable(value = "business_code") String businessCode) {
        return success(dropDownService.getActivatedCurrenciesByBusinessCode(businessCode));
    }

    @GetMapping("store/corporation/{id}")
    public ResponseEntity<?> getActivatedStoresByCorporationId(@PathVariable(value = "id") Integer corporationId) {
        return success(dropDownService.geActivatedtStoresByCorporationId(corporationId));
    }

    @GetMapping("member-status/program-id/{program_id}")
    public ResponseEntity<?> getAllMemberStatus(@PathVariable(value = "program_id") Integer programId,
                                                @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getAllMemberStatus(programId, status));
    }

    @GetMapping("pool/business/{business_id}/program/{program_id}")
    public ResponseEntity<?> getPools(
            @PathVariable(value = "business_id") Integer businessId,
            @PathVariable(value = "program_id") Integer programId) {
        return success(dropDownService.getPoolsByBusinessAndProgramId(businessId, programId));
    }

    @GetMapping("service-code/program/{program_id}")
    public ResponseEntity<?> getServiceCode(@PathVariable(value = "program_id") Integer programId) {
        return success(dropDownService.getServiceCodeByProgramId(programId));
    }

    @GetMapping("terminal/corporation/{corporation_id}")
    public ResponseEntity<?> getTerminal(
            @PathVariable(value = "corporation_id") Integer corporationId,
            @RequestParam(value = "status", required = false) ECommonStatus status) {
        return success(dropDownService.getTerminalByCorporationId(corporationId, status));
    }
}