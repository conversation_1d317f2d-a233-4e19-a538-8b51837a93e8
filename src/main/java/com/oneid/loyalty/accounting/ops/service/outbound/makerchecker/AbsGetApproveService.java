package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker;

import com.oneid.loyalty.accounting.ops.service.outbound.config.MarkerCheckerConfigParam;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

public abstract class AbsGetApproveService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MarkerCheckerConfigParam configParam;

    public AbsGetApproveRes createChange(HttpEntity httpEntity) {
        try {
            return restTemplate.exchange(configParam.makerCheckerUrlChange,
                    HttpMethod.POST, httpEntity, AbsGetApproveRes.class).getBody();
        } catch (Exception e) {
            Log.error("Error call to maker/checker service", e);
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(),
                    LogData.createLogData()
                            .append("req_url", configParam.makerCheckerUrlChange).append("err_msg", e.getMessage()));
        }
    }

    public AbsGetApproveRes updateChange(HttpEntity httpEntity) {
        try {
            return restTemplate.exchange(configParam.makerCheckerUrlChange,
                    HttpMethod.POST, httpEntity, AbsGetApproveRes.class).getBody();
        } catch (Exception e) {
            Log.error("Error call to maker/checker service", e);
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(),
                    LogData.createLogData()
                            .append("req_url", configParam.makerCheckerUrlChange).append("err_msg", e.getMessage()));
        }
    }
}