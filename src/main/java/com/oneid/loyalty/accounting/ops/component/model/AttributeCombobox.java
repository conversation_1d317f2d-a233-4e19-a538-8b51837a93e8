package com.oneid.loyalty.accounting.ops.component.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.HashingBeanSingleton;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.util.LogData;
import lombok.Getter;
import lombok.SneakyThrows;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
public class AttributeCombobox {
    private String name;
    private String value;
    private final String description;

    @JsonProperty("original_value")
    private String originalValue;

    public AttributeCombobox(String name, String value, String description, String originalValue) {
        this.name = name;
        this.value = value;
        this.description = description;
        this.originalValue = originalValue;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String name;
        private String value;
        private String description;
        private String originalValue;
        private List<Integer> checksumKeys;

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder originalValue(String originalValue) {
            this.originalValue = originalValue;
            return this;
        }

        public Builder checksumKeys(List<Integer> checksumKeys) {
            this.checksumKeys = checksumKeys;
            return this;
        }

        public AttributeCombobox build() {
            if (this.originalValue != null && !attributeComboboxValid(value,
                    Optional.ofNullable(checksumKeys).orElse(Collections.emptyList())
                            .stream().map(String::valueOf).collect(Collectors.joining()),
                    originalValue)) {

                throw new BusinessException(ErrorCode.CHECK_SUM_NOT_MATCH,
                        "Cannot build attribute combobox: checksum not matched",
                        LogData.createLogData()
                                .append("value", value)
                                .append("salt", checksumKeys)
                                .append("original_value", originalValue)
                );
            } else if (originalValue == null) {
                this.originalValue = buildCheckSum(this.value,
                        checksumKeys.stream().map(String::valueOf).collect(Collectors.joining()));
            }
            return new AttributeCombobox(this.name, this.value, this.description, this.originalValue);
        }
    }

    @SneakyThrows
    private static String buildCheckSum(String value, String salt) {
        StringBuilder s = new StringBuilder();
        int indexSalt = 0;
        for (int i = 0; i < value.length(); ++i) {
            s.append(value.charAt(i));
            if (indexSalt < salt.length()) {
                s.append(salt.charAt(indexSalt++));
            }
        }
        return HashingBeanSingleton.getHashing().encode(s.toString());
    }

    private static boolean attributeComboboxValid(String value, String salt, String checksum) {
        return checksum != null && checksum.equals(buildCheckSum(value, salt));
    }
}