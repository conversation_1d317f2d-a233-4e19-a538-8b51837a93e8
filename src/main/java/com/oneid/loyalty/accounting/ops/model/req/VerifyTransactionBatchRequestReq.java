package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EGenerationInvoiceNoMethod;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VerifyTransactionBatchRequestReq {

    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be null")
    private Integer programId;

    @NotNull(message = "'transaction_batch_type' must not be null")
    private ETransactionBatchType transactionBatchType;

    @NotNull(message = "'gen_invoice_no_method' must not be null")
    private EGenerationInvoiceNoMethod genInvoiceNoMethod;

    @NotNull(message = "'gen_transaction_time_method' must not be null")
    private EGenerationTransactionTimeMethod genTransactionTimeMethod;
}
