package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreateRuleRecordRes {
    @JsonProperty("rule_id")
    private Integer ruleId;

    private EConditionType conditionLogic;

    @JsonProperty("seq_no")
    private Integer seqNo;

    private ECommonStatus status;

    @JsonProperty("condition_list")
    private List<ConditionRecordRes> listCondition;
}
