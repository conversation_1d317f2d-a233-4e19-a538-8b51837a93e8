package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class SystemAttributeRes {
    
    private EAttributeType attributeType;
    
    private Integer id;
    
    private Integer requestId;
    
    @Setter
    private Integer reviewId;
    
    private String code;
    
    private String name;
    
    private String description;
    
    private String dataType;
    
    private String dataTypeDisplay;
    
    private List<String> operators;
    
    private String regexValidation;
    
    private String resourcePath;
    
    private Boolean supportFilter;
    
    private ECommonStatus status;
    
    private EApprovalStatus approvalStatus;
    
    private Date createdAt;
    
    private String createdBy;
    
    private Date approvedAt;
    
    private String approvedBy;
    
    private String rejectedReason;
    
}
