package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberTransactionRes {
    private Long memberId;
    private String memberCode;
    private String memberName;
    private String phoneNo;
    private String dob;
    private String gender;
    private String email;
    private String identifyType;
    private String identifyNo;
    private String address;
    private String avatar;
    private String status;
}
