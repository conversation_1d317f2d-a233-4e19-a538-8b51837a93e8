package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OtherInfo implements Serializable {

    private static final long serialVersionUID = 9126554713423028759L;

    @JsonProperty("created_at")
    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_at")
    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_at")
    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("edit_key")
    private String editKey;

}
