package com.oneid.loyalty.accounting.ops.datasource.checkout.repository;

import com.oneid.loyalty.accounting.ops.datasource.checkout.entity.TransactionRedeemInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface TransactionRedeemInfoRepository
        extends JpaRepository<TransactionRedeemInfo, Long>, JpaSpecificationExecutor<TransactionRedeemInfo> {
    List<TransactionRedeemInfo> findByCpmTransactionRef(String cpmTransactionRef);
}
