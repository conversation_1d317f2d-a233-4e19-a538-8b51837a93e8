package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProfileAttribute;
import com.oneid.oneloyalty.common.entity.*;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramDetailRes extends ProgramRes {

    private static final long serialVersionUID = 2221113337027863198L;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("website")
    private String website;

    @JsonProperty("hotline")
    private String hotline;

    @JsonProperty("program_name_en")
    private String programNameEn;

    @JsonProperty("edit_key")
    private String editKey;

    @JsonProperty("description_en")
    private String descriptionEn;

    @JsonProperty("levels_info")
    private Set<ProgramLevelInfo> levelsInfo;

    @JsonProperty("functions_info")
    private Map<String, List<ProgramFunctionInfo>> functionsInfo;

    @JsonProperty("others_info")
    private Map<String, OtherInfo> otherInfo = new HashMap<>();

    @JsonProperty("program_ref_info")
    private RefProgramDto programRefInfo;

    @Getter
    @Setter
    @Builder
    public static class RefProgramDto {
        @JsonProperty("ref_program_id")
        public Integer refProgramId;
        @JsonProperty("ref_program_name")
        public String refProgramName;
        @JsonProperty("ref_program_code")
        public String refProgramCode;
    }

    public static ProgramDetailRes valueOf(Program loyaltyProgram) {
        ProgramDetailRes programRes = new ProgramDetailRes();
        programRes.setProgramId(loyaltyProgram.getId());
        programRes.setBusinessId(loyaltyProgram.getBusinessId());
        programRes.setDescription(loyaltyProgram.getDescription());
        programRes.setProgramCode(loyaltyProgram.getCode());
        programRes.setProgramName(loyaltyProgram.getName());
        programRes.setStatus(loyaltyProgram.getStatus());
        programRes.setWebsite(loyaltyProgram.getWebsite());
        programRes.setHotline(loyaltyProgram.getHotline());

        programRes.setProgramNameEn(loyaltyProgram.getEnName());
        programRes.setDescriptionEn(loyaltyProgram.getEnDescription());

        programRes.setCreatedAt(loyaltyProgram.getCreatedAt());
        programRes.setUpdatedAt(loyaltyProgram.getUpdatedAt());
        programRes.setApprovedAt(loyaltyProgram.getApprovedAt());
        programRes.setCreatedBy(loyaltyProgram.getCreatedBy());
        programRes.setUpdatedBy(loyaltyProgram.getUpdatedBy());
        programRes.setApprovedBy(loyaltyProgram.getApprovedBy());
        programRes.setLogoUrl(loyaltyProgram.getLogoUrl());
        programRes.setAutoRegister(Objects.nonNull(loyaltyProgram.getAutoRegister()) ? loyaltyProgram.getAutoRegister().getValue() : null);
        programRes.setProgramRef(loyaltyProgram.getProgramRef());
        return programRes;
    }

    public void withProgramCorporations(List<ProgramCorporation> programCorporations, List<Corporation> corporations) {
        Set<CorporationInfo> corporationInfos = new HashSet<>();
        corporations.stream().forEach(item -> {
            corporationInfos.add(CorporationInfo.valueOf(item));
        });
        this.setCorporationsInfo(corporationInfos);

        OtherInfo otherInfo = new OtherInfo();
        List<ProgramCorporation> programCorporationSorted = programCorporations
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(ele -> Objects.nonNull(ele.getCreatedAt()))
                .sorted(Comparator.comparing(ProgramCorporation::getCreatedAt))
                .collect(Collectors.toList());
        if (!programCorporationSorted.isEmpty()) {
            otherInfo.setCreatedAt(programCorporationSorted.get(0).getCreatedAt());
            otherInfo.setCreatedBy(programCorporationSorted.get(0).getCreatedBy());
            otherInfo.setUpdatedAt(programCorporationSorted.get(programCorporationSorted.size() - 1).getUpdatedAt());
            otherInfo.setUpdatedBy(programCorporationSorted.get(programCorporationSorted.size() - 1).getUpdatedBy());
            otherInfo.setApprovedAt(programCorporationSorted.get(programCorporationSorted.size() - 1).getApprovedAt());
            otherInfo.setApprovedBy(programCorporationSorted.get(programCorporationSorted.size() - 1).getApprovedBy());
        }
        this.otherInfo.put("program_corporation", otherInfo);
    }

    public void withProgramLevels(List<ProgramLevel> levels) {
        Set<ProgramLevelInfo> programLevelInfo = new HashSet<>();
        levels.stream().forEach(item -> {
            programLevelInfo.add(ProgramLevelInfo.valueOf(item));
        });
        this.levelsInfo = programLevelInfo;
    }

    public void withProgramFunctions(List<ProgramFunction> programFunctions, List<Function> functions, List<ProgramFunctionProfileAttribute> profileAttributes) {
        this.functionsInfo = collectFunctionInfo(programFunctions, functions, profileAttributes);

        OtherInfo otherInfo = new OtherInfo();
        List<ProgramFunction> programFunctionsSorted = programFunctions.stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(ele -> Objects.nonNull(ele.getCreatedAt()))
                .sorted(Comparator.comparing(ProgramFunction::getCreatedAt))
                .collect(Collectors.toList());
        if (!programFunctionsSorted.isEmpty()) {
            otherInfo.setCreatedAt(programFunctionsSorted.get(0).getCreatedAt());
            otherInfo.setCreatedBy(programFunctionsSorted.get(0).getCreatedBy());
            otherInfo.setUpdatedAt(programFunctionsSorted.get(programFunctionsSorted.size() - 1).getUpdatedAt());
            otherInfo.setUpdatedBy(programFunctionsSorted.get(programFunctionsSorted.size() - 1).getUpdatedBy());
            otherInfo.setApprovedAt(programFunctionsSorted.get(programFunctionsSorted.size() - 1).getApprovedAt());
            otherInfo.setApprovedBy(programFunctionsSorted.get(programFunctionsSorted.size() - 1).getApprovedBy());
        }
        this.otherInfo.put("program_function", otherInfo);
    }

    public static Map<String, List<ProgramFunctionInfo>> collectFunctionInfo(List<ProgramFunction> programFunctions, List<Function> functions, List<ProgramFunctionProfileAttribute> profileAttributes) {
        Set<String> functionCodes = programFunctions
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .map(ProgramFunction::getFunctionCode)
                .collect(Collectors.toSet());
        Map<String, Set<EProfileAttribute>> profileAttributeMap = profileAttributes
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .collect(Collectors
                        .groupingBy(ProgramFunctionProfileAttribute::getFunction,
                                Collectors.mapping(ProgramFunctionProfileAttribute::getProfileAttribute,
                                        Collectors.toSet())));
        return functions
                .stream()
                .map(ele -> ProgramFunctionInfo.valueOf(ele, functionCodes, profileAttributeMap))
                .collect(Collectors.groupingBy(ProgramFunctionInfo::getGroupName));
    }

    public void withRefProgram(Program refProgram) {
        RefProgramDto ref = RefProgramDto.builder()
                .refProgramCode(refProgram.getCode())
                .refProgramId(refProgram.getId())
                .refProgramName(refProgram.getName())
                .build();
        this.programRefInfo = ref;
    }

    public void withBusiness(Business business) {
        this.businessId = business.getId();
        this.businessName = business.getName();
        this.businessCode = business.getCode();
    }
}