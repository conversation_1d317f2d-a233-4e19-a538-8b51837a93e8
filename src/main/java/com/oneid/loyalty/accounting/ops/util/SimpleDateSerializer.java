package com.oneid.loyalty.accounting.ops.util;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

public class SimpleDateSerializer extends StdSerializer<Date> {
    private static final long serialVersionUID = -2241757658255994893L;
    
    private String pattern = "yyyyMMdd";
    
    public SimpleDateSerializer() {
        super(Date.class);
    }

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeString(new SimpleDateFormat(pattern).format(value));
    }

}
