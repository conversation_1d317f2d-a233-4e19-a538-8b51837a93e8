package com.oneid.loyalty.accounting.ops.util;

import java.security.MessageDigest;

public class OECrypt {
//    private static String encKey = "ladfioaerealerlclad98162";
    private static String encKey = "aadfioaerealerlclad98162";


    public static String revEncrypt(String toEncr) {
        byte[] arrEncrypted = null;
        String strEncoded = "";
        strEncoded = new String(arrEncrypted);
        strEncoded = strEncoded.trim();
        strEncoded = strEncoded.substring(1, strEncoded.length() - 2);

        return strEncoded;
    }

    public static String revDecrypt(String toDecr) {
        String strDecrypted = "";
        byte[] arrDecoded = (byte[]) null;
        byte[] arrDecrypted = (byte[]) null;
        arrDecoded = TripleDESUtil.decodeByteArray(toDecr);
        try {
            arrDecrypted = TripleDESUtil.decrypt(encKey, arrDecoded);
        } catch (Exception e) {
            e.printStackTrace();
        }
        strDecrypted = new String(arrDecrypted);
        strDecrypted = strDecrypted.trim();
        return strDecrypted;
    }

    public static String revDecrypta(String toDecr) {
        String strDecrypted = "";
        byte[] arrDecoded = null;
        byte[] arrDecrypted = null;

        arrDecoded = toDecr.getBytes();
        strDecrypted = new String(arrDecrypted);
        strDecrypted = strDecrypted.trim();

        return strDecrypted;
    }


    public static byte[] encrypt(String toEncrypt) {
        MessageDigest md = null;
        byte[] arrEncrypted = null;
        try {
            md = MessageDigest.getInstance("MD5");
            arrEncrypted = null;
            md.update(toEncrypt.getBytes());
            arrEncrypted = md.digest();
        } catch (Exception cnse) {
            cnse.printStackTrace();
        }

        return arrEncrypted;
    }
}