package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusDetailAvailableRes extends MemberStatusDetailRes {

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private Integer version;

    private String editKey;
}
