package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECancellation;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CpmTransactionRes implements Serializable {

    private static final long serialVersionUID = 1970328645779012187L;

    private String cpmTransactionRef;

    private String invoiceNo;

    private String locTransactionRef;

    private ShortEntityRes corporation;

    private ShortEntityRes terminal;

    private String serviceCode;

    private String channelCode;

    private EIdType productAccountType;

    private String productAccountCode;

    private Long memberId;

    private BigDecimal grossAmount;

    private BigDecimal redeemPoint;

    private String currency;

    private EBoolean cancellationStatus;

    private Date cancellationTime;

    private Date transactionTime;

    private ETransactionStatus status;
}
