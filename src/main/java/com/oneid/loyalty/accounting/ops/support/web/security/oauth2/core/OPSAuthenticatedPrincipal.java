package com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.AccessControlConverter;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class OPSAuthenticatedPrincipal implements OAuth2AuthenticatedPrincipal {
    @JsonProperty("user_id")
    private Long id;
    
    @JsonProperty("user_name")
    private String userName;
    
    @JsonProperty("user_email")
    private String userEmail;
    
    @JsonDeserialize(converter = AccessControlConverter.class, keyAs = AccessRole.class)
    private Map<AccessRole, Long> permissions;
    
    @Override
    public String getName() {
        return userName;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return Collections.emptyMap();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }
}