package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationPreviewRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import org.springframework.data.domain.Page;

import java.util.List;

public interface OpsProgramCorporationService {
    Page<ProgramCorporationRes> searchProgramCorporation(Integer businessId, String programCode,
                                                         ECommonStatus status,
                                                         Integer offset,
                                                         Integer limit);

    ProgramCorporationPreviewRes getAvailableProgramCorporationRequest(Integer programId);

    ProgramCorporationPreviewRes getInReviewProgramCorporationRequestById(Integer reviewId);

    ProgramCorporationPreviewRes getEditAttributeRequestSetting(Integer reviewId);

    MakerCheckerInternalMakerRes requestEditingAttributeRequest(Integer id, UpdateProgramCorporationReq request);

    List<ProgramDropDownRes> getProgramByBusinessExcludeExist(Integer businessId);

    MakerCheckerInternalMakerRes create(CreateProgramCorporationReq request);

    void update(ApprovalReq req);

    Page<ProgramCorporationPreviewRes> getProgramCorporationInReview(EApprovalStatus approvalStatus, Integer offset, Integer limit);
}
