package com.oneid.loyalty.accounting.ops.service;

import java.time.LocalDate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.SystemAttributeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.SystemAttributeRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

public interface OpsSystemAttributeService {
    
    Page<SystemAttributeRes> getRequestPage(String code, String name, ECommonStatus status, EApprovalStatus approvalStatus, Pageable pageable);
    
    SystemAttributeRes getRequestById(Integer requestId);
    
    SystemAttributeRes getChangeableRequestById(Integer requestId);
    
    SystemAttributeRes requestChange(Integer requestId, SystemAttributeUpdateReq req);
    
    Page<SystemAttributeRes> getInReviewPage(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable);
    
    SystemAttributeRes getInReviewById(int reviewId);
    
}
