package com.oneid.loyalty.accounting.ops.config;

import com.oneid.loyalty.accounting.ops.kafka.config.KafkaConfigParam;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableKafka
public class KafkaConfiguration {

    @Autowired
    private KafkaConfigParam kafkaConfigParam;


    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> config = new HashMap<>();

        config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfigParam.kafkaBootstrapServers);
        config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (kafkaConfigParam.sslEnabled) {
            config.put("security.protocol", "SSL");
            config.put("ssl.truststore.location", kafkaConfigParam.truststoreLocation);
            config.put("ssl.truststore.password", kafkaConfigParam.truststorePassword);
            config.put("ssl.keystore.location", kafkaConfigParam.keystoreLocation);
            config.put("ssl.keystore.password", kafkaConfigParam.keystorePassword);
            config.put("ssl.key.password", kafkaConfigParam.keyPassword);
        }

        return new DefaultKafkaProducerFactory<>(config);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate(producerFactory());
    }
}
