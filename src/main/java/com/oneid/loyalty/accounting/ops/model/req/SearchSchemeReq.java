package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.oneid.loyalty.accounting.ops.util.EpochTimeDeserialize;
import com.oneid.loyalty.accounting.ops.validation.TimeStamp;
import com.oneid.loyalty.accounting.ops.validation.search.SortableAttribute;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.ConstraintViolationException;
import java.util.Date;
import java.util.HashMap;

@Getter
@Setter
public class SearchSchemeReq extends SortableAttribute {
    @JsonProperty("business_id")
    private Integer businessId;
    // TODO: sort by created date

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("scheme_type")
    private ESchemeType schemeType;

    @JsonProperty("scheme_code")
    private String schemeCode;

    @JsonProperty("status")
    private ECommonStatus status;

    @TimeStamp
    @JsonProperty("start_date")
    private Date startDate;

    @TimeStamp
    @JsonProperty("end_date")
    private Date endDate;

    @Override
    protected void initMapPropsToAttribute() {
        mapPropsToAttribute.put("program_id", "id");
        mapPropsToAttribute.put("created_at", "createdAt");
        mapPropsToAttribute.put("updated_at", "updatedAt");
    }

    @Override
    protected String getSortByDefault() {
        if(sortBy != null && !mapPropsToAttribute.containsKey(sortBy))
            throw new ConstraintViolationException(String.format("Cannot sort by %s", sortBy), null);

        return "id";
    }
}