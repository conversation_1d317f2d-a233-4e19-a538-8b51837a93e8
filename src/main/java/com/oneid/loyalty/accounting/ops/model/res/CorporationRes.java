package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
public class CorporationRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("en_name")
    private String enName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("contact_person")
    private String contactPerson;

    @JsonProperty("email_address")
    private String emailAddress;

    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("tax_no")
    private String taxNo;

    @JsonProperty("website")
    private String webSite;

    @JsonProperty("address1")
    private String address1;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @JsonProperty("service_registration_no")
    private String serviceRegistrationNo;

    @JsonProperty("settlement_mode")
    private String settlementMode;

    @JsonProperty("card_limit_in_stock")
    private Integer cardLimitInStock;

    @JsonProperty("tax_identification_number")
    private Integer taxIdentificationNumber;

    @JsonProperty("postal_code")
    private String postalCode;

    @JsonProperty("address2")
    private String address2;

    public static CorporationRes valueOf(Corporation corporation, Business business) {
        CorporationRes corporationRes = of(corporation, null);
        
        corporationRes.setBusinessName(business.getName());
        return corporationRes;
    }
    
    public static CorporationRes of(Corporation corporation, Map<Integer, String> businessIdToName) {
        CorporationRes corporationRes = new CorporationRes();
        corporationRes.setId(corporation.getId());
        corporationRes.setCode(corporation.getCode());
        corporationRes.setBusinessId(corporation.getBusinessId());
        corporationRes.setBusinessName(businessIdToName != null ? businessIdToName.get(corporation.getBusinessId()) : null);
        corporationRes.setName(corporation.getName());
        corporationRes.setEnName(corporation.getEnName());
        corporationRes.setDescription(corporation.getDescription());
        corporationRes.setEnDescription(corporation.getEnDescription());
        corporationRes.setStatus(corporation.getStatus() != null ? corporation.getStatus().getValue() : null);
        corporationRes.setCreatedBy(corporation.getCreatedBy());
        corporationRes.setUpdatedBy(corporation.getUpdatedBy());
        corporationRes.setApprovedBy(corporation.getApprovedBy());
        corporationRes.setCreatedAt(corporation.getCreatedAt() != null ? corporation.getCreatedAt().toInstant().getEpochSecond() : null);
        corporationRes.setUpdatedAt(corporation.getUpdatedAt() != null ? corporation.getUpdatedAt().toInstant().getEpochSecond() : null);
        corporationRes.setApprovedAt(corporation.getApprovedAt() != null ? corporation.getApprovedAt().toInstant().getEpochSecond() : null);
        corporationRes.setCreatedYmd(corporation.getCreatedYmd());
        corporationRes.setContactPerson(corporation.getContactPerson());
        corporationRes.setEmailAddress(corporation.getEmailAddress());
        corporationRes.setPhoneNo(corporation.getPhoneNo());
        corporationRes.setTaxNo(corporation.getTaxNo());
        corporationRes.setWebSite(corporation.getWebSite());
        corporationRes.setAddress1(corporation.getAddress1());
        corporationRes.setCountryId(corporation.getCountryId());
        corporationRes.setProvinceId(corporation.getProvinceId());
        corporationRes.setDistrictId(corporation.getDistrictId());
        corporationRes.setWardId(corporation.getWardId());
        corporationRes.setServiceStartDate(corporation.getServiceStartDate() != null ? corporation.getServiceStartDate().toInstant().getEpochSecond() : null);
        corporationRes.setServiceEndDate(corporation.getServiceEndDate() != null ? corporation.getServiceEndDate().toInstant().getEpochSecond() : null);
        corporationRes.setServiceRegistrationNo(corporation.getServiceRegistrationNo());
        corporationRes.setSettlementMode(corporation.getSettlementMode() != null ? corporation.getSettlementMode().getValue() : null);
        corporationRes.setCardLimitInStock(corporation.getCardLimitInStock());
        corporationRes.setTaxIdentificationNumber(corporation.getTaxIdentificationNumber());
        corporationRes.setPostalCode(corporation.getPostalCode());
        corporationRes.setAddress2(corporation.getAddress2());
        return corporationRes;
    }
}
