package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchemeAndRuleRes {

    @JsonProperty("scheme_id")
    private Integer schemeId;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("scheme_code")
    private String schemeCode;

    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @JsonProperty("scheme_name")
    private String schemeName;

    private String description;

    @JsonProperty("scheme_type")
    private ESchemeType schemeType;

    @JsonProperty("pool_id")
    private Integer poolId;

    private ECommonStatus status;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("rule_list")
    private List<CreateRuleRecordRes> ruleList;
}