package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.PoolDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.PoolEnum;
import com.oneid.loyalty.accounting.ops.model.res.PoolRes;
import com.oneid.loyalty.accounting.ops.service.OpsPoolService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.List;

@RestController
@RequestMapping(value = "v1/pools")
@Validated
public class PoolController extends BaseController {
    @Autowired
    OpsPoolService opsPoolService;

    @GetMapping("enum")
    public ResponseEntity<?> getAll(@RequestParam(name = "program_id", required = true) Integer programId) {
        List<PoolEnum> result = opsPoolService.getAllActiveByProgram(programId);
        return success(result);
    }

    @GetMapping("")
    @Authorize(role = AccessRole.POOL, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterPools(@RequestParam(name = "business_id", required = false) Integer businessId,
                                         @RequestParam(name = "program_id", required = false) Integer programId,
                                         @RequestParam(name = "pool_id", required = false) Integer poolId,
                                         @RequestParam(name = "currency_id", required = false) Integer currencyId,
                                         @RequestParam(name = "status", required = false)
                                         @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                         @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                         @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<PoolRes> page = opsPoolService.filterPool(businessId, programId, poolId, currencyId, status, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{poolId}")
    @Authorize(role = AccessRole.POOL, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getPool(@PathVariable(value = "poolId") Integer poolId) {
        PoolDetailRes result = opsPoolService.getPool(poolId);
        return success(result);
    }

    @PostMapping("create")
    @Authorize(role = AccessRole.POOL, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createPool(@RequestBody @Valid PoolCreateReq poolCreateReq) {
        opsPoolService.addPool(poolCreateReq);
        return success(null);
    }

    @PostMapping("{poolId}/update")
    @Authorize(role = AccessRole.POOL, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updatePool(@PathVariable(value = "poolId") Integer poolId,
                                        @RequestBody @Valid PoolUpdateReq poolUpdateReq) {
        opsPoolService.updatePool(poolId, poolUpdateReq);
        return success(null);
    }
    
    @GetMapping("/sharing/program/{programId}")
    @Authorize(role = AccessRole.POOL, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getSharedPools(@PathVariable(value = "programId") Integer programId) {
        return success(opsPoolService.getSharedPools(programId));
    }
}