package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractRes implements Serializable {

    public static final int SUCCESS_CODE = 200;

    private static final long serialVersionUID = 4812120694069481680L;

    @JsonProperty("meta")
    protected Meta meta;

    @JsonIgnore
    public boolean isSuccess() {
        return this.meta != null && this.meta.getCode() == SUCCESS_CODE;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Meta {

        @JsonProperty("request_id")
        private String requestId;

        @JsonProperty("code")
        private Integer code;

        @JsonProperty("message")
        private String message;
    }
}