package com.oneid.loyalty.accounting.ops.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {OpsCodeValidator.class})
public @interface OpsCode {
    String message() default "Code invalid format";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
