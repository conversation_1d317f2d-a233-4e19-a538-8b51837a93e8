package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.entity.Rule;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RuleReq implements Serializable {
    private static final long serialVersionUID = 2813661866947854920L;

    private Integer id;

    private EConditionType conditionLogic;

    private String code;

    private List<RuleConditionReq> conditions;

    private boolean isArchive;

    //    @NotBlank(message = "rule 'name' mus not be blank")
//    @Length(max = 255, message = "rule 'name' max length 255")
    private String name;

    //    @NotNull(message = "rule 'start_date' mus not be null")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date startDate;

    //    @NotNull(message = "rule 'end_date' mus not be null")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date endDate;

    @Size(max = 255, message = "'description' in rule max length {max}")
    private String description;

    public boolean hasEdit(Rule rule) {
        if (!Objects.equals(this.conditionLogic, rule.getRuleLogic())) {
            return true;
        }
        if (!Objects.equals(this.name, rule.getName())) {
            return true;
        }
        if (!Objects.equals(this.startDate, rule.getStartDate())) {
            return true;
        }
        if (!Objects.equals(this.endDate, rule.getEndDate())) {
            return true;
        }

        return false;
    }
}