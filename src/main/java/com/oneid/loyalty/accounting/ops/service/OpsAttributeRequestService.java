package com.oneid.loyalty.accounting.ops.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.CreateAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.EditAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.DataTypeDisplayRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

public interface OpsAttributeRequestService {
    Integer requestCreatingAttributeRequest(CreateAttributeRequestReq req);
    
    Page<AttributeRequestRes> getAvailableAttributeRequests(
            EAttributeType type,
            Integer businessId,
            Integer programId,
            String code,
            String name,
            ECommonStatus status,
            EApprovalStatus approvalStatus,
            Pageable pageable);
    
    Page<AttributeRequestRes> getInReviewAttributeRequests(
            EAttributeType type,
            EApprovalStatus approvalStatus, 
            Integer fromDate, 
            Integer toDate, 
            Pageable pageable);

    AttributeRequestRes getAvailableAttributeRequestById(Integer requestId, EAttributeType type);

    AttributeRequestRes getInReviewAttributeRequestById(Integer reviewId, EAttributeType type);
    
    Integer requestEditingAttributeRequest(Integer requestId, EditAttributeRequestReq req, EAttributeType type);

    AttributeRequestRes getEditAttributeRequestSetting(Integer requestId);

    List<DataTypeDisplayRes> getDataTypeDisplay();

}