package com.oneid.loyalty.accounting.ops.support.web.security.authentication;

import java.util.Collection;
import java.util.Collections;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.authentication.OPSAuthenticationToken;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class OPSAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {
    private ObjectMapper objectMapper;

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
        return new OPSAuthenticationToken(jwt, authorities, extractPrincipal(jwt, authorities));
    }

    protected Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {
        return Collections.emptyList();
    }

    protected OPSAuthenticatedPrincipal extractPrincipal(Jwt jwt, Collection<GrantedAuthority> authorities) {
        return objectMapper.convertValue(jwt.getClaims(), OPSAuthenticatedPrincipal.class);
    }
}