package com.oneid.loyalty.accounting.ops.mapper;

import java.util.Date;

import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateChainReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;

public class ChainMapper {
    public static Chain toChainOne(Chain chain, ChainUpdateReq request) {
        chain.setName(request.getName());
        chain.setDescription(request.getDescription());
        chain.setServiceStartDate(new Date(request.getServiceStartDate() * 1000));
        chain.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        chain.setContactPerson(request.getContactPerson());
        chain.setEmailAddress(request.getEmailAddress());
        chain.setAddress1(request.getAddress1());
        chain.setAddress2(request.getAddress2());
        chain.setCountryId(request.getCountryId());
        chain.setProvinceId(request.getProvinceId());
        chain.setDistrictId(request.getDistrictId());
        chain.setWardId(request.getWardId());
        chain.setPostalCode(request.getPostalCode());
        chain.setPhoneNo(request.getPhoneNo());
        chain.setWebSite(request.getWebsite());
        chain.setStatus(ECommonStatus.of(request.getStatus()));
        return chain;
    }
    
    public static Chain toChainOne(CreateChainReq request, Business business, Corporation corporation) {
        Chain chain = new Chain();
        
        chain.setCode(request.getCode());
        chain.setBusinessId(business.getId());
        chain.setCorporationId(corporation.getId());
        
        chain.setName(request.getName());
        chain.setDescription(request.getDescription());
        chain.setServiceStartDate(new Date(request.getServiceStartDate() * 1000));
        chain.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        chain.setContactPerson(request.getContactPerson());
        chain.setEmailAddress(request.getEmailAddress());
        chain.setAddress1(request.getAddress1());
        chain.setAddress2(request.getAddress2());
        chain.setCountryId(request.getCountryId());
        chain.setProvinceId(request.getProvinceId());
        chain.setDistrictId(request.getDistrictId());
        chain.setWardId(request.getWardId());
        chain.setPostalCode(request.getPostalCode());
        chain.setPhoneNo(request.getPhoneNo());
        chain.setWebSite(request.getWebsite());
        chain.setStatus(ECommonStatus.of(request.getStatus()));
        return chain;
    }
}
