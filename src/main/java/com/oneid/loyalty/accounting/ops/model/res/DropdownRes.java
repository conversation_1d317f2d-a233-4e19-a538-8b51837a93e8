package com.oneid.loyalty.accounting.ops.model.res;

import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.Store;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DropdownRes {

    public DropdownRes(Integer id, String code, String name) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public DropdownRes(Integer id, String code, String name, String evName) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.enName = evName;
    }

    public static DropdownRes valueOf(ProgramTier iter){
        DropdownRes res = new DropdownRes(
                iter.getId(),
                iter.getTierCode(),
                iter.getName()
        );
        res.setIcon(iter.getBadgeIconUrl());
        return res;
    }

    private Integer id;

    private String code;

    private String name;

    private String enName;

    private String icon;
}