package com.oneid.loyalty.accounting.ops.support.web.argument.resolver;

import javax.validation.ConstraintViolationException;

import org.springframework.core.MethodParameter;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.data.web.SortArgumentResolver;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

import com.oneid.loyalty.accounting.ops.support.data.databind.OffsetSetting;
import com.oneid.loyalty.accounting.ops.support.data.domain.MakerCheckerOffsetPageRequest;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class MakerCheckerOffsetPageRequestArgumentResolver extends PageableHandlerMethodArgumentResolver {

    public static final String OFFSET = "offset";
    public static final String LIMIT = "limit";
    
    private SortArgumentResolver sortResolver;
    
    private OffsetSetting offsetSetting;
    
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(MakerCheckerOffsetPageable.class) != null;
    }
    
    @Override
    public Pageable resolveArgument(MethodParameter methodParameter, ModelAndViewContainer mavContainer,
            NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        String offset = webRequest.getParameter(OFFSET);
        String limit = webRequest.getParameter(LIMIT);
        Sort sort = sortResolver.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);
        
        return new MakerCheckerOffsetPageRequest(
                parseAndApplyDefault(offset, offsetSetting.getDefaultOffset(), offsetSetting.getMinOffset(), null, "offset"), 
                parseAndApplyDefault(limit, offsetSetting.getDefaultLimit(), offsetSetting.getMinLimit(), offsetSetting.getMaxLimit(), "limit"),
                sort);
    }
    
    private Integer parseAndApplyDefault(String parameter, int defaultValue, int min, Integer max, String label) {
        if (!StringUtils.hasText(parameter)) {
            return defaultValue;
        }

        try {
            int value = Integer.parseInt(parameter);
            
            if(value < min)
                throw new ConstraintViolationException(String.format("'%s' must be greater than or equal to %s ", label, min), null);
            
            if(max != null && value > max)
                throw new ConstraintViolationException(String.format("'%s' must be less than or equal to  %s ", label, max), null);
            
            return value;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

}
