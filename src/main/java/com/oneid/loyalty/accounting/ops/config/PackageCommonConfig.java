package com.oneid.loyalty.accounting.ops.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@ComponentScan("com.oneid.oneloyalty.common")
@EnableJpaRepositories("com.oneid.oneloyalty.common.repository")
@EntityScan("com.oneid.oneloyalty.common.entity")
public class PackageCommonConfig {
}
