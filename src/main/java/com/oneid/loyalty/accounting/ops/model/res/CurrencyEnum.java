package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Currency;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class CurrencyEnum {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    public static CurrencyEnum of(Currency currency) {
        return new CurrencyEnum(
                currency.getId(),
                currency.getCode(),
                currency.getName(),
                currency.getStatus().getValue()
        );
    }
}
