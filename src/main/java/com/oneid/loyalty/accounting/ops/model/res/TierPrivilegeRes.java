package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.TierPrivilegeReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierPrivilegeRes {
    private Integer id;
    private String name;
    private String description;
    private String enName;
    private String enDescription;
    private Integer displayOrder;
    private String icon;
    private String partnerSupplierName;
    private Integer partnerSupplierId;
    private String brand;
    private String action;
    private ECommonStatus status;

    public static TierPrivilegeRes valueOf(TierPrivilegeReq privilegeReq) {
        return TierPrivilegeRes.builder()
                .id(privilegeReq.getId())
                .name(privilegeReq.getName())
                .description(privilegeReq.getDescription())
                .enName(privilegeReq.getEnName())
                .enDescription(privilegeReq.getEnDescription())
                .displayOrder(privilegeReq.getDisplayOrder())
                .icon(privilegeReq.getIcon())
                .partnerSupplierName(privilegeReq.getPartnerSupplierName())
                .partnerSupplierId(privilegeReq.getPartnerSupplierId())
                .brand(privilegeReq.getBrand())
                .action(privilegeReq.getAction())
                .status(privilegeReq.getStatus() != null ? ECommonStatus.valueOf(privilegeReq.getStatus()) : null)
                .build();
    }
}
