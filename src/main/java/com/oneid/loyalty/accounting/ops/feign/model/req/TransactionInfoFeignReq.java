package com.oneid.loyalty.accounting.ops.feign.model.req;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.constant.EAdjustmentType;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransactionInfoFeignReq {
    @JsonProperty("store_id")
    private String storeCode;

    @JsonProperty("terminal_id")
    private String terminalCode;
    
    private Integer channelId;

    @Setter
    private String invoiceNo;

    @Setter
    private BigDecimal gmv;

    @Setter
    private BigDecimal grossAmount;

    @Setter
    private BigDecimal redeemPoint;

    private Long transactionTime;

    private String description;

    private boolean isHolding;
    
    @Setter
    @JsonProperty("adjust_type")
    private EAdjustmentType adjustmentType;
    
    @Setter
    private String reasonCode;
    
    @Setter
    private BigDecimal adjustPoint;
    
    @Setter
    @JsonProperty("pool_id")
    private String poolCode;
}