package com.oneid.loyalty.accounting.ops.validation.search;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {TimeFormatValidator.class})
public @interface TimeFormat {

    String message() default "Invalid date time";

    String format() default "yyyyMMdd";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
