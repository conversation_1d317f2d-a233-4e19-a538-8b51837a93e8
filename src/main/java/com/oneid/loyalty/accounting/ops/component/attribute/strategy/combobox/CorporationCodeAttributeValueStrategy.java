package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CorporationCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private CorporationRepository corporationRepository;

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(CorporationCodeAttributeValueStrategy.class);

    public CorporationCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getCorporationCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {
        Corporation corporation = corporationRepository.findByProgramIdAndCorporationCode(programIds[0], value);

        if (corporation == null) {
            LOGGER.warn("Corporation {} not found", value);
            throw new IllegalArgumentException();
        }

        return AttributeCombobox.builder()
                .name(corporation.getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> corporationCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return corporationRepository.findByProgramIdAndCorporationCodes(programIds[0], corporationCodes)
                    .stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(s.getName())
                            .value(s.getCode())
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}