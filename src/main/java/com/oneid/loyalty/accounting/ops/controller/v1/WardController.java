package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.res.WardDTO;
import com.oneid.loyalty.accounting.ops.service.OpsWardService;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.entity.Ward;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "v1/wards")
@Validated
public class WardController extends BaseController {

    @Autowired
    private OpsWardService opsWardService;

    @GetMapping
    public ResponseEntity<?> getWards(@RequestParam(value = "district_id", required = false) Integer districtId,
                                      @RequestParam(value = "country_code", required = false) String countryCode,
                                      @RequestParam(value = "province_code", required = false) String provinceCode,
                                      @RequestParam(value = "district_code", required = false) String districtCode) {

        List<Ward> wards;

        if (districtId != null) {
            wards = this.opsWardService.getByDistrict(districtId);
        } else {
            wards = this.opsWardService.getByDistrict(countryCode, provinceCode, districtCode);
        }

        return success(wards.stream().map(WardDTO::valueOf).collect(Collectors.toList()));
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getOne(@PathVariable("id") Integer id) {
        return success(this.opsWardService.getOne(id));
    }
}
