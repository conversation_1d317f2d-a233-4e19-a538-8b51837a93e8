package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SearchChangeRequestsRes<T> {
    private Integer page;
    private Integer pageSize;
    private Integer totalRecordCount;
    private List<RecordChangeRequestRes<T>> records;

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class RecordChangeRequestRes<T> {
        private Long id;
        private Long makerId;
        private String makerName;
        private String module;
        private String actionType;
        private String objectId;
        private T payload;
    }
}