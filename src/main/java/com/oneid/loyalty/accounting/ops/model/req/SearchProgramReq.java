package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.search.MinFieldRequiredForSearch;
import com.oneid.loyalty.accounting.ops.validation.search.SortableAttribute;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Sort;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Valid
@MinFieldRequiredForSearch(count = 2, ignores = {"mapPropsToAttr"}) // partner is required + 1 field
public class SearchProgramReq  extends SortableAttribute {

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    private ECommonStatus status;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("program_name")
    private String programName;

    @Override
    protected void initMapPropsToAttribute() {
        mapPropsToAttribute = new HashMap<>();
        mapPropsToAttribute.put("program_code", "code");
        mapPropsToAttribute.put("program_name", "name");
        mapPropsToAttribute.put("status", "status");
        mapPropsToAttribute.put("business_id", "businessId");
        mapPropsToAttribute.put("program_id", "id");
        mapPropsToAttribute.put("program_name", "name");
    }

    @Override
    protected String getSortByDefault() {
        return "businessId";
    }
}