package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CpmTxnRefundReq {

    private CustomerIdentify customerIdentifier;

    private String originalInvoiceNo;

    private Long transactionTime;

    private String description;

    private String txnRefNo;

    private String updatedBy;
}
