package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateMemberCPRFeignReq {
    private String businessCode;
    private String programCode;
    private String storeCode;
    private Integer storeId;
    private Integer noOfCard;
    private String cardBinCode;
    private String cardTypeCode;
    private String description;
    private ECommonStatus status;
    private String userId;
    private Long createdAt;
}
