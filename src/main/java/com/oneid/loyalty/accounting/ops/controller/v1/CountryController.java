package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.res.CountryDTO;
import com.oneid.loyalty.accounting.ops.service.OpsCountryService;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.service.CountryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "v1/countries")
@Validated
public class CountryController extends BaseController {

    @Autowired
    private CountryService countryService;

    @Autowired
    private OpsCountryService opsCountryService;

    @GetMapping
    public ResponseEntity<?> getAllCountries() {
        return success(this.countryService.getAll().stream()
                .map(e -> CountryDTO.valueOf(e))
                .collect(Collectors.toList()));
    }

    @GetMapping("{id}")
    public ResponseEntity<?> getOne(@PathVariable("id") Integer id) {
        return success(this.opsCountryService.getOne(id));
    }
}
