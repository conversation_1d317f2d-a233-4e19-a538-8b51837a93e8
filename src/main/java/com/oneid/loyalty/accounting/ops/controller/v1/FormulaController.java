package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.VerifyFormulaGroupReq;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "v1/formula")
public class FormulaController extends BaseController {

    @Autowired
    private OpsFormulaService opsFormulaService;

    @PostMapping("verify")
    public ResponseEntity<?> getCurrency(@RequestBody @Valid VerifyFormulaGroupReq req) {
//       nothing business
        return success(null);
    }
}