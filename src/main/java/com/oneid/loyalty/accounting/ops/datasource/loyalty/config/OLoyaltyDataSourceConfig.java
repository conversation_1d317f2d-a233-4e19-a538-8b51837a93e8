package com.oneid.loyalty.accounting.ops.datasource.loyalty.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "entityManagerFactory",
        transactionManagerRef = "transactionManager",
        basePackages = { "com.oneid.oneloyalty.common.repository.*" }
)
public class OLoyaltyDataSourceConfig {

    @Value("")
    private String loyaltyScheme;

    @Primary
    @Bean(name = "loyaltyDataSourceProperties")
    @ConfigurationProperties(prefix = "spring.oracle-datasource.loyalty")
    public DataSourceProperties loyaltySourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "loyaltyDataSource")
    public DataSource loyaltyDataSource() {
        return loyaltySourceProperties()
                .initializeDataSourceBuilder()
                .build();
    }

    @Primary
    @Bean(name = "loyaltyDataSource")
    @ConfigurationProperties("spring.oracle-datasource.loyalty.hikari")
    public DataSource loyaltyDataSource(@Qualifier("loyaltyDataSourceProperties") DataSourceProperties loyaltyDataSourceProperties) {
        return loyaltyDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Primary
    @Bean(name = "entityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder entityManagerFactoryBuilder, @Qualifier("loyaltyDataSource") DataSource loyaltyDataSource) {

        Map<String, String> loyaltyJpaProperties = new HashMap<>();
        loyaltyJpaProperties.put("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");
//        loyaltyJpaProperties.put("hibernate.default_schema", loyaltyScheme);
        loyaltyJpaProperties.put("hibernate.ddl-auto", "none");
        loyaltyJpaProperties.put("hibernate.hbm2ddl.auto", "none");

        return entityManagerFactoryBuilder
                .dataSource(loyaltyDataSource)
                .packages("com.oneid.oneloyalty.common.entity")
//                .persistenceUnit("oracleDataSource")
                .properties(loyaltyJpaProperties)
                .build();
    }

    @Primary
    @Bean(name = "transactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("entityManagerFactory") EntityManagerFactory entityManagerFactory) {

        return new JpaTransactionManager(entityManagerFactory);
    }
}
