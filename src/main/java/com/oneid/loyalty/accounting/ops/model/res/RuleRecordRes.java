package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode
public class RuleRecordRes {
    @JsonProperty("scheme_id")
    private Integer schemeId;

    @JsonProperty("rule_id")
    private Integer ruleId;

    @JsonProperty("condition_logic")
    private EConditionType conditionLogic;

    @JsonProperty("seq_no")
    private Integer seqNo;

    private ECommonStatus status;

    @JsonProperty("rule_name")
    private String ruleName;

    @JsonProperty("rule_description")
    private String ruleDescription;

    @JsonProperty("condition_list")
    private List<ConditionRecordRes> listCondition;

    @JsonIgnore
    private String createdBy;

    @JsonIgnore
    private Date createdAt;

    @JsonIgnore
    private String updatedBy;

    @JsonIgnore
    private Date updatedAt;

    @JsonIgnore
    private String approvedBy;

    @JsonIgnore
    private Date approvedAt;

    public static RuleRecordRes valueOf(SchemeRule schemeRule) {
        RuleRecordRes result = new RuleRecordRes();
        result.setRuleId(schemeRule.getId());
        result.setStatus(schemeRule.getStatus());
        result.setConditionLogic(schemeRule.getConditionLogic());
        result.setSeqNo(schemeRule.getSeqNo());
        result.setSchemeId(schemeRule.getSchemeId());
        result.setRuleName(schemeRule.getName());
        result.setRuleDescription(schemeRule.getDescription());
        return result;
    }
}