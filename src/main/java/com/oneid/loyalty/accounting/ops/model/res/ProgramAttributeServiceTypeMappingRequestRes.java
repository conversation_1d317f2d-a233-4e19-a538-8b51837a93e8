package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ProgramAttributeServiceTypeMappingRequestRes {
    
    private Integer id;
    
    private String attributeCode;
    
    private String attributeName;
    
    private String attributeDescription;
    
    private Boolean selected;
    
    private Boolean used;
    
}
