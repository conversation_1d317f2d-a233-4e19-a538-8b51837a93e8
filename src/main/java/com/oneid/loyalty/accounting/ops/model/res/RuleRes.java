package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.oneloyalty.common.constant.EConditionType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RuleRes {
    private Integer id;
    private String code;
    private String name;
    private EConditionType conditionLogic;
    private List<RuleConditionRes> conditions;
    private Date startDate;
    private Date endDate;
    private String description;

    public static RuleRes valueOf(RuleReq ruleReq) {
        List<RuleConditionRes> conditions = null;
        if (ruleReq.getConditions() != null) {
            conditions = ruleReq.getConditions().stream()
                    .map(RuleConditionRes::valueOf)
                    .collect(Collectors.toList());
        }
        return RuleRes.builder()
                .id(ruleReq.getId())
                .code(ruleReq.getCode())
                .name(ruleReq.getName())
                .conditionLogic(ruleReq.getConditionLogic())
                .conditions(conditions)
                .startDate(ruleReq.getStartDate())
                .startDate(ruleReq.getEndDate())
                .description(ruleReq.getDescription())
                .build();
    }
}
