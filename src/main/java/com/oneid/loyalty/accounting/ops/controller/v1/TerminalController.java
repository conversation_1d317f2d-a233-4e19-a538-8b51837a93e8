package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TerminalCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TerminalUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.TerminalEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.TerminalRes;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.List;

@RestController
@RequestMapping("v1/terminals")
public class TerminalController extends BaseController {
    @Autowired
    OpsTerminalService opsTerminalService;

    @GetMapping("enum-all")
    public ResponseEntity<?> getEnumAll() {
        List<TerminalEnumAll> result = opsTerminalService.getEnumAll();
        return success(result);
    }

    @GetMapping("")
    @Authorize(role = AccessRole.TERMINAL, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterTerminal(@RequestParam(name = "business_id", required = false) Integer businessId,
                                                @RequestParam(name = "corporation_id", required = false) Integer corporationId,
                                                @RequestParam(name = "chain_id", required = false) Integer chainId,
                                                @RequestParam(name = "store_id", required = false) Integer storeId,
                                                @RequestParam(name = "terminal_name", required = false) String terminalName,
                                                @RequestParam(name = "terminal_code", required = false) String terminalCode,
                                                @RequestParam(name = "status", required = false)
                                                @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                                @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                                @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<TerminalRes> page = opsTerminalService.filter(businessId, corporationId, chainId, storeId, terminalName, terminalCode, status, offset, limit);
        return success(page, offset, limit);
    }

    @PostMapping
    @Authorize(role = AccessRole.TERMINAL, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createTerminal(@RequestBody @Valid TerminalCreateReq terminalCreateReq) {
        this.opsTerminalService.add(terminalCreateReq);
        return success(null);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.TERMINAL, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getTerminal(@PathVariable(value = "id") Integer id) {
        TerminalRes result = opsTerminalService.get(id);
        return success(result);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.TERMINAL, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateTerminal(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid TerminalUpdateReq terminalUpdateReq) {
        this.opsTerminalService.update(id, terminalUpdateReq);
        return success(null);
    }
}
