package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
@JsonDeserialize(builder = PayloadOperatorRes.PayloadOperatorResBuilder.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PayloadOperatorRes {
    private List<OperatorCorp> operatorCorps;

    @JsonPOJOBuilder(withPrefix = "")
    public static class PayloadOperatorResBuilder {
    }

    @Getter
    @Builder
    @JsonDeserialize(builder = OperatorCorp.OperatorCorpBuilder.class)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class OperatorCorp {
        private ShortEntityRes value;

        private List<OperatorChain> operatorChains;

        @JsonPOJOBuilder(withPrefix = "")
        public static class OperatorCorpBuilder {
        }

        @Getter
        @Builder
        @JsonDeserialize(builder = OperatorChain.OperatorChainBuilder.class)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class OperatorChain {
            private ShortEntityRes value;

            private List<OperatorStore> operatorStores;

            @JsonPOJOBuilder(withPrefix = "")
            public static class OperatorChainBuilder {
            }

            @Getter
            @Builder
            @JsonDeserialize(builder = OperatorStore.OperatorStoreBuilder.class)
            @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
            public static class OperatorStore {
                private ShortEntityRes value;

                private List<OperatorTerminal> operatorTerminals;

                @JsonPOJOBuilder(withPrefix = "")
                public static class OperatorStoreBuilder {
                }

                @Getter
                @Builder
                @JsonDeserialize(builder = OperatorTerminal.OperatorTerminalBuilder.class)
                @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
                public static class OperatorTerminal {
                    private ShortEntityRes value;

                    @JsonPOJOBuilder(withPrefix = "")
                    public static class OperatorTerminalBuilder {
                    }
                }
            }
        }
    }
}
