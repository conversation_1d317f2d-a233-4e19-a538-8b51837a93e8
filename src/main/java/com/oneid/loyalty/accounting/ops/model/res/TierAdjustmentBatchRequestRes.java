package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentBatchProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class TierAdjustmentBatchRequestRes {

    private Integer batchNo;
    private ShortEntityRes business;
    private ShortEntityRes program;
    private ETierAdjustmentType type;
    private String createdBy;
    private Date createdAt;
    private String approvedBy;
    private Date approvedAt;
    private EApprovalStatus approvalStatus;
    private ETierAdjustmentBatchProcessStatus processStatus;
    private String description;
    private Date updatedAt;
    private String reason;
    private String referenceCode;
    private Boolean desReplace;
    private Boolean failedRecords;
    private String rejectReason;
}
