package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.constant.GroupNameConstant;
import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.ConditionAttributeGroupInfo;
import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.entity.ProgramAttributeServiceType;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import com.oneid.oneloyalty.common.entity.SchemeRuleCondition;
import com.oneid.oneloyalty.common.entity.SystemAttribute;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.repository.ProgramAttributeServiceTypeRepository;
import com.oneid.oneloyalty.common.repository.ProgramTransactionAttributeRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRepository;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.SchemeRuleConditionService;
import com.oneid.oneloyalty.common.service.SchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsConditionServiceImpl implements OpsConditionService {

    @Value("${rule-condition.attributes.ignored.system-attributes:}")
    List<String> systemAttributesIgnored;

    @Value("${rule-condition.attributes.ignored.program-attributes:}")
    List<String> programAttributesIgnored;

    @Value("${rule-condition.attributes.ignored.transaction-attributes:}")
    List<String> transactionAttributesIgnored;

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private SchemeRuleConditionService schemeRuleConditionService;

    @Autowired
    private AttributeValueFactory attributeValueFactory;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private ProgramTransactionAttributeRepository programTransactionAttributeRepository;

    @Autowired
    private ProgramAttributeRepository programAttributeRepository;

    @Autowired
    private SystemAttributeRepository systemAttributeRepository;

    @Autowired
    private ProgramAttributeServiceTypeRepository programAttributeServiceTypeRepository;

    @Autowired
    private AttributeMasterDataRepository attributeMasterDataRepository;

    @Override
    public void verifyConditions(final Scheme scheme, List<ConditionRecordReq> conditions) {
        Collection<ConditionAttributeDto> attributes = conditionAttributeDtos(scheme.getProgramId());

        final Map<String, ConditionAttributeDto> mapAttribute = attributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                a -> a
        ));
        final Map<String, AttributeValueStrategy<?>> map = getMapConditionStrategy(attributes);
        conditions.forEach(c -> {
            if (!map.containsKey(c.getAttribute())) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_NOT_FOUND, "Attribute not found", conditions);
            }
            ConditionAttributeDto conditionAttributeDto = mapAttribute.get(c.getAttribute());
            if (!conditionAttributeDto.getOperators().contains(c.getOperator().getExpression())) {
                throw new BusinessException(ErrorCode.OPERATOR_NOT_FOUND, "Operator not found", conditions);
            }
            map.get(c.getAttribute()).getWriteValue(
                    c.getAttribute(),
                    c.getOperator(),
                    c.getValue(),
                    scheme.getProgramId()
            );
        });
    }

    @Override
    public List<ConditionRecordRes> getAllByRule(SchemeRule rule) {
        final Scheme scheme = getScheme(rule);
        Collection<ConditionAttributeDto> attributes = conditionAttributeDtos(scheme.getProgramId());
        final Map<String, ConditionAttributeDto> mapAttribute = attributes.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                a -> a
        ));
        final Map<String, AttributeValueStrategy<?>> attributeCodeToStrategyMap = getMapConditionStrategy(attributes);
        return schemeRuleConditionService.getAllByRuleId(rule.getId()).stream()
                .map(f -> this.toRes(scheme.getProgramId(), attributeCodeToStrategyMap.get(f.getAttribute()),
                        f, mapAttribute.get(f.getAttribute()))
                ).collect(Collectors.toList());
    }

    private ConditionRecordRes toRes(Integer programId, AttributeValueStrategy<?> attributeValueStrategy,
                                     SchemeRuleCondition condition, ConditionAttributeDto conditionAttributeDto) {
        ConditionRecordRes res = new ConditionRecordRes();
        res.setId(condition.getId());
        res.setAttribute(condition.getAttribute());
        res.setOperator(condition.getOperator());
        res.setStatus(condition.getStatus());
        res.setDataDisplayType(conditionAttributeDto.getDataTypeDisplay());
        res.setResourcePath(conditionAttributeDto.getResourcePath());
        res.setSupportFilter(conditionAttributeDto.getSupportFilter());
        res.setValue(attributeValueStrategy.getReadValue(condition.getAttribute(), EAttributeOperator.lookup(condition.getOperator()),
                condition.getValue(), programId));
        res.setExistMasterData(conditionAttributeDto.isExistMasterData());
        res.setEnableValidateMasterData(conditionAttributeDto.getEnableValidateMasterData());
        res.setAttributeType(conditionAttributeDto.getAttributeType());
        return res;
    }

    private Map<String, AttributeValueStrategy<?>> getMapConditionStrategy(
            Collection<ConditionAttributeDto> conditionAttributeDtos
    ) {
        return conditionAttributeDtos.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                conditionAttributeDto -> attributeValueFactory.lookup(conditionAttributeDto)
        ));
    }

    private Scheme getScheme(SchemeRule rule) {
        return schemeService.find(rule.getSchemeId()).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND,
                        "[CONDITION_SERVICE]: Scheme not found", rule.getSchemeId())
        );
    }

    @Override
    public Collection<ConditionAttributeGroupInfo> getAllConditionAttribute(Integer programId) {
        return getAllWithFilter(programId, null, false);
    }

    @Override
    public Collection<ConditionAttributeGroupInfo> getByProgramIdAndModule(Integer programId, EServiceType module) {
        List<ProgramAttributeServiceType> listProgramAttributeServiceTypes = programAttributeServiceTypeRepository
                .findByProgramIdAndServiceType(programId, module);

        return getAllWithFilter(programId, listProgramAttributeServiceTypes.stream()
                        .map(ProgramAttributeServiceType::getAttribute)
                        .collect(Collectors.toList()),
                true);
    }

    private Collection<ConditionAttributeGroupInfo> getAllWithFilter(Integer programId, List<String> attributes, boolean filter) {

        Collection<ProgramTransactionAttribute> programTransactionAttributes =
                programTransactionAttributeRepository.findByProgram(programId);

        Collection<ProgramAttribute> programAttributes = programAttributeRepository.findByProgramId(programId);

        Collection<SystemAttribute> systemAttributes = systemAttributeRepository.findAll();

        ConditionAttributeGroupInfo groupTransactionAttribute = ConditionAttributeGroupInfo.builder()
                .groupName(GroupNameConstant.PROGRAM_TRANSACTION_ATTRIBUTE)
                .attributeList(
                        programTransactionAttributes.stream()
                                .filter(attr -> !filter || attributes.contains(attr.getAttribute()))
                                .filter(att -> !transactionAttributesIgnored.contains(att.getAttribute()))
                                .map(attr -> {
                                            boolean existMasterData = attributeMasterDataRepository.existsByAttributeCodeAndAttributeType(attr.getAttribute(), EAttributeType.PROGRAM_TRANSACTION);
                                            return ConditionAttributeDto
                                                    .builder()
                                                    .attribute(attr.getAttribute())
                                                    .name(attr.getName())
                                                    .description(attr.getDescription())
                                                    .operators(attr.getOperators())
                                                    .dataType(attr.getDataType())
                                                    .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                                    .valueValidationPattern(attr.getValueValidationPattern())
                                                    .existMasterData(existMasterData)
                                                    .enableValidateMasterData(attr.getEnableValidateMasterData())
                                                    .attributeType(GroupNameConstant.PROGRAM_TRANSACTION_ATTRIBUTE)
                                                    .build();
                                        }
                                ).collect(Collectors.toList()))
                .build();

        ConditionAttributeGroupInfo groupSystemAttribute = ConditionAttributeGroupInfo.builder()
                .groupName(GroupNameConstant.SYSTEM_ATTRIBUTE)
                .attributeList(
                        systemAttributes.stream()
                                .filter(attr -> !filter || attributes.contains(attr.getAttribute()))
                                .filter(att -> !systemAttributesIgnored.contains(att.getAttribute()))
                                .map(attr ->
                                        ConditionAttributeDto
                                                .builder()
                                                .attribute(attr.getAttribute())
                                                .name(attr.getName())
                                                .description(attr.getDescription())
                                                .operators(attr.getOperators())
                                                .dataType(attr.getDataType())
                                                .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                                .resourcePath(attr.getResourcePath())
                                                .supportFilter(attr.getSupportFilter() == EBoolean.YES ? Boolean.TRUE : Boolean.FALSE)
                                                .valueValidationPattern(attr.getValueValidationPattern())
                                                .build()
                                ).collect(Collectors.toList()))
                .build();

        ConditionAttributeGroupInfo groupProgramAttribute = ConditionAttributeGroupInfo.builder()
                .groupName(GroupNameConstant.PROGRAM_ATTRIBUTE)
                .attributeList(
                        programAttributes.stream()
                                .filter(attr -> !filter || attributes.contains(attr.getCode()))
                                .filter(att -> !programAttributesIgnored.contains(att.getCode()))
                                .map(attr -> {
                                            boolean existMasterData = attributeMasterDataRepository.existsByAttributeCodeAndAttributeType(attr.getCode(), EAttributeType.MEMBER);
                                            return ConditionAttributeDto
                                                    .builder()
                                                    .attribute(attr.getCode())
                                                    .name(attr.getName())
                                                    .description(attr.getDescription())
                                                    .operators(attr.getOperators())
                                                    .dataType(attr.getDataType())
                                                    .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                                    .valueValidationPattern(attr.getValueValidationPattern())
                                                    .existMasterData(existMasterData)
                                                    .enableValidateMasterData(attr.isEnableValidateMasterData())
                                                    .attributeType(GroupNameConstant.PROGRAM_ATTRIBUTE)
                                                    .build();

                                        }
                                ).collect(Collectors.toList()))
                .build();

        return List.of(groupProgramAttribute, groupSystemAttribute, groupTransactionAttribute)
                .stream()
                .filter(att -> att.getAttributeList().size() > 0)
                .collect(Collectors.toList());
    }

    @Override
    public Collection<ConditionAttributeDto> conditionAttributeDtos(Integer programId) {
        Collection<ConditionAttributeGroupInfo> infos = getAllWithFilter(programId, null, false);
        List<ConditionAttributeDto> result = new ArrayList<>();
        for (ConditionAttributeGroupInfo info : infos) {
            result.addAll(info.getAttributeList());
        }
        return result;
    }
}