package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.MasterWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MasterWorkerTierAdjustmentFeignReq;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentBatchRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierAdjustmentBatchService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentBatchProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.TierAdjustmentBatchRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.custom.CustomTierAdjustmentBatchRequestRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.TierAdjustmentBatchRequestService;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpsTierAdjustmentBatchServiceImpl implements OpsTierAdjustmentBatchService {

    @Autowired
    private CustomTierAdjustmentBatchRequestRepository customTierAdjustmentBatchRequestRepository;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private TierAdjustmentBatchRequestService tierAdjustmentBatchRequestService;

    @Autowired
    private TierAdjustmentBatchRequestService batchRequestService;

    @Autowired
    private MasterWorkerFeignClient masterWorkerFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Override
    public Page<TierAdjustmentBatchRequestRes> filterAdjustment(Integer businessId, Integer programId, Integer batchNo, ETierAdjustmentBatchProcessStatus processStatus,
                                                                ETierAdjustmentType type, EApprovalStatus approvalStatus, String createdBy, Date createdStart, Date createdEnd,
                                                                String approvedBy, Date approvedStart, Date approvedEnd, Boolean failedRecords, Pageable pageable) throws ParseException {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + (1000 * 60 * 60 * 24)) : null;
        approvedEnd = approvedEnd != null ? new Date(approvedEnd.getTime() + (1000 * 60 * 60 * 24)) : null;
        Page<Object[]> result = customTierAdjustmentBatchRequestRepository.filter(businessId, programId, batchNo, processStatus, type, approvalStatus, createdBy, createdStart, createdEnd, approvedBy, approvedStart, approvedEnd, failedRecords, pageable);

        List<TierAdjustmentBatchRequestRes> tierAdjustmentBatchRequestRes = result.stream().map(
                batch -> {
                    TierAdjustmentBatchRequest tierAdjustmentBatchRequest = (TierAdjustmentBatchRequest) batch[0];
                    Business business = (Business) batch[1];
                    Program program = (Program) batch[2];

                    boolean failed = tierAdjustmentBatchRequest.getFailedRequests() != null && tierAdjustmentBatchRequest.getFailedRequests() > 0;

                    return TierAdjustmentBatchRequestRes.builder()
                            .batchNo(tierAdjustmentBatchRequest.getId())
                            .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .type(tierAdjustmentBatchRequest.getType())
                            .createdBy(tierAdjustmentBatchRequest.getCreatedBy())
                            .createdAt(tierAdjustmentBatchRequest.getCreatedAt())
                            .approvedBy(tierAdjustmentBatchRequest.getApprovedBy())
                            .approvedAt(tierAdjustmentBatchRequest.getApprovedAt())
                            .approvalStatus(tierAdjustmentBatchRequest.getApprovalStatus())
                            .processStatus(tierAdjustmentBatchRequest.getProcessStatus())
                            .description(tierAdjustmentBatchRequest.getDescription())
                            .updatedAt(tierAdjustmentBatchRequest.getUpdatedAt())
                            .failedRecords(failed)
                            .rejectReason(tierAdjustmentBatchRequest.getRejectReason())
                            .build();
                }
        ).collect(Collectors.toList());

        return new PageImpl<>(tierAdjustmentBatchRequestRes, pageable, result.getTotalElements());
    }

    @Override
    public TierAdjustmentBatchRequestRes inReviewDetail(Integer inReviewId) {
        return getDetail(inReviewId);
    }

    @Override
    public TierAdjustmentBatchRequestRes availableDetail(Integer requestId) {
        return getDetail(requestId);
    }

    private TierAdjustmentBatchRequestRes getDetail(Integer requestId) {
        TierAdjustmentBatchRequest tierAdjustmentBatchRequest = tierAdjustmentBatchRequestService.findActive(requestId);

        Program program = programService.find(tierAdjustmentBatchRequest.getProgramId()).orElse(null);

        Business business = businessService.find(tierAdjustmentBatchRequest.getBusinessId()).orElse(null);

        Date updatedAt = tierAdjustmentBatchRequest.getProcessStatus().equals(ETierAdjustmentBatchProcessStatus.COMPLETED) ? tierAdjustmentBatchRequest.getUpdatedAt() : null;

        return TierAdjustmentBatchRequestRes.builder()
                .batchNo(tierAdjustmentBatchRequest.getId())
                .createdBy(tierAdjustmentBatchRequest.getCreatedBy())
                .createdAt(tierAdjustmentBatchRequest.getCreatedAt())
                .approvedBy(tierAdjustmentBatchRequest.getApprovedBy())
                .approvedAt(tierAdjustmentBatchRequest.getApprovedAt())
                .approvalStatus(tierAdjustmentBatchRequest.getApprovalStatus())
                .reason(tierAdjustmentBatchRequest.getRejectReason())
                .business(business != null ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(program != null ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .type(tierAdjustmentBatchRequest.getType())
                .referenceCode(tierAdjustmentBatchRequest.getReferenceCode())
                .description(tierAdjustmentBatchRequest.getDescription())
                .desReplace(tierAdjustmentBatchRequest.getDesReplace())
                .processStatus(tierAdjustmentBatchRequest.getProcessStatus())
                .updatedAt(updatedAt)
                .build();
    }

    @Override
    public TierAdjustmentBatchRequestRes approveBatchRequest(ApprovalReq req) {
        TierAdjustmentBatchRequest batchRequest = batchRequestService.findActive(req.getId().intValue());
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());

        if(EApprovalStatus.APPROVED.equals(req.getStatus())) {
            validateApproved(batchRequest);

            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.APPROVED);
            batchRequest.setProcessStatus(ETierAdjustmentBatchProcessStatus.PROCESSING);
            batchRequest = batchRequestService.save(batchRequest);

            // Call master worker to do tier adjustment
            try {
                MasterWorkerTierAdjustmentFeignReq feignRequest = MasterWorkerTierAdjustmentFeignReq.builder()
                        .batchRequestId(batchRequest.getId()).build();

                APIResponse<?> apiResponse = masterWorkerFeignClient.adjustTiers(feignRequest);

                if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
                    Log.info(LogData.createLogData()
                            .append("msg", "Call to master/worker tier adjustment successfully")
                            .append("Approve tier adjustment batch request ", batchRequest.getId())
                    );
                } else {
                    Log.error(LogData.createLogData()
                            .append("msg", "Error call to master/worker tier adjustment")
                            .append("error code", apiResponse.getMeta().getCode())
                            .append("error message", apiResponse.getMeta().getMessage())
                    );
                }
            } catch (Exception e) {
                Log.error(LogData.createLogData()
                        .append("msg", "Error call to master/worker tier adjustment")
                        .append("error message", e.getMessage())
                );
            }
            batchRequest = batchRequestService.findActive(req.getId().intValue());

        } else if(EApprovalStatus.REJECTED.equals(req.getStatus())) {
            validateRejected(batchRequest);

            batchRequest.setApprovedBy(opsReqPendingValidator.getCurrentUser());
            batchRequest.setApprovedAt(new Date());
            batchRequest.setApprovalStatus(EApprovalStatus.REJECTED);
            batchRequest.setRejectReason(req.getComment());
            batchRequest = batchRequestService.save(batchRequest);

        } else {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "EApprovalStatus mismatch ", req.getStatus());
        }

        return TierAdjustmentBatchRequestRes.builder()
                .batchNo(batchRequest.getId())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .reason(batchRequest.getRejectReason())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .type(batchRequest.getType())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .desReplace(batchRequest.getDesReplace())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateApproved(TierAdjustmentBatchRequest batchRequest) {
        if(!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_APPROVAL_STATUS_NOT_VALID);
        }
        if(!ETierAdjustmentBatchProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_PROCESS_STATUS_NOT_VALID);
        }
    }

    private void validateRejected(TierAdjustmentBatchRequest batchRequest) {
        if(!EApprovalStatus.PENDING.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_APPROVAL_STATUS_NOT_VALID);
        }
        if(!ETierAdjustmentBatchProcessStatus.PENDING.equals(batchRequest.getProcessStatus())) {
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_PROCESS_STATUS_NOT_VALID);
        }
    }

    @Override
    public TierAdjustmentBatchRequestRes retryBatchRequest(Integer id) {
        TierAdjustmentBatchRequest batchRequest = batchRequestService.findActive(id);
        Business business = businessService.findActive(batchRequest.getBusinessId());
        Program program = programService.findActive(batchRequest.getProgramId());
        validateRetry(batchRequest);

        MasterWorkerTierAdjustmentFeignReq feignRequest = MasterWorkerTierAdjustmentFeignReq.builder()
                .batchRequestId(batchRequest.getId()).build();

        APIResponse<?> apiResponse = masterWorkerFeignClient.adjustTiers(feignRequest);

        if (ErrorCode.SUCCESS.getValue() == apiResponse.getMeta().getCode()) {
            Log.info(LogData.createLogData()
                    .append("msg", "Call to master/worker tier adjustment successfully")
                    .append("Retry tier adjustment batch request ", id)
            );
        } else {
            throw new BusinessException(apiResponse.getMeta().getCode(), apiResponse.getMeta().getMessage(), null);
        }
        batchRequest = batchRequestService.findActive(id);

        return TierAdjustmentBatchRequestRes.builder()
                .batchNo(batchRequest.getId())
                .createdBy(batchRequest.getCreatedBy())
                .createdAt(batchRequest.getCreatedAt())
                .approvedBy(batchRequest.getApprovedBy())
                .approvedAt(batchRequest.getApprovedAt())
                .approvalStatus(batchRequest.getApprovalStatus())
                .reason(batchRequest.getRejectReason())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .type(batchRequest.getType())
                .referenceCode(batchRequest.getReferenceCode())
                .description(batchRequest.getDescription())
                .desReplace(batchRequest.getDesReplace())
                .processStatus(batchRequest.getProcessStatus())
                .build();
    }

    private void validateRetry(TierAdjustmentBatchRequest batchRequest) {
        if(!EApprovalStatus.APPROVED.equals(batchRequest.getApprovalStatus())) {
            throw new BusinessException(ErrorCode.TIER_ADJUSTMENT_APPROVAL_STATUS_NOT_VALID);
        }
        if(batchRequest.getSuccessRequests().equals(batchRequest.getTotalRequests())) {
            throw new OpsBusinessException(OpsErrorCode.TIER_ADJUSTMENT_COMPLETELY_FINISHED,
                    "Tier adjustment completely finished",
                    LogData.createLogData().append("batch_request_id", batchRequest.getId()));
        }
    }
}
