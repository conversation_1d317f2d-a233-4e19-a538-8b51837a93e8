package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.SchemeAttribute;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SchemeAttributeRes {
    @JsonProperty("group_name")
    private String groupName;

    @JsonProperty("list_attribute")
    private List<Attribute> attributeList;

    @Getter
    @Setter
    private static class Attribute {
        @JsonProperty("attribute_name")
        private String attributeName;

        @JsonProperty("data_type")
        private String dataType;

        @JsonProperty("list_operator")
        private List<String> operators;

        @JsonProperty("data_type_display")
        private String dataTypeDisplay;
    }

    public static List<SchemeAttributeRes> toListSchemeAttributeRes(List<SchemeAttribute> schemeAttributes) {
        List<SchemeAttributeRes> res = new ArrayList<>();
        Map<String, SchemeAttributeRes> mapByGroupName = new HashMap<>();

        for (SchemeAttribute schemeAttribute : schemeAttributes) {
            if (mapByGroupName.containsKey(schemeAttribute.getGroupName())) {
                SchemeAttributeRes newValue = mapByGroupName.get(schemeAttribute.getGroupName());
                newValue.getAttributeList().add(toAttribute(schemeAttribute));
                mapByGroupName.put(schemeAttribute.getGroupName(), newValue);
            } else {
                SchemeAttributeRes newValue = new SchemeAttributeRes();
                newValue.setGroupName(schemeAttribute.getGroupName());
                List<Attribute> listAttribute = new ArrayList<>();
                listAttribute.add(toAttribute(schemeAttribute));
                newValue.setAttributeList(listAttribute);
                mapByGroupName.put(schemeAttribute.getGroupName(), newValue);
            }
        }

        for (SchemeAttributeRes schemeAttributeRes : mapByGroupName.values()) {
            res.add(schemeAttributeRes);
        }

        return res;
    }

    private static Attribute toAttribute(SchemeAttribute schemeAttribute) {

        Attribute attributeRes = new Attribute();
        attributeRes.setAttributeName(schemeAttribute.getName());
        attributeRes.setDataType(schemeAttribute.getDataType());
        attributeRes.setDataTypeDisplay(schemeAttribute.getDataTypeDisplay());
        attributeRes.setOperators(schemeAttribute.getOperators());

        return attributeRes;
    }
}
