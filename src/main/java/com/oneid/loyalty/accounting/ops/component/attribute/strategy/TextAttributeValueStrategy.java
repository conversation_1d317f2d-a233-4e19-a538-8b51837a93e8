package com.oneid.loyalty.accounting.ops.component.attribute.strategy;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

@Component
public class TextAttributeValueStrategy extends AttributeValueStrategy<String> {

    public TextAttributeValueStrategy(AttributeMasterDataRepository attributeMasterDataRepository) {
        super(attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(EAttributeDataDisplayType type) {
        return EAttributeDataDisplayType.TEXT.equals(type);
    }

    @Override
    public String serialize(Object value, final Integer... programId) {
        String result = String.valueOf(value);
        if (StringUtils.isNotBlank(this.conditionAttributeDto.getValueValidationPattern())) {
            Pattern pattern = Pattern.compile(this.conditionAttributeDto.getValueValidationPattern());
            if (!pattern.matcher(result).matches()) {
                throw new BusinessException(
                        ErrorCode.INVALID_ATTRIBUTE_VALIDATION_PATTERN,
                        null,
                        null,
                        new Object[]{this.conditionAttributeDto.getAttribute(), this.conditionAttributeDto.getValueValidationPattern()}
                );
            }
        }
        verifyMasterData(result);
        if (result.length() > 255) {
            throw new BusinessException(
                    ErrorCode.INVALID_ATTRIBUTE_VALIDATION_PATTERN,
                    null,
                    null,
                    new Object[]{this.conditionAttributeDto.getAttribute(), "Max length > 255"}
            );
        }
        return String.valueOf(value);
    }

    @Override
    public String deserialize(String attribute, String value, final Integer... programId) {
        return value;
    }
}