package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AddCardMemberFeignReq {
    private String businessCode;

    private String programCode;

    private String memberCode;

    private Integer cardTypeId;

    private String storeCode;

    private String cardNo;

    private String userId;
    
    private String status;
}
