package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import lombok.Builder;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@JsonDeserialize(builder = CreateGiftCardRequestReq.CreateGiftCardRequestReqBuilder.class)
public class CreateGiftCardRequestReq {
    @NotNull(message = "'Business_id' must not be null")
    private Integer businessId;

    @NotNull(message = "'Store_id' must not be null")
    private Integer storeId;

    @NotNull(message = "'Program_id' must not be null")
    private Integer programId;

    @NotNull(message = "'No_of_card' must not be null")
    @Positive(message = "'No_of_card' must not be negative or zero")
    @Max(message = "Max value of 'No_of_card' is 10,000", value = 10000)
    private Integer noOfCard;

    private String description;

    @NotNull(message = "'Gift_card_bin_id' must not be null")
    private Integer giftCardBinId;

    @NotNull(message = "'Gift_card_type_id' must not be null")
    private Integer giftCardTypeId;

    @Length(max = 2, message = "Max length of 'serial_suffix' is 2")
    private String serialSuffix;

    private EGiftCardStatus initialGcStatus;

    @NotNull(message = "'generate_qr' mus not be null")
    private EBoolean generateQr;

    @NotNull(message = "'batch_type' must not be null")
    private EGiftCardBatchType batchType;

    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateGiftCardRequestReqBuilder {}
}