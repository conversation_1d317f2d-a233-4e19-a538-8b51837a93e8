package com.oneid.loyalty.accounting.ops.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum EAttributeDataTypeDisplay {
    TEXT(Arrays.asList(EAttributeDataType.STRING), Arrays.asList(EAttributeOperator.EQUAL,
            EAttributeOperator.NOT_EQUAL,
            EAttributeOperator.IN,
            EAttributeOperator.NOT_IN,
            EAttributeOperator.RANGE_MONTH,
            EAttributeOperator.OUT_OF_RANGE_MONTH), false),

    NUMBER(Arrays.asList(EAttributeDataType.LONG), Arrays.asList(EAttributeOperator.EQUAL,
            EAttributeOperator.NOT_EQUAL,
            EAttributeOperator.GREATER_THAN,
            EAttributeOperator.LESS_THAN,
            EAttributeOperator.GREATER_THAN_OR_EQUAL,
            EAttributeOperator.LESS_THAN_OR_EQUAL,
            EAttributeOperator.MODULO,
            EAttributeOperator.RANGE_MONTH,
            EAttributeOperator.OUT_OF_RANGE_MONTH), false),

    DATE(Arrays.asList(EAttributeDataType.STRING), Arrays.asList(EAttributeOperator.GREATER_THAN,
            EAttributeOperator.GREATER_THAN_OR_EQUAL,
            EAttributeOperator.LESS_THAN,
            EAttributeOperator.LESS_THAN_OR_EQUAL,
            EAttributeOperator.EQUAL,
            EAttributeOperator.NOT_EQUAL), false),

    DATE_TIME(Arrays.asList(EAttributeDataType.LONG), Arrays.asList(EAttributeOperator.GREATER_THAN,
            EAttributeOperator.GREATER_THAN_OR_EQUAL,
            EAttributeOperator.LESS_THAN,
            EAttributeOperator.LESS_THAN_OR_EQUAL,
            EAttributeOperator.EQUAL,
            EAttributeOperator.NOT_EQUAL), false),

    COMBOBOX(List.of(EAttributeDataType.STRING), Arrays.asList(EAttributeOperator.EQUAL,
            EAttributeOperator.NOT_EQUAL,
            EAttributeOperator.IN,
            EAttributeOperator.NOT_IN), false);

    private List<EAttributeDataType> dataTypes;

    private List<EAttributeOperator> operators;

    private boolean availableForSystemField;

    public static EAttributeDataTypeDisplay lookup(String s) {
        if (s == null) {
            return TEXT;
        } else {
            return valueOf(s);
        }
    }
    
    public boolean hasDataType(EAttributeDataType dataType) {
        return this.getDataTypes().contains(dataType);
    }
    
    public boolean hasOperator(EAttributeOperator operator) {
      return this.getOperators().contains(operator);  
    }
}