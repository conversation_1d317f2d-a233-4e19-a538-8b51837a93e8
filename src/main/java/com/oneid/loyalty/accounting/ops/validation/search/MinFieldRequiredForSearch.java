package com.oneid.loyalty.accounting.ops.validation.search;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {MinNumberFieldValidator.class})

public @interface MinFieldRequiredForSearch {

    String message() default "Required min field is 1";
    String[] ignores() default {};

    int count() default 1;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

