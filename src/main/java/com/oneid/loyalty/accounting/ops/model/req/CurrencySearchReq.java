package com.oneid.loyalty.accounting.ops.model.req;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.search.SortableAttribute;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;

@Getter
@Setter
@Valid
public class CurrencySearchReq extends SortableAttribute {
    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("code")
    private String code;

    @JsonProperty("status")
    private ECommonStatus status;

    @JsonProperty("created_date_from")
    private Date createdDateFrom;

    @JsonProperty("created_date_to")
    private Date createdDateTo;

    @JsonProperty("created_at")
    private Date createdAt;

    @Override
    protected void initMapPropsToAttribute() {
        mapPropsToAttribute = new HashMap<>();
        mapPropsToAttribute.put("business_id", "businessId");
        mapPropsToAttribute.put("code", "code");
        mapPropsToAttribute.put("status", "status");
        mapPropsToAttribute.put("created_date_from", "createdAt");
        mapPropsToAttribute.put("created_date_to", "createdAt");
        mapPropsToAttribute.put("created_at", "createdAt");
    }

    @Override
    public String getSortByDefault() {
        return "createdAt";
    }
}
