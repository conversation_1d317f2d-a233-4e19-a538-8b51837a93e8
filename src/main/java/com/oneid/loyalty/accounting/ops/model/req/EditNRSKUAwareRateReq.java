package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = EditNRSKUAwareRateReq.EditNRSKUAwareRateReqBuilder.class)
public class EditNRSKUAwareRateReq {
    @NotNull
    private Double rate;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class EditNRSKUAwareRateReqBuilder {
    }
}
