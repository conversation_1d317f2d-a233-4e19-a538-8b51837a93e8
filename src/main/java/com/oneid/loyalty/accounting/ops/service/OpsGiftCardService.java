package com.oneid.loyalty.accounting.ops.service;

import java.time.LocalDate;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.GiftCardUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;

public interface OpsGiftCardService {
    
    Page<GiftCardRes> getPage(int businessId, Integer corporationId, Integer chainId, Integer storeId,
            Integer programId, String serial, Long batchNo, Long point,
            EGiftCardStatus status, EApprovalStatus approvalStatus,
            LocalDate topupDate, LocalDate expirationDate, Pageable pageable);
    
    GiftCardRes getApprovedDetail(int programId, String serial);
    
    GiftCardRes getRequestDetail(int programId, String serial, int requestId);
    
    GiftCardRes getChangeable(int programId, String serial);
    
    GiftCardRes requestUpdate(GiftCardUpdateReq req);
    
    Page<GiftCardRes> getInReviewPage(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable);
    
    GiftCardRes getInReviewDetail(int reviewId);

    ResourceDTO exportGiftCard(int giftCardRequestId);
}
