package com.oneid.loyalty.accounting.ops.component.attribute.strategy;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class DateTimeAttributeValueStrategy extends AttributeValueStrategy<Long> {

    public DateTimeAttributeValueStrategy(AttributeMasterDataRepository attributeMasterDataRepository) {
        super(attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(EAttributeDataDisplayType type) {
        return EAttributeDataDisplayType.DATE_TIME.equals(type);
    }

    @Override
    public Long serialize(Object value, final Integer... programId) {
        try {
            Long result = Long.parseLong(value.toString());
            verifyMasterData(DateTimes.formatDateTime(new Date(result * 1000)));
            return result;
        } catch (NumberFormatException e) {
            throw new BusinessException(ErrorCode.INVALID_ATTRIBUTE_VALIDATION_PATTERN,
                    null,
                    null,
                    new Object[]{this.conditionAttributeDto.getAttribute()}
            );
        }
    }

    @Override
    public Long deserialize(String attribute, String value, final Integer... programIds) {
        return Long.parseLong(value);
    }
}