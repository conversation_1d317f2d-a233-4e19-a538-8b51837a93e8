package com.oneid.loyalty.accounting.ops.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.MemberCardChangedStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberCardRes;
import com.oneid.loyalty.accounting.ops.model.res.TransitionCardStatusRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberCardService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardTMP;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.MemberCardRequest;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CardTMPRepository;
import com.oneid.oneloyalty.common.repository.CardTypeRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.MemberCardRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;

@Service
public class OpsMemberCardServiceImpl implements OpsMemberCardService {
    @Autowired
    private MemberCardRequestRepository memberCardRequestRepository;

    @Autowired
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @Value("${maker-checker.module.member-card}")
    private String moduleId;
    
    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private CardTMPRepository cardTMPRepository;

    @Autowired
    private CardTypeRepository cardTypeRepository;
    
    @Autowired
    private CorporationRepository corporationRepository;
    
    @Autowired
    private ProgramRepository programRepository;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private ChainRepository chainRepository;
    
    @Autowired
    private StoreRepository storeRepository;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    
    private TransactionTemplate transactionTemplate;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public Page<MemberCardRes> filter(Integer businessId, Integer programId, Integer corporationId, Integer chainId, Integer storeId, Long batchNo, String cardNo, 
            LocalDate fromLocalDate, LocalDate toLocalDate, ECardStatus currentStatus, EApprovalStatus approvalStatus, Pageable pageRequest) {
        Date fromDate = null;
        Date toDate = null;
        
        if(fromLocalDate != null) {
            fromDate = Date.from(fromLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }
        
        if(toLocalDate != null) {
            toDate = Date.from(toLocalDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        }

        Page<Object[]> page = memberCardRequestRepository.filter(businessId, programId, corporationId, chainId,
                storeId, batchNo, cardNo, fromDate, toDate, currentStatus, approvalStatus, pageRequest);
        
        return new PageImpl<>(transform(page.getContent(), null), page.getPageable(), page.getTotalElements());
    }

    @Override
    public Page<MemberCardRes> getInReview(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable) {
        Integer fromEpochSecond = null;
        Integer toEpochSecond = null;
        
        if(fromDate != null) {
            fromEpochSecond = (int) fromDate.atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond();
        }
        
        if(toDate != null) {
            toEpochSecond = (int) toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toEpochSecond();
        }
        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerFeignClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromEpochSecond,
                toEpochSecond);

        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        List<MemberCardRes> memberCardRes = Collections.emptyList();

        if (changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeignResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each));

            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());
            
            if(CollectionUtils.isNotEmpty(requestIds)) {
                Map<Integer, MemberCardRes> mapRes = transform(memberCardRequestRepository.filter(requestIds), changeRecordFeignResMap)
                .stream()
                .collect(Collectors.toMap(MemberCardRes::getRequestId, each -> each));
                
                memberCardRes = requestIds.stream()
                .map(id -> mapRes.get(id))
                .collect(Collectors.toList());
            }
        }

        return new PageImpl<>(memberCardRes, pageable, changeRequestPageFeignRes.getTotalRecordCount());
    }

    @Override
    public MemberCardRes getInReviewDetail(Integer reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> response = makerCheckerFeignClient.getChangeRequestById(String.valueOf(reviewId));
        
        MemberCardRequest availableRequest = memberCardRequestRepository.findById(Integer.parseInt(response.getData().getObjectId()))
                .orElseThrow(() -> new BusinessException(ErrorCode.CARD_CHANGED_STATUS_REQUEST_NOT_FOUND, "Card changed status request not found", response));
        
        CardTMP cardTMP = cardTMPRepository.findByCardNoAndProgramId(availableRequest.getCardNo(), availableRequest.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CARD_TMP_NOT_FOUND, null, null));
        
        return getDetail(cardTMP, availableRequest, reviewId);
    }
    @Override
    public MemberCardRes getAvailableDetail(Integer programId, String cardNo) {
        CardTMP cardTMP = cardTMPRepository.findByCardNoAndProgramId(cardNo, programId)
                .orElseThrow(() -> new BusinessException(ErrorCode.CARD_TMP_NOT_FOUND, null, null));
        
        MemberCardRequest availableRequest = memberCardRequestRepository.findEffectedVersion(programId, cardNo)
                .orElse(null);
        
        return getDetail(cardTMP, availableRequest, null);
    }
    
    @Override
    public MemberCardRes getEditChangedStatusRequestSetting(Integer programId, String cardNo) {
        CardTMP cardTMP = getChangeableStatusCardTMP(programId, cardNo);
        
        MemberCardRequest availableRequest = memberCardRequestRepository.findEffectedVersion(programId, cardNo)
                .orElse(null);
        
        Program program = programRepository.getOne(cardTMP.getProgramId());
        Business business = businessRepository.getOne(cardTMP.getBusinessId());
        CardType cardType = cardTypeRepository.getOne(cardTMP.getCardTypeId());
        Store store = storeRepository.getOne(cardTMP.getStoreId());
        Corporation corporation = corporationRepository.getOne(store.getCorporationId());
        Chain chain = chainRepository.getOne(store.getChainId());
        
        String createdBy = null;
        String updatedBy = null;
        Date createdAt = null;
        Date updatedAt = null;
        Integer version = null;
        
        if(availableRequest != null) {
            createdAt = availableRequest.getCreatedAt();
            createdBy = availableRequest.getCreatedBy();
            updatedAt = availableRequest.getUpdatedAt();
            updatedBy = availableRequest.getUpdatedBy();
            version = availableRequest.getVersion();
        } else {
            createdAt = cardTMP.getCreatedAt();
            createdBy = cardTMP.getCreatedBy();
            updatedAt = cardTMP.getUpdatedAt();
            updatedBy = cardTMP.getUpdatedBy();
            version = 1;
        }
        
        return MemberCardRes.builder()
                .programId(cardTMP.getProgramId())
                .batchNo(cardTMP.getCprBatchNo())
                .cardNo(cardTMP.getCardNo())
                .currentCardStatus(cardTMP.getCardStatus())
                .version(version)
                .programName(program.getName())
                .businessName(business.getName())
                .storeName(store.getName())
                .chainName(chain.getName())
                .cardType(cardType.getName())
                .corporationName(corporation.getName())
                .transitionStatuses(Arrays.asList(
                        TransitionCardStatusRes.builder()
                        .status(ECardStatus.BLOCK)
                        .build()))
                .createdBy(createdBy)
                .updateBy(updatedBy)
                .createdAt(createdAt)
                .updateAt(updatedAt)
                .build();
    }
    
    @Override
    public Integer requestEditingChangedStatusRequest(Integer programId, String cardNo, MemberCardChangedStatusReq req) {
        MemberCardRequest request = transactionTemplate.execute(status -> {
            MemberCardRequest entity = createRequest(programId, cardNo, req);
            
            return entity;
        });
               
        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(request.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(request.getId())
                        .build())
                .build();

        makerCheckerServiceClient.changes(feignReq);
        
        return request.getId();
    }
    
    private MemberCardRequest createRequest(Integer programId, String cardNo, MemberCardChangedStatusReq req) {
        Program program = programRepository.findById(programId)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));
        
        if(!program.getStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, null, null);
        
        CardTMP cardTMP = getChangeableStatusCardTMP(program.getId(), cardNo);
        
        int currentVersion = memberCardRequestRepository.findEffectedVersion(programId, cardNo)
                .map(MemberCardRequest::getVersion)
                .orElse(0);
        
        MemberCardRequest request = new MemberCardRequest();
        
        request.setApprovalStatus(EApprovalStatus.PENDING);
        request.setRequestStatus(ECommonStatus.PENDING);
        request.setStatus(req.getStatus());
        request.setVersion(currentVersion + 1);
        request.setCardNo(cardTMP.getCardNo());
        request.setProgramId(program.getId());
        request.setChangedReason(req.getReason());
        
        memberCardRequestRepository.save(request);
        
        return request;
    }
    
    private CardTMP getChangeableStatusCardTMP(Integer programId, String cardNo) {
        CardTMP cardTMP = cardTMPRepository.findByCardNoAndProgramId(cardNo, programId)
                .orElseThrow(() -> new BusinessException(ErrorCode.CARD_TMP_NOT_FOUND, null, null));
        
        if(!cardTMP.getCardStatus().equals(ECardStatus.PENDING))
            throw new BusinessException(ErrorCode.MEMBER_CARD_STATUS_CAN_NOT_CHANGE, null, null, new Object[] { cardTMP.getCardStatus(), ECardStatus.PENDING });
        
        memberCardRequestRepository.findPendingVersion(programId, cardNo)
        .ifPresent(entity -> {
            throw new BusinessException(ErrorCode.MEMBER_CARD_REQUEST_IS_ALREADY_REQUESTED, null, null);
        });
        
        return cardTMP;
    }
    
    private MemberCardRes getDetail(
            CardTMP cardTMP, 
            MemberCardRequest availableRequest,
            Integer reviewId) {
        Program program = programRepository.getOne(cardTMP.getProgramId());
        Business business = businessRepository.getOne(cardTMP.getBusinessId());
        CardType cardType = cardTypeRepository.getOne(cardTMP.getCardTypeId());
        Store store = storeRepository.getOne(cardTMP.getStoreId());
        Corporation corporation = corporationRepository.getOne(store.getCorporationId());
        Chain chain = chainRepository.getOne(store.getChainId());
        
        String createdBy = null;
        String updatedBy = null;
        Date createdAt = null;
        Date updatedAt = null;
        Integer version = null;
        ECardStatus changedCardStatus = null;
        
        if(availableRequest != null) {
            createdAt = availableRequest.getCreatedAt();
            createdBy = availableRequest.getCreatedBy();
            updatedAt = availableRequest.getUpdatedAt();
            updatedBy = availableRequest.getUpdatedBy();
            changedCardStatus = availableRequest.getStatus();
            version = availableRequest.getVersion();
        } else {
            createdAt = cardTMP.getCreatedAt();
            createdBy = cardTMP.getCreatedBy();
            updatedAt = cardTMP.getUpdatedAt();
            updatedBy = cardTMP.getUpdatedBy();
            changedCardStatus = cardTMP.getCardStatus();
            version = 1;
        }
        
        return MemberCardRes.builder()
                .reviewId(reviewId)
                .programId(cardTMP.getProgramId())
                .batchNo(cardTMP.getCprBatchNo())
                .cardNo(cardTMP.getCardNo())
                .currentCardStatus(cardTMP.getCardStatus())
                .changedCardStatus(reviewId != null ? changedCardStatus : null)
                .version(version)
                .programId(program.getId())
                .programName(program.getName())
                .businessName(business.getName())
                .storeName(store.getName())
                .chainName(chain.getName())
                .cardType(cardType.getName())
                .corporationName(corporation.getName())
                .createdBy(createdBy)
                .updateBy(updatedBy)
                .createdAt(createdAt)
                .updateAt(updatedAt)
                .build();
    }

    private List<MemberCardRes> transform(List<Object[]> resultSet, Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> changeRecordFeignResMap) {
        if (changeRecordFeignResMap == null) {
            changeRecordFeignResMap = new HashMap<>();
        }

        final Map<Integer, ChangeRequestPageFeignRes.ChangeRecordFeginRes> finalChangeRecordFeignResMap = changeRecordFeignResMap;

        return resultSet.stream()
                .map(objects -> {
                    CardTMP cardTMP = (CardTMP) objects[0];
                    MemberCardRequest cardChangedStatusRequest = (MemberCardRequest) objects[1];
                    Business business = (Business) objects[2];
                    Program program = (Program) objects[3];
                    Corporation corporation = (Corporation) objects[4];
                    Chain chain = (Chain) objects[5];
                    Store store = (Store) objects[6];
                    CardType cardType = (CardType) objects[7];
                    Integer requestId = null;
                    Integer reviewId = null;
                    
                    if(cardChangedStatusRequest != null) {
                        ChangeRequestPageFeignRes.ChangeRecordFeginRes changeRequest = finalChangeRecordFeignResMap.getOrDefault(cardChangedStatusRequest.getId(), null);
                        reviewId = changeRequest != null ? changeRequest.getChangeRequestId() : null;
                        
                        requestId = cardChangedStatusRequest.getId();
                    }

                    return MemberCardRes.builder()
                            .requestId(requestId)
                            .reviewId(reviewId)
                            .batchNo(cardTMP.getCprBatchNo())
                            .cardNo(cardTMP.getCardNo())
                            .createdAt(cardTMP.getCreatedAt())
                            .createdBy(cardTMP.getCreatedBy())
                            .updateAt(cardTMP.getUpdatedAt())
                            .updateBy(cardTMP.getUpdatedBy())
                            .cardType(cardType.getName())
                            .businessName(business.getName())
                            .programId(program.getId())
                            .programName(program.getName())
                            .corporationName(corporation.getName())
                            .chainName(chain.getName())
                            .storeName(store.getName())
                            .currentCardStatus(cardTMP.getCardStatus())
                            .build();
                }).collect(Collectors.toList());
    }
}