package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltySchemeFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetSchemeRuleEvent;
import com.oneid.loyalty.accounting.ops.mapper.SchemeMapper;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.elasticsearch.SchemeES;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.ResetSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifySchemeInfoReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeBaseRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeCombineRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeInReviewDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeInReviewRes;
import com.oneid.loyalty.accounting.ops.publisher.MessagePublisher;
import com.oneid.loyalty.accounting.ops.repository.elasticsearch.SchemeESRepository;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleSchemeService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.SchemeDTO;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.ProgramPoolRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.SchemeRuleService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class OpsSchemeServiceImpl implements OpsSchemeService {

    @Value("${spring.redis.reset-scheme-rule.channel}")
    private String resetSchemeRuleTopic;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private PoolService poolService;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private OpsFormulaService opsFormulaService;

    @Autowired
    MakerCheckerFeignClient makerCheckerFeignClient;

    @Autowired
    MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    private OpsConditionService opsConditionService;

    @Autowired
    private AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Autowired
    private ProgramPoolRepository programPoolRepository;
    
    @Autowired
    private SchemeESRepository schemeESRepository;
    
    @Autowired
    private CorporationRepository corporationRepository;
    
    @Autowired
    private ChainRepository chainRepository;
    
    @Autowired
    private StoreRepository storeRepository;
    
    @Autowired
    private PosRepository posRepository;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private OpsRuleSchemeService opsRuleSchemeService;

    @Autowired
    private OneloyaltySchemeFeignClient oneloyaltySchemeFeignClient;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    SchemeRuleService schemeRuleService;

    @Override
    public void verifySchemeInfo(VerifySchemeInfoReq req) {
        verifySchemeInfoExceptCode(req, null);
    }

    @Override
    public void verifySchemeCombine(CreateSchemeReq req) {
        verifySchemeCombineExceptCode(req, null);
    }


    @Override
    public Object getApproveCreate(CreateSchemeReq req) {
        verifySchemeCombine(req);

        MakerCheckerInternalMakerReq<CreateSchemeReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateSchemeReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.SCHEME.getName())
                .requestType(EMakerCheckerType.SCHEME.getType())
                .payload(req)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerScheme = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerScheme.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Counter call maker error: " + createMakerScheme.getMeta().getMessage(), null);
        }
        return createMakerScheme.getData();
    }

    @Override
    public MakerCheckerInternalMakerRes getApproveUpdate(UpdateSchemeReq req) {
        Scheme scheme = schemeService.find(req.getSchemeId()).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", req)
        );

        if (ECommonStatus.INACTIVE.equals((scheme.getStatus()))) {
            throw new BusinessException(ErrorCode.SCHEME_CANNOT_UPDATE, "Scheme cannot update", req);
        } else if (ECommonStatus.ACTIVE.equals(scheme.getStatus()) && ECommonStatus.INACTIVE.equals(req.getStatus())) {
            throw new BusinessException(ErrorCode.SCHEME_CANNOT_UPDATE, "Scheme cannot update", req);
        }

        opsReqPendingValidator.verifyEditKey(req.getEditKey(), scheme.getRequestCode(), scheme.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.SCHEME.getType(), scheme.getRequestCode());

        verifySchemeCombineExceptCode(req, scheme.getCode());

        MakerCheckerInternalMakerReq<CreateSchemeReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateSchemeReq>builder()
                .requestCode(Objects.nonNull(scheme.getRequestCode()) ? scheme.getRequestCode() : UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.SCHEME.getName())
                .requestType(EMakerCheckerType.SCHEME.getType())
                .payload(createReq(req, scheme))
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> apiResponse =
                makerCheckerInternalFeignClient.maker(createFeignInternalReq);

        if (ErrorCode.SUCCESS.getValue() != apiResponse.getMeta().getCode())
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Scheme - request editing attribute request call checker error: " + apiResponse.getMeta().getMessage(), null);

        return apiResponse.getData();
    }

    private SchemeCombineRes setInfoBusiness(SchemeCombineRes result) {
        Program program = programService.find(result.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", result)
        );
        result.setBusinessId(program.getBusinessId());
        return result;
    }

    @Override
    public Page<SchemeBaseRes> getPage(Integer businessId, Integer programId, 
            ESchemeType type, String code, ECommonStatus status, 
            OffsetDateTime startDate, OffsetDateTime endDate, 
            Integer corporationId,
            Integer chainId,
            Integer storeId,
            Integer terminalId,
            Pageable pageable
    ) {
        String corporationCode = null;
        String chainCode = null;
        String storeCode = null;
        String terminalCode = null;
        
        if(corporationId != null) {
            Corporation corporation = corporationRepository.findById(corporationId).orElse(null);
            
            if(corporation != null) {
                corporationCode = corporation.getCode();
                
                if(chainId != null) {
                    Chain chain = chainRepository.findById(chainId).orElse(null);
                    
                    if(chain != null && corporation.getId().equals(chain.getCorporationId()) ) {
                        chainCode = chain.getCode();
                        
                        if(storeId != null) {
                            Store store = storeRepository.findById(storeId).orElse(null);
                            
                            if(store != null && chain.getId().equals(store.getChainId())) {
                                storeCode = store.getCode();
                                
                                if(terminalId != null) {
                                    Pos pos = posRepository.findById(terminalId).orElse(null);
                                    
                                    if(pos != null && store.getId().equals(pos.getStoreId())) {
                                        terminalCode = pos.getCode();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        Page<SchemeES> page = schemeESRepository.findPage(businessId, programId, 
                type, code, status, 
                startDate, endDate, corporationCode, chainCode, storeCode, terminalCode,
                pageable);
        List<SchemeBaseRes> content = this.convertToBaseRes(page.getContent());
        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response = makerCheckerInternalFeignClient.previewDetail(req.getId());
        if (response.getMeta().getCode() != 200) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
        CreateSchemeReq payload = objectMapper.convertValue(response.getData().getPayload(), CreateSchemeReq.class);

        if (req.getStatus() == EApprovalStatus.APPROVED) {
            Scheme scheme;
            programService.findActive(payload.getProgramId());
            businessService.findActive(payload.getBusinessId());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(response.getData().getMadeByUserName());
            String createdBy = null;
            String updatedBy;

            if (payload.getId() != null) {
                scheme = schemeService.find(payload.getId()).orElseThrow(
                        () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", req)
                );

                if (ECommonStatus.INACTIVE.equals((scheme.getStatus()))) {
                    throw new BusinessException(ErrorCode.SCHEME_CANNOT_UPDATE, "Scheme cannot update", req);
                }

                Pool pool = poolService.find(scheme.getPoolId()).orElseThrow(
                        () -> new BusinessException(ErrorCode.POOL_NOT_FOUND, null, null));

                SchemeMapper.setValuesFromReqUpdate(scheme, payload);

                Scheme schemeSaved = schemeService.update(scheme);

                List<RuleRecordRes> ruleRecordResList = opsRuleSchemeService.updateRules(scheme, payload.getRuleList());
                opsFormulaService.updateFormula(scheme, payload.getFormulaGroup());
                updatedBy = response.getData().getMadeByUserName();
                publishResetRuleEvent(schemeSaved, ruleRecordResList);
            } else {
                verifySchemeCode(payload.getProgramId(), payload.getSchemeCode());

                programService.findActive(payload.getProgramId()); // verify program
                Pool pool = poolService.findActive(payload.getPoolId());
                // because not save record from (pool & program owner pool) into programPool
                boolean notAcceptedPool = !pool.getProgramId().equals(payload.getProgramId());

                notAcceptedPool &= Stream.of(programPoolRepository.findPoolByProgramId(payload.getProgramId()))
                        .noneMatch(objects -> {
                            Pool poolIter = (Pool) objects[0];
                            return poolIter.getId().equals(payload.getPoolId()) && poolIter.getStatus() == ECommonStatus.ACTIVE;
                        });

                if (notAcceptedPool) {
                    throw new BusinessException(ErrorCode.POOL_NOT_IN_PROGRAM, "Pool not in program", payload.getPoolId());
                }

                scheme = SchemeMapper.toSchemeCreate(payload);
                scheme.setStatus(payload.getStatus());

                Scheme schemeSaved = schemeService.save(scheme);

                List<RuleRecordRes> ruleRecordResList = opsRuleSchemeService.createRules(scheme, payload.getRuleList());
                opsFormulaService.create(scheme, payload.getFormulaGroup());
                updatedBy = createdBy = response.getData().getMadeByUserName();
                publishResetRuleEvent(schemeSaved, ruleRecordResList);
            }
            scheme.setRequestCode(response.getData().getRequestCode());
            scheme.setVersion(response.getData().getVersion());

            opsReqPendingValidator.updateInfoChecker(scheme, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
            schemeService.save(scheme);
        }

        // Call to MakerChecker
        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient.checker(checkerReq);
        if (plAprroved.getMeta().getCode() != 200) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }

    @Override
    public Page<SchemeInReviewRes> getInReviewSchemeRequests(EApprovalStatus approvalStatus, String fromCreatedAt, String toCreatedAt, String fromReviewedAt, String toReviewedAt, String createdBy, String reviewedBy, Integer offset, Integer limit) {
        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.SCHEME.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        reviewedBy,
                        checkedDate
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);
        List<SchemeInReviewRes> content = new ArrayList<>();
        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            CreateSchemeReq scheme = objectMapper.convertValue(item.getPayload(), CreateSchemeReq.class);
            Program program = programService.find(scheme.getProgramId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
            );

            Business business = businessService.find(scheme.getBusinessId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
            );

            Pool pool = poolService.findByIdAndProgramId(scheme.getPoolId(), program.getId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.POOL_NOT_FOUND)
            );

            content.add(SchemeInReviewRes.builder()
                    .businessName(business.getName())
                    .businessId(business.getId())
                    .programName(program.getName())
                    .programId(program.getId())
                    .schemeId(Math.toIntExact(item.getId()))
                    .schemeName(scheme.getSchemeName())
                    .schemeCode(scheme.getSchemeCode())
                    .schemeType(scheme.getSchemeType())
                    .startDate(scheme.getStartDate() != null ? new Date(scheme.getStartDate() * 1000) : null)
                    .endDate(scheme.getEndDate() != null ? new Date(scheme.getEndDate() * 1000) : null)
                    .poolId(scheme.getPoolId())
                    .poolName(pool.getName())
                    .status(scheme.getStatus())
                    .approvalStatus(EApprovalStatus.of(item.getStatus()))
                    .build());
        }
        return new PageImpl<SchemeInReviewRes>(content, new OffsetBasedPageRequest(offset, limit, null), previewRes.getMeta().getTotal());
    }

    @Override
    public Page<SchemeDTO> getAvailableSchemeRequests(
            Integer businessId,
            Integer programId,
            String code,
            String name,
            ESchemeType type,
            ECommonStatus status,
            Pageable pageable
    ) {
        return schemeService.filter(businessId, programId, type, code, name, status, pageable);
    }

    @Override
    public SchemeCombineRes getEdit(Integer requestId) {
        Scheme scheme = schemeService.find(requestId).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", requestId)
        );
        SchemeCombineRes result = SchemeCombineRes.valueOf(scheme);

        result.setRuleList(opsRuleService.findAllBySchemeId(requestId));
        result.setFormulaGroup(opsFormulaService.getFormulaGroupByScheme(scheme));

        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.SCHEME.getType(), scheme.getRequestCode());
        String editKey = opsReqPendingValidator.generateEditKey(scheme.getRequestCode(), scheme.getVersion());
        result.setEditKey(editKey);

        return setInfoBusiness(result);
    }

    private List<SchemeBaseRes> convertToBaseRes(List<SchemeES> ess) {
        List<SchemeBaseRes> res = ess.stream()
                .map(es -> this.convertToBaseRes(es))
                .collect(Collectors.toList());
        return res;
    }
    
    private SchemeBaseRes convertToBaseRes(SchemeES es) {
        SchemeBaseRes res = SchemeBaseRes.builder()
                .schemeId(es.getId())
                .schemeCode(es.getCode())
                .schemeName(es.getName())
                .description(es.getDescription())
                .schemeType(ESchemeType.of(es.getSchemeType()))
                .ruleLogic(EConditionType.of(es.getRuleLogic()))
                .poolId(es.getPoolId())
                .poolName(es.getPoolName())
                .status(ECommonStatus.of(es.getStatus()))
                .programId(es.getProgramId())
                .programName(es.getProgramName())
                .businessId(es.getBusinessId())
                .businessName(es.getBusinessName())
                .startDate(es.getStartDate() != null ? es.getStartDate().toEpochSecond() : null)
                .endDate(es.getEndDate() != null ? es.getEndDate().toEpochSecond() : null)
                .build();
        return res;
    }

    @Override
    public SchemeCombineRes getDetails(Integer schemeId) {
        Scheme scheme = schemeService.find(schemeId).orElseThrow(
                () -> new BusinessException(ErrorCode.SCHEME_NOT_FOUND, "Scheme not found", schemeId)
        );
        SchemeCombineRes result = SchemeCombineRes.valueOf(scheme);

        result.setRuleList(opsRuleService.findAllBySchemeId(schemeId));
        result.setFormulaGroup(opsFormulaService.getFormulaGroupByScheme(scheme));

        return setInfoBusiness(result);
    }

    @Override
    public SchemeInReviewDetailRes getInReviewSchemeRequestById(Integer requestId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(requestId));

        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Scheme - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.SCHEME.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Scheme - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        CreateSchemeReq payload = objectMapper.convertValue(previewDetailRes.getData().getPayload(), CreateSchemeReq.class);
        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );
        Business business = businessService.find(payload.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
        );

        Pool pool = poolService.find(payload.getPoolId()).orElseThrow(
                () -> new BusinessException(ErrorCode.POOL_NOT_FOUND)
        );

        return SchemeInReviewDetailRes.builder()
                .schemeId(payload.getId())
                .schemeCode(payload.getSchemeCode())
                .schemeName(payload.getSchemeName())
                .description(payload.getDescription())
                .schemeType(payload.getSchemeType())
                .poolId(payload.getPoolId())
                .poolName(pool.getName())
                .businessId(business.getId())
                .businessName(business.getName())
                .programId(program.getId())
                .programName(program.getName())
                .minAmount(payload.getMinAmount())
                .maxAmount(payload.getMaxAmount())
                .startDate(payload.getStartDate())
                .endDate(payload.getEndDate())
                .isVAT(payload.getIsVAT())
                .ruleLogic(payload.getRuleLogic())
                .roundingRule(payload.getRoundingRule())
                .editKey(String.valueOf(previewDetailRes.getData().getId()))
                .ruleList(payload.getRuleList())
                .formulaGroup(payload.getFormulaGroup())
                .status(payload.getStatus())
                .approvalStatus(EApprovalStatus.of(previewDetailRes.getData().getStatus()))
                .createdAt(previewDetailRes.getData().getMadeDateToTimestamp())
                .createdBy(previewDetailRes.getData().getMadeByUserName())
                .updatedAt(payload.getUpdatedAt())
                .updatedBy(payload.getUpdatedBy())
                .approvedAt(previewDetailRes.getData().getCheckedDateToTimestamp())
                .approvedBy(previewDetailRes.getData().getCheckedByUserName())
                .build();
    }

    private void verifyProgramAndPool(Integer programId, Integer poolId) {
        programService.findActive(programId); // verify program
        boolean notAcceptedPool = Stream.of(programPoolRepository.findPoolByProgramId(programId))
                .noneMatch(objects -> {
                    Pool pool = (Pool) objects[0];
                    return pool.getId().equals(poolId) && pool.getStatus() == ECommonStatus.ACTIVE;
                });

        // because not save record from (pool & program owner pool) into programPool
        Pool pool = poolService.findActive(poolId);
        notAcceptedPool &= !pool.getProgramId().equals(programId);

        if (notAcceptedPool) {
            throw new BusinessException(ErrorCode.POOL_NOT_IN_PROGRAM, "Pool not in program", poolId);
        }
    }

    private void verifySchemeCode(Integer programId, String schemeCode, String exceptCode) {
        Scheme scheme = schemeService.find(programId, schemeCode).orElse(null);
        if (scheme != null && !scheme.getCode().equals(exceptCode)) {
            throw new BusinessException(ErrorCode.SCHEME_CODE_EXISTED, "Scheme code existed", schemeCode);
        }
    }

    private void verifyStartAndEndDate(Long startDate, Long endDate) {
        if (startDate > endDate) {
            throw new BusinessException(ErrorCode.START_DATE_IS_LESS_END_DATE, "Start date is less than end date", null);
        }
    }

    private void verifySchemeInfoExceptCode(VerifySchemeInfoReq req, String exceptCode) {
        verifyStartAndEndDate(req.getStartDate(), req.getEndDate());
        verifySchemeCode(req.getProgramId(), req.getSchemeCode(), exceptCode);
        verifyProgramAndPool(req.getProgramId(), req.getPoolId());
    }

    private void verifySchemeCombineExceptCode(CreateSchemeReq req, String exceptCode) {
        /* verify info */
        verifySchemeInfoExceptCode(req, exceptCode);

        /* verify rules and condition*/
        if (req.getRuleList() != null) {
            VerifyCreateListSchemeRuleReq rules = new VerifyCreateListSchemeRuleReq();
            rules.setRuleLogic(req.getRuleLogic());
            rules.setRuleList(req.getRuleList());
            opsRuleService.verifyListRule(SchemeMapper.toSchemeCreate(req), rules);
        }
        opsFormulaService.verify(req.getFormulaGroup());
    }

    private CreateSchemeReq createReq(UpdateSchemeReq req, Scheme scheme) {

        Program program = programService.findActive(req.getProgramId());
        Business business = businessService.findActive(program.getBusinessId());
        Pool pool = poolService.findActive(req.getPoolId());
        req.setId(scheme.getId());
        req.setBusinessId(program.getBusinessId());
        req.setUpdatedAt(DateTimes.toEpochSecond(new Date()));
        req.setUpdatedBy(auditorAware.getCurrentAuditor().get().getUserName());
        req.setProgramName(program.getName());
        req.setBusinessName(business.getName());
        req.setPoolName(pool.getName());

        Collection<ConditionAttributeDto> conditionAttributeDtos = opsConditionService.conditionAttributeDtos(program.getId());
        Map<String, ConditionAttributeDto> mapConditionDtos = conditionAttributeDtos.stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                c->c
        ));
        for (int i = 0; i < req.getRuleList().size(); ++i) {
            for (int j = 0; j < req.getRuleList().get(i).getListCondition().size(); ++j) {
                ConditionRecordReq reqCondition = req.getRuleList().get(i).getListCondition().get(j);
                reqCondition.setResourcePath(mapConditionDtos.get(reqCondition.getAttribute()).getResourcePath());
                reqCondition.setSupportFilter(mapConditionDtos.get(reqCondition.getAttribute()).getSupportFilter());
            }
        }
        return req;
    }

    private void verifySchemeCode(Integer programId, String schemeCode) {
        Scheme scheme = schemeService.find(programId, schemeCode).orElse(null);
        if (scheme != null) {
            throw new BusinessException(ErrorCode.SCHEME_CODE_EXISTED, "Scheme code existed", schemeCode);
        }
    }

    private void publishResetRuleEvent(Scheme scheme, List<RuleRecordRes> ruleRecordResList) {
        ResetSchemeRuleReq resetSchemeRuleReq = ResetSchemeRuleReq.buildSchemeRuleReq(scheme);
        List<ResetSchemeRuleReq.Rule> rules = new ArrayList<>();

        for (RuleRecordRes ruleRecordRes : ruleRecordResList) {
            ResetSchemeRuleReq.Rule rule = ResetSchemeRuleReq.buildRuleReq(ruleRecordRes);
            rules.add(rule);
        }

        resetSchemeRuleReq.setRules(rules);

        // Check reset scheme rule
        APIFeignInternalResponse<?> ruleRes = oneloyaltySchemeFeignClient.checkReset(resetSchemeRuleReq);
        if (ruleRes.getMeta().getCode() != 200) {
            throw new BusinessException(ErrorCode.RESET_SCHEME_RULE_FAILED);
        }

        ResetSchemeRuleEvent<?> ruleEvent = ResetSchemeRuleEvent.builder()
                .id(UUID.randomUUID().toString())
                .eventType(ResetSchemeRuleEvent.RESET_RULE_EVENT_TYPE)
                .timeStamp(System.currentTimeMillis())
                .payload(resetSchemeRuleReq)
                .build();
        messagePublisher.publish(resetSchemeRuleTopic, ruleEvent);
    }
}
