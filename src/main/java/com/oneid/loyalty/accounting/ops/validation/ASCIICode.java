package com.oneid.loyalty.accounting.ops.validation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import javax.validation.Constraint;
import javax.validation.Payload;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = { ASCIICodeValidator.class })
public @interface ASCIICode {
    String message() default "Code is invalid ASCII format";
    
    String nonWhitespaceMessage() default "Code format does not allow to contain whitespace";
    
    boolean nonWhitespace() default true;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
