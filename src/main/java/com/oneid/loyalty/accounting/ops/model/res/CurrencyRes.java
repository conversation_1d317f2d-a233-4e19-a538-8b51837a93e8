package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Currency;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
public class CurrencyRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("en_name")
    private String enName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    public static CurrencyRes of(Currency currency, Map<Integer, String> businessIdToName) {
        CurrencyRes currencyRes = new CurrencyRes();
        currencyRes.setId(currency.getId());
        currencyRes.setCode(currency.getCode());
        currencyRes.setBusinessId(currency.getBusinessId());
        currencyRes.setBusinessName(businessIdToName.get(currency.getBusinessId()));
        currencyRes.setName(currency.getName());
        currencyRes.setEnName(currency.getEnName());
        currencyRes.setDescription(currency.getDescription());
        currencyRes.setEnDescription(currency.getEnDescription());
        currencyRes.setStatus(currency.getStatus() != null ? currency.getStatus().getValue() : null);
        currencyRes.setCreatedBy(currency.getCreatedBy());
        currencyRes.setUpdatedBy(currency.getUpdatedBy());
        currencyRes.setApprovedBy(currency.getApprovedBy());
        currencyRes.setCreatedAt(currency.getCreatedAt() != null ? currency.getCreatedAt().toInstant().getEpochSecond() : null);
        currencyRes.setUpdatedAt(currency.getUpdatedAt() != null ? currency.getUpdatedAt().toInstant().getEpochSecond() : null);
        currencyRes.setApprovedAt(currency.getApprovedAt() != null ? currency.getApprovedAt().toInstant().getEpochSecond() : null);
        currencyRes.setCreatedYmd(currency.getCreatedYmd());
        return currencyRes;
    }
}
