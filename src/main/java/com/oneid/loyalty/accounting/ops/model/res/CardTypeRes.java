package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(value = Include.NON_NULL)
public class CardTypeRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("card_policy_id")
    private Integer cardPolicyId;

    @JsonProperty("card_type")
    private String cardType;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    public static CardTypeRes of(CardType cardType, Business business, Program program) {
        CardTypeRes cardBinRes = new CardTypeRes();
        cardBinRes.setId(cardType.getId());
        cardBinRes.setBusinessId(cardType.getBusinessId());
        cardBinRes.setBusinessName(business != null ? business.getName() : null);
        cardBinRes.setProgramId(cardType.getProgramId());
        cardBinRes.setProgramName(program != null ? program.getName() : null);
        cardBinRes.setCardPolicyId(cardType.getCardPolicyId());
        cardBinRes.setCardType(cardType.getCardType());
        cardBinRes.setName(cardType.getName());
        cardBinRes.setDescription(cardType.getDescription());
        cardBinRes.setStatus(cardType.getStatus() != null ? cardType.getStatus().getValue() : null);
        cardBinRes.setCreatedBy(cardType.getCreatedBy());
        cardBinRes.setUpdatedBy(cardType.getUpdatedBy());
        cardBinRes.setApprovedBy(cardType.getApprovedBy());
        cardBinRes.setCreatedAt(cardType.getCreatedAt() != null ? cardType.getCreatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setUpdatedAt(cardType.getUpdatedAt() != null ? cardType.getUpdatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setApprovedAt(cardType.getApprovedAt() != null ? cardType.getApprovedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setCreatedYmd(cardType.getCreatedYmd());
        return cardBinRes;
    }
}
