package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.CardBin;

public class CardBinMapper {
    public static CardBin toCardBinOne(CardBinCreateReq request) {
        CardBin cardBin = new CardBin();
        cardBin.setBusinessId(request.getBusinessId());
        cardBin.setProgramId(request.getProgramId());
        cardBin.setBinCode(request.getBinCode());
        cardBin.setDescription(request.getDescription());
        cardBin.setStatus(ECommonStatus.of(request.getStatus()));
        return cardBin;
    }

    public static CardBin toCardBinOne(CardBin cardBin, CardBinUpdateReq request) {
//        cardBin.setBusinessId(request.getBusinessId());
//        cardBin.setProgramId(request.getProgramId());
//        cardBin.setBinCode(request.getBinCode());
        cardBin.setDescription(request.getDescription());
        cardBin.setStatus(ECommonStatus.of(request.getStatus()));
        return cardBin;
    }
}
