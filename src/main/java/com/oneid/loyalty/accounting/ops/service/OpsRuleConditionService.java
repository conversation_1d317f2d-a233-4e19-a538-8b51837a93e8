package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.oneloyalty.common.constant.EServiceType;

import java.util.Collection;
import java.util.Map;

public interface OpsRuleConditionService {
    Collection<ConditionAttributeDto> getAttributes(Integer programId, EServiceType serviceType);

    Map<String, AttributeValueStrategy<?>> getMapAttributeValueStrategy(Integer programId, EServiceType serviceType);

    Map<String, ConditionAttributeDto> getMapAttributeValueDto(Integer programId, EServiceType serviceType);
}