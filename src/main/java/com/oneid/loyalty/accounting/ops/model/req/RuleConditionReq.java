package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RuleConditionReq implements Serializable {
    private static final long serialVersionUID = -5549945088494102089L;

    private Integer id;

    @NotBlank(message = "'attribute' must not be blank")
    @Length(max = 255)
    private String attribute;

    private Object value;

    @NotNull
    private EAttributeOperator operator;

    private Boolean archive;

    private EAttributeDataDisplayType dataTypeDisplay;

    public boolean hasEdit(RuleCondition rule, String reqValue) {
        if (!Objects.equals(this.attribute, rule.getAttribute())) {
            return true;
        }
        if (!Objects.equals(this.operator.getExpression(), rule.getOperator())) {
            return true;
        }
        if (!Objects.equals(reqValue, rule.getValue())) {
            return true;
        }

        return false;
    }
}
