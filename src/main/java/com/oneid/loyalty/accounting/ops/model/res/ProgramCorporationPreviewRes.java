package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ProgramCorporationPreviewRes extends OtherInfo implements Serializable {

    private static final long serialVersionUID = -3016228745884514515L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("corporations")
    List<CorporationInfo> corporations;

    @JsonProperty("approval_status")
    private EApprovalStatus approvalStatus;

    @JsonProperty("program_status")
    private ECommonStatus programStatus;
}
