package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardTypeRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardTypeService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping(value = "v1/cardtypes")
@Validated
public class CardTypeController extends BaseController {
    @Autowired
    OpsCardTypeService opsCardTypeService;

    @GetMapping("")
    @Authorize(role = AccessRole.MEMBER_CARD_TYPE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(@RequestParam(name = "business_id", required = false) Integer businessId,
                                              @RequestParam(name = "program_id", required = false) Integer programId,
                                              @RequestParam(name = "card_type_code", required = false) String cardType,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<CardTypeRes> page = opsCardTypeService.filter(businessId, programId, cardType, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_TYPE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCardType(@PathVariable(value = "id") Integer id) {
        CardTypeRes result = opsCardTypeService.get(id);
        return success(result);
    }

    @PostMapping
    @Authorize(role = AccessRole.MEMBER_CARD_TYPE, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createCardType(@RequestBody @Valid CardTypeCreateReq cardTypeCreateReq) {
        opsCardTypeService.add(cardTypeCreateReq);
        return success(null);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_TYPE, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCardType(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid CardTypeUpdateReq cardTypeUpdateReq) {
        opsCardTypeService.update(id, cardTypeUpdateReq);
        return success(null);
    }
}
