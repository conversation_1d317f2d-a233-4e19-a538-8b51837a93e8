package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RequestTransferDetailRes {
    private Integer id;
    private Long cardTransferNo;
    private ShortEntityRes business;
    private ShortEntityRes corporation;
    private ShortEntityRes chain;
    private ShortEntityRes program;
    private Integer cardQuantity;
    private ShortEntityRes storeFrom;
    private ShortEntityRes storeTo;
    private ECardTransferIndicatorStatus indicator;
    private ECardTransferStatus status;
    private String description;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date createdAt;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date updatedAt;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date approvedAt;
    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    private String deniedReason;
    private Long batchNo;
}