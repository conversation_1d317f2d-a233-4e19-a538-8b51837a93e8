package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramFunctionReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionAvailableDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionInfo;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface OpsProgramFunctionService {

    Map<String, List<ProgramFunctionInfo>> getFunctions();

    ProgramDetailRes getEditAttributeRequestSetting(Integer reviewId);

    MakerCheckerInternalMakerRes requestEditingAttributeRequest(ProgramFunctionReq request);

    MakerCheckerInternalMakerRes create(ProgramFunctionReq request);

    PageImpl<ProgramFunctionRes> searchAvailable(Integer businessId, String programCode, ECommonStatus status, Pageable pageable);

    PageImpl<ProgramFunctionRes> searchInReview(EApprovalStatus status, int offset, int limit);

    void approve(ApprovalReq req);

    ProgramFunctionAvailableDetailRes getAvailableDetail(Integer programId);

    ProgramFunctionAvailableDetailRes getInReviewDetail(Integer id);

    List<ProgramDropDownRes> getProgramByBusinessExcludeExist(Integer businessId);
}