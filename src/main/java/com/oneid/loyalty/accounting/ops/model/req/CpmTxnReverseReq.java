package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CpmTxnReverseReq implements Serializable {

    private static final long serialVersionUID = 2728346603457685911L;

    @JsonProperty("cpm_transaction_ref")
    @NotBlank(message = "CPM transaction ref must not be blank")
    private String cpmTransactionRef;

    @JsonProperty("invoice_no")
    @NotBlank(message = "Invoice no must not be blank")
    private String invoiceNo;

}
