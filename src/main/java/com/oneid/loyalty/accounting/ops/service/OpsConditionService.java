package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.ConditionAttributeGroupInfo;
import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;

import java.util.Collection;
import java.util.List;

public interface OpsConditionService {
    void verifyConditions(final Scheme scheme, List<ConditionRecordReq> conditions);

    List<ConditionRecordRes> getAllByRule(SchemeRule rule);

    Collection<ConditionAttributeGroupInfo> getAllConditionAttribute(Integer programId);

    Collection<ConditionAttributeGroupInfo> getByProgramIdAndModule(Integer programId, EServiceType module);

    Collection<ConditionAttributeDto> conditionAttributeDtos(Integer programId);
}