package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GiftCardProductionRequestReq {
    @NotNull(message = "BusinessId can not blank")
    private Integer businessId;

    @NotNull(message = "ProgramId can not blank")
    private Integer programId;

    @NotNull(message = "StoreId can not blank")
    private Integer storeId;

    @NotNull(message = "GiftCardBinId can not blank")
    private Integer giftCardBinId;

    @NotNull(message = "GiftCardTypeId can not blank")
    private Integer giftCardTypeId;

    @Min(value = 1,message = "no of card must be greater than 0")
    @Max(value = 10000,message = "no of card must be less than or equal 1000")
    private Integer noOfCard;

    @Length(max = 2, message = "SerialSuffix length maximum is 2")
    private String serialSuffix;

    private String description;

    @Pattern(regexp = "^(P|A)?$", message = "'Status' Only accept P/A values")
    private String initialGcStatus;

    @Pattern(regexp = "^(GIFT_CARD|GIFT_CODE)?$", message = "'Status' Only accept GIFT_CARD/GIFT_CODE values")
    private String batchType;

    @Pattern(regexp = "^(Y|N)?$", message = "'Status' Only accept Y/N values")
    private String generateQr;

    private Long batchNo;
}
