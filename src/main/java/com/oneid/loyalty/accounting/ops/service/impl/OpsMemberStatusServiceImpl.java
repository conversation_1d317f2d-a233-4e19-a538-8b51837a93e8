package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.ERequestAgentType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailAvailableRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionInfo;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberStatusService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.MemberStatus;
import com.oneid.oneloyalty.common.entity.MemberStatusFunction;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramFunction;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.DestinationMemberStatusService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.MemberStatusFunctionService;
import com.oneid.oneloyalty.common.service.MemberStatusService;
import com.oneid.oneloyalty.common.service.ProgramFunctionService;
import com.oneid.oneloyalty.common.service.ProgramService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsMemberStatusServiceImpl implements OpsMemberStatusService {

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private MemberStatusService memberStatusService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MemberService memberService;

    @Autowired
    private FunctionService functionService;

    @Autowired
    private ProgramFunctionService programFunctionService;

    @Autowired
    private MemberStatusFunctionService memberStatusFunctionService;

    @Autowired
    private DestinationMemberStatusService destinationMemberStatusService;

    @Override
    public MakerCheckerInternalMakerRes createRequest(CreateMemberStatusReq req) {
        businessService.findActive(req.getBusinessId());
        Program program = programService.findActive(req.getProgramId());
        verifyMemberStatusReq(req);
        validationCodeExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getCode(), req.getViName(), req.getEnName(), req.getDefaultStatus());
        validationFunction(req.getFunctions(), program.getId());

        req.setStatus(ECommonStatus.ACTIVE);
        req.setDefaultStatus(Objects.nonNull(req.getDefaultStatus()) ? req.getDefaultStatus() : EBoolean.NO);
        req.setRevertLatestStatus(Objects.nonNull(req.getRevertLatestStatus()) ? req.getRevertLatestStatus() : EBoolean.NO);
        req.setExpiredAllPoints(Objects.nonNull(req.getExpiredAllPoints()) ? req.getExpiredAllPoints() : EBoolean.NO);

        MakerCheckerInternalMakerReq<CreateMemberStatusReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateMemberStatusReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.MEMBER_STATUS.getName())
                .requestType(EMakerCheckerType.MEMBER_STATUS.getType())
                .payload(req)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);

        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Member status call maker error: " + createMakerProgramCorporation.getMeta().getMessage(),
                    null);
        }
        return createMakerProgramCorporation.getData();
    }

    @Override
    public Page<MemberStatusInReviewRes> getListInReviewRequests(EApprovalStatus approvalStatus, String fromCreatedAt, String toCreatedAt, String fromReviewedAt, String toReviewedAt, String createdBy, String checkedBy, Integer offset, Integer limit) {
        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.MEMBER_STATUS.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        checkedBy,
                        checkedDate
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<MemberStatusInReviewRes> content = Optional.ofNullable(previewRes.getData())
                .orElse(Collections.emptyList())
                .stream()
                .map(request -> {
                    CreateMemberStatusReq memberStatusReq = objectMapper.convertValue(request.getPayload(), CreateMemberStatusReq.class);
                    Program program = programService.find(memberStatusReq.getProgramId()).orElseThrow(
                            () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
                    );

                    Business business = businessService.find(memberStatusReq.getBusinessId()).orElseThrow(
                            () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
                    );
                    return MemberStatusInReviewRes.builder()
                            .id(request.getId())
                            .code(memberStatusReq.getCode())
                            .name(memberStatusReq.getViName())
                            .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                            .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                            .status(memberStatusReq.getStatus())
                            .approvalStatus(EApprovalStatus.of(request.getStatus()))
                            .createdBy(request.getMadeByUserName())
                            .createdAt(request.getMadeDateToDate())
                            .reviewedBy(request.getCheckedByUserName())
                            .reviewedAt(request.getCheckedDateToDate())
                            .build();
                })
                .collect(Collectors.toList());
        return new PageImpl<>(content, new OffsetBasedPageRequest(offset, limit, null), previewRes.getMeta().getTotal());
    }

    @Override
    public MemberStatusDetailInReviewRes getInReviewRequestById(Long reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(reviewId);
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "MemberStatus Config - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.MEMBER_STATUS.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "MemberStatus Config - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }

        MakerCheckerInternalDataDetailRes makerCheckerReq = previewDetailRes.getData();
        CreateMemberStatusReq memberStatusReq = this.objectMapper.convertValue(previewDetailRes.getData().getPayload(), CreateMemberStatusReq.class);
        Program program = programService.find(memberStatusReq.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
        Business business = businessService.find(memberStatusReq.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        List<MemberStatusDetailInReviewRes.Function> functions = Optional.ofNullable(memberStatusReq.getFunctions())
                .orElse(Collections.emptyList())
                .stream()
                .map(f -> {
                    MemberStatusDetailInReviewRes.Function function = new MemberStatusDetailInReviewRes.Function();
                    function.setGroup(f.getGroup());
                    function.setFunctionCode(f.getFunctionCode());
                    function.setFunctionName(f.getFunctionName());
                    return function;
                })
                .collect(Collectors.toList());

        return MemberStatusDetailInReviewRes.builder()
                .id(makerCheckerReq.getId())
                .requestType(Objects.nonNull(memberStatusReq.getId()) ?
                        EMakerCheckerActionType.UPDATE.getValue() : EMakerCheckerActionType.CREATE.getValue())
                .approvalStatus(EApprovalStatus.of(previewDetailRes.getData().getStatus()))
                .createdBy(makerCheckerReq.getMadeByUserName())
                .createdAt(makerCheckerReq.getMadeDateToDate())
                .reviewedBy(makerCheckerReq.getCheckedByUserName())
                .reviewedAt(makerCheckerReq.getCheckedDateToDate())
                .reason(makerCheckerReq.getComment())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .code(memberStatusReq.getCode())
                .name(memberStatusReq.getViName())
                .enName(memberStatusReq.getEnName())
                .status(memberStatusReq.getStatus())
                .viDescription(memberStatusReq.getViDescription())
                .enDescription(memberStatusReq.getEnDescription())
                .defaultStatus(memberStatusReq.getDefaultStatus())
                .revertLatestStatus(memberStatusReq.getRevertLatestStatus())
                .expiredAllPoints(memberStatusReq.getExpiredAllPoints())
                .functions(functions)
                .build();
    }

    private void validationFunction(List<CreateMemberStatusReq.Function> functions, Integer programId) {
        if (Objects.nonNull((functions))) {
            functions
                    .forEach(e ->
                            programFunctionService.find(programId, e.getFunctionCode(), ECommonStatus.ACTIVE, EBoolean.YES)
                                    .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_FUNCTION_NOT_FOUND, null, null))

                    );
        }

    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.MEMBER_STATUS);
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                processApprove(detailResData);
            }
        }
        makerCheckerInternalFeignClient.checkerDefault(req);
    }

    @Override
    public MemberStatusDetailAvailableRes getChangeableByRequestId(Integer requestId) {
        MemberStatus memberStatus = memberStatusService.find(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_STATUS_NOT_FOUND,
                        "Limitation not found", null, new Integer[]{requestId}));
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.MEMBER_STATUS.getType(), memberStatus.getRequestCode());
        return getMemberStatusDetail(memberStatus);
    }

    @Override
    public MakerCheckerInternalMakerRes update(Integer memberStatusId, CreateMemberStatusReq req) {
        MemberStatus memberStatus = memberStatusService.find(memberStatusId).orElseThrow(
                () -> new BusinessException(ErrorCode.MEMBER_STATUS_NOT_FOUND, null, null)
        );

        Program program = programService.findActive(memberStatus.getProgramId());

        opsReqPendingValidator.verifyEditKey(req.getEditKey(), memberStatus.getRequestCode(), memberStatus.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.MEMBER_STATUS.getType(), memberStatus.getRequestCode());
        validationUpdate(req, memberStatus);
        validationFunction(req.getFunctions(), program.getId());

        businessService.findActive(program.getBusinessId());
        req.setId(memberStatus.getId());
        MakerCheckerInternalMakerReq<CreateMemberStatusReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateMemberStatusReq>builder()
                .requestCode(memberStatus.getRequestCode())
                .requestName(EMakerCheckerType.MEMBER_STATUS.getName())
                .requestType(EMakerCheckerType.MEMBER_STATUS.getType())
                .payload(req)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerChecker = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerChecker.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "memberStatus error: " + createMakerChecker.getMeta().getMessage(),
                    null);
        }
        return createMakerChecker.getData();
    }

    private MemberStatusDetailAvailableRes getMemberStatusDetail(MemberStatus memberStatus) {
        Program program = programService.find(memberStatus.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        Business business = businessService.findById(program.getBusinessId());

        String editKey = opsReqPendingValidator.generateEditKey(memberStatus.getRequestCode(), memberStatus.getVersion());

        List<String> functionCodes = memberStatusFunctionService.findByMemberStatusIdAndStatus(memberStatus.getId(), ECommonStatus.ACTIVE)
                .stream()
                .map(MemberStatusFunction::getFunctionCode)
                .collect(Collectors.toList());

        List<MemberStatusDetailRes.Function> functions = functionService.findByCodeIn(functionCodes)
                .stream().map(e -> new MemberStatusDetailRes.Function(e.getCode(), e.getName(), e.getGroupName()))
                .collect(Collectors.toList());

        return MemberStatusDetailAvailableRes.builder()
                .id(memberStatus.getId())
                .editKey(editKey)
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .code(memberStatus.getCode())
                .viName(memberStatus.getViName())
                .enName(memberStatus.getEnName())
                .viDescription(memberStatus.getViDescription())
                .enDescription(memberStatus.getEnDescription())
                .status(memberStatus.getStatus())
                .revertLatestStatus(memberStatus.getRevertLastedStatus())
                .expiredAllPoints(memberStatus.getExpiredAllPoints())
                .defaultStatus(memberStatus.getDefaultStatus())
                .createdBy(memberStatus.getCreatedBy())
                .updatedBy(memberStatus.getUpdatedBy())
                .approvedBy(memberStatus.getApprovedBy())
                .createdAt(memberStatus.getCreatedAt())
                .updatedAt(memberStatus.getUpdatedAt())
                .approvedAt(memberStatus.getApprovedAt())
                .version(memberStatus.getVersion())
                .functions(functions)
                .build();
    }

    private void verifyMemberStatusReq(CreateMemberStatusReq req) {
        if (memberStatusService.findByProgramIdAndCode(req.getProgramId(), req.getCode()).isPresent())
            throw new BusinessException(ErrorCode.MEMBER_STATUS_CODE_EXISTED);
        if (memberStatusService.findByProgramIdAndViName(req.getProgramId(), req.getViName()).isPresent())
            throw new BusinessException(ErrorCode.MEMBER_STATUS_VI_NAME_EXISTED);
        if (memberStatusService.findByProgramIdAndEnName(req.getProgramId(), req.getEnName()).isPresent())
            throw new BusinessException(ErrorCode.MEMBER_STATUS_EN_NAME_EXISTED);
        if (EBoolean.YES.equals(req.getDefaultStatus())
                && memberStatusService.findByProgramIdAndDefaultStatusAndStatus(req.getProgramId(), EBoolean.YES, ECommonStatus.ACTIVE).isPresent())
            throw new BusinessException(ErrorCode.ALREADY_EXIST_DEFAULT_STATUS);
    }

    private void validationCodeExistInOtherReqPending(Integer businessId, Integer programId, String code, String viName, String enName, EBoolean defaultStatus) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.MEMBER_STATUS.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        previewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), CreateMemberStatusReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId)
                        && ele.getCode().equals(code))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.MEMBER_STATUS_CODE_EXISTED,
                            "[VALIDATION MEMBER STATUS] member status existed in other requests pending", code, new Object[]{code});
                });

        previewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), CreateMemberStatusReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId)
                        && ele.getViName().equals(viName))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.MEMBER_STATUS_VI_NAME_EXISTED,
                            "[VALIDATION MEMBER STATUS] member status existed in other requests pending", viName, new Object[]{viName});
                });

        previewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), CreateMemberStatusReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId)
                        && ele.getEnName().equals(enName))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.MEMBER_STATUS_EN_NAME_EXISTED,
                            "[VALIDATION MEMBER STATUS] member status existed in other requests pending", enName, new Object[]{enName});
                });

        if (EBoolean.YES.equals(defaultStatus)) {
            previewRes.getData().stream()
                    .map(data -> this.objectMapper.convertValue(data.getPayload(), CreateMemberStatusReq.class))
                    .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId)
                            && EBoolean.YES.equals(ele.getDefaultStatus()) && ECommonStatus.ACTIVE.equals(ele.getStatus()))
                    .findAny().ifPresent(ele -> {
                        throw new BusinessException(ErrorCode.ALREADY_EXIST_DEFAULT_STATUS,
                                "[VALIDATION MEMBER STATUS] member status already exist default status in other requests pending", null, null);
                    });
        }
    }

    private void processApprove(MakerCheckerInternalDataDetailRes detailResData) {
        CreateMemberStatusReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), CreateMemberStatusReq.class);
        String createdBy = null;
        String updatedBy;
        Program program = programService.findActive(payload.getProgramId());
        validationFunction(payload.getFunctions(), program.getId());
        MemberStatus memberStatus;
        if (payload.getId() != null) {
            memberStatus = memberStatusService.find(payload.getId()).orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_STATUS_NOT_FOUND));
            validationUpdate(payload, memberStatus);
            updatedBy = detailResData.getMadeByUserName();
        } else {
            verifyMemberStatusReq(payload);
            updatedBy = createdBy = detailResData.getMadeByUserName();
            memberStatus = new MemberStatus();
            memberStatus.setBusinessId(payload.getBusinessId());
            memberStatus.setProgramId(payload.getProgramId());
            memberStatus.setCode(payload.getCode());
        }
        memberStatus.setViName(payload.getViName());
        memberStatus.setEnName(payload.getEnName());
        memberStatus.setViDescription(payload.getViDescription());
        memberStatus.setEnDescription(payload.getEnDescription());
        memberStatus.setDefaultStatus(payload.getDefaultStatus());
        memberStatus.setRevertLastedStatus(payload.getRevertLatestStatus());
        memberStatus.setExpiredAllPoints(payload.getExpiredAllPoints());
        memberStatus.setStatus(payload.getStatus());
        memberStatus.setVersion(detailResData.getVersion());
        memberStatus.setRequestCode(detailResData.getRequestCode());

        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        opsReqPendingValidator.updateInfoChecker(memberStatus, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        MemberStatus result = memberStatusService.save(memberStatus);

        for (MemberStatusFunction ele : memberStatusFunctionService.findByMemberStatusIdAndStatus(result.getId(), ECommonStatus.ACTIVE)) {
            ele.setStatus(ECommonStatus.INACTIVE);
            memberStatusFunctionService.save(ele);
        }

        if (!CollectionUtils.isEmpty(payload.getFunctions())) {
            List<String> functionCodes = payload.getFunctions()
                    .stream()
                    .map(CreateMemberStatusReq.Function::getFunctionCode)
                    .collect(Collectors.toList());

            for (String code : functionCodes) {
                MemberStatusFunction memberStatusFunction = new MemberStatusFunction();
                memberStatusFunction.setMemberStatusId(result.getId());
                memberStatusFunction.setFunctionCode(code);
                memberStatusFunction.setStatus(ECommonStatus.ACTIVE);
                opsReqPendingValidator.updateInfoChecker(memberStatusFunction, detailResData.getMadeDate(), result.getCreatedBy(), result.getUpdatedBy(), result.getApprovedBy());

                memberStatusFunctionService.save(memberStatusFunction);
            }
        }
    }

    private void validationUpdate(CreateMemberStatusReq req, MemberStatus memberStatus) {
        if (Objects.nonNull(req.getStatus()) && ECommonStatus.INACTIVE.equals(req.getStatus())) {
            if (memberService.findFirstByProgramIdAndStatus(req.getProgramId(), memberStatus.getCode()).isPresent()
                    || destinationMemberStatusService.verifyExistConfig(memberStatus.getId(), memberStatus.getId()).isPresent())
                throw new BusinessException(ErrorCode.MEMBER_STATUS_ALREADY_USE);
        } else {
            req.setStatus(ECommonStatus.ACTIVE);
        }
        if (!memberStatus.getCode().equals(req.getCode())) {
            if (memberStatusService.findByProgramIdAndCode(req.getProgramId(), req.getCode()).isPresent())
                throw new BusinessException(ErrorCode.MEMBER_STATUS_CODE_EXISTED);
        }
        if (!memberStatus.getViName().equals(req.getViName())) {
            if (memberStatusService.findByProgramIdAndViName(req.getProgramId(), req.getViName()).isPresent())
                throw new BusinessException(ErrorCode.MEMBER_STATUS_VI_NAME_EXISTED);
        }
        if (!memberStatus.getEnName().equals(req.getEnName())) {
            if (memberStatusService.findByProgramIdAndEnName(req.getProgramId(), req.getEnName()).isPresent())
                throw new BusinessException(ErrorCode.MEMBER_STATUS_EN_NAME_EXISTED);
        }
        if (EBoolean.NO.equals(memberStatus.getDefaultStatus()) && EBoolean.YES.equals(req.getDefaultStatus())
                && memberStatusService.findByProgramIdAndDefaultStatusAndStatus(req.getProgramId(), EBoolean.YES, ECommonStatus.ACTIVE).isPresent())
            throw new BusinessException(ErrorCode.ALREADY_EXIST_DEFAULT_STATUS);
    }

    @Override
    public Map<String, List<ProgramFunctionInfo>> getFunctions(Integer programId) {
        programService.findActive(programId);
        List<ProgramFunction> programFunctions = programFunctionService.find(programId, ECommonStatus.ACTIVE, EBoolean.YES);

        List<String> functionCodes = programFunctions
                .stream()
                .map(ProgramFunction::getFunctionCode)
                .collect(Collectors.toList());

        return functionService.findByCodeIn(functionCodes)
                .stream()
                .peek(ele -> {
                    if (Objects.isNull(ele.getGroupName())) {
                        ele.setGroupName(ERequestAgentType.OTHER.getValue());
                    }
                })
                .map(ProgramFunctionInfo::valueOf)
                .collect(Collectors.groupingBy(ProgramFunctionInfo::getGroupName));
    }

    @Override
    public Page<MemberStatusAvaiRes> getListAvailableRequests(Integer businessId,
                                                              Integer programId,
                                                              String code,
                                                              String status,
                                                              Integer offset,
                                                              Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.Direction.DESC, "createdAt");

        SpecificationBuilder<MemberStatus> specificationBuilder = new SpecificationBuilder<>();

        specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null) {
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        }

        if (code != null) {
            specificationBuilder.add(new SearchCriteria("code", code, SearchOperation.MATCH));
        }

        if (status != null) {
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));
        }

        Page<MemberStatus> memberStatuses = memberStatusService.find(specificationBuilder, pageRequest);

        return memberStatuses.map(request -> {
            Program program = programService.find(request.getProgramId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
            );

            Business business = businessService.find(request.getBusinessId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
            );
            return MemberStatusAvaiRes.builder()
                    .id(request.getId())
                    .code(request.getCode())
                    .name(request.getViName())
                    .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                    .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                    .status(request.getStatus())
                    .createdBy(request.getCreatedBy())
                    .createdAt(request.getCreatedAt())
                    .updatedBy(request.getUpdatedBy())
                    .updatedAt(request.getUpdatedAt())
                    .build();
        });
    }

    @Override
    public MemberStatusDetailAvaiRes getAvailableRequestById(Integer id) {
        MemberStatus memberStatus = memberStatusService.find(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_STATUS_NOT_FOUND, "Member status not found", null));

        Business business = businessService.find(memberStatus.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        Program program = programService.find(memberStatus.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        List<String> functionCodes = memberStatusFunctionService.findByMemberStatusIdAndStatus(memberStatus.getId(), ECommonStatus.ACTIVE)
                .stream().map(MemberStatusFunction::getFunctionCode)
                .collect(Collectors.toList());

        List<MemberStatusDetailAvaiRes.Function> functions = functionService.findByCodeIn(functionCodes)
                .stream().map(e -> new MemberStatusDetailAvaiRes.Function(e.getCode(), e.getName(), e.getGroupName()))
                .collect(Collectors.toList());

        return MemberStatusDetailAvaiRes.builder()
                .id(memberStatus.getId())
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .code(memberStatus.getCode())
                .name(memberStatus.getViName())
                .enName(memberStatus.getEnName())
                .status(memberStatus.getStatus())
                .viDescription(memberStatus.getViDescription())
                .enDescription(memberStatus.getEnDescription())
                .defaultStatus(memberStatus.getDefaultStatus())
                .revertLatestStatus(memberStatus.getRevertLastedStatus())
                .expiredAllPoints(memberStatus.getExpiredAllPoints())
                .functions(functions)
                .createdBy(memberStatus.getCreatedBy())
                .createdAt(memberStatus.getCreatedAt())
                .updatedBy(memberStatus.getUpdatedBy())
                .updatedAt(memberStatus.getUpdatedAt())
                .approvedBy(memberStatus.getApprovedBy())
                .approvedAt(memberStatus.getApprovedAt())
                .build();
    }
}