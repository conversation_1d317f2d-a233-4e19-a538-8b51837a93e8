package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CreateProgramReq;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Program;

import java.util.Objects;

public class ProgramMapper {
    public static Program toProgram(CreateProgramReq createProgramReq) {
        Program program = new Program();
        program.setCode(createProgramReq.getProgramCode());
        program.setName(createProgramReq.getProgramName());
        program.setEnName(createProgramReq.getProgramNameEn());
        program.setWebsite(createProgramReq.getWebsite());
        program.setHotline(createProgramReq.getHotline());
        program.setLogoUrl(createProgramReq.getLogoUrl());
        program.setProgramRef(createProgramReq.getProgramRef());
        program.setAutoRegister(Objects.nonNull(createProgramReq.getProgramRef()) ? EBoolean.YES : EBoolean.NO);
        program.setBusinessId(createProgramReq.getBusinessId());
        if (createProgramReq.getStatus() == null) {
            program.setStatus(ECommonStatus.ACTIVE);
        } else {
            program.setStatus(ECommonStatus.of(createProgramReq.getStatus()));
        }
        program.setDescription(createProgramReq.getDescription());
        program.setEnDescription(createProgramReq.getDescriptionEn());
        program.setCreatedYmd(DateTimes.getNowLongYmd());
        return program;
    }
}