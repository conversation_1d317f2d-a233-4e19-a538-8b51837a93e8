package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AttributeMasterDataVerifyReq {
    @NotNull(message = "'data_type' must not be null")
    private EAttributeDataType dataType;

    @NotNull(message = "'data_type_display' must not be null")
    private EAttributeDataTypeDisplay dataTypeDisplay;

    private String regexValidation;
}