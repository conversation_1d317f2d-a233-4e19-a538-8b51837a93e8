package com.oneid.loyalty.accounting.ops.feign.decoder;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.support.feign.BadRequestFeignException;
import com.oneid.loyalty.accounting.ops.support.feign.decoder.ResponseErrorHandlingVerifier;
import com.oneid.loyalty.accounting.ops.support.web.security.authentication.IntrospectProps;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.Meta;

import feign.Request;

@Component
public class APIResponseVerifier implements ResponseErrorHandlingVerifier<APIResponse<?>>  {
    @Override
    public boolean isApplicable(Type type) {
        if (type instanceof ParameterizedType) {
            return APIResponse.class.getCanonicalName().equals(((ParameterizedType) type).getRawType().getTypeName());
        }
       
        return false;
    }

    @Override
    public void verify(Request request, APIResponse<?> response) {
        Meta meta = response.getMeta();
        
        if(meta.getCode() != ErrorCode.SUCCESS.getValue() && meta.getCode() != IntrospectProps.REQUEST_SUCCESS_STATUS) 
            throw new BadRequestFeignException(HttpStatus.OK.value(), 
                    new StringBuilder()
                    .append(request.httpMethod())
                    .append(" ")
                    .append(request.url()).toString(), meta.getCode(), meta.getMessage());
    }
}