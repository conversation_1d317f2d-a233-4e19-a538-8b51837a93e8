package com.oneid.loyalty.accounting.ops.model.req;

import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Getter
@Setter
public class RequestTransferReq {
    @NotBlank(message = "business_id is missing")
    private Integer businessId;
    @NotBlank(message = "program_id is missing")
    private Integer programId;
    @NotBlank(message = "from_store_id is missing")
    private Integer fromStoreId;
    @NotBlank(message = "to_store_id is missing")
    private Integer toStoreId;
    @NotNull(message = "batch_no is missing")
    private Long batchNo;

    private String description; // remark
    private Set<String> cardNoList;
}