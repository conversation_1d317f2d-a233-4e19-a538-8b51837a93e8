package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EFormulaAttribute;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class FormulaGroupRes {
    @NotNull(message = "attribute must not be null")
    @JsonProperty("attribute")
    private EFormulaAttribute attribute;

    @Valid
    @JsonProperty("formula_list")
    private List<FormulaRecordRes> formulaList;
}