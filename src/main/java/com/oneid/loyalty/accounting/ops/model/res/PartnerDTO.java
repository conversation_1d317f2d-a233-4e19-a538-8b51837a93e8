//package com.oneid.loyalty.accounting.ops.model.res;
//
//import com.fasterxml.jackson.annotation.JsonProperty;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.oneid.loyalty.accounting.ops.converter.EpochTimeSerialize;
//import com.oneid.loyalty.accounting.ops.entity.WlPartner;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.util.Date;
//
//@Setter
//@Getter
//public class PartnerDTO {
//    @JsonProperty("partner_code")
//    private String partnerCode;
//
//    @JsonProperty("name")
//    private String name;
//
//    @JsonProperty("description")
//    private String description;
//
//    @JsonProperty("display_order")
//    private Integer displayOrder;
//
//    @JsonProperty("status")
//    private String status;
//
//    @JsonProperty("logo_url")
//    private String logoUrl;
//
//    @JsonProperty("created_by")
//    private String createdBy;
//
//    @JsonProperty("created_at")
//    @JsonSerialize(converter = EpochTimeSerialize.class)
//    private Date createdAt;
//
//    @JsonProperty("updated_by")
//    private String updatedBy;
//
//    @JsonProperty("updated_at")
//    @JsonSerialize(converter = EpochTimeSerialize.class)
//    private Date updatedAt;
//
//    @JsonProperty("approved_by")
//    private String approvedBy;
//
//    @JsonProperty("approved_at")
//    @JsonSerialize(converter = EpochTimeSerialize.class)
//    private Date approvedAt;
//
//    public static PartnerDTO valueOf(WlPartner partner) {
//        PartnerDTO result = new PartnerDTO();
//        result.setPartnerCode(partner.getPartnerCode());
//        result.setName(partner.getName());
//        result.setDescription(partner.getDescription());
//        result.setDisplayOrder(partner.getDisplayOrder());
//        result.setStatus(partner.getStatus());
//        result.setLogoUrl(partner.getLogoUrl());
//        result.setCreatedBy(partner.getCreatedBy());
//        result.setCreatedAt(partner.getCreatedAt());
//        result.setUpdatedBy(partner.getUpdatedBy());
//        result.setUpdatedAt(partner.getUpdatedAt());
//        result.setApprovedBy(partner.getApprovedBy());
//        result.setApprovedAt(partner.getApprovedAt());
//
//        return result;
//    }
//}
