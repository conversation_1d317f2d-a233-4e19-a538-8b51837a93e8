package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierMatchingRequestRes extends VersioningRes {

    private Integer requestId;

    private Integer reviewId;
    
    private Integer businessId;

    private String businessName;

    private Integer baseProgramId;

    private String baseProgramName;

    private Integer matchedProgramId;

    private String matchedProgramName;

    private String tierMatchingCode;

    private String tierMatchingName;

    @JsonSerialize(using =  DateSerializer.class)
    private Date startDate;

    @JsonSerialize(using =  DateSerializer.class)
    private Date endDate;
    
    private ECommonStatus status;
    
    private EApprovalStatus approvalStatus;

}
