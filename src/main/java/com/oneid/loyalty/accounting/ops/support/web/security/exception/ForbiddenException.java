package com.oneid.loyalty.accounting.ops.support.web.security.exception;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ForbiddenException extends RuntimeException {
    private static final long serialVersionUID = -1157519001554168050L;
    private Object[] arguments;
    private OpsErrorCode errorCode;
}