package com.oneid.loyalty.accounting.ops.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.poiji.annotation.ExcelCellName;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;

@Data
@Validated
public class AutoEarningTransactionExcelDTO {

    @ExcelCellName("File Name")
    @JsonProperty("data_file_name")
    private String fileName;

    @ExcelCellName("OneU ID")
    @JsonProperty("oneu_id")
    private String oneuId;

    @ExcelCellName("TCB ID")
    @JsonProperty("loyalty_cust_id")
    private String loyaltyCustId;

    @ExcelCellName("Transaction Id")
    @JsonProperty("transaction_id")
    private String transactionId;

    @ExcelCellName("Transaction Amount")
    @JsonProperty("transaction_amount")
    private BigDecimal transactionAmount;

    @ExcelCellName("FT Transaction ID")
    @JsonProperty("ft_transaction_id")
    private String ftTransactionId;

    @ExcelCellName("OneU Transaction Ref")
    @JsonProperty("oneu_transaction_ref")
    private String oneuTransactionRef;

    @ExcelCellName("OneU Status")
    @JsonProperty("oneu_status")
    private ETransactionStatus oneuStatus;

    @ExcelCellName("OneU Error Message")
    @JsonProperty("oneu_error_message")
    private String oneuErrorMessage;

    @ExcelCellName("TCB Transaction Ref")
    @JsonProperty("tcb_transaction_ref")
    private String tcbTransactionRef;

    @ExcelCellName("TCB Status")
    @JsonProperty("tcb_status")
    private ETransactionStatus tcbStatus;

    @ExcelCellName("TCB Error Message")
    @JsonProperty("tcb_error_message")
    private String tcbErrorMessage;
}
