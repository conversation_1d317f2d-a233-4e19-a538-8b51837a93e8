package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.MemberSortingField;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogCardMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateMemberReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberProfileRes;
import com.oneid.loyalty.accounting.ops.model.res.TierHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
import com.oneid.loyalty.accounting.ops.service.CommonCheckLogService;
import com.oneid.loyalty.accounting.ops.service.OpsGenerateTemplateService;
import com.oneid.loyalty.accounting.ops.service.OpsMemberService;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.nio.file.Path;
import java.util.LinkedList;
import java.util.List;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

@Validated
@RestController
@RequestMapping("v1/users")
public class UsersController extends BaseController {
    @Autowired
    OpsTransactionService opsTransactionService;

    @Autowired
    OpsMemberService opsMemberService;

    @Autowired
    CommonCheckLogService commonChecklogService;

    @Autowired
    OpsGenerateTemplateService opsGenerateTemplateService;

    @GetMapping
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberProfiles(
            @RequestParam(name = "business_id", required = true) Integer businessId,
            @RequestParam(name = "program_id", required = true) Integer programId,
            @RequestParam(name = "member_code", required = false) String memberCode,
            @RequestParam(name = "phone_no", required = false) String phoneNo,
            @RequestParam(name = "full_name", required = false) String fullName,
            @RequestParam(name = "identify_no", required = false) String identifyNo,
            @RequestParam(name = "card_no", required = false) String cardNo,
            @Valid @RequestParam(name = "limit", defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
            @RequestParam(value = "sort_direction", required = false) Direction direction,
            @Valid @RequestParam(value = "sort_by", required = false) MemberSortingField sortBy) {
        Page<MemberProfileRes> result = opsMemberService.getMemberProfiles(businessId, programId, memberCode, phoneNo, fullName, identifyNo, limit, offset, direction, sortBy, cardNo);
        return success(result.getContent(), offset, result.getNumberOfElements(), (int) result.getTotalElements());
    }

    @GetMapping("/member/{memberId}/profile")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberProfileById(@PathVariable("memberId") Long memberId) {
        return success(opsMemberService.getProfileById(memberId));
    }

    @PostMapping("/member/{memberId}/update")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.EDIT})
    public ResponseEntity<?> updateMember(
            @PathVariable("memberId") Long memberId,
            @RequestBody @Valid UpdateMemberReq req) throws Exception {
        return success(opsMemberService.updateMember(memberId, req));
    }

    @PostMapping("/member/add")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> addMember(@RequestBody @Valid CreateMemberReq req) {
        return success(opsMemberService.addMember(req));
    }

    @GetMapping(value = "{member_id}/transactions")
    @Authorize(role = AccessRole.TRANSACTION, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionByMemberCode(
            @Valid @RequestParam(value = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", defaultValue = "10") @Min(1) @Max(200) Integer limit,
            @RequestParam(name = "business_id", required = false) Integer businessId,
            @RequestParam(name = "corporation_id", required = false) Integer corporationId,
            @RequestParam(name = "chain_id", required = false) Integer chainId,
            @RequestParam(name = "store_id", required = false) Integer storeId,
            @RequestParam(name = "terminal_id", required = false) Integer terminalId,
            @RequestParam(name = "program_id", required = false) Integer programId,
            @RequestParam(name = "cancellation_type", required = false) EOpsCancellationType cancellationType,
            @RequestParam(name = "invoice_number", required = false) String invoiceNumber,
            @RequestParam(name = "tnx_ref_no", required = false) String tnxRefNo,
            @RequestParam(name = "member_id", required = false) Long memberIdParam,
            @Valid @RequestParam(value = "tnx_date_from", required = false) Integer transactionFrom,
            @Valid @RequestParam(value = "tnx_date_to", required = false) Integer transactionTo,
            @PathVariable(name = "member_id", required = true) Long memberId,
            @RequestParam(name = "status", required = false) ETransactionStatus status
    ) {
        if (memberIdParam != null)
            memberId = memberIdParam;

        TransactionSearchReq transactionSearchReq = TransactionSearchReq
                .builder()
                .businessId(businessId)
                .corporationId(corporationId)
                .chainId(chainId)
                .storeId(storeId)
                .terminalId(terminalId)
                .programId(programId)
                .cancellationType(cancellationType)
                .invoiceNumber(invoiceNumber)
                .tnxRefNo(tnxRefNo)
                .memberId(memberId)
                .transactionFrom(transactionFrom)
                .transactionTo(transactionTo)
                .status(status)
                .build();

        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, null);
        Page<TransactionRes> page = opsTransactionService.search(transactionSearchReq, pageRequest, Boolean.TRUE);
        return success(page, offset, limit);
    }

    @GetMapping("/member/{memberId}/balance")
    @Authorize(role = AccessRole.MEMBER_BALANCE, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberBalanceById(@PathVariable("memberId") Long memberId) {
        return new ResponseEntity<APIResponse<?>>(opsMemberService.getMemberBalanceById(memberId), HttpStatus.OK);
    }

    @GetMapping("/bulk/registration/template-file")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.EXPORT})
    public ResponseEntity<Resource> downloadRegisterMemberTemplate() throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", "attachment; filename=template-register-member.xlsx");
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");

        Path path = opsGenerateTemplateService.registerMember();

        return new ResponseEntity<>(new InputStreamResource(new FileSystemResource(path).getInputStream()),
                headers, HttpStatus.OK);
    }

    @PostMapping("/bulk/registration")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.IMPORT})
    public ResponseEntity<Resource> registerMemberByBatchFile(
            Authentication authentication,
            @RequestPart(name = "files", required = false) MultipartFile[] multipartFiles) {
        return opsMemberService.registerMemberByBatchFile((OPSAuthenticatedPrincipal) authentication.getPrincipal(), multipartFiles);
    }

    @GetMapping("/member/{memberId}/checklog")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getChecklogOfMember(
            Authentication authentication,
            @PathVariable("memberId") Long memberId,
            @RequestParam(value = "time_from", required = false) Long timeFrom,
            @RequestParam(value = "time_to", required = false) Long timeTo) {
        return success(commonChecklogService.filterCheckLogMember(memberId,
                FilterCheckLogMemberReq.builder()
                        .timeFrom(timeFrom)
                        .timeTo(timeTo)
                        .build())
        );
    }

    @GetMapping("/member/{memberId}/checklog/{checklogId}")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getChecklogOfMember(
            Authentication authentication,
            @PathVariable("memberId") Long memberId,
            @PathVariable("checklogId") Long checklogId) {
        return success(commonChecklogService.getDetailCheckLogMemberById(memberId, checklogId));
    }

    @GetMapping("/member/{memberId}/cards/{cardNo}/checklog")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getCheckCardlogOfMember(
            Authentication authentication,
            @PathVariable("memberId") Long memberId,
            @PathVariable("cardNo") String cardNo,
            @RequestParam(value = "time_from", required = false) Long timeFrom,
            @RequestParam(value = "time_to", required = false) Long timeTo) {
        return success(commonChecklogService.filterCheckLogCardMember(memberId,
                FilterCheckLogCardMemberReq.builder()
                        .timeTo(timeTo)
                        .timeFrom(timeFrom)
                        .cardNo(cardNo)
                        .build())
        );
    }

    @GetMapping("/member/{memberId}/cards/{cardNo}/checklog/{checklogId}")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getCheckCardlogOfMember(
            Authentication authentication,
            @PathVariable("memberId") Long memberId,
            @PathVariable("cardNo") String cardNo,
            @PathVariable("checklogId") Long checklogId) {
        return success(commonChecklogService.getDetailCheckLogCardMemberById(memberId, cardNo, checklogId));
    }

    @GetMapping("/member/{memberId}/tier-histories")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTierHistories(
            @PathVariable("memberId") Long memberId,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, null);
        Page<TierHistoryRes> page = opsMemberService.getMemberTierHistory(memberId, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/member/{memberId}/member-attributes")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberAttributes(@PathVariable("memberId") Long memberId) {
        return success(opsMemberService.getMemberAttributes(memberId));
    }

    @GetMapping("/member/{memberId}/member-link-account")
    @Authorize(role = AccessRole.MEMBER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberLinkAccount(@PathVariable("memberId") Long memberId) {
        return success(opsMemberService.getLinkAccountInfo(memberId));
    }
}