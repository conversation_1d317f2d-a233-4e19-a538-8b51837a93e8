package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import org.springframework.data.domain.Page;

public interface OpsGiftCardTypeService {
    Page<GiftCardTypeRes> filter(Integer businessId, Integer programId, String code, Integer offset, Integer limit);

    GiftCardTypeRes get(Integer id);

    void add(GiftCardTypeCreateReq giftCardTypeCreateReq);

    void update(Integer id, GiftCardTypeUpdateReq giftCardTypeUpdateReq);
}
