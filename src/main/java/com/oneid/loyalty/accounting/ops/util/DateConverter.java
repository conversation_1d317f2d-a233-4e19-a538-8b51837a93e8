package com.oneid.loyalty.accounting.ops.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateConverter {
    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("dd/MM/yyyy");

    private static final SimpleDateFormat FORMATTER_2 = new SimpleDateFormat("dd-MMM-yyyy");

    private static final SimpleDateFormat FORMATTER_3 = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss");

    private static final SimpleDateFormat FORMATTER_OUT = new SimpleDateFormat("yyyyMMdd");

    // Convert yyyyMMdd to dd/MM/yyyy
    public static String toDDMMYYYY(String src) {
        if (src == null) {
            return null;
        }
        try {
            Date date = FORMATTER_OUT.parse(src);
            return FORMATTER.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    // Convert sting dd-MMM-yyyy to Date
    public static Date toDate(String src) {
        if (src == null) {
            return null;
        }
        try {
            return FORMATTER_2.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }

    // Convert dd-MMM-yyyy HH:mm:ss to Date
    public static Date toDateTime(String src) {
        if (src == null) {
            return null;
        }
        try {
            return FORMATTER_3.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }
}