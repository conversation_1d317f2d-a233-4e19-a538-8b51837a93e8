package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@EqualsAndHashCode
public class AddCardReq {
    @NotNull(message = "Business code not null")
	@JsonProperty("business_code")
	private String businessCode;

    @NotNull(message = "Program code not null")
    @JsonProperty("program_code")
    private String programCode;

    @NotNull(message = "Member code not null")
    @JsonProperty("member_code")
    private String memberCode;

    @NotNull(message = "Card type id not null")
    @JsonProperty("card_type_id")
    private Integer cardTypeId;

    @NotNull(message = "Store id not null")
    @JsonProperty("store_code")
    private String storeCode;
    
    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("card_no")
    @NotNull(message = "Card no not null")
    @NotBlank(message = "Card no nt empty")
    private String cardNo;

    @JsonProperty("member_id")
    // @NotNull(message = "Member not null")
    private String memberId;
    
    @JsonProperty("status")
    @Pattern(regexp = "^(A|I|P)?$", message = "'Status' Only accept A/I/P values")
    private String status;
    
    @JsonProperty("card_status")
    @Pattern(regexp = "^(A|I|P)?$", message = "'Status' Only accept A/I/P values")
    private String cardStatus;
    
    @JsonProperty("user_id")
    private String userId;
}
