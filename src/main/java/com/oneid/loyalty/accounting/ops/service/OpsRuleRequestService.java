package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.oneloyalty.common.constant.EServiceType;

import java.util.List;

public interface OpsRuleRequestService {

    void validateRules(Integer programId, EServiceType serviceType, List<RuleReq> rulesReq);

    void createRuleRequest(Integer requestId, Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs);

    List<ResetRuleReq> cuRules(Integer programId, EServiceType serviceType, String serviceCode, List<RuleReq> ruleReqs);

    void editRuleRequest(Integer requestId, Integer newRequestId, Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs);

    List<RuleRes> getRuleRequests(Integer requestId, Integer programId, EServiceType serviceType);

    List<RuleRes> getRules(Integer programId, EServiceType serviceType, String serviceCode);
}