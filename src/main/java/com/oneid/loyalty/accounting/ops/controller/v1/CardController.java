package com.oneid.loyalty.accounting.ops.controller.v1;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateCardReq;
import com.oneid.loyalty.accounting.ops.model.res.CardRes;
import com.oneid.loyalty.accounting.ops.model.res.CardTempRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardService;
import com.oneid.loyalty.accounting.ops.service.OpsCardTmpService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/cards")
public class CardController extends BaseController {

    @Autowired
    OpsCardService opsCardService;

    @Autowired
    OpsCardTmpService opsCardTmpService;

    @GetMapping("")
    @Authorize(role = AccessRole.MEMBER_CARD, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filterCardMember(@RequestParam(name = "card_type_id", required = false) Integer cardType,
                                              @RequestParam(name = "card_no", required = false) String cardNo,
                                              @RequestParam(name = "business_code", required = true) String businessCode,
                                              @RequestParam(name = "program_code", required = true) String programCode,
                                              @RequestParam(name = "store_code", required = true) String storeCode,
                                              @RequestParam(name = "member_code", required = false) String memberCode,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<CardRes> page = opsCardService.filter(cardType, cardNo, businessCode, programCode, storeCode, memberCode, offset, limit);
        return success(page, offset, limit);
    }

    @PostMapping("/add-card")
    @Authorize(role = AccessRole.MEMBER_CARD, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> addCard(
            Authentication authentication,
            @RequestBody @Valid AddCardReq request) {
        return success(opsCardService.addCard((OPSAuthenticatedPrincipal) authentication.getPrincipal(), request));
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCard(@PathVariable(value = "id") Integer id,
                                     @RequestParam(name = "business_code", required = true) String businessCode,
                                     @RequestParam(name = "program_code", required = true) String programCode) {
        CardRes result = opsCardService.getDetail(id, businessCode, programCode);
        return success(result);
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.MEMBER_CARD, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCardById(
            Authentication authentication,
            @PathVariable(value = "id") Integer id,
            @Valid @RequestBody UpdateCardReq card) {
        return success(opsCardService.update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), id, null, card));
    }

    @PutMapping("/card_no/{card_no}")
    @Authorize(role = AccessRole.MEMBER_CARD, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCard(
            Authentication authentication,
            @PathVariable(value = "card_no") String cardNo,
            @Valid @RequestBody UpdateCardReq card) {
        return success(opsCardService.update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), null, cardNo, card));
    }

    @GetMapping("availability")
    public ResponseEntity<?> searchCardAvailability(
            @RequestParam("business_id") Integer businessId,
            @RequestParam("program_id") Integer programId,
            @RequestParam("store_id") Integer storeId,
            @RequestParam("batch_no") Integer batchNo,
            @RequestParam(value = "card_status", defaultValue = "P") ECardStatus status,
            @RequestParam(value = "card_no", required = false) String cardNo,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "20") int limit) {

        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit);

        Page<CardTempRes> cardTempResPage = opsCardTmpService
                .filter(businessId, programId, storeId, batchNo, status, cardNo, pageRequest);
        return success(cardTempResPage.getContent(), offset, limit, (int) cardTempResPage.getTotalElements());
    }
}