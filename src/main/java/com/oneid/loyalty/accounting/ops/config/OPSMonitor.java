package com.oneid.loyalty.accounting.ops.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.UUID;

/**
 * author viet.le2
 */
@Aspect
@Configuration
@Slf4j
public class OPSMonitor {

    @Autowired
    private ObjectMapper jsonMapper;

//    @Around("within(com.oneid.loyalty.accounting.ops.controller..*)")
//    public Object log(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
//        long time = System.currentTimeMillis();
//        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
//        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
//        String requestId = request.getHeader(OPSConstant.KEY_X_REQUEST_ID);
//        if (Objects.isNull(requestId)) {
//            requestId = UUID.randomUUID().toString();
//        }
//
//        MDC.put(OPSConstant.REQUEST_ID, requestId);
//        String requestUrl = getFullRequest(request.getServletPath(), request.getQueryString());
//        String requestMethod = Objects.requireNonNull(request.getMethod());
//        String body = getBody(requestMethod, proceedingJoinPoint);
//        Log.info(
//                LogData.createLogData()
//                        .append(OPSConstant.MSG, OPSConstant.REQUEST)
//                        .append(OPSConstant.REQUEST_URL, requestUrl)
//                        .append(OPSConstant.REQUEST_METHOD, requestMethod)
//                        .append(OPSConstant.REQUEST_BODY, body)
//        );
//
//        // MAIN PROCESS
//        Object res = proceedingJoinPoint.proceed();
//
//        long took = System.currentTimeMillis() - time;
//        Meta metaRes = null;
//        if (res instanceof ResponseEntity<?>) {
//            Object obj = ((ResponseEntity<?>) res).getBody();
//            if (Objects.nonNull(obj)) {
//                metaRes = ((BaseResponseData) obj).getMeta();
//            }
//        }
//        Log.info(
//                LogData.createLogData()
//                        .append(OPSConstant.MSG, OPSConstant.RESPONSE)
//                        .append(OPSConstant.REQUEST_URL, requestUrl)
//                        .append(OPSConstant.REQUEST_METHOD, requestMethod)
//                        .append(OPSConstant.RESPONSE_META, metaRes)
//                        .append(OPSConstant.RESPONSE_TIME, took));
//        MDC.remove(OPSConstant.REQUEST_ID);
//        return res;
//    }

    public String getBody(String reqMethod, JoinPoint joinPoint) {
        String body = StringUtils.EMPTY;
        if (!"GET".equals(reqMethod)) {
            try {
                body = jsonMapper.writeValueAsString(joinPoint.getArgs());
            } catch (Exception ex) {
                Log.error(ex.getMessage(), ex);
            }
        }
        return body;
    }

    public String getFullRequest(String servletPath, String queryString) {
        if (StringUtils.isBlank(queryString)) return servletPath;
        return servletPath + "?" + queryString;
    }
}
