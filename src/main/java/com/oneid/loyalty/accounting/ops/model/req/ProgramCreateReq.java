package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ProgramCreateReq implements Serializable {
    private Integer id;
    private String logoUrl;
    private Integer programId;
    private String programCode;
    private String programName;
    private Integer businessId;
    private String businessCode;
    private String businessName;
    private String description;
    private String descriptionEn;
    private String website;
    private String hotline;
    private Integer programRef;
    private String status;
    private Long createdAt;
    private String createdBy;
    private Long updatedAt;
    private String updatedBy;
}
