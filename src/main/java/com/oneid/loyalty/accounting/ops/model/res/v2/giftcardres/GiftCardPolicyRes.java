package com.oneid.loyalty.accounting.ops.model.res.v2.giftcardres;

import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GiftCardPolicyRes extends ShortEntityRes {
    private static final long serialVersionUID = 3981571366514636060L;

    private String qrUrl;

    public GiftCardPolicyRes(CardPolicy policy){
        super(policy.getId(), policy.getName(), policy.getCode());
        this.qrUrl = policy.getQrUrl();
    }

}
