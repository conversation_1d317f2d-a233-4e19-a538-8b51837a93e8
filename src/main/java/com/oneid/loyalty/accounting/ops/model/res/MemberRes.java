package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Member;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MemberRes {
    private Long id;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("first_name")
    private String firstName;

    @JsonProperty("mid_name")
    private String midName;

    @JsonProperty("last_name")
    private String lastName;

    @JsonProperty("full_name")
    private String fullName;

    @JsonProperty("identify_type")
    private String identifyType;

    @JsonProperty("identify_no")
    private String identifyNo;

    @JsonProperty("gender")
    private String gender;

    private String job;

    @JsonProperty("dob")
    private String dob;

    @JsonProperty("email")
    private String email;

    @JsonProperty("ref_code")
    private String refCode;

    @JsonProperty("status")
    private String status;

    private String houseNumber;

    private String street;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("tier_id")
    private Integer tierId;

    @JsonProperty("program_id")
    private Integer programId;

    public static MemberRes valueOf(Member member) {
        MemberRes result = new MemberRes();
        result.setId(member.getId());
        result.setMemberCode(member.getMemberCode());
        result.setPhoneNo(member.getPhoneNo());
        result.setFirstName(member.getFirstName());
        result.setMidName(member.getMidName());
        result.setLastName(member.getLastName());
        result.setFullName(member.getFullName());
        result.setEmail(member.getEmail());
        result.setGender(member.getGender());
        result.setJob(member.getJob());
        result.setDob(member.getDob());
        //result.setIdentifyType("???");
        result.setIdentifyNo(member.getIdentifyNo());
        // Application Date
        result.setHouseNumber(member.getHouseNumber());
        result.setStreet(member.getStreet());
//        result.setWardId(member.getWardId());
//        result.setDistrictId(member.getDistrictId());
//        result.setProvinceId(member.getProvinceId());
        //result.setRefCode(member.get);
        result.setStatus(member.getStatus());
        result.setTierId(member.getTierId());
        result.setProgramId(member.getProgramId());

        return result;
    }
}
