package com.oneid.loyalty.accounting.ops.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

import com.oneid.loyalty.accounting.ops.model.req.CreateStoreReq;
import com.oneid.loyalty.accounting.ops.model.req.StoreUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.StoreEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.StoreRes;
import com.oneid.oneloyalty.common.entity.Store;

public interface OpsStoreService {
    Page<StoreRes> filter(Integer businessId, Integer corporationId, Integer chainId, String storeName, String storeCode, String status, Integer offset, Integer limit);

    StoreRes get(Integer id);

    List<StoreEnumAll> getEnumAll();

    void update(Integer id, StoreUpdateReq storeUpdateReq);

    Map<Integer, Store> getMapById(Collection<Integer> ids);
    
    StoreRes create(CreateStoreReq req);

}
