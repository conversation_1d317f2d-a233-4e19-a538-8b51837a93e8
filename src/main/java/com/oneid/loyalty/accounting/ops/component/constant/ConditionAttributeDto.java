package com.oneid.loyalty.accounting.ops.component.constant;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
public class ConditionAttributeDto {

    @JsonProperty("attribute")
    private String attribute;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("operators")
    private List<String> operators;

    @JsonProperty("data_type")
    private String dataType;

    @JsonProperty("data_type_display")
    private EAttributeDataDisplayType dataTypeDisplay;

    @JsonProperty("resource_path")
    private String resourcePath;

    @JsonProperty("support_filter")
    private Boolean supportFilter;

    @JsonProperty("value_validation_pattern")
    private String valueValidationPattern;

    @JsonProperty("exist_master_data")
    private boolean existMasterData;

    @JsonProperty("enable_validate_master_data")
    private Boolean enableValidateMasterData;

    @JsonProperty("attribute_type")
    private String attributeType;
}