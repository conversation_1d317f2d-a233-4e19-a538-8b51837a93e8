package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.EpochTimeSerialize;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CardTempRes {
    private Integer id;
    private String cardNo;
    private long batchNo;
    @JsonSerialize(converter = EpochTimeSerialize.class)
    private Date issueDate;
    private String nameOnCard;
    private String description;
    private ECardStatus cardStatus;
    private ShortEntityRes cardType;
    private ECommonStatus status;
    private Boolean isTransferring;
}
