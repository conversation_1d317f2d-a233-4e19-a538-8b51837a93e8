package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyEnum;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRes;
import com.oneid.oneloyalty.common.entity.Currency;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OpsCurrencyService {
    List<CurrencyEnum> getEnum(Integer businessId);

    List<CurrencyEnum> getCurrenciesForTransaction(Integer businessId);

    Page<CurrencyRes> filterCurrency(CurrencySearchReq currencySearchReq, Pageable pageRequest);

    CurrencyRes getCurrency(Integer id);

    void addCurrency(CurrencyCreateReq currencyCreateReq);

    void updateCurrency(Integer id, CurrencyUpdateReq currencyData);

    Map<Integer, Currency> getMapById(Collection<Integer> ids);

}
