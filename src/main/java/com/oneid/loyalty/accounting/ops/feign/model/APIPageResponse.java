package com.oneid.loyalty.accounting.ops.feign.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.oneid.loyalty.accounting.ops.support.feign.FeignResponse;
import com.oneid.oneloyalty.common.model.MetaList;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(value = Include.NON_NULL)
public class APIPageResponse<T> implements FeignResponse {
    private List<T> data;
    private MetaList meta;
}
