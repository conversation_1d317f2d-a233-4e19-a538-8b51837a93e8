package com.oneid.loyalty.accounting.ops.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ETransactionTemplateType {

    EARN_BURN_SALE("EARN_BURN_SALE", "Template_TransactionRequest_Earn_Burn_Sale"),
    ADJUST("ADJUST", "Template_TransactionRequest_Adjust"),
    REVERT_FULL("REVERT_FULL", "Template_TransactionRequest_Revert_Full"),
    REVERT_PARTIAL("REVERT_PARTIAL", "Template_TransactionRequest_Revert_Partial");

    private String value;
    private String fileName;

    public static ETransactionTemplateType lookup(String value) {
        if (StringUtils.isEmpty(value)) return null;
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETransactionTemplateType> {
        @Override
        public ETransactionTemplateType convert(String value) {
            return ETransactionTemplateType.lookup(value);
        }
    }
}
