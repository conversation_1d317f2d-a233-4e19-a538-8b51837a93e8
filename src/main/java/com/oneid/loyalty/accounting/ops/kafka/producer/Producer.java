package com.oneid.loyalty.accounting.ops.kafka.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.AppConfigParam;
import com.oneid.loyalty.accounting.ops.kafka.event.CommonEventData;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.oneloyalty.common.constant.AppConstant;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
public class Producer {

    @Autowired
    private AppConfigParam appConfigParam;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${kafka.topic.name.common-events}")
    private String topic;

    @Value("${kafka.reset-rule.topic}")
    private String resetRuleTopic;

    @Value("${kafka.produce-enable:true}")
    private boolean kafkaEnableProducer;

    @Autowired
    ObjectMapper objectMapper;

    @Async("backgroundProcessThreadPool")
    public void send(CommonEventData<?> data) {
        try {
            String body = objectMapper.writeValueAsString(data);
            if (!AppConstant.APP_ENV_PROD.equals(appConfigParam.applicationMode)) {
                LogData logData = LogData.createLogData()
                        .append("msg", "Kafka produce message")
                        .append("topic", topic)
                        .append("event", body);
                Log.info(logData);
            }
            if (kafkaEnableProducer) {
                kafkaTemplate.send(topic, body);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @TransactionalEventListener
    public void sendResetRule(ResetRuleEvent<?> event) {
        try {
            String body = objectMapper.writeValueAsString(event);
            if (!AppConstant.APP_ENV_PROD.equals(appConfigParam.applicationMode)) {
                LogData logData = LogData.createLogData()
                        .append("msg", "Kafka produce message")
                        .append("topic", resetRuleTopic)
                        .append("event", body);
                Log.info(logData);
            }
            if (kafkaEnableProducer) {
                kafkaTemplate.send(resetRuleTopic, body);
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }
}