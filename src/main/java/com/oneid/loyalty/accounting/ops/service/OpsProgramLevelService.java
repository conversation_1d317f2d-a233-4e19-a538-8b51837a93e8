package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

public interface OpsProgramLevelService {
    Page<ProgramLevel> search(Integer businessId,
                              Integer programId,
                              String level,
                              Pageable pageable);

    @Transactional
    void approval(ApprovalReq req);
}
