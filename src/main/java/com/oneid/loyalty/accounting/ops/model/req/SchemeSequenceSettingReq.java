package com.oneid.loyalty.accounting.ops.model.req;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = SchemeSequenceSettingReq.SchemeSequenceSettingReqBuilder.class)
public class SchemeSequenceSettingReq {
    @Valid
    @NotNull(message = "'scheme_sequences' must not be null")
    private List<SchemeSequenceReq> schemeSequences;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class SchemeSequenceSettingReqBuilder {
    }
}
