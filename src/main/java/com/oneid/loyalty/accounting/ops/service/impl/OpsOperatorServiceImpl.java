package com.oneid.loyalty.accounting.ops.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.oneid.loyalty.accounting.ops.model.res.*;
import com.oneid.oneloyalty.common.constant.EBoolean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeOperatorReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateChangeRequestReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.SearchChangeRequestsRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.PayloadOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.RejectOperatorRequest;
import com.oneid.loyalty.accounting.ops.model.req.RejectReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchOperatorReq;
import com.oneid.loyalty.accounting.ops.service.OpsOperatorService;
import com.oneid.loyalty.accounting.ops.util.TripleDESUtil;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.OperatorProfile;
import com.oneid.oneloyalty.common.entity.OperatorRequest;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.OperatorProfileRepository;
import com.oneid.oneloyalty.common.repository.OperatorRequestRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;

import lombok.SneakyThrows;

@Service
public class OpsOperatorServiceImpl implements OpsOperatorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpsOperatorServiceImpl.class);

    @Value("${maker-checker.module.operator:operator}")
    public String moduleId;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private CorporationRepository corporationRepository;

    @Autowired
    private ChainRepository chainRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private PosRepository posRepository;

    @Autowired
    private OperatorRequestRepository operatorRequestRepository;

    @Autowired
    private OperatorProfileRepository operatorProfileRepository;

    @Autowired
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @Autowired
    private MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @SneakyThrows
    public OperatorGetApproveRes getApprove(CreateOperatorReq request) {
        if(request.getStatus() != ECommonStatus.INACTIVE && request.getStatus() != ECommonStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Cannot create operator with status not in [active, in-active]", request);
        }
        boolean operatorIdExisted = operatorProfileRepository.findByBusinessIdAndOperatorId(
                request.getBusinessId(), request.getOperatorId()).isPresent();

        if (operatorIdExisted) {
            throw new BusinessException(ErrorCode.OPERATOR_ID_EXISTED, "Operator id existed",
                    LogData.createLogData().append("operator_id", request.getOperatorId()));
        }

        Business business = businessService.findActive(request.getBusinessId());

        buildPayload(business.getId(), request.getPayload(), true);

        if (request.getPinCode() == null || request.getPinCode().trim().isEmpty()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Pin must not be blank", null, null);
        }
        String pinCode = TripleDESUtil.encryptByGivenKey(request.getOperatorId(), request.getPinCode());

        OperatorRequest operatorRequest = saveToDatabaseFromRequest(request, pinCode, 1, EBoolean.NO);

        CreateChangeRequestReq<ChangeOperatorReq> requestToMC = CreateChangeRequestReq
                .<ChangeOperatorReq>builder()
                .actionType(makerCheckerConfigParam.ACTION_TYPE_CREATE)
                .module(makerCheckerConfigParam.OPERATOR_MODULE)
                .objectId(String.valueOf(operatorRequest.getId()))
                .payload(ChangeOperatorReq.builder().requestId(operatorRequest.getId()).build())
                .build();

        try {
            makerCheckerFeignClient.createRequestOperator(requestToMC);
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.REQUEST_FAILED_NEED_RETRY,
                    "Call maker checker service error: " + e.getMessage(),
                    LogData.createLogData().append("request", requestToMC));
        }

        return OperatorGetApproveRes.builder().requestId(operatorRequest.getId()).build();
    }

    @Override
    public OperatorGetApproveRes getApproveForUpdate(final CreateOperatorReq request) {
        if(request.getStatus() != ECommonStatus.INACTIVE && request.getStatus() != ECommonStatus.ACTIVE) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Cannot update operator with status not in [active, in-active]", request);
        }
        OperatorRequest effectedOperatorRequest = operatorRequestRepository.findEffectedVersion(request.getBusinessId(), request.getOperatorId())
                .orElseThrow(() -> new BusinessException(ErrorCode.OPERATOR_NOT_FOUND, "Operator not found",
                        LogData.createLogData().append("request", request)));

        if (!request.getBusinessId().equals(effectedOperatorRequest.getBusinessId())) {
            throw new BusinessException(ErrorCode.CANNOT_UPDATE, "Cannot update business",
                    LogData.createLogData().append("request", request));
        }

        boolean operatorExistedPending = !operatorRequestRepository.findByOperatorIdAndBusinessIdAndApprovalStatus(
                request.getOperatorId(), request.getBusinessId(), EApprovalStatus.PENDING
        ).isEmpty();

        if (operatorExistedPending) {
            throw new BusinessException(ErrorCode.OPERATOR_IS_WAITING_FOR_APPROVE, "Operator waiting for approve",
                    LogData.createLogData().append("request", request));
        }

        if (effectedOperatorRequest.getLegacy() != EBoolean.YES) {
            buildPayload(request.getBusinessId(), request.getPayload(), true);
        }

        OperatorRequest operatorRequest = saveToDatabaseFromRequest(request, effectedOperatorRequest.getPinCode(),
                (effectedOperatorRequest.getVersion() != null ? effectedOperatorRequest.getVersion() : 0) + 1, effectedOperatorRequest.getLegacy());

        operatorRequest = operatorRequestRepository.save(operatorRequest);

        CreateChangeRequestReq<ChangeOperatorReq> requestToMC = CreateChangeRequestReq
                .<ChangeOperatorReq>builder()
                .actionType(makerCheckerConfigParam.ACTION_TYPE_UPDATE)
                .module(makerCheckerConfigParam.OPERATOR_MODULE)
                .objectId(String.valueOf(operatorRequest.getId()))
                .payload(ChangeOperatorReq.builder().requestId(operatorRequest.getId()).build())
                .params(Collections.singletonMap("operator_request_id", String.valueOf(operatorRequest.getId())))
                .build();

        try {
            makerCheckerFeignClient.createRequestOperator(requestToMC);
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.REQUEST_FAILED_NEED_RETRY,
                    "Call maker checker service error: " + e.getMessage(),
                    LogData.createLogData().append("request", requestToMC));
        }

        return OperatorGetApproveRes.builder().requestId(operatorRequest.getId()).build();
    }

    @Override
    public void getReject(RejectOperatorRequest request) {
        Long requestIdFromMakerChecker = getRequestIdFromMakerChecker(request.getRequestId());
        makerCheckerFeignClient.reject(requestIdFromMakerChecker,
                RejectReq.builder()
                        .reason(request.getReason())
                        .build()
        );
    }

    @Override
    public OperatorDetailRes getDetail(Integer requestId) {
        OperatorRequest operatorRequest = operatorRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.OPERATOR_NOT_FOUND, "Operator request not found",
                        LogData.createLogData().append("operator request id", requestId)));

        Business business = businessService.find(operatorRequest.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found",
                        LogData.createLogData().append("Business id", operatorRequest.getBusinessId())));

        OperatorDetailRes.LegacyPayload legacyPayload;

        if (operatorRequest.getLegacy() == EBoolean.YES && operatorRequest.getApprovalStatus() == EApprovalStatus.APPROVED) {
            OperatorProfile operatorProfile = operatorProfileRepository.findByBusinessIdAndOperatorId(
                            operatorRequest.getBusinessId(), operatorRequest.getOperatorId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.OPERATOR_NOT_FOUND, "Operator request not found",
                            LogData.createLogData().append("operator request id", requestId)));
            legacyPayload = OperatorDetailRes.LegacyPayload.builder()
                    .operatorCorps(operatorProfile.getCorporationId())
                    .operatorChains(operatorProfile.getChainId())
                    .operatorStores(operatorProfile.getStoreId())
                    .operatorTerminals(operatorProfile.getPosId())
                    .build();
        } else {
            legacyPayload = operatorRequest.getLegacy() == EBoolean.YES ?
                            objectMapper.convertValue(operatorRequest.getRequestPayload(),
                                    OperatorDetailRes.LegacyPayload.class): null;
        }

        return OperatorDetailRes.builder()
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .payload(
                        operatorRequest.getLegacy() == EBoolean.NO ? buildPayload(operatorRequest.getBusinessId(),
                                objectMapper.convertValue(operatorRequest.getRequestPayload(),
                                        PayloadOperatorReq.class), false) : null
                )
                .id(operatorRequest.getId())
                .operatorId(operatorRequest.getOperatorId())
                .approvedAt(operatorRequest.getApprovedAt())
                .approvedBy(operatorRequest.getApprovedBy())
                .createdAt(operatorRequest.getCreatedAt())
                .createdBy(operatorRequest.getCreatedBy())
                .updatedAt(operatorRequest.getUpdatedAt())
                .updatedBy(operatorRequest.getUpdatedBy())
                .status(operatorRequest.getInitialStatusOperatorProfile())
                .approvalStatus(operatorRequest.getApprovalStatus())
                .legacy(operatorRequest.getLegacy())
                .legacyPayload(legacyPayload)
                .build();
    }

    @Override
    public Page<OperatorRequestRes> filter(SearchOperatorReq request, Pageable pageable) {
        Page<OperatorRequest> page;
        if (request.getApprovalStatus() == EApprovalStatus.APPROVED) {
            return getAvailableOperatorRequests(
                    request.getBusinessId(),
                    request.getCorporationId(),
                    request.getChainId(),
                    request.getStoreId(),
                    request.getTerminalId(),
                    request.getOperatorId(),
                    request.getApprovalStatus(),
                    request.getStatus(),
                    pageable);
        }

        String statusMC = request.getApprovalStatus() == EApprovalStatus.PENDING ? "PENDING" : "REJECTED";
        SearchChangeRequestsRes<ChangeOperatorReq> response = makerCheckerFeignClient
                .getChangeRequestOperators(
                        moduleId,
                        null,
                        statusMC,
                        pageable.getPageNumber() + 1,
                        pageable.getPageSize(), null, null)
                .getData();

        List<SearchChangeRequestsRes.RecordChangeRequestRes<ChangeOperatorReq>> changeRequests = response.getRecords();
        pageable = new OffsetBasedPageRequest(0, pageable.getPageSize(),
                Sort.by(Sort.Direction.DESC, "id"));
        page = operatorRequestRepository.findByIdInAndApprovalStatus(
                changeRequests.stream()
                        .map(iter -> iter.getPayload().getRequestId()).collect(Collectors.toList()),
                request.getApprovalStatus(), pageable
        );
        page = new PageImpl<>(page.getContent(), pageable, response.getTotalRecordCount());

        final Map<Integer, ShortEntityRes> shortEntityResMap = businessRepository.findAll()
                .stream()
                .map(iter -> new ShortEntityRes(iter.getId(), iter.getName(), iter.getCode()))
                .collect(Collectors.toMap(ShortEntityRes::getId, s -> s));
        return new PageImpl<>(
                page.getContent()
                        .stream()
                        .map(iter ->
                                OperatorRequestRes
                                        .builder()
                                        .id(iter.getId())
                                        .operatorId(iter.getOperatorId())
                                        .business(shortEntityResMap.get(iter.getBusinessId()))
                                        .approvalStatus(iter.getApprovalStatus())
                                        .payload(
                                                iter.getLegacy() == EBoolean.NO ? buildPayload(iter.getBusinessId(),
                                                        objectMapper.convertValue(iter.getRequestPayload(),
                                                                PayloadOperatorReq.class), false) : null)
                                        .legacyPayload(iter.getLegacy() == EBoolean.YES ? objectMapper
                                                .convertValue(iter.getRequestPayload(), OperatorLegacyPayload.class) : null
                                        )
                                        .status(iter.getInitialStatusOperatorProfile())
                                        .legacy(iter.getLegacy())
                                        .build())
                        .sorted((o1, o2) -> o2.getId().compareTo(o1.getId())) // order by id desc
                        .collect(Collectors.toList()),
                PageRequest.of(response.getPage() - 1, response.getPageSize()), page.getTotalElements()
        );
    }

    @Override
    public void approve(Integer requestId) {
        Long makerCheckerRequestId = getRequestIdFromMakerChecker(requestId);
        makerCheckerFeignClient.approve(makerCheckerRequestId);
    }

    private Long getRequestIdFromMakerChecker(Integer myRequestId) {
        APIResponse<SearchChangeRequestsRes<ChangeOperatorReq>> changeRequests = makerCheckerFeignClient
                .getChangeRequestOperators(
                        makerCheckerConfigParam.OPERATOR_MODULE,
                        Long.valueOf(myRequestId), null, null, null, null, null);

        if (changeRequests.getData().getRecords().size() == 0) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Request id not found",
                    LogData.createLogData().append("request_id", myRequestId));
        }
        if (changeRequests.getData().getRecords().size() == 2) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_IS_DUPLICATED, "Conflict data request, object_id is duplicate",
                    LogData.createLogData().append("request_id", myRequestId));
        }
        return changeRequests.getData().getRecords().get(0).getId();
    }

    private PayloadOperatorRes buildPayload(Integer businessId, PayloadOperatorReq payload, final boolean selectActive) {

        if (payload.getOperatorCorps() != null) {
            Map<Integer, Corporation> mapCorporations = corporationRepository
                    .findByBusinessIdAndIdIn(businessId,
                            payload.getOperatorCorps().stream()
                                    .map(PayloadOperatorReq.OperatorCorp::getId)
                                    .collect(Collectors.toList()))
                    .stream()
                    .filter(corporation -> !selectActive || corporation.getStatus() == ECommonStatus.ACTIVE)
                    .collect(Collectors.toMap(
                            Corporation::getId, c -> c
                    ));

            if (mapCorporations.size() != payload.getOperatorCorps().size()) {
                throw new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "corporation not found",
                        LogData.createLogData().append("payload", payload));
            }
            List<PayloadOperatorRes.OperatorCorp> operatorCorps = new LinkedList<>();
            for (PayloadOperatorReq.OperatorCorp corp : payload.getOperatorCorps()) {
                Corporation entity = mapCorporations.get(corp.getId());
                operatorCorps.add(
                        PayloadOperatorRes.OperatorCorp
                                .builder()
                                .operatorChains(buildChainFromPayload(mapCorporations.get(corp.getId()), corp, selectActive))
                                .value(new ShortEntityRes(entity.getId(), entity.getName(), entity.getCode()))
                                .build()
                );
            }
            return PayloadOperatorRes
                    .builder()
                    .operatorCorps(operatorCorps)
                    .build();
        }
        return null;
    }

    private List<PayloadOperatorRes.OperatorCorp.OperatorChain> buildChainFromPayload(
            Corporation corporation,
            PayloadOperatorReq.OperatorCorp operatorCorp,
            final boolean selectActive) {

        if (operatorCorp.getOperatorChains() != null) {
            Map<Integer, Chain> mapChains = chainRepository
                    .findByCorporationIdAndIdIn(corporation.getId(),
                            operatorCorp.getOperatorChains().stream()
                                    .map(PayloadOperatorReq.OperatorCorp.OperatorChain::getId)
                                    .collect(Collectors.toList()))
                    .stream()
                    .filter(chain -> !selectActive || chain.getStatus() == ECommonStatus.ACTIVE)
                    .collect(Collectors.toMap(
                            Chain::getId, c -> c
                    ));

            if (mapChains.size() != operatorCorp.getOperatorChains().size()) {
                throw new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "chain not found",
                        LogData.createLogData().append("payload", operatorCorp));
            }
            List<PayloadOperatorRes.OperatorCorp.OperatorChain> operatorChains = new LinkedList<>();
            for (PayloadOperatorReq.OperatorCorp.OperatorChain chain : operatorCorp.getOperatorChains()) {
                Chain entity = mapChains.get(chain.getId());
                operatorChains.add(
                        PayloadOperatorRes.OperatorCorp.OperatorChain
                                .builder()
                                .operatorStores(buildStoreFromPayload(mapChains.get(chain.getId()), chain, selectActive))
                                .value(new ShortEntityRes(entity.getId(), entity.getName(), entity.getCode()))
                                .build());
            }
            return operatorChains;
        }
        return null;
    }

    private List<PayloadOperatorRes.OperatorCorp.OperatorChain.OperatorStore> buildStoreFromPayload(
            Chain chain,
            PayloadOperatorReq.OperatorCorp.OperatorChain operatorChain, final boolean selectActive) {

        if (operatorChain.getOperatorStores() != null) {
            Map<Integer, Store> mapStores = storeRepository
                    .findByChainIdAndIdIn(chain.getId(),
                            operatorChain.getOperatorStores().stream()
                                    .map(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore::getId)
                                    .collect(Collectors.toList()))
                    .stream()
                    .filter(store -> !selectActive || store.getStatus() == ECommonStatus.ACTIVE)
                    .collect(Collectors.toMap(
                            Store::getId, s -> s
                    ));

            if (mapStores.size() != operatorChain.getOperatorStores().size()) {
                throw new BusinessException(ErrorCode.STORE_NOT_FOUND, "store not found",
                        LogData.createLogData().append("payload", operatorChain));
            }
            List<PayloadOperatorRes.OperatorCorp.OperatorChain.OperatorStore> operatorStores = new LinkedList<>();
            for (PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore store : operatorChain.getOperatorStores()) {
                Store entity = mapStores.get(store.getId());
                operatorStores.add(PayloadOperatorRes.OperatorCorp.OperatorChain.OperatorStore
                        .builder()
                        .value(new ShortEntityRes(entity.getId(), entity.getName(), entity.getCode()))
                        .operatorTerminals(buildTerminalFromPayload(mapStores.get(store.getId()), store, selectActive))
                        .build());
            }
            return operatorStores;
        }
        return null;
    }

    private List<PayloadOperatorRes.OperatorCorp.OperatorChain.OperatorStore.OperatorTerminal> buildTerminalFromPayload(
            Store store,
            PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore operatorStore, final boolean selectActive) {

        if (operatorStore.getOperatorTerminals() != null) {
            List<Pos> posList = posRepository
                    .findByStoreIdAndIdIn(store.getId(),
                            operatorStore.getOperatorTerminals().stream()
                                    .map(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore.OperatorTerminal::getId)
                                    .collect(Collectors.toList()))
                    .stream()
                    .filter(s -> !selectActive || s.getStatus() == ECommonStatus.ACTIVE)
                    .collect(Collectors.toList());

            if (posList.size() != operatorStore.getOperatorTerminals().size()) {
                throw new BusinessException(ErrorCode.POS_NOT_FOUND, "pos not found",
                        LogData.createLogData().append("payload", operatorStore));
            }
            return posList.stream().map(
                    iter -> PayloadOperatorRes.OperatorCorp.OperatorChain.OperatorStore.OperatorTerminal
                            .builder()
                            .value(new ShortEntityRes(iter.getId(), iter.getName(), iter.getCode()))
                            .build()
            ).collect(Collectors.toList());
        }
        return null;
    }

    @SneakyThrows
    @Transactional
    public OperatorRequest saveToDatabaseFromRequest(CreateOperatorReq request, String pinCode, Integer version, EBoolean legacy) {

        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(request.getBusinessId());
        operatorRequest.setOperatorId(request.getOperatorId());
        if (legacy == EBoolean.YES) {
            operatorRequest.setRequestPayload(request.getLegacyPayload());
        } else {
            operatorRequest.setRequestPayload(request.getPayload());
        }
        operatorRequest.setLegacy(legacy);
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(request.getStatus());
        operatorRequest.setPinCode(pinCode);
        operatorRequest.setVersion(version);

        return operatorRequestRepository.save(operatorRequest);
    }

    @Override
    public Page<OperatorRequestRes> getAvailableOperatorRequests(
            Integer businessId,
            Integer corporationId,
            Integer chainId,
            Integer storeId,
            Integer terminalId,
            String operatorId,
            EApprovalStatus approvalStatus,
            ECommonStatus operatorStatus,
            Pageable pageable) {
        ECommonStatus requestStatus = null;

        if (approvalStatus != null) {
            switch (approvalStatus) {
                case APPROVED:
                    requestStatus = ECommonStatus.ACTIVE;

                    break;

                case REJECTED:
                    requestStatus = ECommonStatus.INACTIVE;

                    break;

                default:
                    requestStatus = ECommonStatus.PENDING;

                    break;
            }
        }

        Page<Object[]> page = operatorRequestRepository.filter(
                operatorId,
                businessId,
                corporationId,
                chainId,
                storeId,
                terminalId,
                null,
                operatorStatus,
                requestStatus,
                approvalStatus,
                null,
                null,
                Direction.DESC,
                null,
                pageable);

        return new PageImpl<>(transform(page.getContent()), pageable, page.getTotalElements());
    }

    private List<OperatorRequestRes> transform(List<Object[]> entities) {
        List<OperatorRequestRes> content = new ArrayList<>();
        Integer businessId;

        try {
            for (Object[] each : entities) {
                businessId = ((BigDecimal) each[8]).intValue();
                
                EBoolean legacy = each[7] != null ? EBoolean.of(Character.class.cast(each[7]).toString()) : EBoolean.NO;
                content.add(OperatorRequestRes.builder()
                        .id(((BigDecimal) each[0]).intValue())
                        .operatorId((String) each[1])
                        .business(new ShortEntityRes(businessId, (String) each[9], (String) each[10]))
                        .approvalStatus(EApprovalStatus.of(((Character) each[4]).toString()))
                        .status(ECommonStatus.of(((Character) each[5]).toString()))
                        .version(each[6] != null ? ((BigDecimal) each[6]).intValue() : null)
                        .payload(legacy == EBoolean.NO && each[2] != null ? buildPayload(businessId, objectMapper.readValue(each[2].toString(), PayloadOperatorReq.class), false) : null)
                        .legacyPayload(legacy == EBoolean.YES && each[2] != null ? objectMapper
                                .readValue(String.valueOf(each[2]), OperatorLegacyPayload.class) : null
                        )
                        .legacy(legacy)
                        .build());
            }
        } catch (Exception e) {
            LOGGER.error("{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.SERVER_ERROR, null, null);
        }

        return content;
    }
}
