package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
public class ConditionAttributeGroupInfo {

    @JsonProperty("group_name")
    private String groupName;

    @JsonProperty("attribute_list")
    private List<ConditionAttributeDto> attributeList;
}