package com.oneid.loyalty.accounting.ops.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramLevelReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramLevelReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramLevelInfo;
import com.oneid.loyalty.accounting.ops.service.OpsProgramLevelService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ProgramLevelService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.*;

@RestController
@RequestMapping("v1/program-level")
@Validated
public class ProgramLevelController extends BaseController {

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private OpsProgramLevelService opsProgramLevelService;

    @Autowired
    private ProgramLevelService programLevelService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ObjectMapper objectMapper;

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> search(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "sort_direction", required = false, defaultValue = "DESC") Sort.Direction sortDirection,
            @Valid @RequestParam(value = "sort_by", required = false, defaultValue = "id") String sortBy,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "level", required = false) String level
    ) {
        Sort sort = Sort.by(sortDirection, sortBy);
        Pageable pageable = PageRequest.of(offset / limit, limit, sort);

        Page<ProgramLevel> resPage = opsProgramLevelService.search(businessId, programId, level, pageable);
        List<ProgramLevelInfo> result = new ArrayList<>();
        resPage.getContent().stream().forEach(item -> {
            ProgramLevelInfo plInfo = ProgramLevelInfo.valueOf(item);
            this.setProgramAndBusiness(plInfo);
            result.add(plInfo);
        });
        return success(result, (int) pageable.getOffset(), pageable.getPageSize(), (int) resPage.getTotalElements());
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReview(
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus) {
        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.PROGRAM_LEVEL.getType(), null, approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : null);
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);
        if (ErrorCode.SUCCESS.getValue() != previewRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program level call in-review error: " + previewRes.getMeta().getMessage(),
                    null);
        }
        List<ProgramLevelInfo> result = new ArrayList<>();
        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            UpdateProgramLevelReq req = objectMapper.convertValue(item.getPayload(), UpdateProgramLevelReq.class);
            ProgramLevelInfo plInfo = ProgramLevelInfo.valueOf(item, req);
            this.setProgramAndBusiness(plInfo);
            result.add(plInfo);
        }
        return success(result, offset, limit, previewRes.getMeta().getTotal());
    }

    @PostMapping("/requests")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> create(@RequestBody @Valid CreateProgramLevelReq req) {
        programService.findActive(req.getProgramId());
        this.validateRank(req.getProgramId(), req.getRank(), null);
        MakerCheckerInternalMakerReq createFeignInternalReq = MakerCheckerInternalMakerReq
                .builder()
                .requestCode(UUID.randomUUID().toString())
                .requestType(EMakerCheckerType.PROGRAM_LEVEL.getType())
                .payload(req)
                .build();
        APIFeignInternalResponse previewProgramLevel = makerCheckerInternalFeignClient.maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != previewProgramLevel.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program level call maker create error: " + previewProgramLevel.getMeta().getMessage(),
                    null);
        }
        return success(previewProgramLevel.getData());
    }

    @PutMapping("/requests")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> update(@RequestBody @Valid UpdateProgramLevelReq req) {
        programService.findActive(req.getProgramId());
        this.validateRank(req.getProgramId(), req.getRank(), req.getId());
        ProgramLevel pl = programLevelService.findById(req.getId());
        if (pl == null) {
            throw new BusinessException(ErrorCode.PROGRAM_LEVEL_NOT_FOUND, "Program level not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
        if (Objects.isNull(pl.getRequestCode())) {
            throw new BusinessException(ErrorCode.REQUEST_CODE_NOT_FOUND, "request code not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), pl.getRequestCode(), pl.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_LEVEL.getType(), pl.getRequestCode());
        MakerCheckerInternalMakerReq createFeignInternalReq = MakerCheckerInternalMakerReq
                .builder()
                .requestCode(pl.getRequestCode())
                .requestType(EMakerCheckerType.PROGRAM_LEVEL.getType())
                .payload(req)
                .build();
        APIFeignInternalResponse previewProgramLevel = makerCheckerInternalFeignClient.maker(createFeignInternalReq);

        if (ErrorCode.SUCCESS.getValue() != previewProgramLevel.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program level call maker update error: " + previewProgramLevel.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
        return success(previewProgramLevel.getData());
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approval(@RequestBody @Valid ApprovalReq req) {
        opsProgramLevelService.approval(req);
        return success(null);
    }

    @GetMapping("/requests/in-review/{request_id}")
    @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewDetail(@PathVariable(value = "request_id") Long id) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewProgramLevel = makerCheckerInternalFeignClient.previewDetail(id);
        if (previewProgramLevel.getMeta().getCode() != 200
                || !EMakerCheckerType.PROGRAM_LEVEL.getType().equals(previewProgramLevel.getData().getRequestType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", id));
        }
        UpdateProgramLevelReq req = objectMapper.convertValue(previewProgramLevel.getData().getPayload(), UpdateProgramLevelReq.class);
        ProgramLevelInfo plInfo = ProgramLevelInfo.valueOf(previewProgramLevel.getData(), req);
        this.setProgramAndBusiness(plInfo);
        return success(plInfo);
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableDetail(@PathVariable(value = "id") Integer id) {
        ProgramLevel pl = programLevelService.findById(id);
        if (pl == null) {
            throw new BusinessException(ErrorCode.PROGRAM_LEVEL_NOT_FOUND, "Program level not found",
                    LogData.createLogData().append("id", id));
        }
        ProgramLevelInfo plInfo = ProgramLevelInfo.valueOf(pl);
        this.setProgramAndBusiness(plInfo);
        return success(plInfo);
    }

    @GetMapping("/requests/available/{id}/edit")
    @Authorize(role = AccessRole.PROGRAM_LEVEL, permissions = {AccessPermission.EDIT})
    public ResponseEntity<?> getEditAvailableDetail(@PathVariable(value = "id") Integer id) {
        ProgramLevel pl = programLevelService.findById(id);
        if (pl == null) {
            throw new BusinessException(ErrorCode.PROGRAM_LEVEL_NOT_FOUND, "Program level not found",
                    LogData.createLogData().append("id", id));
        }
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_LEVEL.getType(), pl.getRequestCode());
        ProgramLevelInfo plInfo = ProgramLevelInfo.valueOf(pl);
        String editKey = opsReqPendingValidator.generateEditKey(pl.getRequestCode(), pl.getVersion());
        plInfo.setEditKey(editKey);
        this.setProgramAndBusiness(plInfo);
        return success(plInfo);
    }


    private void setProgramAndBusiness(ProgramLevelInfo plInfo) {
        Optional<Program> pOptional = programService.find(plInfo.getProgramId());
        if (pOptional.isPresent()) {
            plInfo.withProgram(pOptional.get());
            Optional<Business> bOptional = businessService.find(pOptional.get().getBusinessId());
            if (bOptional.isPresent()) {
                plInfo.withBusiness(bOptional.get());
            }
        }
    }

    private void checkRequest(ProgramLevel pl) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_LEVEL.getType())
                .requestCode(pl.getRequestCode())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, 0, 1);
        if (previewRes.getMeta().getTotal() > 0) {
            throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED, null, null);
        }
    }

    private void validateRank(Integer programId, Integer rank, Integer plId) {
        List<ProgramLevel> programLevels = programLevelService.findAllByProgramId(programId);
        if (programLevels.stream().filter(item -> item.getRank().equals(rank) && !item.getId().equals(plId)).findFirst().isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_LEVEL_DUPLICATE_RANK, "Program level duplicate rank",
                    LogData.createLogData()
                            .append("programId", programId)
                            .append("rank", rank)
                            .append("plId", plId));
        }

        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_LEVEL.getType())
                .requestCode(null)
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, 0, 100);

        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            UpdateProgramLevelReq req = objectMapper.convertValue(item.getPayload(), UpdateProgramLevelReq.class);
            if (req.getProgramId().equals(programId) && req.getRank().equals(rank) && (req.getId() == null || !req.getId().equals(plId))) {
                throw new BusinessException(ErrorCode.PROGRAM_LEVEL_DUPLICATE_RANK, "Program level duplicate rank",
                        LogData.createLogData()
                                .append("programId", programId)
                                .append("rank", rank)
                                .append("plId", plId));
            }
        }
    }
}