package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.res.AccountBalanceRes;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberInquiryRes implements Serializable {

    private static final long serialVersionUID = -8626647094987454665L;

    private MemberProfile memberProfile;

    @JsonIgnore
    private MemberProfile memberProfileLink;

    private List<AccountBalanceRes> accountPoolBalances;


    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class MemberProfile {
        private Long id;

        private String businessId;

        private String programId;

        private String phoneNo;

        private String fullName;

        private String gender;

        private String identifyNo;

        private EIdentifyType identifyType;

        private String status;

        private String memberCode;

        private String memberStatus;

        private String address;

        private String dob;

        private String email;

        private String firstName;

        private String lastName;

        private String houseNumber;

        private String street;

        private String countryId;

        private String wardId;

        private String districtId;

        private String provinceId;

        private String homePhone;

        private String officePhone;

        private String referralCode;

        private Integer storeRegisterId;

        private String registrationDate;

        private String midName;

        private String job;

        private String level;

        private String masterUserId;

        private String userProfileId;

        private String partnerCustomerId;

        private Long profileLinkedAt;

        private String oneuLinkStatus;

        private String oneuSourceOfLink;

        private String token;

        @JsonIgnore
        private String linkPartnerCustomerId;

        @JsonIgnore
        private String linkPhoneNo;

        private Long oneuLinkStatusLastModified;
    }
}
