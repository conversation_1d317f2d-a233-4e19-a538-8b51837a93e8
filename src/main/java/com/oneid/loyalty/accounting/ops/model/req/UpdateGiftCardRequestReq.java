package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import lombok.Builder;
import lombok.Getter;

import javax.validation.constraints.NotNull;

@Getter
@Builder
@JsonDeserialize(builder = UpdateGiftCardRequestReq.UpdateGiftCardRequestReqBuilder.class)
public class UpdateGiftCardRequestReq {
    @NotNull(message = "'status' must not be null")
    private EGiftCardStatus status;

    @JsonPOJOBuilder(withPrefix = "")
    public static class UpdateGiftCardRequestReqBuilder{}
}
