package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifySchemeInfoReq;
import com.oneid.loyalty.accounting.ops.model.res.SchemeBaseRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeCombineRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeInReviewDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeInReviewRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.model.SchemeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime;

public interface OpsSchemeService {
    void verifySchemeInfo(VerifySchemeInfoReq req);

    void verifySchemeCombine(CreateSchemeReq req);

    Object getApproveCreate(CreateSchemeReq req);

    MakerCheckerInternalMakerRes getApproveUpdate(UpdateSchemeReq req);

    SchemeCombineRes getDetails(Integer schemeId);
    
    Page<SchemeBaseRes> getPage(Integer businessId, Integer programId, 
            ESchemeType type, String code, ECommonStatus status, 
            OffsetDateTime startDate, OffsetDateTime endDate,
            Integer corporationId,
            Integer chainId,
            Integer storeId,
            Integer terminalId,
            Pageable pageable
    );

    void approve(ApprovalReq req);

    Page<SchemeInReviewRes> getInReviewSchemeRequests(
            EApprovalStatus approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String reviewedBy,
            Integer offset,
            Integer limit
    );

    Page<SchemeDTO> getAvailableSchemeRequests(Integer businessId, Integer programId, String code, String name, ESchemeType type, ECommonStatus status, Pageable pageRequest);

    SchemeCombineRes getEdit(Integer requestId);

    SchemeInReviewDetailRes getInReviewSchemeRequestById(Integer requestId);
}
