package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramRes extends OtherInfo implements Serializable {

    private static final long serialVersionUID = 897838497631407147L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("program_name_en")
    private String programNameEn;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("description_en")
    private String descriptionEn;

    @Convert(converter = ECommonStatus.Converter.class)
    @JsonProperty("status")
    private ECommonStatus status;

    @JsonProperty("logo_url")
    private String logoUrl;

    @JsonProperty("auto_register")
    private String autoRegister;

    @JsonProperty("program_ref")
    private Integer programRef;

    @JsonProperty("program_ref_code")
    private String programRefCode;

    @JsonProperty("program_ref_name")
    private String programRefName;

    @JsonProperty("approval_status")
    private String approvalStatus;

    @JsonProperty("corporations_info")
    private Set<CorporationInfo> corporationsInfo;

    public static ProgramRes valueOf(Program loyaltyProgram) {
        ProgramRes programRes = new ProgramRes();
        programRes.setProgramId(loyaltyProgram.getId());
        programRes.setBusinessId(loyaltyProgram.getBusinessId());
        programRes.setDescription(loyaltyProgram.getDescription());
        programRes.setDescriptionEn(loyaltyProgram.getEnDescription());
        programRes.setProgramCode(loyaltyProgram.getCode());
        programRes.setProgramName(loyaltyProgram.getName());
        programRes.setProgramNameEn(loyaltyProgram.getEnName());
        programRes.setStatus(loyaltyProgram.getStatus());
        programRes.setLogoUrl(loyaltyProgram.getLogoUrl());
        programRes.setCreatedAt(loyaltyProgram.getCreatedAt());
        programRes.setAutoRegister(Objects.nonNull(loyaltyProgram.getAutoRegister()) ? loyaltyProgram.getAutoRegister().getValue() : null);
        programRes.setProgramRef(loyaltyProgram.getProgramRef());
        return programRes;
    }

    public static ProgramRes valueOfWithVersionDetails(Program program) {
        ProgramRes programRes = valueOf(program);
        programRes.setUpdatedAt(program.getUpdatedAt());
        programRes.setCreatedAt(program.getCreatedAt());
        programRes.setUpdatedBy(program.getUpdatedBy());
        programRes.setCreatedBy(program.getCreatedBy());

        return programRes;
    }

}