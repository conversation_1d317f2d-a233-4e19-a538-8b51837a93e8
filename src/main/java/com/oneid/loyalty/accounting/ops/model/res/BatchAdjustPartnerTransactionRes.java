package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BatchAdjustPartnerTransactionRes extends VersioningRes {
    private Integer requestId;
    private Integer reviewId;
    private String businessName;
    private String programName;
    private String corporationName;
    private String batchFileName;
    private String batchName;
    private String rejectedReason;
    
    @JsonSerialize(using =  DateSerializer.class)
    private Date createdAt;
    
    private EBatchProcessStatusType batchProcessStatus;
    private EApprovalStatus approvalStatus;
}
