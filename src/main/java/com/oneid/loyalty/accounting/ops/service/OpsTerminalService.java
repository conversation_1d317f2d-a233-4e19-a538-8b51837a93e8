package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.TerminalCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TerminalUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.StoreEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.StoreRes;
import com.oneid.loyalty.accounting.ops.model.res.TerminalEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.TerminalRes;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Pos;
import org.springframework.data.domain.Page;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface OpsTerminalService {
    Page<TerminalRes> filter(Integer businessId, Integer corporationId, Integer chainId, Integer storeId, String terminalName, String terminalCode, String status, Integer offset, Integer limit);

    TerminalRes get(Integer id);

    List<TerminalEnumAll> getEnumAll();

    void update(Integer id, TerminalUpdateReq terminalUpdateReq);

    Map<Integer, Pos> getMapById(Collection<Integer> ids);

    void add(TerminalCreateReq terminalCreateReq);
}
