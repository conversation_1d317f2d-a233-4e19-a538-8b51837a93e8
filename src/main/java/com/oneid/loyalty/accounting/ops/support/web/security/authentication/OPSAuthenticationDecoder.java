package com.oneid.loyalty.accounting.ops.support.web.security.authentication;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.security.oauth2.jwt.BadJwtException;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;

import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import com.oneid.loyalty.accounting.ops.feign.OpsAdminServiceFeignClient;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;

public class OPSAuthenticationDecoder implements JwtDecoder {
    private static final String DECODING_ERROR_MESSAGE_TEMPLATE = "An error occurred while attempting to decode the Jwt: %s";
    
    private OpsAdminServiceFeignClient opsAdminServiceFeignClient;
    private String modules;
    
    public OPSAuthenticationDecoder(OpsAdminServiceFeignClient opsAdminServiceFeignClient) {
        this.opsAdminServiceFeignClient = opsAdminServiceFeignClient;
        
        modules = Arrays.stream(AccessRole.values())
                .map(AccessRole::getName)
                .collect(Collectors.joining(","));
    }
    
    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            JWT jwtParser = JWTParser.parse(token);
            
            return new Jwt(token, null, null, jwtParser.getHeader().toJSONObject(), verifyToken(token));
        } catch (ParseException e) {
            throw new BadJwtException(String.format(DECODING_ERROR_MESSAGE_TEMPLATE, e.getMessage()), e);
        }
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> verifyToken(String jwt) {
        JwtIntrospectRequest body = JwtIntrospectRequest.builder().jwtToken(jwt).build();
        
        APIResponse<Object> apiResponse = opsAdminServiceFeignClient.introspectToken(modules, body);
        
        Map<String, Object> response = (Map<String, Object>) apiResponse.getData();
        
        boolean valid = (Boolean) Optional.of(response.get("is_valid_token"))
                .orElse(Boolean.FALSE);
        
        if(valid) {
            return response;
        }
        
        throw new BadJwtException(null);
    }
}
