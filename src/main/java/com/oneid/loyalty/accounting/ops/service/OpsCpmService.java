package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CpmTxnReverseReq;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionDetailRes;
import org.springframework.data.domain.Page;
import com.oneid.loyalty.accounting.ops.model.req.CpmTransactionReq;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionRes;

public interface OpsCpmService {

    Page<CpmTransactionRes> getCpmTransactions(CpmTransactionReq cpmTransactionReq, Integer offset, Integer limit);

    CpmTransactionDetailRes getCpmTransactionDetail(String transactionRef);

    void reverseCpmTransaction(CpmTxnReverseReq cpmTxnReverseReq);
}
