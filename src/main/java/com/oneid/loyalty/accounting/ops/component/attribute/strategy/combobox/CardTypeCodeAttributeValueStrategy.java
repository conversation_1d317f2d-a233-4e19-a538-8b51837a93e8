package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.CardTypeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CardTypeCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private CardTypeRepository cardTypeRepository;

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(CardTypeCodeAttributeValueStrategy.class);

    public CardTypeCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getCardTypeCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {

        CardType cardType = cardTypeRepository.findByProgramIdAndCardType(programIds[0], value);

        if (cardType == null) {
            LOGGER.warn("Card type {} not found", value);
            throw new IllegalArgumentException();
        }
        return AttributeCombobox.builder()
                .name(cardType.getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {

            Set<String> cardTypes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            final Map<String, CardType> allCardType =
                    cardTypeRepository.findByProgramId(programIds[0])
                            .stream().collect(Collectors.toMap(CardType::getCardType, cardType -> cardType));

            return cardTypes.stream()
                    .map(cardTypeCode -> AttributeCombobox.builder()
                            .name(allCardType.get(cardTypeCode).getName())
                            .value(cardTypeCode)
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    )
                    .collect(Collectors.toList());
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}