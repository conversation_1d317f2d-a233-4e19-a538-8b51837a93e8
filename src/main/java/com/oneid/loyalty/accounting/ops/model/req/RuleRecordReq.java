package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EConditionType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class RuleRecordReq {
    @JsonIgnore
    private Integer schemeId;

    @JsonProperty("rule_id")
    private Integer ruleId;

    @NotNull(message = "condition_logic must not be null")
    @JsonProperty("condition_logic")
    private EConditionType conditionLogic;

    @JsonProperty("rule_name")
    @Length(max = 255, message = "name: length must be between 1 and 255")
    private String name;

    @JsonProperty("rule_description")
    @Length(max = 255, message = "description: length must be between 1 and 255")
    private String description;

    @JsonProperty("seq_no")
    private Integer seqNo = 0;

    @Valid
    @Size(min = 1, max = 10, message = "Condition list size between 1,10")
    @NotNull(message = "Condition  must not be null")
    @JsonProperty("condition_list")
    private List<ConditionRecordReq> listCondition;
}