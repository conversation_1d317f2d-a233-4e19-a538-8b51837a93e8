package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRateRes;
import org.springframework.data.domain.Page;

public interface OpsCurrencyRateService {
    Page<CurrencyRateRes> filterCurrencyRate(Integer baseCurrencyId, Integer currencyId, String status, Integer businessId, Integer offset, Integer limit);

    CurrencyRateRes getCurrencyRate(Integer id);

    void addCurrencyRate(CurrencyRateCreateReq currencyRateCreateReq);

    void updateCurrencyRate(Integer id, CurrencyRateUpdateReq currencyRateUpdateReq);
}