package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.LOCFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.LocTxnRefundReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.LocTxnRefundRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "oneloyalty-checkout-service", url = "${loc.url}", configuration = LOCFeignConfig.class)
public interface LOCServiceFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/v1/transactions/refund")
    APIResponse<LocTxnRefundRes> refund(@RequestBody LocTxnRefundReq req);

}
