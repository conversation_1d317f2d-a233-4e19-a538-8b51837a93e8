package com.oneid.loyalty.accounting.ops.datasource.cpm.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "cpmEntityManagerFactory",
        transactionManagerRef = "cpmTransactionManager",
        basePackages = { "com.oneid.loyalty.accounting.ops.datasource.cpm.repository" }
)
public class CpmDataSourceConfig {

    @Bean(name = "cpmDataSourceProperties")
    @ConfigurationProperties("spring.mysql-datasource.cpm")
    public DataSourceProperties cpmDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "cpmDataSource")
    @ConfigurationProperties("spring.mysql-datasource.cpm.hikari")
    public DataSource cpmDataSource(@Qualifier("cpmDataSourceProperties") DataSourceProperties cpmDataSourceProperties) {
        return cpmDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }

    @Bean(name = "cpmEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cpmEntityManagerFactory(
            EntityManagerFactoryBuilder cpmEntityManagerFactoryBuilder, @Qualifier("cpmDataSource") DataSource cpmDataSource) {

        Map<String, String> cpmJpaProperties = new HashMap<>();
        cpmJpaProperties.put("hibernate.dialect", "org.hibernate.dialect.MySQL8Dialect");
        cpmJpaProperties.put("hibernate.ddl-auto", "none");
        cpmJpaProperties.put("hibernate.hbm2ddl.auto", "none");

        return cpmEntityManagerFactoryBuilder
                .dataSource(cpmDataSource)
                .packages("com.oneid.loyalty.accounting.ops.datasource.cpm.entity")
//                .persistenceUnit("mysqlDataSource")
                .properties(cpmJpaProperties)
                .build();
    }

    @Bean(name = "cpmTransactionManager")
    public PlatformTransactionManager cpmTransactionManager(
            @Qualifier("cpmEntityManagerFactory") EntityManagerFactory cpmEntityManagerFactory) {

        return new JpaTransactionManager(cpmEntityManagerFactory);
    }
}
