package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateLimitationReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterLimitationAvailableReq;
import com.oneid.loyalty.accounting.ops.model.req.ResetLimitationReq;
import com.oneid.loyalty.accounting.ops.model.res.*;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface OpsLimitationService {
    MakerCheckerInternalMakerRes createRequest(CreateLimitationReq req);

    LimitationRes getAvailableLimitationByRequestId(Integer limitationId);

    LimitationDetailAvailableRes getChangeableByRequestId(Integer limitationId);

    void approve(ApprovalReq req);

    Page<LimitationRes> getAvailableLimitations(FilterLimitationAvailableReq req, Pageable pageable);

    Page<LimitationInReviewRes> getInReview(EApprovalStatus approvalStatus, String fromCreatedAt, String toCreatedAt, String fromReviewedAt,
                                            String toReviewedAt, String createdBy, String reviewedBy, Pageable pageable);


    List<CounterShortInformationRes> getAvailableCounters(Integer businessId);

    List<CounterShortInformationRes> getAvailableCountersByBusinessIdAndProgramId(Integer businessId, Integer programId);

    List<CounterShortInformationRes> getCountersByBusinessIdAndProgramIdAndStatus(Integer businessId, Integer programId, String status);

    MakerCheckerInternalMakerRes update(Integer limitationId, CreateLimitationReq req);

    LimitationInReviewDetailRes getInReviewLimitationRequestById(Integer id);

    LimitationDetailAvailableRes getAvailableLimitationDetailById(Integer id);

    List<RuleRes> getAvailableCounterRuleById(Integer id);

    LimitationStatisticRes limitationStatistic(Integer limitationId, Date startedAt, Date endedAt, String code, ECommonStatus historyStatus, Pageable pageable);
    void approveResetCounting(ResetLimitationReq req);

    MakerCheckerInternalMakerRes resetCounterRequest(Integer limitationId, CreateLimitationReq req);
    SumStatisticRes getSummaryStatistic(Integer limitationId);

}
