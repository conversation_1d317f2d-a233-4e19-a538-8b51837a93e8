package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardTransferReq;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EProcessStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GiftCardTransferringAvailableRes implements Serializable {
    private static final long serialVersionUID = -90892714946426286L; // how to gen????

    private Long id;

    private Long transferNo;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private Integer batchQuantity;

    private Integer cardQuantity;

    private EProcessStatus processStatus;

    private List<CreateGiftCardTransferReq.SelectedBatch> selectedBatchList;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private String createdBy;

    private String approvedBy;

    private String updatedBy;

    private EBoolean archiveFolder;

    private EBoolean encryptPGP;

    private EBoolean existArchiveFolderPassword;
    private String receivePasswordPhoneNo;

    private BasicRecipientInfo recipient;
}
