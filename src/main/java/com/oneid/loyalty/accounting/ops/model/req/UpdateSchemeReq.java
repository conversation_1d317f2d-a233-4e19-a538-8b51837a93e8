package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class UpdateSchemeReq extends CreateSchemeReq {
    @JsonIgnore
    private Integer schemeId;

    @NotNull(message = "Status must not be null")
    @JsonProperty("status")
    private ECommonStatus status;

    @NotBlank(message = "'edit_key' must not be blank")
    @Length(max = 255)
    private String editKey;
}