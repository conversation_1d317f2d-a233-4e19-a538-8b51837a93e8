package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CreateMemberCPRReq.CreateMemberCPRReqBuilder.class)
public class CreateMemberCPRReq {
    @NotBlank
    private String businessCode;
    
    @NotBlank
    private String programCode;
    
    @NotBlank
    private String storeCode;
    
    private Integer noOfCard;
    private String cardBinCode;
    private String cardTypeCode;
    private String description;
    private ECommonStatus status;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateMemberCPRReqBuilder {
    }
}
