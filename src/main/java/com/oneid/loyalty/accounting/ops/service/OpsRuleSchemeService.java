package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.RuleRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.oneloyalty.common.entity.Scheme;

import java.util.List;

public interface OpsRuleSchemeService {

    List<RuleRecordRes> createRules(final Scheme scheme, List<RuleRecordReq> ruleList);

    List<RuleRecordRes> updateRules(Scheme scheme, List<RuleRecordReq> ruleList);
}