package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.SchemeSequenceSettingReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateSchemeReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifySchemeInfoReq;
import com.oneid.loyalty.accounting.ops.model.res.SchemeBaseRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeSequenceRes;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeSequenceRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.model.SchemeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;


@RestController
@RequestMapping("v1/schemes")
@Validated
public class SchemeController extends BaseController {

    @Autowired
    private OpsSchemeService opsSchemeService;
    
    @Autowired
    private OpsSchemeSequenceRequestService opsSchemeSequenceRequestService;

    @PostMapping("verify-scheme-info")
    @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> verify(@RequestBody @Valid VerifySchemeInfoReq req) {
        opsSchemeService.verifySchemeInfo(req);
        return success(null);
    }

    @PostMapping("verify-combine")
    @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> verifyCombine(@RequestBody @Valid  CreateSchemeReq req) {
        opsSchemeService.verifySchemeCombine(req);
        return success(null);
    }

    // Forward for front-end
    @PostMapping("change")
    @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> getApproveCreate(@RequestBody @Valid CreateSchemeReq req) {
        return success(opsSchemeService.getApproveCreate(req));
    }

    @PostMapping("change/{scheme_id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getApproveUpdate(@RequestBody @Valid UpdateSchemeReq req,
                                              @PathVariable("scheme_id") Integer schemeId) {
        req.setSchemeId(schemeId);
        return success(opsSchemeService.getApproveUpdate(req));
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> details(@PathVariable("id") Integer id) {
        return success(opsSchemeService.getDetails(id));
    }

    @GetMapping("approved")
    @Authorize(role = AccessRole.SCHEME, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> search(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "scheme_type", required = false) ESchemeType schemeType,
            @RequestParam(value = "scheme_code", required = false) String schemeCode,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "start_date", required = false) LocalDate startDate,
            @RequestParam(value = "end_date", required = false) LocalDate endDate,
            @RequestParam(value = "corporation_id", required = false) Integer corporationId,
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "store_id", required = false) Integer storeId,
            @RequestParam(value = "terminal_id", required = false) Integer terminalId,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "sort_direction", required = false) Direction direction,
            @Valid @RequestParam(value = "sort_by", required = false) ESchemeSortingField sortBy
    ) {
        ZoneId systemZoneId = ZoneId.systemDefault();
        OffsetDateTime offsetStartDate = startDate == null ? null :
                startDate.atStartOfDay(systemZoneId).toOffsetDateTime();
        OffsetDateTime offsetEndDate = endDate == null ? null :
                endDate.atTime(LocalTime.MAX).atZone(systemZoneId).toOffsetDateTime();
        
        Sort sort = Sort.by((direction == null ? Direction.DESC : direction), (sortBy == null ? ESchemeSortingField.CREATED_AT : sortBy).getMappingColumn());
        
        Pageable pageable = new OffsetBasedPageRequest(offset, limit, sort);
        
        Page<SchemeBaseRes> page = opsSchemeService.getPage(businessId, programId, 
                schemeType, schemeCode, status, 
                offsetStartDate, offsetEndDate, corporationId, chainId, storeId, terminalId,
                pageable);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/sequence/approved-requests")
    @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getApprovedRequests(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "scheme_type", required = false) ESchemeType schemeType,
            @RequestParam(value = "version", required = false) Integer version,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "from_date", required = false) Integer fromDate,
            @RequestParam(value = "to_date", required = false) Integer toDate,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit){
        Page<SchemeSequenceRes> page = opsSchemeSequenceRequestService.getApprovedRequests(
                businessId, 
                programId, 
                schemeType, 
                version, 
                status, 
                fromDate, 
                toDate, 
                new OffsetBasedPageRequest(offset, limit, null));
        
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/sequence/approved-request/{id}")
    @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getApprovedRequestById(@PathVariable("id") Integer id){
        return success(opsSchemeSequenceRequestService.getApprovedRequestById(id));
    }
    
    @GetMapping("/business/{business_id}/program/{program_id}/scheme-type/{scheme_type}/sequence/request-approval")
    @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getRequestApprovalSetting(
            Authentication authentication,
            @PathVariable("business_id") Integer businessId,
            @PathVariable("program_id") Integer programId,
            @PathVariable("scheme_type") ESchemeType schemeType){
        return success(opsSchemeSequenceRequestService.getRequestApprovalSetting(
                (OPSAuthenticatedPrincipal) authentication.getPrincipal(),
                businessId, 
                programId, 
                schemeType));
    }
    
    @PostMapping("/business/{business_id}/program/{program_id}/scheme-type/{scheme_type}/sequence/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestApproval(
            @PathVariable("business_id") Integer businessId,
            @PathVariable("program_id") Integer programId,
            @PathVariable("scheme_type") ESchemeType schemeType,
            @RequestBody @Valid SchemeSequenceSettingReq req){
        return success(opsSchemeSequenceRequestService.sendRequestForApproval(businessId, programId, schemeType, req));
    }
    
    @GetMapping("/sequence/request/{changeRequestId}")
    @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getChangeRequestDetailById(@PathVariable("changeRequestId") Integer changeRequestId){
        return success(opsSchemeSequenceRequestService.getChangeRequestDetailById(changeRequestId));
    }
    
    @GetMapping("/sequence/requests")
    @Authorize(role = AccessRole.SCHEME_SEQUENCE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getChangeRequests(
            @RequestParam(value = "status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_date", required = false) Integer fromDate,
            @RequestParam(value = "to_date", required = false) Integer toDate,
            @MakerCheckerOffsetPageable Pageable pageable) {
        Page<SchemeSequenceRes> page = opsSchemeSequenceRequestService.getChangeRequests(
                approvalStatus,
                fromDate,
                toDate,
                pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsSchemeService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/in-review")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> getInReviewSchemeRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
            @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
            @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
            @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        Page<SchemeInReviewRes> page = opsSchemeService.getInReviewSchemeRequests(approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/available")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> getAvailableSchemeRequests(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "scheme_type", required = false) ESchemeType schemeType,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, sort);

        Page<SchemeDTO> page =
                opsSchemeService.getAvailableSchemeRequests(businessId, programId, code, name, schemeType, status, pageRequest);

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/change/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.SCHEME, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEdit(@PathVariable("id") Integer requestId) {
        return success(opsSchemeService.getEdit(requestId));
    }

    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewSchemeRequestById(@PathVariable("id") Integer reviewId) {
        return success(opsSchemeService.getInReviewSchemeRequestById(reviewId));
    }
}
