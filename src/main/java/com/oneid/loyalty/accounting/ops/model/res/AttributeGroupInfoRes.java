package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@Getter
@Builder
public class AttributeGroupInfoRes {

    @JsonProperty("group_name")
    private String groupName;

    @JsonProperty("attribute_list")
    private List<Attribute> attributeList;

    @Getter
    @Builder
    public static class Attribute {

        @JsonIgnore
        private String attributeGroupName;

        @JsonProperty("attribute")
        private String attribute;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("operators")
        private List<String> operators;

        @JsonProperty("data_type_display")
        private EAttributeDataDisplayType dataTypeDisplay;

        @JsonProperty("resource_path")
        private String resourcePath;

        @JsonProperty("support_filter")
        private Boolean supportFilter;
    }
}