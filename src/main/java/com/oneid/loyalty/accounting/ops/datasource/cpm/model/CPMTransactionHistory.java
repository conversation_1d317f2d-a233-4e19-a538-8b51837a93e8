package com.oneid.loyalty.accounting.ops.datasource.cpm.model;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "transaction_history")
@Setter
@Getter
public class CPMTransactionHistory implements Serializable {


    private static final long serialVersionUID = -2575604763801675508L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "master_id")
    private Long masterId;

    @Column(name = "cus_id")
    private String cusId;

    @Column(name = "cus_id_type")
    @Convert(converter = EIdType.Converter.class)
    private EIdType cusIdType;

    @Column(name = "business_code")
    private String businessCode;

    @Column(name = "program_code")
    private String programCode;

    @Column(name = "corp_code")
    private String corpCode;

    @Column(name = "pos_code")
    private String posCode;

    @Column(name = "pool_code")
    private String poolCode;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "loyalty_transaction_ref_no")
    private String loyaltyTransactionRefNo;

    @Column(name = "redeem_point")
    private BigDecimal redeemPoint;

    @Column(name = "balance_before")
    private BigDecimal balanceBefore;

    @Column(name = "balance_after")
    private BigDecimal balanceAfter;

    @Column(name = "status")
    @Convert(converter = ETransactionStatus.Converter.class)
    private ETransactionStatus status;

    @Column(name = "cancellation")
    @Convert(converter = EBoolean.Converter.class)
    private EBoolean cancellation;

    @Column(name = "cancellation_time")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date cancellationTime;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date createdAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name = "approved_by")
    private String approvedBy;

    @Column(name = "approved_at")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date approvedAt;

    @Column(name = "created_ymd")
    private Long createdYmd;
}
