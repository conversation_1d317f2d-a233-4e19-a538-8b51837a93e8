package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VersioningRes {
    private Integer version;
    private Date updateAt;
    private String updateBy;
    private Date createdAt;
    private String createdBy;
    private Date approvedAt;
    private String approvedBy;
    private Date rejectedAt;
    private String rejectedBy;
}
