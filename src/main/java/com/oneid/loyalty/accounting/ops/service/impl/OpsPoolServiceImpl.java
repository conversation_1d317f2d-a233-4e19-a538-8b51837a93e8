package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.BalanceExpirePolicyMapper;
import com.oneid.loyalty.accounting.ops.mapper.PoolMapper;
import com.oneid.loyalty.accounting.ops.mapper.RetentionPolicyMapper;
import com.oneid.loyalty.accounting.ops.model.req.BalanceExpirePolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.RetentionPolicyReq;
import com.oneid.loyalty.accounting.ops.model.res.PoolDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.PoolEnum;
import com.oneid.loyalty.accounting.ops.model.res.PoolRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramPoolRes;
import com.oneid.loyalty.accounting.ops.service.OpsPoolService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EBalanceExpirePolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERetentionExpireType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.BalanceExpirePolicy;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramPool;
import com.oneid.oneloyalty.common.entity.RetentionPolicy;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.PoolRepository;
import com.oneid.oneloyalty.common.repository.ProgramPoolRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BalanceExpirePolicyService;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.RetentionPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsPoolServiceImpl implements OpsPoolService {
    @Autowired
    PoolService poolService;

    @Autowired
    PoolRepository poolRepository;

    @Autowired
    RetentionPolicyService retentionPolicyService;

    @Autowired
    BalanceExpirePolicyService balanceExpirePolicyService;

    @Autowired
    BusinessService businessService;

    @Autowired
    ProgramService programService;

    @Autowired
    CurrencyService currencyService;

    @Autowired
    private ProgramPoolRepository programPoolRepository;

    @Autowired
    AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Override
    public Page<PoolRes> filterPool(Integer businessId, Integer programId, Integer poolId, Integer currencyId, String status, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<Pool> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (poolId != null)
            specificationBuilder.add(new SearchCriteria("id", poolId, SearchOperation.EQUAL));

        if (currencyId != null)
            specificationBuilder.add(new SearchCriteria("currencyId", currencyId, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        Page<Pool> pools = poolService.find(specificationBuilder, pageRequest);

        return new PageImpl<PoolRes>(pools.getContent()
                .stream().map(it -> PoolRes.of(
                        it,
                        businessService.find(it.getBusinessId()).orElse(null),
                        programService.find(it.getProgramId()).orElse(null),
                        currencyService.find(it.getCurrencyId()).orElse(null)
                )).collect(Collectors.toList())
                , pageRequest, pools.getTotalElements());
    }

    @Override
    public PoolDetailRes getPool(Integer id) {
        Optional<Pool> poolChecker = poolService.find(id);
        if (!poolChecker.isPresent())
            throw new BusinessException(ErrorCode.POOL_NOT_FOUND, "Pool not found", id);
        Pool pool = poolChecker.get();
        RetentionPolicy retentionPolicy = retentionPolicyService.find(pool.getRetentionPolicyId()).orElse(null);
        BalanceExpirePolicy balanceExpirePolicy = balanceExpirePolicyService.find(pool.getBalanceExpiredPolicyId()).orElse(null);

        List<Integer> sharedProgramIds = programPoolRepository.findProgramByPoolId(pool.getId())
                .stream()
                .map(Program::getId)
                .collect(Collectors.toList());

        PoolDetailRes res = PoolDetailRes.of(
                pool,
                retentionPolicy,
                balanceExpirePolicy,
                businessService.find(pool.getBusinessId()).orElse(null),
                programService.find(pool.getProgramId()).orElse(null),
                currencyService.find(pool.getCurrencyId()).orElse(null)
        );

        res.setSharedProgramIds(sharedProgramIds);

        return res;
    }

    @Override
    @Transactional
    public void addPool(PoolCreateReq poolCreateReq) {
        RetentionPolicy retentionPolicy = RetentionPolicyMapper.toRetentionPolicyOneFromPoolCreateReq(poolCreateReq);
        retentionPolicyService.create(retentionPolicy);

        Business business = businessService.findActive(poolCreateReq.getBusinessId());

        BalanceExpirePolicy balanceExpirePolicy = BalanceExpirePolicyMapper.toBalanceExpirePolicyOneFromPoolCreateReq(poolCreateReq);
        balanceExpirePolicyService.create(balanceExpirePolicy);
        Pool pool = PoolMapper.toPoolOne(
                poolCreateReq,
                retentionPolicy.getId(),
                balanceExpirePolicy.getId(),
                auditorAware.getCurrentAuditor().get().getUserName()
        );

        poolService.create(pool);

        if (!CollectionUtils.isEmpty(poolCreateReq.getSharedProgramIds())) {
            programPoolRepository.saveAll(convertToProgramPool(pool.getId(), business, poolCreateReq.getSharedProgramIds()));
        }
    }

    private List<ProgramPool> convertToProgramPool(Integer poolId, Business business, List<Integer> programIds) {
        return programService.findByIdIn(programIds)
                .stream()
                .map(entity -> {
                    if (!entity.getStatus().equals(ECommonStatus.ACTIVE))
                        throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);

                    if (!entity.getBusinessId().equals(business.getId()))
                        throw new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null);

                    ProgramPool programPool = new ProgramPool();

                    programPool.setPoolId(poolId);
                    programPool.setProgramId(entity.getId());

                    return programPool;

                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void updatePool(Integer poolId, PoolUpdateReq poolUpdateReq) {
        Pool poolExist = poolService.find(poolId).orElseThrow(() ->
                new BusinessException(ErrorCode.POOL_NOT_FOUND, "Pool not found", null)
        );

        Business business = businessService.findActive(poolUpdateReq.getBusinessId());

        programPoolRepository.deleteByPoolId(poolExist.getId());

        if (!CollectionUtils.isEmpty(poolUpdateReq.getSharedProgramIds())) {
            programPoolRepository.saveAll(convertToProgramPool(poolExist.getId(), business, poolUpdateReq.getSharedProgramIds()));
        }

        Pool poolUpdate = PoolMapper.toPoolOne(poolExist, poolUpdateReq, auditorAware.getCurrentAuditor().get().getUserName());
        RetentionPolicy retentionPolicyUpdate = updateOrCreateRetentionPolicy(poolUpdate, poolUpdateReq);
        BalanceExpirePolicy balanceExpirePolicyUpdate = updateOrCreateBalanceExpirePolicy(poolUpdate, poolUpdateReq);
        poolUpdate.setRetentionPolicyId(retentionPolicyUpdate.getId());
        poolUpdate.setBalanceExpiredPolicyId(balanceExpirePolicyUpdate.getId());
        poolService.update(poolUpdate);
    }

    private RetentionPolicy updateOrCreateRetentionPolicy(Pool poolUpdate, PoolUpdateReq poolUpdateReq) {
        RetentionPolicy result;
        Optional<RetentionPolicy> retentionPolicyCheck = retentionPolicyService.find(poolUpdate.getRetentionPolicyId());
        if (retentionPolicyCheck.isPresent()) {
            RetentionPolicy retentionPolicyUpdate = RetentionPolicyMapper.toRetentionPolicyOneFromPoolUpdateReq(retentionPolicyCheck.get(), poolUpdateReq);
            retentionPolicyService.update(retentionPolicyUpdate);
            result = retentionPolicyUpdate;
        } else {
            RetentionPolicyReq retentionPolicyReq = poolUpdateReq.getRetentionPolicy();
            RetentionPolicy retentionPolicy = new RetentionPolicy();
            retentionPolicy.setBusinessId(poolUpdate.getBusinessId());
            retentionPolicy.setName(poolUpdate.getName());
            retentionPolicy.setType(ERetentionExpireType.of(retentionPolicyReq.getType()));
            retentionPolicy.setPeriodDay(retentionPolicyReq.getPeriodDay());
            retentionPolicy.setStatus(poolUpdate.getStatus());
            retentionPolicyService.create(retentionPolicy);
            result = retentionPolicy;
        }
        return result;
    }

    private BalanceExpirePolicy updateOrCreateBalanceExpirePolicy(Pool poolUpdate, PoolUpdateReq poolUpdateReq) {
        BalanceExpirePolicy result;
        Optional<BalanceExpirePolicy> balanceExpirePolicyCheck = balanceExpirePolicyService.find(poolUpdate.getBalanceExpiredPolicyId());
        if (balanceExpirePolicyCheck.isPresent()) {
            BalanceExpirePolicy balanceExpirePolicyUpdate = BalanceExpirePolicyMapper.toBalanceExpirePolicyOneFromPoolUpdateReq(balanceExpirePolicyCheck.get(), poolUpdateReq);
            balanceExpirePolicyService.update(balanceExpirePolicyUpdate);
            result = balanceExpirePolicyUpdate;
        } else {
            BalanceExpirePolicyReq balanceExpirePolicyReq = poolUpdateReq.getBalanceExpirePolicy();
            BalanceExpirePolicy balanceExpirePolicy = new BalanceExpirePolicy();
            balanceExpirePolicy.setBusinessId(poolUpdate.getBusinessId());
            balanceExpirePolicy.setName(poolUpdate.getName());
            balanceExpirePolicy.setType(EBalanceExpirePolicyType.of(balanceExpirePolicyReq.getType()));
            balanceExpirePolicy.setPeriodDay(balanceExpirePolicyReq.getPeriod());
            balanceExpirePolicy.setExpiredTimeFixed(balanceExpirePolicyReq.getFixTimeExpire());
            balanceExpirePolicy.setStatus(poolUpdate.getStatus());
            balanceExpirePolicyService.create(balanceExpirePolicy);
            result = balanceExpirePolicy;
        }
        return result;
    }

    @Override
    public List<PoolEnum> getAllActiveByProgram(Integer programId) {
        List<PoolEnum> response = poolRepository.findAllByProgramIdAndStatus(programId, ECommonStatus.ACTIVE).stream()
                .map(PoolEnum::of).collect(Collectors.toList());
        Object[][] programPools = programPoolRepository.findPoolByProgramId(programId);
        response.addAll(Arrays.stream(programPools)
                .map(objects -> PoolEnum.of((Pool) objects[0]))
                .collect(Collectors.toList())
        );

        return response;
    }


    @Override
    public Map<Integer, Pool> getMapById(Collection<Integer> ids) {
        List<Pool> pools = this.poolRepository.findAllByIdIn(ids);
        return pools.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t
        ));
    }

    @Override
    public List<ProgramPoolRes> getSharedPools(Integer programId) {
        return Arrays.stream(programPoolRepository.findPoolByProgramId(programId))
                .map(entity -> {
                    Pool pool = Pool.class.cast(entity[0]);

                    return ProgramPoolRes.builder()
                            .poolId(pool.getId())
                            .poolName(pool.getName())
                            .build();
                })
                .collect(Collectors.toList());
    }
}