package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Builder
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SendSmsPasswordFolderRes implements Serializable {
    private Long DefaultSendSmsTimeSec;
    private Long NextSendSmsTimeSec;
    private EBoolean SendSmsSuccess;
    private Date LastSendSmsAt;
}
