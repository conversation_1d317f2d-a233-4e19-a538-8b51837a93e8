package com.oneid.loyalty.accounting.ops.controller.v1;

import java.time.LocalDate;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.MemberCardChangedStatusReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberCardRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberCardService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECardStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@Validated
@RestController
@RequestMapping("v1/member-card")
public class MemberCardController extends BaseController {
    @Autowired
    private OpsMemberCardService opsMemberCardService;
    
    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(value = "business_id", required = true) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "corporation_id", required = false) Integer corporationId,
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "store_id", required = false) Integer storeId,
            @RequestParam(value = "batch_no", required = false) Long batchNo,
            @RequestParam(value = "card_no", required = false) String cardNo,
            @RequestParam(value = "from_date", required = false) LocalDate fromDate,
            @RequestParam(value = "to_date", required = false) LocalDate toDate,
            @RequestParam(value = "current_card_status", required = false) ECardStatus currentCardStatus,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit){

        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<MemberCardRes> page = opsMemberCardService.filter(businessId, programId, corporationId, chainId, storeId, batchNo, cardNo, fromDate, toDate, currentCardStatus, approvalStatus, pageRequest);

        return success(page.getContent(), (int) pageRequest.getOffset(), pageRequest.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_date", required = false) LocalDate fromDate,
            @RequestParam(value = "to_date", required = false) LocalDate toDate,
            @MakerCheckerOffsetPageable Pageable pageRequest){

        Page<MemberCardRes> page = opsMemberCardService.getInReview(approvalStatus, fromDate, toDate, pageRequest);
        return success(page.getContent(), (int) pageRequest.getOffset(), pageRequest.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/available/program/{program_id}/card-no/{card_no}")
    @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAvailableRequestById(
            @PathVariable("program_id") Integer programId,
            @PathVariable("card_no") String cardNo){
        return success(opsMemberCardService.getAvailableDetail(programId, cardNo));
    }
    
    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewRequestById(@PathVariable("id") Integer reviewId){
        return success(opsMemberCardService.getInReviewDetail(reviewId));
    }
    
    @GetMapping("/request/changed-status/program/{program_id}/card-no/{card_no}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> getEditChangedStatusRequestSetting(
            @PathVariable("program_id") Integer programId,
            @PathVariable("card_no") String cardNo){
        return success(opsMemberCardService.getEditChangedStatusRequestSetting(programId, cardNo));
    }
    
    @PostMapping("/request/changed-status/program/{program_id}/card-no/{card_no}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_CARD_LISTING, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestEditingChangedStatusRequest(
            @PathVariable("program_id") Integer programId,
            @PathVariable("card_no") String cardNo,
            @RequestBody @Valid MemberCardChangedStatusReq req){
        return success(opsMemberCardService.requestEditingChangedStatusRequest(programId, cardNo, req));
    }
}