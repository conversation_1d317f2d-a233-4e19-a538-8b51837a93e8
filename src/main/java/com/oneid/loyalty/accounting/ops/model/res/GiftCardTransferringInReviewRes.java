package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GiftCardTransferringInReviewRes implements Serializable {

    private static final long serialVersionUID = -90892714946426289L;

    private Long requestId;

    private Long transferNo;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private Integer batchQuantity;

    private Integer cardQuantity;

    private String createdBy;

    private String reviewedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date reviewedAt;

    private EApprovalStatus approvalStatus;
}
