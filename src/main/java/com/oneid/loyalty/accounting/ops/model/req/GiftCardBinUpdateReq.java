package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class GiftCardBinUpdateReq {
    @Length(max = 255, message = "'Description' max length is 255")
    @JsonProperty("description")
    private String description;

    @NotBlank
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;
}
