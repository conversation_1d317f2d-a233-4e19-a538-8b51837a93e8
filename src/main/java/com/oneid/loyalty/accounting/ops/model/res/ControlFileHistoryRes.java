package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ControlFileHistoryRes {
    private Long id;

    private String fileName;

    private BigDecimal totalFiles;

    private EProcessingStatus status;

    private EBoolean isIgnored;

    private String errorMessage;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private EFileType fileType;

    private Long totalTransaction;

    private Long totalFailedTransaction;

    private RetryFileRes retryFile;
}
