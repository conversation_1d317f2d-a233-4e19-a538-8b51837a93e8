package com.oneid.loyalty.accounting.ops.model.req;

import java.time.OffsetDateTime;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EDurationType;
import com.oneid.oneloyalty.common.constant.ETierMappingExpireType;
import com.oneid.oneloyalty.common.constant.EVerificationType;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = TierMatchingUpdateReq.TierMatchingUpdateReqBuilder.class)
public class TierMatchingUpdateReq {
    @NotBlank(message = "'name' must not be blank")
    @Size(max = 255)
    private String name;
    
    @Size(max = 512)
    private String description;
    
    @NotNull(message = "'start_date' must not be null")
    private OffsetDateTime startDate;
    
    @NotNull(message = "'end_date' must not be null")
    private OffsetDateTime endDate;
    
    @NotNull(message = "'auto_matching' must not be null")
    private Boolean autoMatching;
    
    @NotNull(message = "'auto_verify' must not be null")
    private Boolean autoVerify;
    
    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;
    
    @NotNull(message = "'expire_type' must not be null")
    private ETierMappingExpireType expireType;
    
    private EDurationType durationType;
    
    @NotEmpty(message = "'expire_type' must not be empty")
    private List<EVerificationType> verificationType;
    
    @Min(value = 0)
    private Integer durationValue;
    
    @Valid
    @NotEmpty(message = "'tier_mappings' must not be empty")
    private List<TierMappingReq> tierMappings;
    
    @AssertTrue(message="'end_date' must be greater than 'start_date' at least 24 hours")
    public boolean isValidEndDate() {
        return endDate.isAfter(startDate.plusHours(24))
                || endDate.isEqual(startDate.plusHours(24));
    }
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class TierMatchingUpdateReqBuilder {
    }
}
