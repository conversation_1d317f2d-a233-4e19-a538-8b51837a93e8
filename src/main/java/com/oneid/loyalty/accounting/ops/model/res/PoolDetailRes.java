package com.oneid.loyalty.accounting.ops.model.res;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.entity.BalanceExpirePolicy;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.RetentionPolicy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class PoolDetailRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @JsonProperty("currency_name")
    private String currencyName;

    @JsonProperty("retention_policy")
    private RetentionPolicyRes retentionPolicy;

    @JsonProperty("balance_expire_policy")
    private BalanceExpirePolicyRes balanceExpiredPolicy;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("negative_balance")
    private String negativeBalance;

    @JsonProperty("allow_refund_remaining_balance")
    private String allowRefundRemainingBalance;
    
    @JsonProperty("shared_program_ids")
    private List<Integer> sharedProgramIds;

    @Setter
    @Getter
    @AllArgsConstructor
    public static class RetentionPolicyRes {
        @JsonProperty("id")
        private Integer id;

        @JsonProperty("type")
        private String type;

        @JsonProperty("period_day")
        private Integer periodDay;
    }

    @Setter
    @Getter
    @AllArgsConstructor
    public static class BalanceExpirePolicyRes {
        @JsonProperty("id")
        private Integer id;

        @JsonProperty("type")
        private String type;

        @JsonProperty("period")
        private Integer period;

        @JsonProperty("fix_time_expire")
        private Long fixTimeExpire;
    }

    public static PoolDetailRes of(Pool pool, RetentionPolicy retentionPolicy, BalanceExpirePolicy balanceExpirePolicy, Business business, Program program, Currency currency) {
        RetentionPolicyRes retentionPolicyRes = new RetentionPolicyRes(
                pool.getRetentionPolicyId(),
                retentionPolicy != null && retentionPolicy.getType() != null ? retentionPolicy.getType().getValue() : null,
                retentionPolicy != null ? retentionPolicy.getPeriodDay() : null
        );
        BalanceExpirePolicyRes balanceExpirePolicyRes = new BalanceExpirePolicyRes(
                pool.getBalanceExpiredPolicyId(),
                balanceExpirePolicy != null && balanceExpirePolicy.getType() != null ? balanceExpirePolicy.getType().getValue() : null,
                balanceExpirePolicy != null ? balanceExpirePolicy.getPeriodDay() : null,
                balanceExpirePolicy != null && balanceExpirePolicy.getExpiredTimeFixed() != null ? balanceExpirePolicy.getExpiredTimeFixed().toInstant().getEpochSecond() : null
        );
        return new PoolDetailRes(
                pool.getId(),
                pool.getCode(),
                pool.getBusinessId(),
                business != null ? business.getName() : null,
                pool.getCurrencyId(),
                currency != null ? currency.getName() : null,
                retentionPolicyRes,
                balanceExpirePolicyRes,
                pool.getName(),
                pool.getDescription(),
                pool.getStatus().getValue(),
                pool.getCreatedBy(),
                pool.getUpdatedBy(),
                pool.getApprovedBy(),
                pool.getCreatedAt() != null ? pool.getCreatedAt().toInstant().getEpochSecond() : null,
                pool.getUpdatedAt() != null ? pool.getUpdatedAt().toInstant().getEpochSecond() : null,
                pool.getApprovedAt() != null ? pool.getApprovedAt().toInstant().getEpochSecond() : null,
                pool.getCreatedYmd(),
                pool.getProgramId(),
                program != null ? program.getName() : null,
                pool.getNegativeBalance() != null ? pool.getNegativeBalance().getValue() : EBoolean.NO.getValue(),
                pool.getAllowRefundRemainingBalance() != null ? pool.getAllowRefundRemainingBalance().getValue() : EBoolean.NO.getValue(),
                null
        );
    }

}
