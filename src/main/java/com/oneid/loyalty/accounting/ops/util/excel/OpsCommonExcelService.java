package com.oneid.loyalty.accounting.ops.util.excel;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.util.excel.entry.ExportConfiguration;
import com.oneid.loyalty.accounting.ops.util.excel.entry.ExportElement;
import com.oneid.loyalty.accounting.ops.util.excel.entry.ExportEntry;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import lombok.Cleanup;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.MDC;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;

/**
 * author viet.le2
 */
@Component
public class OpsCommonExcelService {

    private XSSFWorkbook loadFileTemplate(EntryContext context) {
        XSSFWorkbook xssfWorkbook = null;
        // exp: template/txn/verify-txn-adjustment-template.xlsx
        String pathFileExcel = String.format("template/%s/%s-template.%s", context.getModuleId(), context.getObjectId(), OPSConstant.FILE_EXT_XLSX);
        try {
            xssfWorkbook =  new XSSFWorkbook(Objects.requireNonNull(Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(pathFileExcel)));
        } catch (IOException ex) {
            Log.error(String.format("[%s] - Failed to export object to excel template", MDC.get(OPSConstant.LOG_ID)), ex);
        }
        return xssfWorkbook;
    }

    private ExportConfiguration loadExportConfiguration(EntryContext context) {
        try {
            // exp: template/config/txn/verify-txn-adjustment.yml
            String pathFileYml = String.format("template/config/%s/%s.%s", context.getModuleId(), context.getObjectId(), OPSConstant.FILE_EXT_YML);
            @Cleanup
            InputStream inputStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(pathFileYml);
            Yaml yamlParser = new Yaml(new Constructor(ExportConfiguration.class));
            return yamlParser.load(inputStream);
        } catch (Exception ex) {
            Log.error(String.format("[%s] - Failed to load defined export configuration", MDC.get(OPSConstant.LOG_ID)), ex);
            return null;
        }
    }

    private void writeCell(List<ExportEntry> entries, Sheet sheet, JsonObject jsonObject, int rowNum,
                           XSSFWorkbook workbook) {
        for (ExportEntry entry : entries) {
            this.processWriteCellEntry(sheet, jsonObject, rowNum, workbook, entry);
        }
    }

    private Object getValueOfCell(String value) {
        return "null".equals(value) ? "" : value.replace("\"", "");
    }

    private CellStyle createStyle(XSSFWorkbook workbook) {
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }

    private void processWriteCellEntry(Sheet sheet, JsonObject jsonObject, int rowNum,
                                       XSSFWorkbook workbook, ExportEntry entry) {
        JsonElement jsonElement = jsonObject.get(entry.getPropertyName());
        Object value = this.getValueOfCell(jsonElement.toString());
        if (Objects.isNull(sheet.getRow(rowNum))) {
            sheet.createRow(rowNum);
        }
        if (Objects.isNull(sheet.getRow(rowNum).getCell(entry.getIndex()))) {
            sheet.getRow(rowNum).createCell(entry.getIndex());
        }
        CellStyle cellStyle = this.createStyle(workbook);
        Cell cell = sheet.getRow(rowNum).getCell(entry.getIndex());
        cell.setCellValue(String.valueOf(value));
        cell.setCellStyle(cellStyle);
    }

    public <V> XSSFWorkbook processExport(EntryContext context, List<V> data) {
        ExportConfiguration exportConf = this.loadExportConfiguration(context);
        if (Objects.isNull(exportConf)) {
            return null;
        }
        XSSFWorkbook workbook = this.loadFileTemplate(context);
        if (Objects.isNull(workbook)) {
            return null;
        }
        try {
            String jsonString = JsonUtil.writeValueAsString(data);
            JsonArray asJsonArray = (JsonArray) JsonParser.parseString(jsonString);
            for (ExportElement element : exportConf.getElements()) {
                Sheet sheet = workbook.getSheet(element.getSheetName());
                if (Objects.nonNull(sheet)) {
                    AtomicInteger rowNum = new AtomicInteger(1);
                    if (Objects.nonNull(asJsonArray)) {
                        for (JsonElement jsonElement : asJsonArray) {
                            JsonObject jsonObject = (JsonObject) JsonParser.parseString(String.valueOf(jsonElement));
                            this.writeCell(element.getEntries(), sheet, jsonObject, rowNum.getAndIncrement(), workbook);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Log.error(String.format("[%s] - Failed to export object to excel template", MDC.get(OPSConstant.LOG_ID)), ex);
        }
        return workbook;
    }

    /**
     * Read config yaml files and templates
     * and then writes Excel files in formats XLSX using Apache POI
     * @param context the context
     * @param fileName the file name
     * @param data the data
     * @return the ResourceDTO
     * @param <V> the generic type
     */
    public <V> ResourceDTO opsExport(EntryContext context, String fileName, List<V> data)  {
        String logId = UUID.randomUUID().toString();
        MDC.put(OPSConstant.LOG_ID, logId);
        long start = System.currentTimeMillis();
        Log.info(String.format("[%s] - Export %s file - START", logId, fileName));

        ResourceDTO result = null;
        try {
            XSSFWorkbook sheets = processExport(context, data);
            Path tmpOutputFile = Files.createTempFile(null, null);
            FileOutputStream out = new FileOutputStream(tmpOutputFile.toFile());
            sheets.write(out);
            out.close();
            result = ResourceDTO.builder()
                    .filename(fileName)
                    .resource(new InputStreamResource(new FileSystemResource(tmpOutputFile).getInputStream()))
                    .build();
            Log.info(String.format("[%s] - Export %s file with took %s - END", logId, fileName, System.currentTimeMillis() - start));
        } catch (Exception ex) {
            Log.error(String.format("[%s] - Export %s file - FAIL", logId, fileName), ex);
        }
        MDC.remove(OPSConstant.LOG_ID);
        return result;
    }

    /**
     *  Get template file
     * @param moduleId the moduleId
     * @param fileName the file name
     * @return
     */
    public ResponseEntity<InputStreamResource> getTemplate(String moduleId, String fileName) throws IOException {
        HttpHeaders headers = setHeaderForFile(fileName + "." + OPSConstant.FILE_EXT_XLSX);
        // exp: template/txn/Template_TransactionRequest_Revert_Partial.xlsx
        String pathFileExcel = String.format("template/%s/%s.%s", moduleId, fileName, OPSConstant.FILE_EXT_XLSX);
        return new ResponseEntity<>(new InputStreamResource(new ClassPathResource(pathFileExcel)
                .getInputStream()), headers, HttpStatus.OK);
    }

    public static HttpHeaders setHeaderForFile(String fileName) {
        HttpHeaders headers = new HttpHeaders();
        List<String> accessControlExposeHeaders = headers.getAccessControlExposeHeaders();
        if (accessControlExposeHeaders.isEmpty()) {
            accessControlExposeHeaders = new LinkedList<>();
            accessControlExposeHeaders.add(CONTENT_DISPOSITION);
        }
        headers.setAccessControlExposeHeaders(accessControlExposeHeaders);

        headers.add("Content-Disposition", "attachment; filename=" + fileName);
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        return headers;
    }
}
