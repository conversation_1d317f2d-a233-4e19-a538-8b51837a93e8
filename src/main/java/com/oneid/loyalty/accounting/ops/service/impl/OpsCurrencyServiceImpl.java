package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.CurrencyMapper;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyEnum;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRes;
import com.oneid.loyalty.accounting.ops.service.OpsCurrencyService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.CurrencyRate;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CurrencyRateRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsCurrencyServiceImpl implements OpsCurrencyService {
    @Autowired
    BusinessService businessService;

    @Autowired
    CurrencyService currencyService;

    @Autowired
    CurrencyRepository currencyRepository;

    @Autowired
    CurrencyRateRepository currencyRateRepository;

    @Autowired
    AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Override
    public List<CurrencyEnum> getEnum(Integer businessId) {
        return currencyService.findAllActive().stream().filter(it -> it.getBusinessId().equals(businessId))
                .map(CurrencyEnum::of).collect(Collectors.toList());
    }

    @Override
    public List<CurrencyEnum> getCurrenciesForTransaction(Integer businessId) {
        List<Integer> currencyIds = currencyRateRepository
                .findAllByBusinessId(businessId)
                .stream()
                .filter(currencyRate -> ECommonStatus.ACTIVE.equals(currencyRate.getStatus())
                        || Objects.nonNull(currencyRate.getBaseCurrencyId()))
                .map(CurrencyRate::getBaseCurrencyId)
                .distinct()
                .collect(Collectors.toList());

        return currencyRepository
                .findAllByIdIn(currencyIds)
                .stream()
                .filter(currency -> ECommonStatus.ACTIVE.equals(currency.getStatus()))
                .map(CurrencyEnum::of)
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<Integer, String> getBusinessIdToName() {
        List<Business> businesses = businessService.searchAll(new SpecificationBuilder());
        return businesses.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t.getName()
        ));
    }

    @Override
    public Page<CurrencyRes> filterCurrency(CurrencySearchReq currencySearchReq, Pageable pageRequest) {
        SpecificationBuilder<Currency> specificationBuilder = new SpecificationBuilder<>();

        if (currencySearchReq.getBusinessId() != null)
            specificationBuilder.add(new SearchCriteria("businessId", currencySearchReq.getBusinessId(), SearchOperation.EQUAL));

        if (currencySearchReq.getCode() != null)
            specificationBuilder.add(new SearchCriteria("code", currencySearchReq.getCode(), SearchOperation.EQUAL));

        if (currencySearchReq.getStatus() != null)
            specificationBuilder.add(new SearchCriteria("status", currencySearchReq.getStatus(), SearchOperation.EQUAL));

        if (currencySearchReq.getCreatedDateFrom() != null)
            specificationBuilder.add(new SearchCriteria("createdAt", currencySearchReq.getCreatedDateFrom(), SearchOperation.GREATER_THAN_EQUAL_DATE));

        if (currencySearchReq.getCreatedDateTo() != null)
            specificationBuilder.add(new SearchCriteria("createdAt", currencySearchReq.getCreatedDateTo(), SearchOperation.LESS_THAN_EQUAL_DATE));

        Map<Integer, String> businessIdToName = this.getBusinessIdToName();

        Page<Currency> currencies = currencyService.find(specificationBuilder, pageRequest);

        return new PageImpl<CurrencyRes>(currencies.getContent()
                .stream().map(it -> CurrencyRes.of(it, businessIdToName)).collect(Collectors.toList())
                , pageRequest, currencies.getTotalElements());
    }

    @Override
    public CurrencyRes getCurrency(Integer id) {
        Optional<Currency> currency = currencyService.find(id);
        if (!currency.isPresent())
            throw new BusinessException(ErrorCode.CURRENCY_NOT_FOUND, "Currency not found in business", LogData.createLogData().append("currency_id", id));
        Map<Integer, String> businessIdToName = this.getBusinessIdToName();
        return CurrencyRes.of(currency.get(), businessIdToName);
    }

    @Override
    public void addCurrency(CurrencyCreateReq currencyCreateReq) {
        currencyService.create(CurrencyMapper.toCurrencyOne(currencyCreateReq, auditorAware.getCurrentAuditor().get().getUserName()));
    }

    @Override
    public void updateCurrency(Integer id, CurrencyUpdateReq currencyUpdateReq) {
        Currency currency = currencyService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CURRENCY_NOT_FOUND, "Currency not found in business", LogData.createLogData().append("currency_id", id)));
        Currency currencyUpdate = CurrencyMapper.toCurrencyOne(currency, currencyUpdateReq, auditorAware.getCurrentAuditor().get().getUserName());
        currencyService.update(currencyUpdate);
    }

    @Override
    public Map<Integer, Currency> getMapById(Collection<Integer> ids) {
        List<Currency> currencies = this.currencyRepository.findAllByIdIn(ids);
        return currencies.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t,
                (value1, value2) -> value2
        ));
    }

}
