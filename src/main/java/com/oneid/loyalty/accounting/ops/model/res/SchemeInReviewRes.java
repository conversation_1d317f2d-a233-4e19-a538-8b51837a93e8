package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Builder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SchemeInReviewRes {
    private Integer schemeId;

    private String schemeCode;

    private String schemeName;

    private ESchemeType schemeType;

    private Integer poolId;

    private String poolName;

    private ECommonStatus status;

    private Integer programId;

    private String programName;

    private Integer businessId;

    private String businessName;

    private Date startDate;

    private Date endDate;

    private EApprovalStatus approvalStatus;

}
