package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramTierReq;
import com.oneid.loyalty.accounting.ops.model.res.TierRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/tiers")
@Validated
public class TierController extends BaseController {

    @Autowired
    OpsTierService opsTierService;

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableCounterRequests(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "tier_code", required = false) String code,
            @RequestParam(value = "tier_status", required = false) ECommonStatus status,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        FilterProgramTierReq req = FilterProgramTierReq.builder()
                .businessId(businessId)
                .programId(programId)
                .code(code)
                .status(status)
                .build();

        Page<TierRes> page = opsTierService.getAvailableTiers(
                req,
                new OffsetBasedPageRequest(offset, limit, Sort.by("rankNo"))
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTierAvailable(@PathVariable("id") Integer tierId) {
        TierRes tierRes = opsTierService.getAvailableTier(tierId);

        return success(tierRes);
    }

    @GetMapping("/request/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEditTier(@PathVariable("id") Integer tierId) {
        TierRes editTier = opsTierService.getEditTier(tierId);

        return success(editTier);
    }

    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> create(
            @Valid @RequestBody ProgramTierReq createTierReq
    ) {
        opsTierService.create(createTierReq);
        return success(null);
    }

    @PostMapping("/request/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> update(
            @PathVariable("id") Integer tierId,
            @Valid @RequestBody ProgramTierReq tierReq
    ) {
        opsTierService.update(tierId, tierReq);
        return success(null);
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsTierService.approve(req);

        return success(null);
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewTiers(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "created_start", required = false) String fromCreatedAt,
            @RequestParam(value = "created_end", required = false) String toCreatedAt,
            @RequestParam(value = "approved_start", required = false) String fromReviewedAt,
            @RequestParam(value = "approved_end", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "approved_by", required = false) String reviewedBy,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<TierRes> page = opsTierService.getInReviewTiers(
                approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy,
                offset, limit
        );
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.TIER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewTier(@PathVariable("id") Integer reviewId) {
        TierRes tierRes = opsTierService.getInReviewTier(reviewId);

        return success(tierRes);
    }
}