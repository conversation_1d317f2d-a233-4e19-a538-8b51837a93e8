package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@EqualsAndHashCode
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class EditAttributeRequestReq {

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255, message = "'name' must be less than 255 digit.")
    private String name;

    @NotEmpty(message = "'operators' must not be null")
    private List<EAttributeOperator> operators;

    private String regexValidation;

}
