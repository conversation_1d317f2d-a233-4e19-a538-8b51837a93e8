package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MakerCheckerInternalMakerRes implements Serializable {
    private static final long serialVersionUID = 284894343683180738L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("version")
    private Integer version;
}