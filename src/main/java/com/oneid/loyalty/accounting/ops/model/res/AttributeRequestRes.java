package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.AttributeCreateReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
public class AttributeRequestRes {

    private Integer id;

    private Integer reviewId;

    private ShortEntityRes program;

    private ShortEntityRes business;

    private Integer programId;

    private String programName;

    private Integer businessId;

    private String businessName;

    private String code;

    private String name;

    private String dataType;

    private String dataTypeDisplay;

    private List<String> operators;

    private String regexValidation;

    private EAttributeType attributeType;

    private ECommonStatus status;

    private EApprovalStatus approvalStatus;

    private Date lastUpdateAt;

    private String lastUpdateBy;

    private Date createdAt;

    private String createdBy;

    private Date reviewedAt;

    private String reviewedBy;

    private Boolean enableValidateMasterData;

    private Boolean havingMasterData;

    private List<AttributeCreateReq.MasterData> masterDatas = Collections.emptyList();

    private String reason;

    private Boolean serviceApplied;

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class MasterData {
        @NotBlank(message = "'value' must not be blank")
        private String value;

        private String description;

        private ECommonStatus status = ECommonStatus.ACTIVE;
    }
}