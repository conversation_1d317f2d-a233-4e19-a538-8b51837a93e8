package com.oneid.loyalty.accounting.ops.config;

import org.apache.http.client.HttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate(HttpClient httpClient) {
        return new RestTemplate(clientHttpRequestFactory(httpClient));
    }

    @Bean
    public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory(HttpClient httpClient) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClient);
        return clientHttpRequestFactory;
    }
    
    @Bean("oneloyaltyService")
    RestTemplate oneloyaltyRestTemplate(HttpClient httpClient) {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(httpClient));
        
        return restTemplate;
    }
}
