package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramListRes extends OtherInfo implements Serializable {

    private static final long serialVersionUID = 4623024574145021022L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("program_name_en")
    private String programNameEn;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("description_en")
    private String descriptionEn;

    @Convert(converter = ECommonStatus.Converter.class)
    @JsonProperty("status")
    private ECommonStatus status;

    @JsonProperty("logo_url")
    private String logoUrl;

    @JsonProperty("website")
    private String website;

    @JsonProperty("hotline")
    private String hotline;

    @JsonProperty("auto_register")
    private String autoRegister;

    @JsonProperty("program_ref")
    private Integer programRef;

    @JsonProperty("program_ref_code")
    private String programRefCode;

    @JsonProperty("program_ref_name")
    private String programRefName;

    @JsonProperty("approval_status")
    private String approvalStatus;

    public static ProgramListRes valueOf(Program loyaltyProgram) {
        ProgramListRes programRes = new ProgramListRes();
        programRes.setProgramId(loyaltyProgram.getId());
        programRes.setBusinessId(loyaltyProgram.getBusinessId());
        programRes.setDescription(loyaltyProgram.getDescription());
        programRes.setDescriptionEn(loyaltyProgram.getEnDescription());
        programRes.setProgramCode(loyaltyProgram.getCode());
        programRes.setProgramName(loyaltyProgram.getName());
        programRes.setProgramNameEn(loyaltyProgram.getEnName());
        programRes.setStatus(loyaltyProgram.getStatus());
        programRes.setWebsite(loyaltyProgram.getWebsite());
        programRes.setHotline(loyaltyProgram.getHotline());
        programRes.setLogoUrl(loyaltyProgram.getLogoUrl());
        programRes.setAutoRegister(Objects.nonNull(loyaltyProgram.getAutoRegister()) ? loyaltyProgram.getAutoRegister().getValue() : null);
        programRes.setProgramRef(loyaltyProgram.getProgramRef());
        programRes.setCreatedAt(loyaltyProgram.getCreatedAt());
        programRes.setCreatedBy(loyaltyProgram.getCreatedBy());
        programRes.setUpdatedAt(loyaltyProgram.getUpdatedAt());
        programRes.setUpdatedBy(loyaltyProgram.getUpdatedBy());
        programRes.setApprovedAt(loyaltyProgram.getApprovedAt());
        programRes.setApprovedBy(loyaltyProgram.getApprovedBy());
        return programRes;
    }
}