package com.oneid.loyalty.accounting.ops.model.res;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.util.SimpleDateSerializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberProfileDetailRes {
    private Long memberId;
    private Integer businessId;
    private String businessCode;
    private String businessName;
    private Integer programId;
    private String programCode;
    private String programName;
    private String phoneNo;
    private String fullName;
    private String gender;
    private String identifyNo;
    private EIdentifyType identifyType;
    private String status;
    private String memberCode;
    private String memberStatus;
    private String address;
    private String dob;
    private String email;
    private String firstName;
    private String lastName;
    private String houseNumber;
    private String street;
    private String countryCode;
    private String countryName;
    private String wardCode;
    private String wardName;
    private String districtCode;
    private String districtName;
    private String provinceCode;
    private String provinceName;
    private String homePhone;
    private String officePhone;
    private String storeRegisterCode;
    private String job;
    private Integer tierId;
    private String tierName;
    private AddCardWithMemberRegistrationRes addedMemberCard;

    @JsonSerialize(using = SimpleDateSerializer.class)
    private Date registrationDate;
    private String referralCode;
}
