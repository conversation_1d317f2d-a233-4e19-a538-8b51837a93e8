package com.oneid.loyalty.accounting.ops.component;

import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.DateAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.DateTimeAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.NumberAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.TextAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AttributeValueFactory {
    @Autowired
    private List<AttributeValueStrategy<?>> strategies;

    @Autowired
    private AttributeMasterDataRepository attributeMasterDataRepository;

    public AttributeValueStrategy<?> lookup(ConditionAttributeDto type) {
        AttributeValueStrategy<?> attributeValueStrategy, result;
        try {
            List<AttributeValueStrategy<?>> strategiesApplicable = lookup(type.getDataTypeDisplay());
            if (strategiesApplicable.size() == 0) {
                throw new IllegalArgumentException();
            } else if (strategiesApplicable.size() == 1) {
                attributeValueStrategy = strategiesApplicable.get(0);
            } else {
                attributeValueStrategy = strategiesApplicable.stream()
                        .map(ComboboxAttributeValueStrategy.class::cast)
                        .filter(s -> s.isApplicable(type))
                        .findFirst()
                        .orElseThrow(IllegalArgumentException::new);
            }
            if (attributeValueStrategy instanceof TextAttributeValueStrategy) {
                result = new TextAttributeValueStrategy(attributeMasterDataRepository);
            } else if (attributeValueStrategy instanceof DateTimeAttributeValueStrategy) {
                result = new DateTimeAttributeValueStrategy(attributeMasterDataRepository);
            } else if (attributeValueStrategy instanceof DateAttributeValueStrategy) {
                result = new DateAttributeValueStrategy(attributeMasterDataRepository);
            } else if (attributeValueStrategy instanceof NumberAttributeValueStrategy) {
                result = new NumberAttributeValueStrategy(attributeMasterDataRepository);
            } else {
                return attributeValueStrategy;
            }
            result.setConditionAttributeDto(type);
        } catch (Exception e) {
            Log.info(LogData.createLogData()
                    .append("message", "AttributeValueFactory lookup exception")
                    .append("attribute", type.getAttribute())
                    .append("type", type.getAttributeType())
            );
            throw e;
        }
        return result;
    }

    private List<AttributeValueStrategy<?>> lookup(EAttributeDataDisplayType type) {
        return strategies.stream()
                .filter(strategy -> strategy.isApplicable(type)).collect(Collectors.toList());
    }
}