package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardSearchReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardProductionRequestRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpsGiftCardProductionRequestService {
    Long createNewChangeRequest(CreateGiftCardRequestReq req);

    Page<GiftCardProductionRequestRes> searchInReview(EApprovalStatus approvalStatus, Integer fromDate, Integer toDate, Pageable pageable);

    GiftCardProductionRequestRes getDetailInReview(Integer reviewId);

    Page<GiftCardProductionRequestRes> searchAvailable(GiftCardSearchReq req, Pageable pageable);

    GiftCardProductionRequestRes getDetailAvailable(Integer requestId);

    Long update(Integer requestId, UpdateGiftCardRequestReq req);

    void approve(ApprovalReq req);

    GiftCardProductionRequestRes verifyGiftCardTransferring(int businessId, int programId, long batchNo);
}
