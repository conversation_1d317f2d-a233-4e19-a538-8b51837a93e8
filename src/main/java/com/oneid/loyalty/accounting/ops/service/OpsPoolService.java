package com.oneid.loyalty.accounting.ops.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.PoolDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.PoolEnum;
import com.oneid.loyalty.accounting.ops.model.res.PoolRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramPoolRes;
import com.oneid.oneloyalty.common.entity.Pool;

public interface OpsPoolService {
    Page<PoolRes> filterPool(Integer businessId, Integer programId, Integer poolId, Integer currencyId, String status, Integer offset, Integer limit);

    PoolDetailRes getPool(Integer poolId);

    void addPool(PoolCreateReq poolCreateReq);

    void updatePool(Integer poolId, PoolUpdateReq poolUpdateReq);

    List<PoolEnum> getAllActiveByProgram(Integer programId);

    Map<Integer, Pool> getMapById(Collection<Integer> ids);
    
    List<ProgramPoolRes> getSharedPools(Integer programId);
}
