package com.oneid.loyalty.accounting.ops.controller.v1;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.CreateAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.EditAttributeRequestReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeRequestService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping("v1/attribute/member")
@Validated
public class MemberAttributeRequestController extends BaseController {
    @Autowired
    OpsAttributeRequestService opsAttributeRequestService;
    
    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.CREATE }),
    })
    public ResponseEntity<?> requestCreatingTierRequest(@Valid @RequestBody CreateAttributeRequestReq req){
        req.setAttributeType(EAttributeType.MEMBER);
        return success(opsAttributeRequestService.requestCreatingAttributeRequest(req));
    }
    
    @GetMapping("/requests/available")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getAvailableRequests(
            @RequestParam(value = "business_id", required = true) Integer businessId,
            @RequestParam(value = "program_id", required = true) Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit){

        Page<AttributeRequestRes> page = opsAttributeRequestService.getAvailableAttributeRequests(EAttributeType.MEMBER, businessId, programId, code, name, status, approvalStatus, new OffsetBasedPageRequest(offset, limit, null));
        
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
    
    @GetMapping("/requests/in-review")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getInReviewTierRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "fromDate", required = false) Integer fromDate,
            @RequestParam(value = "toDate", required = false) Integer toDate,
            @MakerCheckerOffsetPageable Pageable pageable){
        Page<AttributeRequestRes> page = opsAttributeRequestService.getInReviewAttributeRequests(EAttributeType.MEMBER, approvalStatus, fromDate, toDate, pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}/view")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getAvailableAttributeRequestById(@PathVariable("id") Integer requestId){
        return success(opsAttributeRequestService.getAvailableAttributeRequestById(requestId, EAttributeType.MEMBER));
    }
    
    @GetMapping("/requests/in-review/{id}/view")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getInReviewTierRequestById(@PathVariable("id") Integer reviewId){
        return success(opsAttributeRequestService.getInReviewAttributeRequestById(reviewId, EAttributeType.MEMBER));
    }
    
    @GetMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> getEditTierRequestSetting(@PathVariable("id") Integer requestId){
        return success(opsAttributeRequestService.getEditAttributeRequestSetting(requestId));
    }

    @PostMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestEditingAttributeRequest(@PathVariable("id") Integer requestId, @Valid @RequestBody EditAttributeRequestReq req){
        return success(opsAttributeRequestService.requestEditingAttributeRequest(requestId, req, EAttributeType.MEMBER));
    }

    @GetMapping("/data-type-display")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW }),
    })
    public ResponseEntity<?> getDataType(){
        return success(opsAttributeRequestService.getDataTypeDisplay());
    }
}
