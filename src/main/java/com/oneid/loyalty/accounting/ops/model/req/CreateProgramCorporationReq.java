package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class CreateProgramCorporationReq extends UpdateProgramCorporationReq implements Serializable {

    private static final long serialVersionUID = 648134152463639816L;
    @NotNull(message = "Business id: must not be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull(message = "Program id: must not be null")
    @JsonProperty("program_id")
    private Integer programId;
}
