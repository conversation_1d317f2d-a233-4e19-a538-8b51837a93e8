package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.oneloyalty.common.constant.EAdjustmentType;
import com.oneid.oneloyalty.common.constant.EGenerationTransactionTimeMethod;
import com.oneid.oneloyalty.common.constant.ETransactionBatchType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateTransactionMemberRequestReq extends CreateTransactionBatchRequestReq {

    @NotNull(message = "'id_type' cannot be null")
    private EOpsIdType idType;

    @NotBlank(message = "'id_no' cannot be blank")
    private String idNo;

    private String invoiceNo;

    @NotBlank(message = "'store_code' cannot be blank")
    private String storeCode;

    @NotBlank(message = "'terminal_code' cannot be blank")
    private String terminalCode;

    @Valid
    private EarnData earnData;

    @Valid
    private BurnData burnData;

    @Valid
    private SaleData saleData;

    @Valid
    private AdjustData adjustData;

    private Map<String, String> attributeMap = new HashMap<>();

    @AssertTrue(message = "'transaction_batch_type' not valid'")
    public boolean isTransactionBatchTypeValid() {
        return !Arrays.asList(ETransactionBatchType.REVERT_FULL, ETransactionBatchType.REVERT_PARTIAL).contains(this.getTransactionBatchType());
    }

    @AssertTrue(message = "'earn_data' or 'burn_data' or 'sale_data' or 'adjust_data' cannot be null")
    public boolean isDataExist() {
        if(ETransactionBatchType.EARN.equals(this.getTransactionBatchType())
                && this.getEarnData() == null) {
            return false;
        }
        if(ETransactionBatchType.BURN.equals(this.getTransactionBatchType())
                && this.getBurnData() == null) {
            return false;
        }
        if(ETransactionBatchType.SALE.equals(this.getTransactionBatchType())
                && this.getSaleData() == null) {
            return false;
        }
        if(ETransactionBatchType.ADJUST.equals(this.getTransactionBatchType())
                && this.getAdjustData() == null) {
            return false;
        }
        return true;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class EarnData {
        @NotNull(message = "'gmv' cannot be null")
        private BigDecimal gmv;

        @NotNull(message = "'gross_amount' cannot be null")
        private BigDecimal grossAmount;

        @NotBlank(message = "'currency_code' cannot be blank")
        private String currencyCode;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class BurnData {
        @NotNull(message = "'redeem_point' cannot be null")
        private BigDecimal redeemPoint;

        @NotBlank(message = "'currency_code' cannot be blank")
        private String currencyCode;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class SaleData {
        @NotNull(message = "'gmv' cannot be null")
        private BigDecimal gmv;

        @NotNull(message = "'gross_amount' cannot be null")
        private BigDecimal grossAmount;

        @NotNull(message = "'redeem_point' cannot be null")
        private BigDecimal redeemPoint;

        @NotBlank(message = "'currency_code' cannot be blank")
        private String currencyCode;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class AdjustData {
        @NotNull(message = "'adjust_type' cannot be null")
        private EAdjustmentType adjustType;

        @NotNull(message = "'adjust_point' cannot be null")
        private BigDecimal adjustPoint;

        @NotBlank(message = "'pool_code' cannot be blank")
        private String poolCode;

        @NotBlank(message = "'reason_code' cannot be blank")
        private String reasonCode;
    }

    @AssertTrue(message = "'gen_transaction_time_method' is not valid")
    public boolean isGenTransactionTimeMethodValid() {
        if(EGenerationTransactionTimeMethod.MANUAL.equals(this.getGenTransactionTimeMethod())) {
            return false;
        }
        return true;
    }
}
