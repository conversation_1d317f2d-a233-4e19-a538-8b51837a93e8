package com.oneid.loyalty.accounting.ops.constant;

import java.util.stream.Stream;

import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ESchemeSortingField {
    
    CREATED_AT("created_at", "createdAt");
    
    @JsonValue
    String name;
    
    private String mappingColumn;
    
    public static ESchemeSortingField lookup(String name) {
        if (StringUtils.isEmpty(name))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
    
    public static class ESchemeSortingFieldConverter implements Converter<String, ESchemeSortingField> {
        @Override
        public ESchemeSortingField convert(String source) {
            return ESchemeSortingField.lookup(source);
        }
    }
    
}
