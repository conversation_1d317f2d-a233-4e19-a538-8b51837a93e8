package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import org.springframework.data.domain.Page;

import java.util.List;

public interface VersioningService {
    Page<VersionRes> getVersions(
            String requestCode,
            Integer currentVersion,
            List<EApprovalStatus> approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String checkedBy,
            Integer offset,
            Integer limit
    );
}
