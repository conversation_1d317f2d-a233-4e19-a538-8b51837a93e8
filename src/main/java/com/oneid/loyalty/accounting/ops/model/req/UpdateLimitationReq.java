package com.oneid.loyalty.accounting.ops.model.req;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.*;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import org.springframework.stereotype.Service;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = UpdateLimitationReq.UpdateLimitationReqBuilder.class)
public class UpdateLimitationReq {
    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    @OpsName
    private String name;

    @OpsName
    @Length(max = 512)
    private String description;

    @NotNull(message = "'end_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date endDate;

    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;

    @Valid
    @NotNull(message = "'rules' must not be null")
    @Size(min = 1, max = 100, message = "'rules' size between 1, 100")
    private List<RuleReq> rules;

    @JsonPOJOBuilder(withPrefix = "")
    public static class UpdateLimitationReqBuilder {
    }
}