package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.CardBinMapper;
import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsCardBinService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardBin;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.CardBinService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OpsCardBinImpl implements OpsCardBinService {
    @Autowired
    CardBinService cardBinService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    ProgramRepository programRepository;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsProgramService opsProgramService;

    @Override
    public Page<CardBinRes> filter(Integer businessId, Integer programId, String binCode, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.Direction.DESC, "createdAt");

        SpecificationBuilder<CardBin> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (binCode != null)
            specificationBuilder.add(new SearchCriteria("binCode", binCode, SearchOperation.EQUAL));

        Page<CardBin> cardBins = cardBinService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = cardBins.getContent().stream().map(CardBin::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> programIds = cardBins.getContent().stream().map(CardBin::getProgramId).collect(Collectors.toSet());
        Map<Integer, Program> programbyIds = opsProgramService.getMapById(programIds);

        return new PageImpl<CardBinRes>(cardBins.getContent()
                .stream().map(it -> CardBinRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        programbyIds.get(it.getProgramId())
                )).collect(Collectors.toList()), pageRequest, cardBins.getTotalElements());
    }

    @Override
    public CardBinRes get(Integer id) {
        Optional<CardBin> cardBin = cardBinService.find(id);
        if (!cardBin.isPresent())
            throw new BusinessException(ErrorCode.CARD_BIN_NOT_FOUND, "Card bin not found in business", LogData.createLogData().append("card_bin_id", id));

        return CardBinRes.of(
                cardBin.get(),
                businessRepository.findById(cardBin.get().getBusinessId()).orElse(null),
                programRepository.findById(cardBin.get().getProgramId()).orElse(null)
        );
    }

    @Override
    public void add(CardBinCreateReq cardBinCreateReq) {
        cardBinService.create(CardBinMapper.toCardBinOne(cardBinCreateReq));
    }

    @Override
    public void update(Integer id, CardBinUpdateReq cardBinUpdateReq) {
        CardBin cardBin = cardBinService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CARD_BIN_NOT_FOUND, "Card bin not found in business", LogData.createLogData().append("card_bin_id", id)));
        cardBinService.update(CardBinMapper.toCardBinOne(cardBin, cardBinUpdateReq));
    }

}
