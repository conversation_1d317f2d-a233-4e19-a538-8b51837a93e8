package com.oneid.loyalty.accounting.ops.util;

import java.io.PrintStream;
import java.io.UnsupportedEncodingException;

public class HexUtil {
    public static byte[] stringToByteArray(String s) {
        byte[] ba = new byte[0];
        try {
            return s.getBytes("US-ASCII");
        } catch (UnsupportedEncodingException e) {
        }
        return ba;
    }

    public static int hex2decimal(String s) {
        String digits = "0123456789ABCDEF";
        s = s.toUpperCase();
        int val = 0;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            int d = digits.indexOf(c);

            val = 16 * val + d;
        }


        return val;
    }

    public static String swap(String str) {
        StringBuffer sb = new StringBuffer();
        String[] st = new String[4];
        int k = st.length;
        for (int i = 0; i < str.length(); i += 2) {
            sb.append(str.substring(i, i + 2));
            k -= 1;
            st[k] = str.substring(i, i + 2);
        }
        sb = new StringBuffer();
        for (int i = 0; i < st.length; i++) {
            sb.append(st[i]);
        }


        return sb.toString();
    }

    public static String byteArrayToString(byte[] ba) {
        try {
            return new String(ba, "US-ASCII");
        } catch (UnsupportedEncodingException e) {
        }
        return "";
    }

    public static String byteArrayToString(byte[] ba, int offset, int len) {
        try {
            return new String(ba, offset, len, "US-ASCII");
        } catch (UnsupportedEncodingException e) {
        }
        return "";
    }


    public static int hexValue(int n) {
        if ((n >= 48) && (n <= 57)) {
            n -= 48;
        } else if ((n >= 65) && (n <= 70)) {
            n = n - 65 + 10;
        } else if ((n >= 97) && (n <= 102))
            n = n - 97 + 10;
        else
            n = 0;
        return n;
    }

    public static String hexToString(String s) {
        StringBuffer sb = new StringBuffer();
        int len = s.length() - 1;
        int i;
        try {
            for (i = 0; i < len; ) {
                int n1 = hexValue(s.charAt(i++));
                int n2 = hexValue(s.charAt(i++));
                sb.append((char) (n1 << 4 | n2));
            }
        } catch (Exception localException) {
        }
        return sb.toString();
    }

    public static String hexToNumString(String s) {
        StringBuffer sb = new StringBuffer();
        int len = s.length() - 1;
        int i;
        try {
            for (i = 0; i < len; ) {
                i++;
                sb.append(s.charAt(i++));
            }
        } catch (Exception localException) {
        }
        return sb.toString();
    }


    static char[] hexChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};


    public static String byteToHex2(byte b) {
        int n1 = b >> 4 & 0xF;
        int n2 = b & 0xF;
        String s = new String(String.valueOf(hexChars[n1]) + String.valueOf(hexChars[n2]));
        return s;
    }


    public static String[] hexTextArray = null;

    static {
        hexTextArray = new String['Ā'];
        for (int i = 0; i < 256; i++) {
            hexTextArray[i] = byteToHex2((byte) i);
        }
    }


    public static String byteToHex(byte b) {
        return hexTextArray[(0xFF & b)];
    }


    public static String shortToHex(int d) {
        char[] ca = new char[4];
        for (int i = ca.length - 1; i >= 0; i--) {
            ca[i] = hexChars[(d & 0xF)];
            d >>= 4;
        }
        return String.valueOf(ca);
    }


    public static String intToHex(int d) {
        char[] ca = new char[8];
        for (int i = ca.length - 1; i >= 0; i--) {
            ca[i] = hexChars[(d & 0xF)];
            d >>= 4;
        }

        return String.valueOf(ca);
    }

    public static String intToHex(int d, int bytes) {
        char[] ca = new char[bytes * 2];
        for (int i = ca.length - 1; i >= 0; i--) {
            ca[i] = hexChars[(d & 0xF)];
            d >>= 4;
        }
        return String.valueOf(ca);
    }

    public static String intToHex2(int d, int bytes) {
        char[] ca = new char[bytes * 2];


        for (int i = 0; i < ca.length; i++) {
            ca[i] = hexChars[(d & 0xF)];
            d >>= 4;
        }
        return String.valueOf(ca);
    }

    public static byte[] intToHexBytes(int d, int bytes) {
        byte[] ca = new byte[bytes * 2];
        for (int i = ca.length - 1; i >= 0; i--) {
            ca[i] = ((byte) (d >> 24));
            d >>= 4;
        }
        return ca;
    }


    public static String byteArrayToHex(byte[] ba) {
        if (ba == null) return "";
        int len = ba.length;
        char[] ca = new char[len * 2];
        int i = 0;
        for (int j = 0; i < len; ) {
            int ch = ba[(i++)];
            ca[(j++)] = hexChars[(ch >> 4 & 0xF)];
            ca[(j++)] = hexChars[(ch & 0xF)];
        }
        return String.valueOf(ca);
    }


    public static String byteArrayToHex(byte[] ba, int offset, int len) {
        if (ba == null) return "";
        int len2 = len + offset > ba.length ? ba.length - offset : len;
        char[] ca = new char[len2 * 2];
        len2 += offset;
        int i = offset;
        for (int j = 0; i < len2; ) {
            int ch = ba[(i++)];
            ca[(j++)] = hexChars[(ch >> 4 & 0xF)];
            ca[(j++)] = hexChars[(ch & 0xF)];
        }
        return String.valueOf(ca);
    }


    public static String byteArrayToHexDigit(byte[] ba, int offset, int len) {
        StringBuffer sb = new StringBuffer();
        if (ba != null) {
            int len2 = (offset + len) % 2 + (offset + len) / 2;
            for (int i = offset / 2; i < len2; i++) {
                sb.append(byteToHex(ba[i]));
            }
        }
        return sb.substring(offset % 2, offset % 2 + len);
    }


    public static byte[] hexToByte_old(String s)
            throws NumberFormatException {
        byte[] bytes = new byte[s.length() / 2];

        int i = 0;
        for (int j = 0; i < s.length(); j++) {
            bytes[j] = ((byte) Integer.parseInt(s.substring(i, i + 2), 16));
            i += 2;
        }
        return bytes;
    }


    public static byte[] hexToByte(String s) {
        if (s == null) s = "";
        int len = s.length();
        byte[] ba = new byte[len / 2];
        int i = 0;
        for (int j = 0; i < len; j++) {
            int n1 = hexValue(s.charAt(i++)) << 4;
            int n2 = hexValue(s.charAt(i++));
            ba[j] = ((byte) (n1 | n2));
        }

        return ba;
    }


    public static void print(byte[] ba) {
        StringBuffer sb = new StringBuffer();
        int i;
        try {
            i = 0;
            for (int len = ba.length; i < len; ) {
                sb.append(byteToHex(ba[(i++)]));
                sb.append(' ');
            }
        } catch (Exception localException) {
        }
    }


    public static String stringToHex(String s) {
        if (s == null) return "";
        int len = s.length();
        char[] ca = new char[len * 2];
        byte[] ba = stringToByteArray(s);
        int i = 0;
        for (int j = 0; i < len; ) {
            int ch = ba[(i++)];
            ca[(j++)] = hexChars[(ch >> 4 & 0xF)];
            ca[(j++)] = hexChars[(ch & 0xF)];
        }
        return String.valueOf(ca);
    }


    public static String numStrToHex(String s, int len) {
        if (s == null) s = "";
        StringBuffer sb = new StringBuffer();
        byte[] ba = stringToByteArray(s);
        int len2 = len - s.length();
        for (int i = 0; i < len2; i++)
            sb.append("30");
        for (int i = 0; i < ba.length; i++) {
            sb.append(byteToHex(ba[i]));
        }
        return sb.substring(0, len << 1);
    }


    public static String padLeadingZero(String s, int len) {
        if (s == null) s = "";
        if (len < 0) len = 0;
        char[] ca = new char[len];
        int i = 0;
        for (int len2 = len - s.length(); len2-- > 0; ca[(i++)] = '0') {
        }
        if (s.length() < len)
            len = s.length();
        s.getChars(0, len, ca, i);
        return String.valueOf(ca);
    }


    public static void printFormat16_old(byte[] ba) {
        int count = 0;
        int len = ba.length;
        for (int i = 0; i < len; i++) {
            System.out.print(byteToHex(ba[i]) + " ");
            count++;
            if (count == 8)
                System.out.print(" ");
            if (count >= 16) {
                count = 0;
            }
        }
    }


    public static void printFormat16_2(byte[] ba) {
        int buffsize = 49;
        char[] buff = new char[buffsize];
        int count = 0;
        int len = ba.length;
        int i = 0;
        for (int j = 0; i < len; i++) {
            if (j == 0)
                for (j = buffsize; j > 0; buff[(--j)] = ' ') {
                }
            int ch = ba[i];

            buff[(j++)] = hexChars[(ch >> 4 & 0xF)];
            buff[(j++)] = hexChars[(ch & 0xF)];
            j++;
            count++;
            if (count == 8) {
                j++;
            } else if (count >= 16) {
                j = 0;
                count = 0;
            }
        }
    }


    public static void printFormat16(PrintStream ps, byte[] ba, int offset, int len) {
        int buffsize = 68;
        char[] buff = new char[buffsize];
        int count = 0;

        len += offset;
        if (len > ba.length)
            len = ba.length;
        int i = offset;
        int j = 0;
        for (int k = 51; i < len; i++) {
            if (j == 0)
                for (j = buffsize; j > 0; buff[(--j)] = ' ') {
                }
            int ch = ba[i];
            buff[(j++)] = hexChars[(ch >> 4 & 0xF)];
            buff[(j++)] = hexChars[(ch & 0xF)];

            char ch2 = (char) (ba[i] & 0x7F);
            buff[(k++)] = (Character.isLetterOrDigit(ch2) ? ch2 : '.');

            j++;
            count++;
            if (count == 8) {
                j++;
                k++;
            } else if (count >= 16) {
                j = 0;
                k = 51;
                count = 0;
                ps.println(buff);
            }
        }
        if (count != 0) {
            ps.println(buff);
        }
    }

    public static void printFormat16(StringBuffer sb, byte[] ba, int offset, int len) {
        int buffsize = 68;
        char[] buff = new char[buffsize];
        int count = 0;

        len += offset;
        if (len > ba.length)
            len = ba.length;
        int i = offset;
        int j = 0;
        for (int k = 51; i < len; i++) {
            if (j == 0)
                for (j = buffsize; j > 0; buff[(--j)] = ' ') {
                }
            int ch = ba[i];
            buff[(j++)] = hexChars[(ch >> 4 & 0xF)];
            buff[(j++)] = hexChars[(ch & 0xF)];

            char ch2 = (char) (ba[i] & 0x7F);
            buff[(k++)] = (Character.isLetterOrDigit(ch2) ? ch2 : '.');

            j++;
            count++;
            if (count == 8) {
                j++;
                k++;
            } else if (count >= 16) {
                j = 0;
                k = 51;
                count = 0;
                sb.append(buff);
                sb.append("\n");
            }
        }
        if (count != 0)
            sb.append(buff);
        sb.append("\n");
    }

    public static void printFormat16(byte[] ba) {
        printFormat16(System.out, ba, 0, ba.length);
    }

    public static void printFormat16(byte[] ba, int offset, int pos) {
        StringBuffer sb = new StringBuffer();
        printFormat16(sb, ba, offset, pos);
        System.out.print(sb);
    }

    public static byte[] xor(byte[] b1, byte[] b2) {
        byte[] b = new byte[b1.length];
        for (int i = 0; i < b1.length; i++)
            b[i] = ((byte) (b1[i] ^ b2[i]));
        return b;
    }

    public static byte[] expand16To24(byte[] b1) {
        byte[] b = new byte[24];
        for (int i = 0; i < 16; i++)
            b[i] = b1[i];
        int i = 16;
        for (int j = 0; i < 24; b[(i++)] = b1[(j++)]) {
        }
        return b;
    }
}
