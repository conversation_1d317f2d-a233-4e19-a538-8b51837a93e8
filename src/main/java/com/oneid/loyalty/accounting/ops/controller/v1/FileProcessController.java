package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.SearchControlFileHistoryReq;
import com.oneid.loyalty.accounting.ops.model.res.AuditTrailAutoEarningRes;
import com.oneid.loyalty.accounting.ops.model.res.ControlFileHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.DataFileHistoryRes;
import com.oneid.loyalty.accounting.ops.service.OpsFileProcessService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("v1/file-process")
@Validated
public class FileProcessController extends BaseController {
    @Autowired
    OpsFileProcessService opsFileProcessService;

    @GetMapping("/list")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getControlFileHistoryList(
            @RequestParam(value = "control_file_name", required = false) String controlFileName,
            @RequestParam(value = "status", required = false) EProcessingStatus status,
            @RequestParam(value = "file_types", required = false) List<EFileType> fileTypes,
            @RequestParam(value = "created_at_from", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdAtFrom,
            @RequestParam(value = "created_at_to", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdAtTo,
            @RequestParam(value = "updated_at_from", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date updatedAtFrom,
            @RequestParam(value = "updated_at_to", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date updatedAtTo,
            @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        SearchControlFileHistoryReq req = SearchControlFileHistoryReq.builder()
                .fileName(controlFileName)
                .status(status)
                .fileTypes(fileTypes)
                .createdAtFrom(createdAtFrom)
                .createdAtTo(createdAtTo)
                .updatedAtFrom(updatedAtFrom)
                .updatedAtTo(updatedAtTo)
                .build();

        Page<ControlFileHistoryRes> page = opsFileProcessService.getControlFileHistoryList(req,
                new OffsetBasedPageRequest(offset, limit));

        return success(page, offset, limit);
    }

    @GetMapping("/{id}")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getControlFileHistoryDetail(@PathVariable(name = "id") Long id) {
        return success(opsFileProcessService.getControlFileHistoryDetail(id));
    }

    @GetMapping("/{id}/data-file")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getDataFileHistoryList(@PathVariable(name = "id") Long id,
                                                    @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
                                                    @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {
        Page<DataFileHistoryRes> page = opsFileProcessService.getDataFileHistoryList(id, new OffsetBasedPageRequest(offset, limit));
        return success(page, offset, limit);
    }

    @GetMapping("/{id}/failed-transactions")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getFailedTransactionList(@PathVariable(name = "id") Long id,
                                                      @RequestParam(value = "offset", defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
                                                      @RequestParam(value = "limit", defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit) {
        Page<AuditTrailAutoEarningRes> page = opsFileProcessService.getFailedTransactionList(id, new OffsetBasedPageRequest(offset, limit));
        return success(page, offset, limit);
    }

    @PutMapping("/{id}/mark-ignored")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> ignoreControlFile(@PathVariable(name = "id") Long id) {
        opsFileProcessService.ignoreControlFile(id);
        return success(null);
    }

    @GetMapping("/{id}/export")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.CREATE}),
    })
    public ResponseEntity<?> exportFailedTransactionList(@PathVariable("id") Long id) {
        ResourceDTO dto = opsFileProcessService.exportFailedTransaction(id);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/{id}/retry-history")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getRetryHistoryList(@PathVariable("id") Long id) {
        return success(opsFileProcessService.getRetryHistoryList(id));
    }

    @PostMapping("/{id}/retry")
    @Authorize(role = AccessRole.TRANSACTION_FILE_PROCESS, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> retryControlFile(@PathVariable(name = "id") Long id) {
        opsFileProcessService.retryControlFile(id);
        return success(null);
    }
}
