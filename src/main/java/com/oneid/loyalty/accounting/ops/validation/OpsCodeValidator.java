package com.oneid.loyalty.accounting.ops.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class OpsCodeValidator implements ConstraintValidator<OpsCode, String> {

    private String message;

    private static final String EN_CHARS = "a-zA-Z" + "\\s";
    private static final String NUMBERS = "0-9" + "\\s";

    private static final Pattern CHAR_PATTERN = Pattern.compile("^[" + EN_CHARS + NUMBERS + "]+$");
    private static final Pattern CHAR_REPEATED_PATTERN = Pattern.compile("^.*(\\S)\\1{2,}.*$");

    @Override
    public void initialize(OpsCode constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        if (!CHAR_PATTERN.matcher(value).matches()) {
            return false;
        }

        return !CHAR_REPEATED_PATTERN.matcher(value).matches();
    }
}
