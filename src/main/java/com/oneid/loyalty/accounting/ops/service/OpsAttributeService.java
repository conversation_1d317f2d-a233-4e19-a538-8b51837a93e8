package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeMasterDataVerifyReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.AttributeServiceRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramTransactionAttributeRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface OpsAttributeService {
    List<ProgramTransactionAttributeRes> findByProgramId(Integer programId);

    void createAttribute(AttributeCreateReq req, EMakerCheckerType type);

    ResourceDTO createAttributeWithExcel(AttributeCreateReq req, MultipartFile file, EMakerCheckerType type) throws Exception;

    ResourceDTO verifyAttributeMasterDataExcel(AttributeMasterDataVerifyReq req, MultipartFile file) throws Exception;

    AttributeRequestRes getInReviewDetail(Integer reviewId, EMakerCheckerType type);

    Page<AttributeRequestRes> getInReviews(EApprovalStatus approvalStatus,
                                           String fromCreatedAt,
                                           String toCreatedAt,
                                           String fromReviewedAt,
                                           String toReviewedAt,
                                           String createdBy,
                                           String reviewedBy,
                                           Integer offset,
                                           Integer limit,
                                           EMakerCheckerType type);

    void approveRequest(ApprovalReq req, EMakerCheckerType type);

    List<AttributeCombobox> getMasterData(Integer programId, String attributeCode, EAttributeType type);

    AttributeRequestRes getAvailableDetail(Integer id, EAttributeType type);

    ResourceDTO exportMasterData(Integer id, EAttributeType type);

    ResourceDTO exportInReviewMasterData(Integer id, EMakerCheckerType type);

    Page<AttributeRequestRes> getTransactionAvailable(Integer businessId,
                                                      Integer programId,
                                                      String code,
                                                      String name,
                                                      Boolean havingMasterData,
                                                      Date createdStart,
                                                      Date createdEnd,
                                                      ECommonStatus status,
                                                      Pageable pageable);

    Page<AttributeRequestRes> getMemberAvailable(Integer businessId,
                                                 Integer programId,
                                                 String code,
                                                 String name,
                                                 Boolean havingMasterData,
                                                 Date createdStart,
                                                 Date createdEnd,
                                                 ECommonStatus status,
                                                 Pageable pageable);

    List<AttributeServiceRes> getAttributeService(Integer id, EAttributeType type);
}