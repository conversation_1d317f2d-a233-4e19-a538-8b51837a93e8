package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.StoreUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Store;

import java.util.Date;

public class StoreMapper {
    public static Store toStoreOne(Store store, StoreUpdateReq request) {
        store.setName(request.getName());
        store.setDescription(request.getDescription());
        store.setServiceStartDate(new Date(request.getServiceStartDate() * 1000));
        store.setServiceEndDate(new Date(request.getServiceEndDate() * 1000));
        store.setContactPerson(request.getContactPerson());
        store.setEmailAddress(request.getEmailAddress());
        store.setAddress1(request.getAddress1());
        store.setAddress2(request.getAddress2());
        store.setCountryId(request.getCountryId());
        store.setProvinceId(request.getProvinceId());
        store.setDistrictId(request.getDistrictId());
        store.setWardId(request.getWardId());
        store.setPostalCode(request.getPostalCode());
        store.setPhoneNo(request.getPhoneNo());
        store.setWebSite(request.getWebsite());
        store.setStatus(ECommonStatus.of(request.getStatus()));
        store.setCardStockLimit(request.getCardStockLimit());
        return store;
    }
}
