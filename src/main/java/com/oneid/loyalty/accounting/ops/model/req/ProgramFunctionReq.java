package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.res.BaseProgramRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramFunctionInfo;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class ProgramFunctionReq extends BaseProgramRes implements Serializable {

    private static final long serialVersionUID = 7905839796267523488L;

    @NotNull(message = "Program id: must not be null")
    @JsonProperty("program_id")
    private Integer programId;

    @NotEmpty(message = "Functions: must not be blank")
    @JsonProperty("functions")
    private Set<String> functions = new HashSet<>();

    @Valid
    @JsonProperty("profile_attributes")
    private Set<ProgramFunctionInfo.ProfileAttribute> profileAttributes = new HashSet<>();
}
