package com.oneid.loyalty.accounting.ops.util;


import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;

public class TripleDESUtil {
    private static final String ALGORITHM = "DESede";

    public static byte[] decrypt(String s, byte[] abyte0)
            throws Exception {
        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");

        SecretKey secretkey = keyFactory.generateSecret(new DESedeKeySpec(padKey(s).getBytes()));
        IvParameterSpec ivparameterspec = new IvParameterSpec(cbc_iv);
        cipher.init(2, secretkey, ivparameterspec);
        return cipher.doFinal(abyte0);
    }

    public static byte[] encrypt(String s, String s1)
            throws Exception {
        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");

        SecretKey secretkey = keyFactory.generateSecret(new DESedeKeySpec(padKey(s).getBytes()));

        IvParameterSpec ivparameterspec = new IvParameterSpec(cbc_iv);
        cipher.init(1, secretkey, ivparameterspec);
        return cipher.doFinal(padData(s1).getBytes());
    }

    private static String padData(String s) {
        int i = s.length();
        int j = i % 8;
        if (j > 0)
            j = 8 - j;
        String s1 = " ";
        for (int k = 0; k < j; k++) {
            s = s + s1;
        }
        return s;
    }

    private static String padKey(String s) {
        int i = s.length();
        if (i < 24) {
            StringBuffer stringbuffer = new StringBuffer(s);
            int j = 0;
            while (stringbuffer.length() < 24) {
                stringbuffer.append(j++);
                if (j == 10)
                    j = 0;
            }
            return stringbuffer.toString();
        }
        if (i > 24) {
            String s1 = s.substring(0, 24);
            return s1;
        }
        return s;
    }


    static byte[] cbc_iv = {-2, -36, -70, -104, 118, 84, 50, 16};

    public static byte[] decrypt(String s, byte[] abyte0, byte[] cbc_iv) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");

            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");

            SecretKey secretkey = keyFactory.generateSecret(new DESedeKeySpec(
                    padKey(s).getBytes()));

            IvParameterSpec ivparameterspec = new IvParameterSpec(cbc_iv);
            cipher.init(2, secretkey, ivparameterspec);
            return cipher.doFinal(abyte0);
        } catch (NoSuchAlgorithmException e) {
            throw e;
        }
    }

    public static byte[] encrypt(String s, String s1, byte[] cbc_iv) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");

            SecretKey secretkey = keyFactory.generateSecret(new DESedeKeySpec(padKey(s).getBytes()));

            IvParameterSpec ivparameterspec = new IvParameterSpec(cbc_iv);
            cipher.init(1, secretkey, ivparameterspec);
            return cipher.doFinal(padData(s1).getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw e;
        }
    }

    public static String encryptByGivenKey(String key, String encryptData) throws Exception {
        byte[] cbc_iv = null;
        String encKey = null;
        String sEncryptPassword = null;
        try {
            if ((key != null) && (encryptData != null)) {
                String hashKey = HexUtil.byteArrayToHex(OECrypt.encrypt(key));
                if ((hashKey != null) && (hashKey.length() == 32)) {
                    encKey = hashKey.substring(0, 24);
                    cbc_iv = hashKey.substring(24, 32).getBytes();
                    byte[] arrEncrypted = encrypt(encKey, encryptData, cbc_iv);
                    sEncryptPassword = encodeByteArray(arrEncrypted);
                    sEncryptPassword = sEncryptPassword.trim();
                    sEncryptPassword = sEncryptPassword.substring(1, sEncryptPassword.length() - 2);
                }

            }

        } catch (Exception e) {
            throw e;
        }
        return sEncryptPassword;
    }

    public static byte[] Base64Encode(byte[] var0) throws IOException {
        ByteArrayOutputStream var1 = new ByteArrayOutputStream();
        Base64OutputStream var2 = new Base64OutputStream(var1);

        try {
            var2.write(var0);
            var2.flush();
        } catch (IOException var4) {
            throw new IOException(var4);
        }

        return var1.toByteArray();
    }

    public static String encodeByteArray(byte[] var0) throws IOException {
        byte[] var1 = Base64Encode(var0);
        return a(var1);
    }
    public static byte[] decodeByteArray(String var0) throws RuntimeException {
        try {
            return Base64Decode(toASCIIBytes(var0));
        } catch (IOException var2) {
            throw new RuntimeException("Fatal decoding error: " + var2);
        }
    }
    private static String a(byte[] var0) {
        try {
            BufferedReader var1 = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(var0)));
            ByteArrayOutputStream var2 = new ByteArrayOutputStream();
            PrintWriter var3 = new PrintWriter(var2);
            String var4 = null;

            while(true) {
                String var5 = var1.readLine();
                if (var5 == null) {
                    var3.println("\"" + var4 + "\";");
                    var3.flush();
                    return new String(var2.toByteArray());
                }

                if (var4 != null) {
                    var3.println("\"" + var4 + "\" +");
                }

                var4 = var5;
            }
        } catch (IOException var6) {
            return null;
        }
    }
    public static byte[] toASCIIBytes(String var0) {
        return var0.getBytes(StandardCharsets.US_ASCII);
    }

    public static String decryptByGivenKey(String key, String decryptData) throws Exception {
        byte[] cbc_iv = null;
        String encKey = null;
        String sOriginalPassword = null;
        try {
            if ((key != null) && (decryptData != null)) {
                String hashKey = HexUtil.byteArrayToHex(OECrypt.encrypt(key));
                if ((hashKey != null) && (hashKey.length() == 32)) {
                    encKey = hashKey.substring(0, 24);
                    cbc_iv = hashKey.substring(24, 32).getBytes();
                    byte[] arrDecrypted = decrypt(encKey, decodeByteArray(decryptData), cbc_iv);
                    sOriginalPassword = new String(arrDecrypted);
                }

            }

        } catch (Exception e) {
            throw e;
        }
        return sOriginalPassword;
    }

    public static byte[] Base64Decode(byte[] var0) throws IOException {
        Base64InputStream var1 = new Base64InputStream(new ByteArrayInputStream(var0));
        ByteArrayOutputStream var2 = new ByteArrayOutputStream();
        byte[] var3 = new byte[1024];

        int var4;
        try {
            while ((var4 = var1.read(var3)) > 0) {
                var2.write(var3, 0, var4);
            }
        } catch (IOException var5) {
            throw new IOException(var5.getMessage());
        }

        return var2.toByteArray();
    }
//    public static void main(String[] args)
//            throws Exception
//    {
//        String sEncryptPassword = null;
//        byte[] arrEncrypted = encrypt("ladfioaerealerlclad98162", "password", cbc_iv);
//        sEncryptPassword = Util.encodeByteArray(arrEncrypted);
//        sEncryptPassword = sEncryptPassword.trim();
//
//        sEncryptPassword = sEncryptPassword.substring(1, sEncryptPassword.length() - 2);
//
//
//        byte[] arrDecrypted = decrypt("ladfioaerealerlclad98162", Util.decodeByteArray("4BvJQMJr3nc="), cbc_iv);
//        String sOriginalPassword = new String(arrDecrypted);
//    }
}