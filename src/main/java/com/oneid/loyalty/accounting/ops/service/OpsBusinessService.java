package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.BusinessDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.BusinessRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.CreateBusinessMCReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.UpdateBusinessMCReq;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.exception.BusinessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface OpsBusinessService {
    List<BusinessDropDownRes> getAll(SpecificationBuilder<Business> specification);

    /**
     * Search businesses.
     */
    Page<BusinessRes> search(SpecificationBuilder<Business> specification, Pageable pageable);

    AbsGetApproveRes getApproveCreate(CreateBusinessMCReq request);

    /**
     * Get one business.
     *
     * @throws BusinessException if the entity is not found
     */
    AbsGetApproveRes getApproveUpdate(UpdateBusinessMCReq request);

    BusinessRes getOne(Integer id);

    Map<Integer, Business> getMapById(Collection<Integer> ids);
}
