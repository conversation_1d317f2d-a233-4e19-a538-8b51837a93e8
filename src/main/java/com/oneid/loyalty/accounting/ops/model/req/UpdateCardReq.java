package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.Max;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@EqualsAndHashCode
public class UpdateCardReq {
	
	@JsonProperty("id")
	private Integer id;
	
	@JsonProperty("business_code")
	private String businessCode;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("card_type_id")
    private Integer cardTypeId;

    @JsonProperty("store_code")
    private String storeCode;
    
    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;
    
    @JsonProperty("status")
    @Pattern(regexp = "^(A|I|B|C)?$", message = "'Status' Only accept A/I/B/C values")
    private String status;
    
    
    @JsonProperty("card_status")
    private String cardStatus;
    
    @JsonProperty("code_block")
    @Max(8)
    private Integer codeBlock;
    
    @JsonProperty("card_no")
	private String cardNo;

    @Size(max = 150, message = "'Remark' cannot exceed 150 characters")
    @JsonProperty("remark")
    private String remark;
    
}
