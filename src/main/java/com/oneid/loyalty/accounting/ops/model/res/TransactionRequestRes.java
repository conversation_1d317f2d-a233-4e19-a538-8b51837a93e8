package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ERequestProcessStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Setter
@Builder
public class TransactionRequestRes {

    private String txnRefNo;

    private String invoiceNo;

    private String originalInvoiceNo;

    private String corporationCode;

    private String chainCode;

    private String storeCode;

    private String terminalCode;

    private Long memberId;

    private String memberCode;

    private String productAccountType;

    private String productAccountCode;

    private Date transactionTime;

    private String transactionType;

    private BigDecimal gmv;

    private BigDecimal grossAmount;

    private BigDecimal nettAmount;

    private BigDecimal awardPoint;

    private BigDecimal redeemPoint;

    private String poolCode;

    private String currencyCode;

    private BigDecimal pointBalanceBefore;

    private BigDecimal pointBalanceAfter;

    private BigDecimal awardPointBeforeLimit;

    private Date awardRetentionTime;

    private String description;

    private boolean cancellation;

    private Date cancellationTime;

    private String cancellationType;

    private String reasonCode;

    private String schemeCode;

    private String channel;

    private String serviceCode;

    private Integer errorCode;

    private String errorMessage;

    private ERequestProcessStatus processStatus;

    private String smsErrorCode;

    private String smsErrorMessage;
}
