package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Card;
import com.oneid.oneloyalty.common.util.DateTimes;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
public class UpdateCardRes {
    @JsonProperty("card_no")
    private String cardNo;

    @JsonProperty("card_code")
    private String cardCode;

    @JsonProperty("status")
    private String cardStatus;

    @JsonProperty("card_name")
    private String cardName;

    @JsonProperty("expired_date")
    private Long expiredDate;

}
