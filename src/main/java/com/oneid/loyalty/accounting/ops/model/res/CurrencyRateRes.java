package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.CurrencyRate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
@AllArgsConstructor
public class CurrencyRateRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @JsonProperty("currency_name")
    private String currencyName;

    @JsonProperty("base_currency_id")
    private Integer baseCurrencyId;

    @JsonProperty("base_currency_name")
    private String baseCurrencyName;

    @JsonProperty("buy_rate")
    private double buyRate;

    @JsonProperty("sell_rate")
    private double sellRate;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    public static CurrencyRateRes of(CurrencyRate currencyRate, Business business, Currency baseCurrency, Currency currency) {
        return new CurrencyRateRes(
                currencyRate.getId(),
                currencyRate.getCurrencyId(),
                currency != null ? currency.getName() : null,
                currencyRate.getBaseCurrencyId(),
                baseCurrency != null ? baseCurrency.getName() : null,
                currencyRate.getBuyRate(),
                currencyRate.getSellRate(),
                currencyRate.getStartDate() != null ? currencyRate.getStartDate().toInstant().getEpochSecond() : null,
                currencyRate.getEndDate() != null ? currencyRate.getEndDate().toInstant().getEpochSecond() : null,
                currencyRate.getStatus().getValue(),
                currencyRate.getCreatedBy(),
                currencyRate.getUpdatedBy(),
                currencyRate.getApprovedBy(),
                currencyRate.getCreatedAt() != null ? currencyRate.getCreatedAt().toInstant().getEpochSecond() : null,
                currencyRate.getUpdatedAt() != null ? currencyRate.getUpdatedAt().toInstant().getEpochSecond() : null,
                currencyRate.getApprovedAt() != null ? currencyRate.getApprovedAt().toInstant().getEpochSecond() : null,
                currencyRate.getCreatedYmd(),
                currencyRate.getBusinessId(),
                business != null ? business.getName() : null
        );
    }
}
