package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SAPSaleOrderCreateFeignRes {
    @JsonProperty("MT_VID_OE_SO_Create_RES")
    private Data data;

    @Getter
    @Setter
    public class Data {
        @JsonProperty("SALESORDER")
        private String saleOrder;

        @JsonProperty("STATUS")
        private String status;

        @JsonProperty("DESCRIPTION")
        private String description;
    }
}
