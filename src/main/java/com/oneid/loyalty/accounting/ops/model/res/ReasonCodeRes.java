package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ReasonCodeRes {
    private Integer id;
    private Integer businessId;
    private Integer programId;
    private Integer functionCodeId;
    private String code;
    private String name;
    private String enName;
    private String description;
    private ECommonStatus status;
    private String createdBy;
    private String updatedBy;
    private String approvedBy;
    private Long createdAt;
    private Long updatedAt;
    private Long approvedAt;
    private Long createdYmd;
}
