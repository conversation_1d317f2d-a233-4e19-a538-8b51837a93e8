package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardBinRes;
import org.springframework.data.domain.Page;

public interface OpsGiftCardBinService {
    Page<GiftCardBinRes> filter(Integer businessId, Integer programId, String binCode, Integer offset, Integer limit);

    GiftCardBinRes get(Integer id);

    void add(GiftCardBinCreateReq giftCardBinCreateReq);

    void update(Integer id, GiftCardBinUpdateReq GiftCardBinUpdateReq);
}
