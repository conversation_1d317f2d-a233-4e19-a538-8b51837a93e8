package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.scheme.SchemeFormulaAnnotation;
import com.oneid.oneloyalty.common.constant.EFormulaAttribute;
import com.oneid.oneloyalty.common.constant.EFormulaType;
import com.oneid.oneloyalty.common.constant.EFormulaUnitType;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@SchemeFormulaAnnotation(maxPercentValue = 999, minPercentValue = 0)
@EqualsAndHashCode
public class FormulaRecordReq {
    @JsonIgnore
    private EFormulaAttribute attribute;

    @JsonProperty("formula_id")
    private Integer formulaId;

    @JsonProperty("scheme_id")
    private Integer schemeId;

    @NotNull(message = "formula_type must not be null")
    @JsonProperty("formula_type")
    private EFormulaType formulaType;

    @JsonProperty("formula_unit_type")
    private EFormulaUnitType formulaUnitType;

    @Valid
    @JsonProperty("factor_list")
    @EqualsAndHashCode.Exclude
    private List<FormulaFactor> factorList = new ArrayList<>();

    private Long n;

    private Long d;

    @Getter
    @Setter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class FormulaFactor {
        public FormulaFactor() {}
        @NotNull(message = "amount_from: must be not null")
        @JsonProperty("amount_from")
        private Long amountFrom;

        @NotNull(message = "amount_to: must be not null")
        @JsonProperty("amount_to")
        private Long amountTo;

        @NotNull(message = "value: must be not null")
        private Double value;
    }
}