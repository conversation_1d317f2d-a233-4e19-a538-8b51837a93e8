package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.RuleRecordReq;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SchemeRuleMapper {

    public static SchemeRule toSchemeRule(RuleRecordReq req) {
        SchemeRule schemeRule = new SchemeRule();
        schemeRule.setConditionLogic(req.getConditionLogic());
        schemeRule.setSeqNo(req.getSeqNo());
        schemeRule.setSchemeId(req.getSchemeId());
        schemeRule.setName(req.getName());
        schemeRule.setDescription(req.getDescription());
        return schemeRule;
    }
}