package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ReverseTransactionFeignReq {
    CustomerFeignReq customerIdentifier;
    
    @JsonProperty("business_id")
    private String businessCode;

    @JsonProperty("program_id")
    private String programCode;

    private String originalInvoiceNo;
    
    private String serviceCode;

    @Setter
    @JsonProperty("currency_id")
    private String currencyCode;
    
    @JsonProperty("new_txn_info")
    private ReverseTransactionInfoFeignReq transaction;

    @JsonProperty("created_by")
    private String createdBy;
}
