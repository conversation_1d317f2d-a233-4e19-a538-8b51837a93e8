package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CreateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.res.BusinessRes;
import com.oneid.loyalty.accounting.ops.model.req.UpdateBusinessReq;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.CreateBusinessMCReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.GetApproveBusinessMCService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.UpdateBusinessMCReq;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("v1/business")
@Validated
public class BusinessController extends BaseController {

    @Autowired
    OpsBusinessService opsBusinessService;

    @GetMapping("all")
    public ResponseEntity<?> getAll(@RequestParam(value = "status", required = false) ECommonStatus status) {
        SpecificationBuilder specification = new SpecificationBuilder();

        if(status != null)
            specification.add(new SearchCriteria("status", status, SearchOperation.EQUAL));

        return success(opsBusinessService.getAll(specification));
    }

    @GetMapping("search")
    @Authorize(role = AccessRole.BUSINESS, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getAll(
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "status", required = false) String status,
            @Valid @RequestParam(value = "offset", required = false, defaultValue = "20") @Min(0) Integer offset,
            @Valid @RequestParam(value = "limit", required = false, defaultValue = "20") @Min(1) @Max(200) Integer limit) {

        SpecificationBuilder specification = new SpecificationBuilder();
        if(StringUtils.isNoneBlank(status)) {
            specification.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));
        }

        if(StringUtils.isNoneBlank(code)) {
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));
        }

        if(StringUtils.isNoneBlank(name)) {
            specification.add(new SearchCriteria("name", name, SearchOperation.EQUAL));
        }

        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit);

        Page<BusinessRes> result = opsBusinessService.search(specification, pageRequest);
        return success(result.getContent(), offset, result.getNumberOfElements(), (int) result.getTotalElements());
    }

    @PostMapping("change")
    @Authorize(role = AccessRole.BUSINESS, permissions = {AccessPermission.CREATE })
    public AbsGetApproveRes getApproveForCreate(@RequestBody @Valid CreateBusinessReq request,
                                                @RequestHeader("Authorization") String token) {
        CreateBusinessMCReq createBusinessMCReq = new CreateBusinessMCReq();
        createBusinessMCReq.setPayload(request);
        createBusinessMCReq.setBearerToken(token);
        return this.opsBusinessService.getApproveCreate(createBusinessMCReq);
    }

    @PutMapping("change/{business_id}")
    @Authorize(role = AccessRole.BUSINESS, permissions = {AccessPermission.EDIT })
    public AbsGetApproveRes getApproveForUpdate(@RequestBody @Valid UpdateBusinessReq request,
                                                @PathVariable("business_id") Integer businessId,
                                                @RequestHeader("Authorization") String token) {
        request.setBusinessId(businessId);
        UpdateBusinessMCReq updateBusinessMCReq = new UpdateBusinessMCReq();
        updateBusinessMCReq.setPayload(request);
        updateBusinessMCReq.setBearerToken(token);
        return this.opsBusinessService.getApproveUpdate(updateBusinessMCReq);
    }


    @GetMapping("{id}")
    @Authorize(role = AccessRole.BUSINESS, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getOne(@PathVariable("id") Integer id) {
        return success(this.opsBusinessService.getOne(id));
    }
}
