package com.oneid.loyalty.accounting.ops.support.web.acl;

import java.util.Arrays;
import java.util.stream.Stream;

import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.support.web.security.exception.ForbiddenException;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

public class ACLMethodSecurityInterceptor implements MethodInterceptor, BeanPostProcessor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        OPSAuthenticatedPrincipal principal = (OPSAuthenticatedPrincipal) authentication.getPrincipal();
        
        Authorize[] authorizes = null;
        Boolean any;
        OpsErrorCode errorCode;
        
        if(invocation.getMethod().isAnnotationPresent(AuthorizeGroup.class)) {
            AuthorizeGroup authorizeGroup = invocation.getMethod().getAnnotation(AuthorizeGroup.class);
            authorizes =  authorizeGroup.authorize();
            
            any = authorizeGroup.any();
            errorCode = authorizeGroup.errorCode();
        } else {
            Authorize authorize = invocation.getMethod().getAnnotation(Authorize.class);
            authorizes = new Authorize [] { authorize };
            
            any = false;
            errorCode = authorize.errorCode();
        }
        
       long matched = Arrays.asList(authorizes)
        .stream()
        .map(authorize -> verify(authorize, principal, errorCode))
        .filter(verified -> verified)
        .count();
       
       if(matched == 0 || (matched < authorizes.length && !any))
           throw ForbiddenException.builder()
           .errorCode(errorCode)
           .arguments(authorizes)
           .build();
       
        return invocation.proceed();
    }
    
    private boolean verify(Authorize authorize, OPSAuthenticatedPrincipal principal, OpsErrorCode errorCode) {
        Long availableCodes = principal.getPermissions().get(authorize.role());
        
        if(availableCodes == null)
            return false;
        
        Long checkCodes = Stream.of(authorize.permissions())
                .mapToLong(AccessPermission::getCode)
                .sum();
        
        return !(checkCodes > 0 && (checkCodes & availableCodes) != checkCodes);
    }
}
