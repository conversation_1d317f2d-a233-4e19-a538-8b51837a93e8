package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.oneid.loyalty.accounting.ops.model.res.WardDTO;
import com.oneid.loyalty.accounting.ops.service.OpsWardService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Province;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CountryRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.ProvinceRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsWardServiceImpl implements OpsWardService {
    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private WardRepository wardRepository;

    @Autowired
    private DistrictRepository districtRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Override
    public WardDTO getOne(Integer id) {
        final Ward ward = this.wardRepository
                .findById(id)
                .orElseThrow(() -> new BusinessException(
                        ErrorCode.WARD_NOT_FOUND, "Ward is not found",
                        LogData.createLogData().append("ward", id)));
        return WardDTO.valueOf(ward);
    }

    @Override
    public List<Ward> getByDistrict(Integer districtId) {
        return wardRepository.findByDistrictIdAndStatusAndNewStatus(districtId, ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
    }

    @Override
    public List<Ward> getByDistrict(String countryCode, String provinceCode, String districtCode) {
        final Optional<Country> country = this.countryRepository.findByCode(countryCode);
        if(!country.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        final Optional<Province> province = this.provinceRepository
                .findByCountryIdAndCode(country.get().getId(), provinceCode);
        if(!province.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        final Optional<District> district = this.districtRepository
                .findByProvinceIdAndCode(province.get().getId(), districtCode);
        if(!district.isPresent()) {
            return Collections.EMPTY_LIST;
        }

        return wardRepository.findByDistrictIdAndStatusAndNewStatus(district.get().getId(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
    }
}
