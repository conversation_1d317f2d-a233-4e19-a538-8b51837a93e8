package com.oneid.loyalty.accounting.ops.datasource.checkout.entity;

import com.oneid.oneloyalty.common.constant.ECancellation;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name = "transaction_redeem_info")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionRedeemInfo implements Serializable {

    private static final long serialVersionUID = -3177605407695917489L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "transaction_ref")
    private String transactionRef;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "redeem_point")
    private BigDecimal redeemPoint;

    @Column(name = "sell_rate")
    private BigDecimal sellRate;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "status")
    @Convert(converter = ETransactionStatus.Converter.class)
    private ETransactionStatus status;

    @Column(name = "cancellation")
    @Convert(converter = ECancellation.Converter.class)
    private ECancellation cancellation;

    @Column(name = "cancellation_time")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date cancellationTime;

    @Column(name = "cpm_transaction_ref")
    private String cpmTransactionRef;
}
