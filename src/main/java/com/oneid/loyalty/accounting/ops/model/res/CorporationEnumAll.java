package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Corporation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class CorporationEnumAll {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    public static CorporationEnumAll of(Corporation corporation) {
        return new CorporationEnumAll(
                corporation.getId(),
                corporation.getCode(),
                corporation.getName(),
                corporation.getStatus().getValue()
        );
    }
}
