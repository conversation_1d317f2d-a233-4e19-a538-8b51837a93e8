package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TierPrivilegeReq {
    private Integer id;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    private String name;

    @Length(max = 4000)
    private String description;

    @Length(max = 255)
    private String enName;

    private Integer partnerSupplierId;

    private String partnerSupplierName;

    @NotBlank(message = "'brand' must not be blank")
    private String brand;

    private String action;

    @Length(max = 4000)
    private String enDescription;

    @NotNull(message = "'display_order' must not be null")
    private Integer displayOrder;

    @Length(max = 2048, message = "'icon' length must be between 1 and 2048")
    private String icon;

    private String status;

    private boolean archive;
}
