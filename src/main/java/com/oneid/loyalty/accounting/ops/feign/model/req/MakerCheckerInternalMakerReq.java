package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MakerCheckerInternalMakerReq<T> implements Serializable {
    private static final long serialVersionUID = 8581127913877422898L;

    private String requestType;

    private String requestName;

    private String requestCode;

    private T payload;
}