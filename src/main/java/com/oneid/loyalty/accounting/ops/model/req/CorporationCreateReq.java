package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.validation.PersonName;
import com.oneid.oneloyalty.common.validation.Address;
import com.oneid.oneloyalty.common.validation.ValidMeta;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Getter
@Setter
public class CorporationCreateReq {
    @NotNull(message = "'BusinessId' cannot be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @Size(max = 16, message = "'Code' cannot exceed 16 characters")
    @NotBlank(message = "'Code' cannot be empty")
    @Pattern(regexp = "^[a-zA-Z0-9]{0,16}$", message = "'Code' is invalid")
    private String code;

    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @NotBlank(message = "'Name' cannot be empty")
    @ValidMeta(message = "'Name' is invalid")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    @Pattern(regexp = "^[ a-zA-Z0-9]{0,255}$", message = "'Description' is invalid")
    private String description;

    @NotNull(message = "'Service start date' cannot be null")
    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @NotNull(message = "'Service end date' cannot be null")
    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @JsonProperty("service_registration_no")
    private String serviceRegistrationNo;

    @NotBlank(message = "'Settlement mode' cannot be empty")
    @Pattern(regexp = "^(AUTO|BATCH_RECON)?$", message = "'Settlement mode' Only accept AUTO | BATCH_RECON values")
    @JsonProperty("settlement_mode")
    private String settlementMode;

    @Min(0)
    @JsonProperty("card_limit_in_stock")
    private Integer cardLimitInStock;

    @JsonProperty("tax_identification_number")
    private Integer taxIdentificationNumber;

//    @NotNull(message = "'Contact person' is required")
    @PersonName(message = "'Contact person' is invalid")
    @JsonProperty("contact_person")
    private String contactPerson;

    @Email(message = "Invalid email format")
    @JsonProperty("email_address")
    private String emailAddress;

    @Address(message = "'Address 1' is invalid")
    @Size(min = 2, message = "'Address 1' min length is 2")
    @JsonProperty("address1")
    private String address1;

    @Address(message = "'Address 2' is invalid")
    @Size(min = 2, message = "'Address 2' min length is 2")
    @JsonProperty("address2")
    private String address2;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @Size(max =16,message = "'Postal code' cannot exceed 16 characters")
    @Pattern(regexp = "^[0-9]{0,16}$", message = "'Postal code' is invalid")
    @JsonProperty("postal_code")
    private String postalCode;

    @Pattern(regexp = "^0\\d{9,10}|84\\d{9,11}$", message = "Phone is invalid")
    @JsonProperty("phone_no")
    private String phoneNo;

    @Pattern(regexp = "(^$)|(^[A-Z0-9a-z./:-]+$)", message = "Website is invalid")
    @JsonProperty("website")
    private String website;

    @NotBlank(message = "'Status' cannot be empty")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;
}
