package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.feign.model.res.GiftCardTransferAvailableListRes;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferringInReviewRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardTransferService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EProcessStatus;
import com.oneid.oneloyalty.common.constant.ESendVia;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;

@RestController
@RequestMapping("v1/gift-card-transfer/requests")
public class GiftCardTransferController extends BaseController {

    @Autowired
    private OpsGiftCardTransferService opsGiftCardTransferService;

    @Autowired
    private OpsGiftCardProductionRequestService opsGiftCardProductionRequestService;

    @PostMapping
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createNewChangeRequest(@RequestBody CreateGiftCardTransferReq request) {
        return success(
                opsGiftCardTransferService.createNewChangeRequest(request));
    }

    @GetMapping("/recipient/{code}")
    @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> findRecipientCode(@PathVariable("code") String code) {
        return success(
                opsGiftCardTransferService.findRecipientLikeCode(code));
    }

    @GetMapping("/recipient/search")
    @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> findRecipientByCodeAndName(@RequestParam("value") String value) {
        return success(
                opsGiftCardTransferService.findRecipientLikeCodeAndName(value));
    }

    @GetMapping("in-review")
    @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getListInReview(@RequestParam(value = "approval_status", required = false, defaultValue = "P") EApprovalStatus approvalStatus,
                                             @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
                                             @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
                                             @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
                                             @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
                                             @RequestParam(value = "created_by", required = false) String createdBy,
                                             @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
                                             @MakerCheckerOffsetPageable Pageable pageable) {

        Page<GiftCardTransferringInReviewRes> inReviewResPage = opsGiftCardTransferService
                .getInReview(approvalStatus, fromCreatedAt, toCreatedAt, fromReviewedAt, toReviewedAt, createdBy, reviewedBy, pageable);

        return success(inReviewResPage.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) inReviewResPage.getTotalElements());
    }

    @GetMapping("/verify")
    @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> verifyCardTransferring(@RequestParam(name = "business_id", required = true) int businessId,
                                                    @RequestParam(name = "program_id", required = true) int programId,
                                                    @RequestParam(name = "batch_no", required = true) int batchNo) {
        return success(opsGiftCardProductionRequestService.verifyGiftCardTransferring(businessId, programId, batchNo));
    }

    @GetMapping("/in-review/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW}),

    })
    public ResponseEntity<?> getInReviewById(@PathVariable("id") Integer reviewId) {
        return success(opsGiftCardTransferService.giftCardTransferInReviewDetail(reviewId));
    }

    @GetMapping("/available")
    @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> search(
            @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
            @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit,
            @Valid @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "batch_no", required = false) Integer batchNo,
            @RequestParam(value = "transfer_no", required = false) Integer transferNo,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdEnd,
            @RequestParam(value = "process_status", required = false) EProcessStatus processStatus,
            @RequestParam(value = "recipient_code", required = false) String recipientCode,
            @RequestParam(value = "send_file_via", required = false) ESendVia sendVia) {

        Page<GiftCardTransferAvailableListRes> response = opsGiftCardTransferService.searchGiftCardTransferAvailable(
                businessId,
                programId,
                batchNo,
                transferNo,
                createdStart,
                createdEnd,
                processStatus,
                recipientCode,
                sendVia,
                offset,
                limit);
        return success(response.getContent(), (int) response.getPageable().getOffset(),
                response.getPageable().getPageSize(), (int) response.getTotalElements());
    }

    @GetMapping("/available/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW}),

    })
    public ResponseEntity<?> getAvailableDetail(@PathVariable("id") Integer id) {
        return success(opsGiftCardTransferService.giftCardTransferAvailableDetail(id));
    }


    @PostMapping("/available/{id}/send-sms-password-folder")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW}),

    })
    public ResponseEntity<?> sendSmsPasswordFolder(@PathVariable("id") Integer id) {
        return success(opsGiftCardTransferService.sendSmsPasswordFolder(id));
    }

    @PostMapping("/approve")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsGiftCardTransferService.approve(req);
        return success(null);
    }

    @GetMapping("/{id}/export")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.GIFT_CARD_TRANSFER, permissions = {AccessPermission.EXPORT}),
    })
    public ResponseEntity<?> exportFeaturedByRequestId(@PathVariable("id") Integer id) {
        ResourceDTO resourceDTO = opsGiftCardTransferService.exportFeature(id);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", String.format("attachment; filename=%s", resourceDTO.getFilename()));
        headers.add(HttpHeaders.CONTENT_TYPE, "application/zip;charset=UTF-8");
        headers.add("Access-Control-Expose-Headers", "*");
        return new ResponseEntity<>(resourceDTO.getResource(), headers, HttpStatus.OK);
    }
}
