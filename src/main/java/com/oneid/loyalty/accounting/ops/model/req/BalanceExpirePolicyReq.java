package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.oneid.loyalty.accounting.ops.util.EpochTimeDeserialize;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.time.DateUtils;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

@Setter
@Getter
public class BalanceExpirePolicyReq {
    @NotBlank
    @Pattern(regexp = "^(DAY|MONTH|YEAR|NEVER|FIX_TIME)?$")
    @JsonProperty("type")
    private String type;

    @Min(0)
    @JsonProperty("period")
    private Integer period = 0;

    @FutureOrPresent
    @JsonProperty("fix_time_expire")
    @JsonDeserialize(converter = EpochTimeDeserialize.class)
    private Date fixTimeExpire = DateUtils.addMinutes(new Date(), 1);
}
