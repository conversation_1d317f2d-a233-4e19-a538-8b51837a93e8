package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class PosCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private PosRepository posRepository;

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(PosCodeAttributeValueStrategy.class);

    public PosCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getPosCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {

        Pos pos = posRepository.findByProgramIdAndTerminalCodeAndStatus(programIds[0], value, ECommonStatus.ACTIVE);
        if (pos == null) {
            LOGGER.warn("Pos {} not found", value);
            throw new IllegalArgumentException();
        }
        return AttributeCombobox.builder()
                .name(pos.getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> posCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return posRepository.findByProgramIdAndTerminalCodes(programIds[0], posCodes)
                    .stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(s.getName())
                            .value(s.getCode())
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}