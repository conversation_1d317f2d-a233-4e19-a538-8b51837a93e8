package com.oneid.loyalty.accounting.ops.model.dto;

import com.poiji.annotation.ExcelCellName;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Data
@Validated
public class EarnBurnSaleTransactionExcelDTO extends ActionTransactionExcelDTO {

    @ExcelCellName("GMV")
    @Min(0)
    private BigDecimal Gmv;

    @ExcelCellName("GROSS_AMOUNT")
    @Min(0)
    private BigDecimal grossAmount;

    @ExcelCellName("REDEEM_POINT")
    @Min(0)
    private BigDecimal redeemPoint;

    @ExcelCellName("CURRENCY_CODE")
    private String currencyCode;
}
