package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.RejectOperatorRequest;
import com.oneid.loyalty.accounting.ops.model.req.SearchOperatorReq;
import com.oneid.loyalty.accounting.ops.model.res.OperatorRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsOperatorService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("v1/operators")
public class OperatorController extends BaseController {
    @Autowired
    OpsOperatorService operatorService;

    @PostMapping
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.OPERATOR, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getApproveCreate(
            @Valid @RequestBody CreateOperatorReq request
    ) {
        return success(operatorService.getApprove(request));
    }

    @PostMapping("update")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.OPERATOR, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getApproveUpdate(
            @Valid @RequestBody CreateOperatorReq request
    ) {
        return success(operatorService.getApproveForUpdate(request));
    }

    @PutMapping("reject")
    @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    public ResponseEntity<?> reject(
            @Valid @RequestBody RejectOperatorRequest request
    ) {
        operatorService.getReject(request);
        return success(null);
    }

    @PutMapping("approve/{request_id}")
    @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    public ResponseEntity<?> approve(@PathVariable("request_id") Integer requestId) {
        operatorService.approve(requestId);
        return success(null);
    }

    @GetMapping
    @Authorize(role = AccessRole.OPERATOR, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "corporation_id", required = false) Integer corporationId,
            @RequestParam(value = "chain_id", required = false) Integer chainId,
            @RequestParam(value = "store_id", required = false) Integer storeId,
            @RequestParam(value = "terminal_id", required = false) Integer terminalId,
            @RequestParam(value = "operator_id", required = false) String operatorId,
            @RequestParam("approval_status") EApprovalStatus approvalStatus,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "20") int limit) {

        if (approvalStatus == EApprovalStatus.APPROVED && businessId == null) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,
                    "Missing business id when search with approval status approved", null);
        }
        SearchOperatorReq searchOperatorReq = SearchOperatorReq
                .builder()
                .businessId(businessId)
                .corporationId(corporationId)
                .chainId(chainId)
                .storeId(storeId)
                .terminalId(terminalId)
                .operatorId(operatorId)
                .approvalStatus(approvalStatus)
                .status(status)
                .build();

        Page<OperatorRequestRes> page = operatorService
                .filter(searchOperatorReq, new OffsetBasedPageRequest(offset, limit));

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("{request_id}")
    @Authorize(role = AccessRole.OPERATOR, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> detail(
            @PathVariable("request_id") Integer requestId
    ) {
        return success(operatorService.getDetail(requestId));
    }
}
