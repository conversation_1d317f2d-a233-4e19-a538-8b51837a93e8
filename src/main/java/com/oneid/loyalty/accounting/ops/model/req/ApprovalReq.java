package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.NotNullIfAnotherFieldHasValue;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Convert;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
@NotNullIfAnotherFieldHasValue(
        fieldName = "status",
        fieldValue = "REJECTED",
        dependFieldName = "comment",
        message = "Reason must not be null"
)
public class ApprovalReq implements Serializable {
    private static final long serialVersionUID = -7543269367252338530L;

    @NotNull(message = "id must not be blank")
    @JsonProperty("id")
    private Long id;

    @JsonProperty("reason")
    private String comment;

    @Convert(converter = EApprovalStatus.Converter.class)
    @JsonProperty("approval_status")
    private EApprovalStatus status;

    @AssertTrue(message = "'Reason' max length is 255")
    public boolean isValidReason() {
        if (EApprovalStatus.REJECTED.equals(status) && comment.length() > 255) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
