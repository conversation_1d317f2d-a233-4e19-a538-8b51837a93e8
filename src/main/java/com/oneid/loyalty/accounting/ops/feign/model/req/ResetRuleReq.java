package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ResetRuleReq {
    private Integer id;

    private Integer programId;

    private String code;

    private String name;

    private String description;

    private ECommonStatus status;

    private EConditionType ruleLogic;

    private EServiceType serviceType;

    private String serviceCode;

    private Date startDate;

    private Date endDate;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private String approvedBy;

    private Date approvedAt;

    private List<ConditionReq> conditions;

    public static ResetRuleReq buildRuleReq(Rule rule) {
        return ResetRuleReq.builder()
                .id(rule.getId())
                .programId(rule.getProgramId())
                .code(rule.getCode())
                .name(rule.getName())
                .description(rule.getDescription())
                .status(rule.getStatus())
                .ruleLogic(rule.getRuleLogic())
                .serviceType(rule.getServiceType())
                .serviceCode(rule.getServiceCode())
                .startDate(rule.getStartDate())
                .endDate(rule.getEndDate())
                .createdBy(rule.getCreatedBy())
                .createdAt(rule.getCreatedAt())
                .updatedBy(rule.getUpdatedBy())
                .updatedAt(rule.getUpdatedAt())
                .approvedBy(rule.getApprovedBy())
                .approvedAt(rule.getApprovedAt())
                .build();
    }

    public static ConditionReq buildConditionReq(RuleCondition ruleCondition) {
        return ConditionReq.builder()
                .id(ruleCondition.getId())
                .ruleId(ruleCondition.getRuleId())
                .attribute(ruleCondition.getAttribute())
                .operator(ruleCondition.getOperator())
                .value(ruleCondition.getValue())
                .dataType(ruleCondition.getDataType())
                .status(ruleCondition.getStatus())
                .createdBy(ruleCondition.getCreatedBy())
                .createdAt(ruleCondition.getCreatedAt())
                .updatedBy(ruleCondition.getUpdatedBy())
                .updatedAt(ruleCondition.getUpdatedAt())
                .approvedBy(ruleCondition.getApprovedBy())
                .approvedAt(ruleCondition.getApprovedAt())
                .build();
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ConditionReq {
        private Integer id;

        private Integer ruleId;

        private String attribute;

        private String operator;

        private String value;

        private String dataType;

        private ECommonStatus status;

        private String createdBy;

        private Date createdAt;

        private String updatedBy;

        private Date updatedAt;

        private String approvedBy;

        private Date approvedAt;
    }
}