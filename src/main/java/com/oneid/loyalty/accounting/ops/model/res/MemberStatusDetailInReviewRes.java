package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


import java.util.List;

@Data
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusDetailInReviewRes extends MemberStatusInReviewRes{

    private String requestType;

    private String enName;

    private String reason;

    private String enDescription;

    private String viDescription;

    private EBoolean revertLatestStatus;

    private EBoolean expiredAllPoints;

    private EBoolean defaultStatus;

    private List<Function> functions;

    @Data
    @NoArgsConstructor
    public static class Function {

        private String functionCode;

        private String functionName;

        private String group;
    }
}
