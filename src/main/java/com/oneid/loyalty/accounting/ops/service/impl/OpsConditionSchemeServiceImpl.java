package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionSchemeService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleConditionService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import com.oneid.oneloyalty.common.entity.SchemeRuleCondition;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.SchemeRuleConditionService;
import com.oneid.oneloyalty.common.service.SchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsConditionSchemeServiceImpl implements OpsConditionSchemeService {

    @Autowired
    private SchemeRuleConditionService schemeRuleConditionService;

    @Autowired
    private OpsRuleConditionService opsRuleConditionService;

    @Autowired
    private SchemeService schemeService;


    @Override
    public List<ConditionRecordRes> createAllInOneSchemeRule(final SchemeRule schemeRule,
                                                             List<ConditionRecordReq> createConditions) {

        final Scheme scheme = getScheme(schemeRule);
        final Map<String, ConditionAttributeDto> attributeDtoMap = opsRuleConditionService
                .getMapAttributeValueDto(scheme.getProgramId(), EServiceType.SCHEME);
        final Map<String, AttributeValueStrategy<?>> attributeCodeToStrategyMap = opsRuleConditionService
                .getMapAttributeValueStrategy(scheme.getProgramId(), EServiceType.SCHEME);

        List<SchemeRuleCondition> conditions = createConditions.stream().map(
                        c -> {
                            SchemeRuleCondition condition = new SchemeRuleCondition();
                            condition.setOperator(c.getOperator().getExpression());
                            condition.setSchemeRuleId(schemeRule.getId());
                            condition.setAttribute(c.getAttribute());
                            condition.setDataType(attributeDtoMap.get(c.getAttribute()).getDataType());
                            condition.setValue(attributeCodeToStrategyMap.get(c.getAttribute())
                                    .getWriteValue(c.getAttribute(), c.getOperator(), c.getValue(), scheme.getProgramId())
                            );
                            condition.setStatus(ECommonStatus.ACTIVE); // default
                            return condition;
                        }
                )
                .peek(t -> t.setSchemeRuleId(schemeRule.getId()))
                .collect(Collectors.toList());

        return schemeRuleConditionService.createAllInOneSchemeRule(conditions, scheme.getProgramId())
                .stream().map(c -> toRes(scheme, attributeCodeToStrategyMap.get(c.getAttribute()), c)).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<ConditionRecordRes> updateAllInOneSchemeRule(SchemeRule schemeRule, List<ConditionRecordReq> updateConditions) {

        final Scheme scheme = getScheme(schemeRule);

        Map<String, AttributeValueStrategy<?>> attributeCodeToStrategyMap = opsRuleConditionService
                .getMapAttributeValueStrategy(scheme.getProgramId(), EServiceType.SCHEME);

        Map<String, ConditionAttributeDto> attributeDtoMap = opsRuleConditionService
                .getMapAttributeValueDto(scheme.getProgramId(), EServiceType.SCHEME);

        List<ConditionRecordRes> result;

        List<SchemeRuleCondition> oldConditions = schemeRuleConditionService.getAllByRuleId(schemeRule.getId());

        final Map<Integer, SchemeRuleCondition> mapIdToOldConditions = oldConditions
                .stream().collect(Collectors.toMap(
                        SchemeRuleCondition::getId,
                        c -> c
                ));

        final List<ConditionRecordReq> conditionWillUpdate = updateConditions
                .stream().filter(u -> u.getConditionId() != null).collect(Collectors.toList());

        final Map<Integer, ConditionRecordReq> mapIdToUpdateConditions = conditionWillUpdate
                .stream().collect(Collectors.toMap(
                        ConditionRecordReq::getConditionId,
                        c -> c
                ));

        long checker = updateConditions.stream().filter(r -> r.getConditionId() != null &&
                !mapIdToOldConditions.containsKey(r.getConditionId())).count();

        if (checker > 0) {
            throw new BusinessException(ErrorCode.CONDITION_NOT_IN_RULE, "Condition not in rule", updateConditions);
        }

        /*
         * Check request
         * */
        long countChecker = updateConditions.stream()
                .filter(u -> u.getConditionId() != null && !mapIdToOldConditions.containsKey(u.getConditionId()))
                .count();

        if (countChecker != 0L) {
            throw new BusinessException(ErrorCode.CONDITION_NOT_IN_RULE,
                    "Condition not in rule", updateConditions);
        }

        countChecker = updateConditions.stream()
                .map(ConditionRecordReq::getRuleId).distinct().count();

        if (countChecker != 1L) {
            throw new BusinessException(ErrorCode.CONDITION_NOT_IN_RULE,
                    "Condition not in rule", updateConditions);
        }

        /* Remove old */
        List<SchemeRuleCondition> conditionsWillRemove = new ArrayList<>();
        for (SchemeRuleCondition u : oldConditions) {
            if (!mapIdToUpdateConditions.containsKey(u.getId())) {
                conditionsWillRemove.add(u);
            }
        }
        remove(conditionsWillRemove);

        /* Create new */
        List<ConditionRecordReq> conditionsWillCreateNew = updateConditions.stream()
                .filter(u -> u.getConditionId() == null)
                .collect(Collectors.toList());

        result = new ArrayList<>(createAllInOneSchemeRule(schemeRule, conditionsWillCreateNew));


        /* Update condition existed*/
        conditionWillUpdate.forEach(uc -> {
            SchemeRuleCondition condition = mapIdToOldConditions.get(uc.getConditionId());
            condition.setStatus(ECommonStatus.ACTIVE);
            condition.setAttribute(uc.getAttribute());
            condition.setOperator(uc.getOperator().getExpression());
            condition.setDataType(attributeDtoMap.get(uc.getAttribute()).getDataType());
            condition.setValue(attributeCodeToStrategyMap.get(uc.getAttribute()).
                    getWriteValue(uc.getAttribute(), uc.getOperator(), uc.getValue(), scheme.getProgramId()));
            result.add(toRes(scheme, attributeCodeToStrategyMap.get(condition.getAttribute()),
                    schemeRuleConditionService.save(condition)));
        });

        return result;
    }

    private void remove(List<SchemeRuleCondition> conditions) {
        conditions.forEach(c -> {
            c.setStatus(ECommonStatus.INACTIVE);
            schemeRuleConditionService.save(c);
        });
    }

    private ConditionRecordRes toRes(Scheme scheme, AttributeValueStrategy<?> attributeValueStrategy, SchemeRuleCondition condition) {
        ConditionRecordRes res = new ConditionRecordRes();
        res.setId(condition.getId());
        res.setAttribute(condition.getAttribute());
        res.setOperator(condition.getOperator());
        res.setStatus(condition.getStatus());
        res.setValue(attributeValueStrategy.getReadValue(condition.getAttribute(),
                EAttributeOperator.lookup(condition.getOperator()), condition.getValue(), scheme.getProgramId())
        );
        res.setRuleId(condition.getSchemeRuleId());
        res.setIsIgnoreRefund(condition.getIsIgnoreRefund());
        res.setDataType(condition.getDataType());
        res.setCreatedAt(condition.getCreatedAt());
        res.setCreatedBy(condition.getCreatedBy());
        res.setApprovedAt(condition.getApprovedAt());
        res.setApprovedBy(condition.getApprovedBy());
        res.setUpdatedAt(condition.getUpdatedAt());
        res.setUpdatedBy(condition.getUpdatedBy());
        res.setValueString(condition.getValue());

        return res;
    }

    private Scheme getScheme(SchemeRule rule) {
        return schemeService.find(rule.getSchemeId()).get();
    }
}