package com.oneid.loyalty.accounting.ops.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;

public class ValidatorUtil {

    private final static SimpleDateFormat FORMATTER_MMM_dd = new SimpleDateFormat("MMM-dd");

    static {
        FORMATTER_MMM_dd.setLenient(false);
    }

    public static boolean isValidFormatMMMdd(String value) {
        try {
            FORMATTER_MMM_dd.parse(value); // verify format
        } catch (ParseException e) {
            return false;
        }
        return value.length() == 6;
    }
}
