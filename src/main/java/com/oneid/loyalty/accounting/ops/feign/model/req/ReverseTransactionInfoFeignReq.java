package com.oneid.loyalty.accounting.ops.feign.model.req;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ReverseTransactionInfoFeignReq {
    @JsonProperty("store_id")
    private String storeCode;

    @JsonProperty("terminal_id")
    private String terminalCode;

    private String invoiceNo;

    private Long transactionTime;

    private String description;

    private BigDecimal refundAmount;

    private BigDecimal redeemPoint;
}