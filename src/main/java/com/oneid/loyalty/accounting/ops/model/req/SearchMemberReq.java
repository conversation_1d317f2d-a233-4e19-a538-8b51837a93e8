package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.search.MinFieldRequiredForSearch;
import com.oneid.loyalty.accounting.ops.validation.search.SortableAttribute;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@MinFieldRequiredForSearch(count = 2, ignores = {"mapPropsToAttr"}) // partner is required + 1 field
public class SearchMemberReq extends SortableAttribute {

    @NotBlank
    @JsonProperty("partner_code")
    private String partnerCode;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("full_name")
    private String fullName;

    @JsonProperty("identify_type")
    private String identifyType;

    @JsonProperty("identify_no")
    private String identifyNo;

    @JsonProperty("card_no")
    private String cardNo;

    @Override
    protected void initMapPropsToAttribute() {
        mapPropsToAttribute = new HashMap<>();
        mapPropsToAttribute.put("member_code", "memberCode");
        mapPropsToAttribute.put("phone_no", "phoneNo");
        mapPropsToAttribute.put("full_name", "fullName");
        mapPropsToAttribute.put("gender", "gender");
        mapPropsToAttribute.put("status", "status");
        mapPropsToAttribute.put("identify_no", "identifyNo");
    }

    @Override
    protected String getSortByDefault() {
        return "member_code";
    }
}