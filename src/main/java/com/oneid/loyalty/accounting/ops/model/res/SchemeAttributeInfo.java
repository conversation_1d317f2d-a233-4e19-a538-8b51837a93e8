package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.SchemeAttribute;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SchemeAttributeInfo {

    @JsonProperty("attribute")
    private String attribute;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("operators")
    private List<String> operators;

    @JsonProperty("data_type")
    private String dataType;

    @JsonProperty("data_type_display")
    private String dataTypeDisplay;

    public static SchemeAttributeInfo valueOf(SchemeAttribute attribute) {
        SchemeAttributeInfo res = new SchemeAttributeInfo();
        res.setAttribute(attribute.getAttribute());
        res.setName(attribute.getName());
        res.setDescription(attribute.getDescription());
        res.setOperators(attribute.getOperators());
        res.setDataType(attribute.getDataType());
        res.setDataTypeDisplay(attribute.getDataTypeDisplay());
        return res;
    }
}
