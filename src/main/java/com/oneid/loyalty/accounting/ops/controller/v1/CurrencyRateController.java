package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRateRes;
import com.oneid.loyalty.accounting.ops.service.OpsCurrencyRateService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

@RestController
@RequestMapping(value = "v1/currency-rate")
@Validated
public class CurrencyRateController extends BaseController {
    @Autowired
    OpsCurrencyRateService opsCurrencyRateService;

    @GetMapping("")
    @Authorize(role = AccessRole.CURRENCY_RATE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(@RequestParam(name = "base_currency_id", required = false) Integer baseCurrencyId,
                                              @RequestParam(name = "currency_id", required = false) Integer currencyId,
                                              @RequestParam(name = "status", required = false)
                                              @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                              @RequestParam(name = "business_id", required = false) Integer businessId,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit) {
        Page<CurrencyRateRes> page = opsCurrencyRateService.filterCurrencyRate(baseCurrencyId, currencyId, status, businessId, offset, limit);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.CURRENCY_RATE, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCurrency(@PathVariable(value = "id") Integer id) {
        CurrencyRateRes result = opsCurrencyRateService.getCurrencyRate(id);
        return success(result);
    }

    @PostMapping("create")
    @Authorize(role = AccessRole.CURRENCY_RATE, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createCurrency(@RequestBody @Valid CurrencyRateCreateReq currencyRateCreateReq) {
        opsCurrencyRateService.addCurrencyRate(currencyRateCreateReq);
        return success(null);
    }

    @PostMapping("{id}/update")
    @Authorize(role = AccessRole.CURRENCY_RATE, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCurrency(@PathVariable Integer id,
                                            @RequestBody @Valid CurrencyRateUpdateReq currencyRateUpdateReq) {
        opsCurrencyRateService.updateCurrencyRate(id, currencyRateUpdateReq);
        return success(null);
    }
}