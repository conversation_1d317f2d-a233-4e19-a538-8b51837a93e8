package com.oneid.loyalty.accounting.ops.model.dto;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.poiji.annotation.ExcelCellName;
import com.poiji.annotation.ExcelRow;
import lombok.Data;
@Data
public class ActionTransactionExcelDTO {

    @ExcelRow
    private int rowIndex;

    @ExcelCellName("BUSINESS_CODE")
    private String businessCode;

    @ExcelCellName("PROGRAM_CODE")
    private String programCode;

    @ExcelCellName("ID_TYPE")
    private String idType;

    @ExcelCellName("ID_NO")
    private String idNo;

    @ExcelCellName("INVOICE_NO")
    private String invoiceNo;

    @ExcelCellName("TRANSACTION_TIME")
    private String transactionTime;

    @ExcelCellName("STORE_CODE")
    private String storeCode;

    @ExcelCellName("TERMINAL_CODE")
    private String terminalCode;

    @ExcelCellName("DESCRIPTION")
    private String description;

    @ExcelCellName("SERVICE_CODE")
    private String serviceCode;

    @ExcelCellName("CHANNEL")
    private String channel;

    private Long memberId;
    private Boolean isValid;
    private String status = OPSConstant.VALID;
    private String errorMessage;
}
