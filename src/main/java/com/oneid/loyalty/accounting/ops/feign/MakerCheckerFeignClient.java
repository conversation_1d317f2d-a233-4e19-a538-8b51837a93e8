package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeOperatorReq;
import com.oneid.loyalty.accounting.ops.model.dto.MakerCheckerChangeRequestResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.oneid.loyalty.accounting.ops.feign.config.MakerCheckerFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.CardTransferringPayloadChangeRequestReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CreateChangeRequestReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.SearchChangeRequestsRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.RejectReq;

@FeignClient(name = "maker-checker-system", url = "${maker-checker.url}", configuration = MakerCheckerFeignConfig.class)
public interface MakerCheckerFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/changes")
    APIResponse<SearchChangeRequestsRes<CardTransferringPayloadChangeRequestReq>> getChangeRequests(
            @RequestParam(value = "module") String module,
            @RequestParam(value = "object_id") Long objectId,
            @RequestParam(value = "status", defaultValue = "PENDING") String status,
            @RequestParam(value = "page", required = false) Integer pageNumber,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "date_from", required = false) Integer dateFrom,
            @RequestParam(value = "date_to", required = false) Integer dateTo);
    
    @RequestMapping(method = RequestMethod.GET, value = "/changes")
    APIResponse<ChangeRequestPageFeignRes> getChangeRequests(
            @RequestParam(value = "module") String module,
            @RequestParam(value = "object_id") Integer objectId,
            @RequestParam(value = "module")  String result,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "page", required = false) Integer pageNumber,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "date_from", required = false) Integer dateFrom,
            @RequestParam(value = "date_to", required = false) Integer dateTo);
    
    @RequestMapping(method = RequestMethod.GET, value = "/changes/{id}")
    APIResponse<ChangeRecordFeginRes> getChangeRequestById(@PathVariable("id") String id);
    
    @RequestMapping(method = RequestMethod.POST, value = "/changes")
    APIResponse<MakerCheckerChangeRequestResponse> changes(@RequestBody ChangeRequestFeignReq req);

    @RequestMapping(method = RequestMethod.POST, value = "/changes")
    APIResponse<Object> createRequestCardTransferring(@RequestBody CreateChangeRequestReq<CardTransferringPayloadChangeRequestReq> changeRequestReq);

    @RequestMapping(method = RequestMethod.POST, value = "/changes")
    APIResponse<Object> createRequestScheme(@RequestBody CreateChangeRequestReq<CreateSchemeReq> req);

    @RequestMapping(method = RequestMethod.POST, value = "/changes")
    APIResponse<Object> createRequestOperator(@RequestBody CreateChangeRequestReq<ChangeOperatorReq> req);

    @RequestMapping(method = RequestMethod.POST, value = "/changes/{id}/approve")
    APIResponse<Object> approve(@PathVariable("id") Long id);

    @RequestMapping(method = RequestMethod.POST, value = "/changes/{id}/reject")
    APIResponse<Object> reject(@PathVariable("id") Long id, @RequestBody RejectReq req);

    @RequestMapping(method = RequestMethod.GET, value = "/changes")
    APIResponse<SearchChangeRequestsRes<ChangeOperatorReq>> getChangeRequestOperators(
            @RequestParam(value = "module") String module,
            @RequestParam(value = "object_id") Long objectId,
            @RequestParam(value = "status", defaultValue = "PENDING") String status,
            @RequestParam(value = "page", required = false) Integer pageNumber,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "date_from", required = false) Integer dateFrom,
            @RequestParam(value = "date_to", required = false) Integer dateTo);
}