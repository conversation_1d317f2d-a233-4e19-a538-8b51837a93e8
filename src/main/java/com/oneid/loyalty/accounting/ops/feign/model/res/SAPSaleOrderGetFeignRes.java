package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
public class SAPSaleOrderGetFeignRes {
    @JsonProperty("MT_VID_OE_SO_Get_RES")
    private Data data;

    @Getter
    @Setter
    public class Data {
        @JsonProperty("TRANSACTION")
        private List<SAPSaleOrderFeignRes> saleOrders = Collections.emptyList();
    }
}
