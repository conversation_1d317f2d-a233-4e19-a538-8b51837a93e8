package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker;

import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public interface HttpEntityService {
    static HttpEntity getHttpEntity(AbsGetApproveReq req) {
        String token = req.getBearerToken().replaceAll("Bearer ", "");
        String requestId = UUID.randomUUID().toString();
        return req.toRequest(token, requestId);
    }
}