package com.oneid.loyalty.accounting.ops.controller.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.exception.ServiceClientException;
import com.oneid.loyalty.accounting.ops.model.ApiRequestError;
import com.oneid.loyalty.accounting.ops.support.feign.BadRequestFeignException;
import com.oneid.loyalty.accounting.ops.support.web.security.exception.ForbiddenException;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.support.ResponseSupport;
import feign.FeignException;
import feign.FeignException.FeignClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@ControllerAdvice
public class ExceptionHandlerAdvice {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExceptionHandlerAdvice.class);

    @Autowired
    private ResponseSupport responseSupport;

    @Autowired
    private ObjectMapper objectMapper;
    
    private UrlPathHelper urlPathHelper;

    @PostConstruct
    void init(){
        urlPathHelper = new UrlPathHelper();
    }

    @ExceptionHandler(value = { 
            HttpMediaTypeNotSupportedException.class,
            MethodArgumentTypeMismatchException.class,
            HttpMessageNotReadableException.class,
            IllegalArgumentException.class,
            MissingServletRequestParameterException.class,
            InvalidDataAccessApiUsageException.class,
            ServletRequestBindingException.class,
            HttpRequestMethodNotSupportedException.class })
    public ResponseEntity<Object> handle(HttpServletRequest request, Exception e) {
        LOGGER.warn("'[{}] - [{}] - [{}]", e.getClass(), e, urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(BaseResponseData.error(ErrorCode.BAD_REQUEST.getValue(), e.getMessage()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handle(HttpServletRequest request, MethodArgumentNotValidException e) {
        String message = null;
        
        if (e.getBindingResult().getAllErrors().size() > 0) {
            message = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        }
        
        LOGGER.warn("'[{}] - [{}] - [{}]", MethodArgumentNotValidException.class, message, urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(BaseResponseData.error(ErrorCode.BAD_REQUEST.getValue(), message), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handle(HttpServletRequest request, ConstraintViolationException e) {
        LOGGER.warn("'[{}] - [{}] - [{}]", ConstraintViolationException.class, e.getMessage(), urlPathHelper.getLookupPathForRequest(request));
        
        StringBuilder stringBuilder = new StringBuilder(1000);
        
        if (e.getConstraintViolations() != null && !e.getConstraintViolations().isEmpty()) {
            for (ConstraintViolation<?> cons : e.getConstraintViolations()) {
                stringBuilder.append(cons.getMessage() + "\n");
            }
        } else if (e.getConstraintViolations() == null) stringBuilder.append(e.getMessage());
        
        return new ResponseEntity<>(errorWithMessage(HttpStatus.BAD_REQUEST.value(), stringBuilder.toString()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Object> handle(HttpServletRequest request, BusinessException e) {
        BaseResponseData responseData = null;
        
        if(e.getMessageParams() != null) {
            responseData = error(e.getCode().getValue(), e.getMessage(), e.getMessageParams());
        } else {
            if (e.getCode() != null) {
                responseData = error(e.getCode().getValue(), e.getMessage());
            }else {
                responseData = errorWithMessage(e.getErrCode(), e.getMessage());
            }
        }
        
        LOGGER.warn("'[{}] - [{}] - [{}] - [{}]", BusinessException.class, responseData.getMeta().getCode(), responseData.getMeta().getMessage(), urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(responseData, HttpStatus.OK);
    }

    @ExceptionHandler(RestClientResponseException.class)
    public ResponseEntity<?> handleRestClientResponseException(RestClientResponseException e, HttpServletRequest request) throws Exception {
        if(e.getRawStatusCode() == HttpStatus.BAD_REQUEST.value()) {
            ApiRequestError requestError = objectMapper.readValue(e.getResponseBodyAsString(), ApiRequestError.class);

            ErrorCode errorCode = Arrays.asList(ErrorCode.values())
            .stream()
            .filter(each -> each.getValue() == requestError.getCode())
            .findAny()
            .get();

            if(ErrorCode.BAD_REQUEST.equals(errorCode)) {
                String messages = requestError.getArguments()
                .stream()
                .map(mesasge -> Objects.toString(mesasge))
                .collect(Collectors.joining(","));

                return new ResponseEntity<>(errorWithMessage(HttpStatus.BAD_REQUEST.value(), messages), HttpStatus.BAD_REQUEST);
            } else {
                Object[] messages = Optional
                .ofNullable(requestError.getArguments())
                .orElse(Collections.emptyList())
                .toArray();

                LOGGER.error("'[{}] - [{}] - [{}]", e.getClass(), messages, urlPathHelper.getLookupPathForRequest(request));
                
                return new ResponseEntity<>(error(errorCode.getValue(), "", messages), HttpStatus.OK);
            }
        } else {
            LOGGER.error("'[{}] - [{}] - [{}]", e.getClass(), e.getMessage(), urlPathHelper.getLookupPathForRequest(request));

            return new ResponseEntity<>(error(ErrorCode.SERVER_ERROR.getValue(), e.getMessage()), HttpStatus.OK);
        }
    }

    @ExceptionHandler(OpsBusinessException.class)
    public ResponseEntity<Object> handle(HttpServletRequest request, OpsBusinessException e) {
        BaseResponseData responseData = null; 
        
        if(e.getMessageParams() != null) {
            responseData = error(e.getCode().getValue(), e.getMessage(), e.getMessageParams());
        } else {
            responseData = errorWithMessage(e.getCode().getValue(), e.getMessage());
        }
        
        LOGGER.warn("'[{}] - [{}] - [{}] - [{}]", e.getClass(),  responseData.getMeta().getCode(), responseData.getMeta().getMessage(), urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(responseData, HttpStatus.OK);
    }

    @ExceptionHandler(ServiceClientException.class)
    public ResponseEntity<Object> handleServiceClientException(ServiceClientException exception, WebRequest request) {
        LOGGER.warn("Request '{}' responsed status: '{}'. Invalid given parameters: '{}'",
                request.getDescription(false),
                HttpStatus.BAD_REQUEST,
                exception.getMessage());

        return ResponseEntity.status(HttpStatus.OK)
                .body(responseSupport.errorResponse(exception.getErrorCode(), exception.getErrorMessage()));
    }

    @ExceptionHandler(ForbiddenException.class)
    public ResponseEntity<Object> handleForbiddenException(ForbiddenException e, HttpServletRequest request) {
        Meta meta = responseSupport.createMeta(e.getErrorCode().getValue(), e.getArguments());
        
        LOGGER.warn("Access denied '{}': '{}' on '{}'", ForbiddenException.class, e.getArguments(), urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(BaseResponseData.instance(meta, null), HttpStatus.OK);
    }
    
    @ExceptionHandler(BadRequestFeignException.class)
    public ResponseEntity<Object> handle(BadRequestFeignException e, HttpServletRequest request) {
        LOGGER.warn("'[{}] - [{}] - [{}]", e.getClass(), e, urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(BaseResponseData.error(ErrorCode.BAD_REQUEST.getValue(), e.getMessage()), HttpStatus.BAD_REQUEST);
    }
    
    @ExceptionHandler(FeignClientException.class)
    public ResponseEntity<Object> handle(FeignException e, HttpServletRequest request) {
        LOGGER.error("'[{}] - [{}] - [{}]", e.getClass(), e, urlPathHelper.getLookupPathForRequest(request));
        
        return new ResponseEntity<>(BaseResponseData.error(ErrorCode.SERVER_ERROR.getValue(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    @ExceptionHandler(Throwable.class)
    public ResponseEntity<Object> handle(HttpServletRequest request, Throwable e) {
        LOGGER.error("'[{}] - [{}] - [{}]", e.getClass(), e, urlPathHelper.getLookupPathForRequest(request));

        return new ResponseEntity<>(responseSupport.errorResponse(ErrorCode.SERVER_ERROR.getValue()), HttpStatus.INTERNAL_SERVER_ERROR);
    }
    private BaseResponseData error(int code, String message) {
        return responseSupport.errorResponse(code);
    }

    private BaseResponseData errorWithMessage(int code, String message) {
        return responseSupport.errorResponse(code, message);
    }

    private BaseResponseData error(int code, String message, Object[] messageParams) {
        return new BaseResponseData(responseSupport.createMeta(code, messageParams), null);
    }
}