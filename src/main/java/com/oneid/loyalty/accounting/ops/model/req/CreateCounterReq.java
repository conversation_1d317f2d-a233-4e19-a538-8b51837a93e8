package com.oneid.loyalty.accounting.ops.model.req;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Convert;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.validation.NotNullIfAnotherFieldHasValue;
import com.oneid.loyalty.accounting.ops.validation.NullIfAnotherFieldHasValue;
import com.oneid.oneloyalty.common.constant.EBoolean;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.loyalty.accounting.ops.validation.ASCIICode;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterAttribute;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.ECounterType;
import com.oneid.oneloyalty.common.constant.EServiceType;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = CreateCounterReq.CreateCounterReqBuilder.class)
@NotNullIfAnotherFieldHasValue(
        fieldName = "counterType",
        fieldValue = "VALUE",
        dependFieldName = "counterAttribute", message = "counter_attribute must not be null")
@NullIfAnotherFieldHasValue(
        fieldName = "counterType",
        fieldValue = "FREQUENCY",
        dependFieldName = "counterAttribute", message = "counter_attribute must be null")
public class CreateCounterReq {

    private Integer id;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    private String name;

    private String description;

    @NotNull(message = "'enable_revert' must not be null")
    private EBoolean enableRevert;

    @NotBlank(message = "'code' must not be blank")
    @Length(max = 100)
    @ASCIICode
    private String code;
    
    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;
    
    @NotNull(message = "'program_id' must not be null")
    private Integer programId;
    
    @NotNull(message = "'start_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
//    @JsonSerialize(using = DateSerializer.class)
    private Date startDate;
    
    @NotNull(message = "'end_date' must not be null")
    @JsonDeserialize(using = DateDeserializer.class)
//    @JsonSerialize(using = DateSerializer.class)
    private Date endDate;
    
    @NotNull(message = "'period' must not be null")
    @JsonProperty("period")
    private ECounterPeriod period;
    
    @NotNull(message = "'counter_level' must not be null")
    private ECounterLevel counterLevel;
    
    @NotNull(message = "'counter_type' must not be null")
    private ECounterType counterType;

    private BigDecimal resetValue;

    @NotBlank(message = "counter_status must not blank")
    @Convert(converter = ECommonStatus.Converter.class)
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("counter_status")
    private String counterStatus;
    
    private ECounterAttribute counterAttribute;
    
    private EServiceType serviceType;
    
    @Valid
    @NotNull(message = "'rules' must not be null")
    @Size(min = 1, max = 100, message = "'rules' size between 1, 100")
    private List<RuleReq> rules;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class CreateCounterReqBuilder {
    }
}