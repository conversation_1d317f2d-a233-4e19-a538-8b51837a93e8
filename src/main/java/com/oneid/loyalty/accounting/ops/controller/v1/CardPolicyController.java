package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardPolicyRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardPolicyService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolationException;
import javax.validation.Valid;

@RestController
@RequestMapping("v1/card-policy")
public class CardPolicyController extends BaseController {

    @Autowired
    OpsCardPolicyService cardPolicyService;

    @GetMapping(value = "/{id}")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.CARD_POLICY, permissions = {AccessPermission.VIEW }),
            @Authorize(role = AccessRole.MEMBER_CARD_PR, permissions = {AccessPermission.VIEW }),
    }, any = true)
    public ResponseEntity<?> getDetail(@PathVariable(name = "id") Integer id) {
        return success(cardPolicyService.viewDetails(id));
    }

    @GetMapping
    @Authorize(role = AccessRole.CARD_POLICY, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> search(@RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                    @RequestParam(value = "limit", defaultValue = "20") Integer limit,
                                    @RequestParam(value = "business_id") Integer businessId,
                                    @RequestParam(value = "sort_by", required = false) String sortBy,
                                    @RequestParam(value = "sort_direction", required = false) Sort.Direction sortDirection,
                                    @Valid @RequestParam(value = "status", required = false) ECommonStatus status,
                                    @Valid @RequestParam(value = "type", required = false) ECardPolicyType policyType
                                    ) {


        CardPolicySearchReq req = new CardPolicySearchReq();
        req.setCardPolicyType(policyType);
        req.setStatus(status);
        req.setBusinessId(businessId);

        if (status != null && !ECommonStatus.ACTIVE.equals(status) && !ECommonStatus.INACTIVE.equals(status)) {
            throw new ConstraintViolationException("Status invalid", null);
        }

        if (sortBy != null) {
            req.setSortBy(sortBy);
        }

        if (sortDirection != null)
            req.setSortDirection(sortDirection);

        Pageable pageable = new OffsetBasedPageRequest(offset, limit, req.getSort());

        Page<CardPolicyRes> page = cardPolicyService.searchPage(req, pageable);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @PutMapping("{id}")
    @Authorize(role = AccessRole.CARD_POLICY, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> update(@PathVariable(name = "id") Integer id,
                                    @Valid @RequestBody CardPolicyUpdateReq req) {
        req.setId(id);
        return success(cardPolicyService.update(req));
    }

    @PostMapping
    @Authorize(role = AccessRole.CARD_POLICY, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> create(@Valid @RequestBody CardPolicyCreateReq req) {
        return success(cardPolicyService.create(req));
    }
}