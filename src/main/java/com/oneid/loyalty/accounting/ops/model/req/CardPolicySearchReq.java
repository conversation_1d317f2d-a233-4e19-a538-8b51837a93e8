package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.loyalty.accounting.ops.validation.search.SortableAttribute;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;

import javax.validation.ConstraintViolationException;

@Getter
@Setter
public class CardPolicySearchReq extends SortableAttribute {
    private Integer businessId;

    private ECardPolicyType cardPolicyType;

    private ECommonStatus status;

    @Override
    protected void initMapPropsToAttribute() {
        mapPropsToAttribute.put("business_id", "businessId");
        mapPropsToAttribute.put("card_policy_type", "policyType");
        mapPropsToAttribute.put("created_at", "createdAt");
        mapPropsToAttribute.put("updated_at", "updatedAt");
    }

    @Override
    protected String getSortByDefault() {
        if(sortBy != null && !mapPropsToAttribute.containsKey(sortBy))
            throw new ConstraintViolationException(String.format("Cannot sort by %s", sortBy), null);

        return "id";
    }
}