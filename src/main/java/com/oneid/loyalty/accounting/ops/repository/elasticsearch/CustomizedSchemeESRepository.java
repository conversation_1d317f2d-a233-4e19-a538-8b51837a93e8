package com.oneid.loyalty.accounting.ops.repository.elasticsearch;

import java.time.OffsetDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.elasticsearch.SchemeES;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;

public interface CustomizedSchemeESRepository {
    
    Page<SchemeES> findPage(Integer businessId, Integer programId, 
            ESchemeType type, String code, ECommonStatus status, 
            OffsetDateTime startDate, OffsetDateTime endDate, 
            String corporationCode,
            String chainCode,
            String storeCode,
            String terminalCode,
            Pageable pageable);
    
}
