package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.constant.MemberSortingField;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberInquiryRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateMemberReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeMemberRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberProfileDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberProfileRes;
import com.oneid.loyalty.accounting.ops.model.res.TierHistoryRes;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;

public interface OpsMemberService {
    MemberProfileDetailRes getProfileById(Long memberId);

    MemberProfileDetailRes addMember(CreateMemberReq req);

    MemberProfileDetailRes updateMember(Long memberId, UpdateMemberReq req);

    public Page<MemberProfileRes> getMemberProfiles(
            Integer businessId,
            Integer programId,
            String memberCode,
            String phoneNo,
            String fullName,
            String identifyNo,
            Integer limit,
            Integer offset,
            Direction direction,
            MemberSortingField sortBy,
            String cardNo);
    
    APIResponse<?> getMemberBalanceById(Long memberId);

    ResponseEntity<Resource> registerMemberByBatchFile(OPSAuthenticatedPrincipal principal, MultipartFile[] multipartFiles);

    Page<TierHistoryRes> getMemberTierHistory(Long memberId, Pageable pageRequest);

    Collection<AttributeMemberRes> getMemberAttributes(Long memberId);

    MemberInquiryRes getLinkAccountInfo(Long memberId);
}