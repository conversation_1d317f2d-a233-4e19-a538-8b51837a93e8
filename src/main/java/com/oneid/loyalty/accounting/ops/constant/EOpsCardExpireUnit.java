package com.oneid.loyalty.accounting.ops.constant;

import com.oneid.oneloyalty.common.constant.PersistentEnum;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import java.util.HashMap;
import java.util.Map;

public enum EOpsCardExpireUnit implements PersistentEnum<String> {
    DAY("D", "Day"),
    MONTH("M", "Month"),
    YEAR("Y", "Year"),
    FIXED_TIME("F", "Fixed time"),
    NEVER("N", "Never");

    private static final Map<String, EOpsCardExpireUnit> mapByValue = new HashMap();
    private final String value;
    private final String displayName;

    private EOpsCardExpireUnit(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static EOpsCardExpireUnit of(String value) {
        return (EOpsCardExpireUnit)mapByValue.get(value);
    }

    public String getValue() {
        return this.value;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    static {
        EOpsCardExpireUnit[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            EOpsCardExpireUnit e = var0[var2];
            mapByValue.put(e.getValue(), e);
        }

    }

    public static class Converter extends PersistentEnumConverter<EOpsCardExpireUnit, String> {
        public Converter() {
            super(EOpsCardExpireUnit.class);
        }
    }
}
