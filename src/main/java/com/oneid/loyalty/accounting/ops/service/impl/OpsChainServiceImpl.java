package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.mapper.ChainMapper;
import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateChainReq;
import com.oneid.loyalty.accounting.ops.model.res.ChainEnumAll;
import com.oneid.loyalty.accounting.ops.model.res.ChainRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.util.LogData;

@Service
public class OpsChainServiceImpl implements OpsChainService {
    @Autowired
    CountryService countryService;

    @Autowired
    ProvinceService provinceService;

    @Autowired
    DistrictService districtService;

    @Autowired
    WardService wardService;

    @Autowired
    ChainRepository chainRepository;

    @Autowired
    ChainService chainService;

    @Autowired
    BusinessRepository businessRepository;

    @Autowired
    CorporationRepository corporationRepository;

    @Autowired
    OpsBusinessService opsBusinessService;

    @Autowired
    OpsCorporationService opsCorporationService;
    
    @Autowired
    private BusinessService businessService;
    
    @Autowired
    CorporationService corporationService;
    
    @Autowired
    private DistrictRepository districtRepository;
    
    @Autowired
    private WardRepository wardRepository;

    @Autowired
    StoreService storeService;

    @Autowired
    PosService posService;

    @Override
    public Page<ChainRes> filter(Integer businessId, Integer corporationId, String chainName, String chainCode, String status, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<Chain> specificationBuilder = new SpecificationBuilder<>();

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (corporationId != null)
            specificationBuilder.add(new SearchCriteria("corporationId", corporationId, SearchOperation.EQUAL));

        if (chainName != null)
            specificationBuilder.add(new SearchCriteria("name", chainName, SearchOperation.EQUAL));

        if (chainCode != null)
            specificationBuilder.add(new SearchCriteria("code", chainCode, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        Page<Chain> chains = chainService.find(specificationBuilder, pageRequest);

        Set<Integer> businessIds = chains.getContent().stream().map(Chain::getBusinessId).collect(Collectors.toSet());
        Map<Integer, Business> businessbyIds = opsBusinessService.getMapById(businessIds);
        Set<Integer> corporationIds = chains.getContent().stream().map(Chain::getCorporationId).collect(Collectors.toSet());
        Map<Integer, Corporation> corporationbyIds = opsCorporationService.getMapById(corporationIds);

        return new PageImpl<ChainRes>(chains.getContent()
                .stream().map(it -> ChainRes.of(
                        it,
                        businessbyIds.get(it.getBusinessId()),
                        corporationbyIds.get(it.getCorporationId())
                )).collect(Collectors.toList())
                , pageRequest, chains.getTotalElements());
    }

    @Override
    public ChainRes get(Integer id) {
        Chain chain = chainService.find(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "Chain not found in business", null));
        
        Business business = businessRepository.getOne(chain.getBusinessId());
        Corporation corporation = corporationRepository.getOne(chain.getCorporationId());
        
        Integer wardId = null;
        Integer districtId = null;
        
        if(chain.getWardId() != null) {
            Optional<Ward> ward = wardRepository.findById(chain.getWardId());

            if (ward.isPresent()){
                Ward existedWard = ward.get();
                if(!existedWard.getStatus().equals(ECommonStatus.ACTIVE) || !existedWard.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedWard = wardRepository.findByCodeAndStatusAndNewStatus(existedWard.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                wardId = existedWard.getId();
                districtId = existedWard.getDistrictId();
            }
        }
        
        if(districtId == null && chain.getDistrictId() != null) {
            Optional<District> district = districtRepository.findById(chain.getDistrictId());

            if(district.isPresent()) {
                District existedDistrict = district.get();
                if (!existedDistrict.getStatus().equals(ECommonStatus.ACTIVE) || !existedDistrict.getNewStatus().equals(ECommonStatus.ACTIVE)) {
                    existedDistrict = districtRepository.findByCodeAndStatusAndNewStatus(existedDistrict.getCode(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);
                }

                districtId = existedDistrict.getId();
            }
        }
        
        ChainRes chainRes = new ChainRes();
        chainRes.setId(chain.getId());
        chainRes.setCode(chain.getCode());
        chainRes.setBusinessId(chain.getBusinessId());
        chainRes.setBusinessName(business != null ? business.getName() : null);
        chainRes.setCorporationId(chain.getCorporationId());
        chainRes.setCorporationName(corporation != null ? corporation.getName() : null);
        chainRes.setName(chain.getName());
        chainRes.setEnName(chain.getEnName());
        chainRes.setDescription(chain.getDescription());
        chainRes.setEnDescription(chain.getEnDescription());
        chainRes.setStatus(chain.getStatus() != null ? chain.getStatus().getValue() : null);
        chainRes.setCreatedBy(chain.getCreatedBy());
        chainRes.setUpdatedBy(chain.getUpdatedBy());
        chainRes.setApprovedBy(chain.getApprovedBy());
        chainRes.setCreatedAt(chain.getCreatedAt() != null ? chain.getCreatedAt().toInstant().getEpochSecond() : null);
        chainRes.setUpdatedAt(chain.getUpdatedAt() != null ? chain.getUpdatedAt().toInstant().getEpochSecond() : null);
        chainRes.setApprovedAt(chain.getApprovedAt() != null ? chain.getApprovedAt().toInstant().getEpochSecond() : null);
        chainRes.setCreatedYmd(chain.getCreatedYmd());
        chainRes.setContactPerson(chain.getContactPerson());
        chainRes.setEmailAddress(chain.getEmailAddress());
        chainRes.setPhoneNo(chain.getPhoneNo());
        chainRes.setTaxNo(chain.getTaxNo());
        chainRes.setWebSite(chain.getWebSite());
        chainRes.setAddress1(chain.getAddress1());
        chainRes.setCountryId(chain.getCountryId());
        chainRes.setProvinceId(chain.getProvinceId());
        chainRes.setDistrictId(districtId);
        chainRes.setWardId(wardId);
        chainRes.setServiceStartDate(chain.getServiceStartDate() != null ? chain.getServiceStartDate().toInstant().getEpochSecond() : null);
        chainRes.setServiceEndDate(chain.getServiceEndDate() != null ? chain.getServiceEndDate().toInstant().getEpochSecond() : null);
        chainRes.setPostalCode(chain.getPostalCode());
        chainRes.setAddress2(chain.getAddress2());
        
        return chainRes;
    }

    @Override
    public List<ChainEnumAll> getEnumAll() {
        return chainRepository.findAll().stream().map(ChainEnumAll::of).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void update(Integer id, ChainUpdateReq chainUpdateReq) {
        validateLocation(chainUpdateReq.getCountryId(), chainUpdateReq.getProvinceId(), chainUpdateReq.getDistrictId(), chainUpdateReq.getWardId());
        validateUniqueChainNameForUpdate(chainUpdateReq.getName(), id);
        validateUniqueChainPhoneForUpdate(chainUpdateReq.getPhoneNo(), id);
        Chain chain = chainService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CHAIN_NOT_FOUND, "Chain not found in business", LogData.createLogData().append("chain_id", id)));
        Chain chainUpdate = ChainMapper.toChainOne(chain, chainUpdateReq);
        Corporation corporation = corporationService.find(chainUpdateReq.getCorporationId()).orElseThrow(() ->
                new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found in business", LogData.createLogData().append("corporation_id", chainUpdateReq.getCorporationId())));
        chainUpdate.setCorporationId(corporation.getId());

        SpecificationBuilder<Store> specificationBuilderStore = new SpecificationBuilder<>();
        specificationBuilderStore.add(new SearchCriteria("chainId", chainUpdate.getId(), SearchOperation.EQUAL));
        List<Store> stores = storeService.find(specificationBuilderStore);
        for(Store store : stores) {
            store.setCorporationId(corporation.getId());
            storeService.save(store);
        }

        SpecificationBuilder<Pos> specificationBuilderPos = new SpecificationBuilder<>();
        specificationBuilderPos.add(new SearchCriteria("chainId", chainUpdate.getId(), SearchOperation.EQUAL));
        Pageable pageable = PageRequest.of(0, 100, Sort.by("id").ascending());
        Page<Pos> posPage = posService.find(specificationBuilderPos, pageable);
        updatePosByCorporationId(corporation, posPage.getContent());
        while (posPage.hasNext()) {
            pageable = posPage.nextPageable();
            posPage = posService.find(specificationBuilderPos, pageable);
            updatePosByCorporationId(corporation, posPage.getContent());
        }

        chainService.update(chainUpdate);
    }

    private void updatePosByCorporationId(Corporation corporation, List<Pos> posList) {
        for(Pos pos : posList) {
            pos.setCorporationId(corporation.getId());
            posService.save(pos);
        }
    }

    private void validateUniqueChainNameForUpdate(String name, Integer id) {
        if (StringUtils.isBlank(name)) {
            return;
        }

        List<Chain> chains = this.chainRepository.getByName(name);
        for (Chain chain : chains) {
            if (!chain.getId().equals(id)) {
                throw new BusinessException(
                        ErrorCode.CHAIN_NAME_EXISTED,
                        "chain name existed",
                        LogData.createLogData().append("chain_name", name));
            }
        }
    }

    @Override
    public Map<Integer, Chain> getMapById(Collection<Integer> ids) {
        List<Chain> chains = this.chainRepository.findAllByIdIn(ids);
        return chains.stream().collect(Collectors.toMap(
                t -> t.getId(),
                t -> t,
                (value1, value2) -> value2
        ));
    }

    private void validateUniqueChainPhoneForUpdate(String phone, Integer id) {
        if (phone == null) return;
        List<Chain> chains = chainRepository.getByPhoneNo(phone);
        for (Chain chain : chains) {
            if (!chain.getId().equals(id))
                throw new OpsBusinessException(OpsErrorCode.CHAIN_PHONE_EXIST, "chain phone exist", LogData.createLogData().append("chain_phone", phone));
        }
    }

    private void validateLocation(Integer countryId, Integer provinceId, Integer districtId, Integer wardId) {
        if (countryId != null) {
            this.countryService.getById(countryId).orElseThrow(() -> new BusinessException(
                    ErrorCode.COUNTRY_NOT_FOUND, "Country is not found", LogData.createLogData().append("country", countryId)));
        }

        if (provinceId != null && !this.provinceService.isValid(provinceId, countryId)) {
            throw new BusinessException(
                    ErrorCode.PROVINCE_NOT_FOUND, "Province is not found", LogData.createLogData().append("province", provinceId));
        }

        if (districtId != null && !this.districtService.isValid(districtId, provinceId)) {
            throw new BusinessException(
                    ErrorCode.DISTRICT_NOT_FOUND, "District is not found", LogData.createLogData().append("district", districtId));
        }

        if (wardId != null && !this.wardService.isValid(wardId, districtId)) {
            throw new BusinessException(
                    ErrorCode.WARD_NOT_FOUND, "Ward is not found", LogData.createLogData().append("ward", wardId));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChainRes create(CreateChainReq req) {
        Business business = businessService.findActive(req.getBusinessId());
        
        Corporation corporation = corporationService.findActive(req.getCorporationId());
        
        if(!corporation.getBusinessId().equals(business.getId()))
            throw new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null);
        
        if(chainService.find(business.getId(), req.getCode()) != null)
            throw new BusinessException(ErrorCode.CHAIN_EXISTED, "Chain exist in business", null);
        
        validateLocation(req.getCountryId(), req.getProvinceId(), req.getDistrictId(), req.getWardId());
        
        if(this.chainRepository.getByName(req.getName())
        .stream()
        .count() > 0)
            throw new BusinessException(ErrorCode.CHAIN_NAME_EXISTED, "chain name existed", null);
        
        if(req.getPhoneNo() != null && chainRepository.getByPhoneNo(req.getPhoneNo())
        .stream()
        .count() > 0)
            throw new OpsBusinessException(OpsErrorCode.CHAIN_PHONE_EXIST, "chain phone exist", null);
        
        Chain chain = ChainMapper.toChainOne(req, business, corporation);
        
        chainService.save(chain);
        
        return ChainRes.of(chain, business, corporation);
    }
}
