package com.oneid.loyalty.accounting.ops.controller.v1;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeServiceTypeRequestRes;
import com.oneid.loyalty.accounting.ops.service.ProgramAttributeManagementService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.MakerCheckerOffsetPageable;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.controller.BaseController;

@RestController
@RequestMapping(value = "v1/program-attribute-management")
@Validated
public class ProgramAttributeManagementController extends BaseController {
    
    @Autowired
    private ProgramAttributeManagementService programAttributeManagementService;
    
    @GetMapping("program/{programId}/service-type/{serviceType}/available")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> isAvailable(@PathVariable("programId") Integer programId,
            @PathVariable("serviceType") EServiceType serviceType
    ) {
        return success(programAttributeManagementService.isAvailable(programId, serviceType));
    }
    
    @GetMapping("program/{programId}/attribute/all")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getAllAttributes(@PathVariable("programId") Integer programId) {
        return success(programAttributeManagementService.getAllAttributes(programId));
    }
    
    @PostMapping("request/create")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestCreate(@RequestBody @Valid ProgramAttributeServiceTypeCreateReq req) {
        return success(programAttributeManagementService.requestCreate(req));
    }
    
    @GetMapping("request/available")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getPage(
            @RequestParam(name = "business_id", required = false) Integer businessId,
            @RequestParam(name = "program_id", required = false) Integer programId,
            @RequestParam(name = "service_type", required = false) EServiceType serviceType,
            @RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(name = "offset", defaultValue = "0") Integer offset,
            @RequestParam(name = "limit", defaultValue = "20") Integer limit
    ) {
        List<EServiceType> serviceTypes = serviceType != null ? Collections.singletonList(serviceType) : 
                Arrays.asList(EServiceType.LIMITATION, EServiceType.TIER, EServiceType.SCHEME, EServiceType.COUNTER);
        
        Pageable pageable = new OffsetBasedPageRequest(offset, limit, null);
        Page<ProgramAttributeServiceTypeRequestRes> page = programAttributeManagementService.getPage(businessId, programId, 
                serviceTypes, approvalStatus, pageable);
        
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("request/available/{id}")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getDetail(@PathVariable("id") int id) {
        return success(programAttributeManagementService.getDetail(id));
    }
    
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    @GetMapping("request/available/{id}/changeable")
    public ResponseEntity<?> getChangeable(@PathVariable("id") int id) {
        return success(programAttributeManagementService.getChangeable(id));
    }
    
    @PostMapping("request/change")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.EDIT }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> requestChange(@RequestBody @Valid ProgramAttributeServiceTypeUpdateReq req) {
        return success(programAttributeManagementService.requestChange(req));
    }
    
    @GetMapping("request/in-review")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewPage(
            @RequestParam(name = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(name = "from_date", required = false) LocalDate fromDate,
            @RequestParam(name = "to_date", required = false) LocalDate toDate,
            @MakerCheckerOffsetPageable Pageable pageable
    ) {
        Page<ProgramAttributeServiceTypeRequestRes> page = programAttributeManagementService.getInReviewPage(approvalStatus, fromDate, toDate, pageable);
        return success(page.getContent(), (int) pageable.getOffset(), pageable.getPageSize(), (int) page.getTotalElements());
    }
    
    @GetMapping("request/in-review/{reviewId}")
    @Authorize(role = AccessRole.PROGRAM_ATTRIBUTE_MANAGEMENT, permissions = { AccessPermission.VIEW })
    public ResponseEntity<?> getInReviewDetail(@PathVariable("reviewId") Integer reviewId) {
        return success(programAttributeManagementService.getInReviewDetail(reviewId));
    }
    
}
