package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateTierAdjustmentReq {

    @NotNull(message = "'business_id' must not be blank")
    private Integer businessId;

    @NotNull(message = "'program_id' must not be blank")
    private Integer programId;

    private String referenceCode;

    private String description;

    private Boolean desReplace;


}
