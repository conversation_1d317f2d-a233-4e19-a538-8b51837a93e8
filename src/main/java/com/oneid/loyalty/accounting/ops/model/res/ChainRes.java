package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
public class ChainRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("corporation_name")
    private String corporationName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("en_name")
    private String enName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("en_description")
    private String enDescription;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    @JsonProperty("contact_person")
    private String contactPerson;

    @JsonProperty("email_address")
    private String emailAddress;

    @JsonProperty("phone_no")
    private String phoneNo;

    @JsonProperty("tax_no")
    private String taxNo;

    @JsonProperty("website")
    private String webSite;

    @JsonProperty("address1")
    private String address1;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("service_start_date")
    private Long serviceStartDate;

    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @JsonProperty("postal_code")
    private String postalCode;

    @JsonProperty("address2")
    private String address2;

    public static ChainRes of(Chain chain, Business business, Corporation corporation) {
        ChainRes chainRes = new ChainRes();
        chainRes.setId(chain.getId());
        chainRes.setCode(chain.getCode());
        chainRes.setBusinessId(chain.getBusinessId());
        chainRes.setBusinessName(business != null ? business.getName() : null);
        chainRes.setCorporationId(chain.getCorporationId());
        chainRes.setCorporationName(corporation != null ? corporation.getName() : null);
        chainRes.setName(chain.getName());
        chainRes.setEnName(chain.getEnName());
        chainRes.setDescription(chain.getDescription());
        chainRes.setEnDescription(chain.getEnDescription());
        chainRes.setStatus(chain.getStatus() != null ? chain.getStatus().getValue() : null);
        chainRes.setCreatedBy(chain.getCreatedBy());
        chainRes.setUpdatedBy(chain.getUpdatedBy());
        chainRes.setApprovedBy(chain.getApprovedBy());
        chainRes.setCreatedAt(chain.getCreatedAt() != null ? chain.getCreatedAt().toInstant().getEpochSecond() : null);
        chainRes.setUpdatedAt(chain.getUpdatedAt() != null ? chain.getUpdatedAt().toInstant().getEpochSecond() : null);
        chainRes.setApprovedAt(chain.getApprovedAt() != null ? chain.getApprovedAt().toInstant().getEpochSecond() : null);
        chainRes.setCreatedYmd(chain.getCreatedYmd());
        chainRes.setContactPerson(chain.getContactPerson());
        chainRes.setEmailAddress(chain.getEmailAddress());
        chainRes.setPhoneNo(chain.getPhoneNo());
        chainRes.setTaxNo(chain.getTaxNo());
        chainRes.setWebSite(chain.getWebSite());
        chainRes.setAddress1(chain.getAddress1());
        chainRes.setCountryId(chain.getCountryId());
        chainRes.setProvinceId(chain.getProvinceId());
        chainRes.setDistrictId(chain.getDistrictId());
        chainRes.setWardId(chain.getWardId());
        chainRes.setServiceStartDate(chain.getServiceStartDate() != null ? chain.getServiceStartDate().toInstant().getEpochSecond() : null);
        chainRes.setServiceEndDate(chain.getServiceEndDate() != null ? chain.getServiceEndDate().toInstant().getEpochSecond() : null);
        chainRes.setPostalCode(chain.getPostalCode());
        chainRes.setAddress2(chain.getAddress2());
        return chainRes;
    }
}
