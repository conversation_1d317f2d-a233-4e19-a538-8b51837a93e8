package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardBin;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class CardBinRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("bin_code")
    private String binCode;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("approved_by")
    private String approvedBy;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("approved_at")
    private Long approvedAt;

    @JsonProperty("created_ymd")
    private Long createdYmd;

    public static CardBinRes of(CardBin cardBin, Business business, Program program) {
        CardBinRes cardBinRes = new CardBinRes();
        cardBinRes.setId(cardBin.getId());
        cardBinRes.setBinCode(cardBin.getBinCode());
        cardBinRes.setBusinessId(cardBin.getBusinessId());
        cardBinRes.setBusinessName(business != null ? business.getName() : null);
        cardBinRes.setProgramId(cardBin.getProgramId());
        cardBinRes.setProgramName(program != null ? program.getName() : null);
        cardBinRes.setName(cardBin.getName());
        cardBinRes.setDescription(cardBin.getDescription());
        cardBinRes.setStatus(cardBin.getStatus() != null ? cardBin.getStatus().getValue() : null);
        cardBinRes.setCreatedBy(cardBin.getCreatedBy());
        cardBinRes.setUpdatedBy(cardBin.getUpdatedBy());
        cardBinRes.setApprovedBy(cardBin.getApprovedBy());
        cardBinRes.setCreatedAt(cardBin.getCreatedAt() != null ? cardBin.getCreatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setUpdatedAt(cardBin.getUpdatedAt() != null ? cardBin.getUpdatedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setApprovedAt(cardBin.getApprovedAt() != null ? cardBin.getApprovedAt().toInstant().getEpochSecond() : null);
        cardBinRes.setCreatedYmd(cardBin.getCreatedYmd());
        return cardBinRes;
    }
}
