package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.CreateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateSchemeReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveReq;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateBusinessMCReq extends AbsGetApproveReq {
    @JsonProperty("payload")
    private CreateBusinessReq payload;

    @Override
    public HttpEntity build(HttpHeaders headers) {
        return new HttpEntity<>(this, headers);
    }
}