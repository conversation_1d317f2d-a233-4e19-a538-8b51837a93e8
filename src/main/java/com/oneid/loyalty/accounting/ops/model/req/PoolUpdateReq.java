package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class PoolUpdateReq {
    @NotNull(message = "Business not null")
    @JsonProperty("business_id")
    private Integer businessId;

    @NotNull(message = "Program not null")
    @JsonProperty("program_id")
    private Integer programId;

    @NotBlank(message = "Code not empty")
    @NotNull(message = "Code not null")
    @JsonProperty("code")
    private String code;

    @NotBlank(message = "Name not empty")
    @NotNull(message = "Name not null")
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @NotNull(message = "Currency not null")
    @JsonProperty("currency_id")
    private Integer currencyId;

    @NotNull
    @Valid
    @JsonProperty("retention_policy")
    private RetentionPolicyReq retentionPolicy;

    @NotNull
    @Valid
    @JsonProperty("balance_expire_policy")
    private BalanceExpirePolicyReq balanceExpirePolicy;

    @NotNull(message = "Negative balance not null")
    @Pattern(regexp="^(Y|N)?$", message = "'Negative Balance' Only accept Y/N values")
    @JsonProperty("negative_balance")
    private String negativeBalance;

    @Pattern(regexp="^(Y|N)?$", message = "'Allow Refund Remaining Balance' Only accept Y/N values")
    @JsonProperty("allow_refund_remaining_balance")
    private String allowRefundRemainingBalance;

    @NotBlank
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("shared_program_ids")
    private List<Integer> sharedProgramIds;
}
