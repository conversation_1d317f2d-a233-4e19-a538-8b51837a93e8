package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Program;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class ProgramDropDownRes {

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("business_id")
    private Integer businessId;

    private ECommonStatus status;

    public static ProgramDropDownRes valueOf(Program loyaltyProgram) {

        ProgramDropDownRes programRes = new ProgramDropDownRes();
        programRes.setBusinessId(loyaltyProgram.getBusinessId());
        programRes.setProgramId(loyaltyProgram.getId());
        programRes.setProgramCode(loyaltyProgram.getCode());
        programRes.setProgramName(loyaltyProgram.getName());
        programRes.setStatus(loyaltyProgram.getStatus());

        return programRes;
    }
}
