package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SchemeBaseRes {
    
    private Integer schemeId;

    private String schemeCode;

    private String schemeName;

    private String description;

    private ESchemeType schemeType;

    private EConditionType ruleLogic;

    private Integer poolId;

    private String poolName;

    private ECommonStatus status;

    private Integer programId;

    private String programName;

    private Integer businessId;

    private String businessName;

    private Long startDate;

    private Long endDate;
    
}
