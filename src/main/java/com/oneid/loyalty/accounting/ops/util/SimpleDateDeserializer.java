package com.oneid.loyalty.accounting.ops.util;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

public class SimpleDateDeserializer extends StdDeserializer<Date> {
    private static final long serialVersionUID = -7063294420688656066L;
    
    private String pattern = "yyyyMMdd";

    public SimpleDateDeserializer() {
        super(Date.class);
    }

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        try {
            return new SimpleDateFormat(pattern).parse(p.getText());
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }

}
