package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeMasterDataVerifyReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.support.web.argument.resolver.RequestPojo;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;

@RestController
@RequestMapping("v1/attributes")
@Validated
public class AttributeController extends BaseController {
    @Autowired
    private OpsAttributeService opsAttributeService;

    @Autowired
    private OpsCommonExcelService opsCommonExcelService;

    @PostMapping("/transactions/requests")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createTransactionAttribute(@Valid @RequestBody AttributeCreateReq req) throws Exception {
        opsAttributeService.createAttribute(req, EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE);
        return success(null);
    }

    @PostMapping("/transactions/requests/excel")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createTransactionAttributeWithExcel(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid AttributeCreateReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = opsAttributeService.createAttributeWithExcel(req, multipartFile, EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE);

        return dto != null ? new ResponseEntity<>(
                dto.getResource(),
                OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()),
                HttpStatus.BAD_REQUEST
        ) : success(null);
    }

    @GetMapping("/transactions/master-data/template")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getTransactionTemplateAttributeMasterData() throws IOException {
        return opsCommonExcelService.getTemplate(
                OPSConstant.ATTRIBUTE,
                "Template_Attribute_Master_Data"
        );
    }

    @PostMapping("/transactions/master-data/verify")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> verifyTransactionAttributeMasterDataExcel(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid AttributeMasterDataVerifyReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = opsAttributeService.verifyAttributeMasterDataExcel(req, multipartFile);

        return dto != null ? new ResponseEntity<>(
                dto.getResource(),
                OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()),
                HttpStatus.BAD_REQUEST
        ) : success(null);
    }

    @GetMapping("/transactions/requests/in-review/{review_id}")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getTransactionInReviewDetail(@PathVariable("review_id") Integer reviewId) {
        return success(opsAttributeService.getInReviewDetail(reviewId, EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE));
    }

    @GetMapping("/transactions/requests/in-review")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionInReviews(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "created_start", required = false) String fromCreatedAt,
            @RequestParam(value = "created_end", required = false) String toCreatedAt,
            @RequestParam(value = "approved_start", required = false) String fromReviewedAt,
            @RequestParam(value = "approved_end", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "approved_by", required = false) String reviewedBy,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<AttributeRequestRes> page = opsAttributeService.getInReviews(
                approvalStatus,
                fromCreatedAt,
                toCreatedAt,
                fromReviewedAt,
                toReviewedAt,
                createdBy,
                reviewedBy,
                offset,
                limit,
                EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @PostMapping("/transactions/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approveTransactionAttribute(@Valid @RequestBody ApprovalReq req) {
        opsAttributeService.approveRequest(req, EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE);
        return success(null);
    }

    @GetMapping("/transactions/requests/master-data")
//    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionMasterData(@RequestParam(value = "program_id") Integer programId,
                                                      @RequestParam(value = "attribute_code") String attributeCode) {
        return success(opsAttributeService.getMasterData(programId, attributeCode, EAttributeType.PROGRAM_TRANSACTION));
    }

    @GetMapping("/transactions/requests/available")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getTransactionAvailable(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "having_master_data", required = false) Boolean havingMasterData,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdEnd,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {
        Page<AttributeRequestRes> page = opsAttributeService.getTransactionAvailable(
                businessId,
                programId,
                code,
                name,
                havingMasterData,
                createdStart,
                createdEnd,
                status,
                new OffsetBasedPageRequest(offset, limit, null)
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/transactions/requests/available/{id}")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getAvailableTransactionDetail(@PathVariable("id") Integer id) {
        return success(opsAttributeService.getAvailableDetail(id, EAttributeType.PROGRAM_TRANSACTION));
    }

    @GetMapping("/transactions/requests/available/{id}/master-data/export")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportAvailableTransactionMasterDataDetail(@PathVariable("id") Integer id) {
        ResourceDTO dto = opsAttributeService.exportMasterData(id, EAttributeType.PROGRAM_TRANSACTION);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/transactions/requests/in-review/{id}/master-data/export")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportInReviewTransactionMasterDataDetail(@PathVariable("id") Integer id) {
        ResourceDTO dto = opsAttributeService.exportInReviewMasterData(id, EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/transactions/requests/available/{id}/service")
    @Authorize(role = AccessRole.PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportAvailableTransactionServiceDetail(@PathVariable("id") Integer id) {
        return success(opsAttributeService.getAttributeService(id, EAttributeType.PROGRAM_TRANSACTION));
    }

    // Flow member attribute

    @PostMapping("/members/requests")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createMemberAttribute(@Valid @RequestBody AttributeCreateReq req) throws Exception {
        opsAttributeService.createAttribute(req, EMakerCheckerType.MEMBER_ATTRIBUTE);
        return success(null);
    }

    @PostMapping("/members/requests/excel")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> createMemberAttributeWithExcel(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid AttributeCreateReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = opsAttributeService.createAttributeWithExcel(req, multipartFile, EMakerCheckerType.MEMBER_ATTRIBUTE);

        return dto != null ? new ResponseEntity<>(
                dto.getResource(),
                OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()),
                HttpStatus.BAD_REQUEST
        ) : success(null);
    }

    @GetMapping("/members/master-data/template")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> getMemberTemplateAttributeMasterData() throws IOException {
        return opsCommonExcelService.getTemplate(
                OPSConstant.ATTRIBUTE,
                "Template_Attribute_Master_Data"
        );
    }

    @PostMapping("/members/master-data/verify")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.CREATE})
    public ResponseEntity<?> verifyMemberAttributeMasterDataExcel(
            @RequestPojo(name = "content", errorCode = ErrorCode.PAYLOAD_MUST_NOT_BE_NULL) @Valid AttributeMasterDataVerifyReq req,
            @RequestPart(name = "file") MultipartFile multipartFile
    ) throws Exception {
        ResourceDTO dto = opsAttributeService.verifyAttributeMasterDataExcel(req, multipartFile);

        return dto != null ? new ResponseEntity<>(
                dto.getResource(),
                OpsCommonExcelService.setHeaderForFile(multipartFile.getOriginalFilename()),
                HttpStatus.BAD_REQUEST
        ) : success(null);
    }

    @GetMapping("/members/requests/in-review/{review_id}")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getMemberInReviewDetail(@PathVariable("review_id") Integer reviewId) {
        return success(opsAttributeService.getInReviewDetail(reviewId, EMakerCheckerType.MEMBER_ATTRIBUTE));
    }

    @GetMapping("/members/requests/in-review")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberInReviews(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "created_start", required = false) String fromCreatedAt,
            @RequestParam(value = "created_end", required = false) String toCreatedAt,
            @RequestParam(value = "approved_start", required = false) String fromReviewedAt,
            @RequestParam(value = "approved_end", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "approved_by", required = false) String reviewedBy,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<AttributeRequestRes> page = opsAttributeService.getInReviews(
                approvalStatus,
                fromCreatedAt,
                toCreatedAt,
                fromReviewedAt,
                toReviewedAt,
                createdBy,
                reviewedBy,
                offset,
                limit,
                EMakerCheckerType.MEMBER_ATTRIBUTE
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @PostMapping("/members/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approveMemberAttribute(@Valid @RequestBody ApprovalReq req) {
        opsAttributeService.approveRequest(req, EMakerCheckerType.MEMBER_ATTRIBUTE);
        return success(null);
    }

    @GetMapping("/members/requests/master-data")
//    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberMasterData(@RequestParam(value = "program_id") Integer programId,
                                                 @RequestParam(value = "attribute_code") String attributeCode) {
        return success(opsAttributeService.getMasterData(programId, attributeCode, EAttributeType.MEMBER));
    }

    @GetMapping("/members/requests/available")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getMemberAvailable(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id") Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "having_master_data", required = false) Boolean havingMasterData,
            @RequestParam(value = "created_start", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdStart,
            @RequestParam(value = "created_end", required = false) @DateTimeFormat(pattern = OPSConstant.DATE_FORMAT) Date createdEnd,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    ) {
        Page<AttributeRequestRes> page = opsAttributeService.getMemberAvailable(
                businessId,
                programId,
                code,
                name,
                havingMasterData,
                createdStart,
                createdEnd,
                status,
                new OffsetBasedPageRequest(offset, limit, null)
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/members/requests/available/{id}")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getAvailableMemberDetail(@PathVariable("id") Integer id) {
        return success(opsAttributeService.getAvailableDetail(id, EAttributeType.MEMBER));
    }

    @GetMapping("/members/requests/available/{id}/master-data/export")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportAvailableMemberMasterDataDetail(@PathVariable("id") Integer id) {
        ResourceDTO dto = opsAttributeService.exportMasterData(id, EAttributeType.MEMBER);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/members/requests/in-review/{id}/master-data/export")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportInReviewMemberMasterDataDetail(@PathVariable("id") Integer id) {
        ResourceDTO dto = opsAttributeService.exportInReviewMasterData(id, EMakerCheckerType.MEMBER_ATTRIBUTE);
        return new ResponseEntity<>(dto.getResource(), OpsCommonExcelService.setHeaderForFile(dto.getFilename()), HttpStatus.OK);
    }

    @GetMapping("/members/requests/available/{id}/service")
    @Authorize(role = AccessRole.MEMBER_ATTRIBUTE_MANAGEMENT, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> exportAvailableMemberServiceDetail(@PathVariable("id") Integer id) {
        return success(opsAttributeService.getAttributeService(id, EAttributeType.MEMBER));
    }
}