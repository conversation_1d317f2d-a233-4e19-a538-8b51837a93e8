package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@AllArgsConstructor
@NoArgsConstructor
public class AuditTrailAutoEarningRes {

    private Long id;

    private Long dataFileId;

    private String dataFileName;

    private MemberInfo oneuId;

    private MemberInfo loyaltyCustId;

    private String transactionId;

    private BigDecimal transactionAmount;

    private String ftTransactionId;

    private String oneuTransactionRef;

    private ETransactionStatus oneuStatus;

    private String oneuErrorMessage;

    private String tcbTransactionRef;

    private ETransactionStatus tcbStatus;

    private String tcbErrorMessage;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;
}

