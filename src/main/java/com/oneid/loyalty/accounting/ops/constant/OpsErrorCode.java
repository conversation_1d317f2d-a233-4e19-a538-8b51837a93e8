package com.oneid.loyalty.accounting.ops.constant;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OpsErrorCode {
    /*
     * Validate - 40491XX
     */
    CORPORATION_NAME_EXIST(4049101),
    CORPORATION_PHONE_EXIST(4049102),
    CHAIN_PHONE_EXIST(4049103),
    STORE_PHONE_EXIST(4049104),
    TAX_IDENTIFICATION_NUMBER_EXIST(4049105),
    RULE_CONDITION_VALUE_IS_REQUIRED(4049106),
    RULE_CONDITION_IS_NOT_EMPTY(4049107),
    /*
     * Validate - 40991XX
     */
    TRANSACTION_NOT_FOUND(4099101),
    TRANSACTION_NOT_ACTIVE(4099102),
    CARD_TYPE_NOT_MATCHED(4099103),

    NEW_TXN_INFO_FOR_REVERT_NOT_FOUND(4099103),
    ADJUST_POINT_MUST_GREATER_THAN_ZERO(4099104),

    TIER_ADJUSTMENT_COMPLETELY_FINISHED(4100048),


    /*
     *
     */
    TRANSACTION_TYPE_NOT_SUPPORT(5009001),
    REASON_CODE_IS_INVALID(5009002),

    RESET_VALUE_MUST_LESS_COUNTER_ACCUMULATION(5009004),
    TOTAL_COUNTED_MUST_LESS_LIMIT_THRESHOLD(5009005),

    
    //403xxxx
    ACCESS_DENIED(4030100);

    private int value;

    private OpsErrorCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return this.name();
    }
}
