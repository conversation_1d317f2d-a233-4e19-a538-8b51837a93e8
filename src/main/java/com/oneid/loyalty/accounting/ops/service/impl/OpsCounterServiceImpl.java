package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyRuleFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CounterFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.kafka.event.ResetRuleEvent;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.EditCounterReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterServicesRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.LinkedServiceRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.loyalty.accounting.ops.publisher.MessagePublisher;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.VersioningService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.ECounterType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.CounterHistory;
import com.oneid.oneloyalty.common.entity.CounterRequest;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.entity.LimitationRequest;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramTierPolicy;
import com.oneid.oneloyalty.common.entity.ServiceCounterRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CounterRepository;
import com.oneid.oneloyalty.common.repository.CounterRequestRepository;
import com.oneid.oneloyalty.common.repository.LimitationRequestRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ServiceCounterRequestRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CounterHistoryService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.LimitationService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierPolicyService;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class OpsCounterServiceImpl implements OpsCounterService {
    @Value("${maker-checker.module.counter_seq}")
    private String moduleId;

    @Value("${spring.redis.reset-rule.channel}")
    private String resetRuleTopic;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private CounterRequestRepository counterRequestRepository;

    private TransactionTemplate transactionTemplate;

    @Autowired
    private OpsRuleRequestService opsRuleRequestService;

    @Autowired
    private CounterRepository counterRepository;

    @Autowired
    private CounterService counterService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramTierPolicyService programTierPolicyService;

    @Autowired
    private LimitationService limitationService;

    @Autowired
    private LimitationRequestRepository limitationRequestRepository;

    @Autowired
    private ServiceCounterRequestRepository serviceCounterRequestRepository;

    @Autowired
    private OpsRuleService opsRuleService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private CounterHistoryService counterHistoryService;

    @Autowired
    private VersioningService versioningService;

    @Autowired
    private OneloyaltyRuleFeignClient oneloyaltyRuleFeignClient;

    @Autowired
    private MessagePublisher messagePublisher;

    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public Integer requestCreatingCounterRequest(CreateCounterReq req) {
        CounterRequest counterRequest = transactionTemplate.execute(status -> {
            CounterRequest entity = createRequest(req);

            opsRuleRequestService.createRuleRequest(entity.getId(), entity.getProgramId(), EServiceType.COUNTER, req.getRules());

            return entity;
        });

        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(counterRequest.getId().toString())
                .payload(CounterFeignReq.builder()
                        .counterRequestId(counterRequest.getId())
                        .build())
                .build();

        makerCheckerServiceClient.changes(feignReq);

        return counterRequest.getId();
    }

    @Override
    public Page<CounterRes> getAvailableCounterRequests(
            Integer businessId,
            Integer programId,
            String code,
            ECommonStatus counterStatus,
            Pageable pageable) {
        SpecificationBuilder specification = new SpecificationBuilder();

        if (counterStatus != null)
            specification.add(new SearchCriteria("status", counterStatus, SearchOperation.EQUAL));

        if (programId != null)
            specification.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        if (businessId != null)
            specification.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (code != null)
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        Page<Counter> page = counterService.find(specification, pageable);
        List<CounterRes> content = new ArrayList<>();
        for (Counter counter : page.getContent()) {
            Optional<Business> businessOpt = businessService.find(counter.getBusinessId());
            Optional<Program> programOpt = programService.find(counter.getProgramId());
            CounterRes counterRes = CounterRes.builder()
                    .counterStatus(counter.getStatus())
                    .counterId(counter.getId())
                    .name(counter.getName())
                    .code(counter.getCode())
                    .startDate(counter.getStartDate())
                    .endDate(counter.getEndDate())
                    .serviceType(counter.getServiceType())
                    .counterAttribute(counter.getCounterAttribute())
                    .counterLevel(counter.getLevel())
                    .period(counter.getPeriod())
                    .periodDisplay(counter.getPeriod().getDisplayName())
                    .description(counter.getDescription())
                    .enableRevert(counter.getEnableRevert())
                    .counterType(counter.getType())
                    .counterTypeDisplay(Objects.nonNull(counter.getType()) ? counter.getType().getDisplayName() : null)
                    .build();
            if (businessOpt.isPresent()) {
                counterRes.setBusinessName(businessOpt.get().getName());
                counterRes.setBusinessCode(businessOpt.get().getCode());
                counterRes.setBusinessId(businessOpt.get().getId());
            }
            if (programOpt.isPresent()) {
                counterRes.setProgramName(programOpt.get().getName());
                counterRes.setProgramCode(programOpt.get().getCode());
                counterRes.setProgramId(programOpt.get().getId());
            }
            content.add(counterRes);
        }
        return new PageImpl<CounterRes>(content, pageable, page.getTotalElements());
    }

    @Override
    public Page<CounterRes> getInReviewCounterRequests(EApprovalStatus approvalStatus,
                                                       String fromCreatedAt, String toCreatedAt,
                                                       String fromReviewedAt, String toReviewedAt,
                                                       String createdBy, String checkedBy,
                                                       Integer offset, Integer limit) {

        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.COUNTER.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        checkedBy,
                        checkedDate
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);
        List<CounterRes> content = new ArrayList<>();
        for (MakerCheckerInternalDataDetailRes item : previewRes.getData()) {
            CreateCounterReq counter = objectMapper.convertValue(item.getPayload(), CreateCounterReq.class);
            Program program = programService.find(counter.getProgramId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
            );

            Business business = businessService.find(counter.getBusinessId()).orElseThrow(
                    () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
            );

            content.add(CounterRes.builder()
                    .businessName(business.getName())
                    .businessId(business.getId())
                    .businessCode(business.getCode())
                    .programName(program.getName())
                    .programId(program.getId())
                    .programCode(program.getCode())
                    .counterStatus(ECommonStatus.of(counter.getCounterStatus()))
//                    .counterId(counter.getId())
                    .id(item.getId())
                    .name(counter.getName())
                    .code(counter.getCode())
                    .startDate(counter.getStartDate())
                    .endDate(counter.getEndDate())
                    .serviceType(counter.getServiceType())
                    .counterAttribute(counter.getCounterAttribute())
                    .counterLevel(counter.getCounterLevel())
                    .period(counter.getPeriod())
                    .description(counter.getDescription())
//                    .enableRevert(counter.getEnableRevert())
                    .counterType(counter.getCounterType())
                    .counterTypeDisplay(counter.getCounterType().getDisplayName())
                    .createdAt(item.getMadeDate() != null ? Date.from(ZonedDateTime.parse(item.getMadeDate()).toInstant()) : null)
                    .createdBy(item.getMadeByUserName())
                    .approvedAt(item.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(item.getCheckedDate()).toInstant()) : null)
                    .approvedBy(item.getCheckedByUserName())
                    .reviewedAt(item.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(item.getCheckedDate()).toInstant()) : null)
                    .reviewedBy(item.getCheckedByUserName())
                    .approvalStatus(EApprovalStatus.of(item.getStatus()))
                    .build());
        }
        return new PageImpl<CounterRes>(content, new OffsetBasedPageRequest(offset, limit, null), previewRes.getMeta().getTotal());
    }

    @Override
    public Page<VersionRes> getVersionList(
            Integer counterId,
            List<EApprovalStatus> approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String checkedBy,
            Integer offset,
            Integer limit
    ) {
        Counter counter = counterService.findById(counterId).
                orElseThrow(() ->
                        new BusinessException(ErrorCode.COUNTER_NOT_FOUND, null, null)
                );
        if (Objects.nonNull(counter.getRequestCode())) {
            return versioningService.getVersions(
                    counter.getRequestCode(),
                    counter.getVersion(),
                    approvalStatus,
                    fromCreatedAt,
                    toCreatedAt,
                    fromReviewedAt,
                    toReviewedAt,
                    createdBy,
                    checkedBy,
                    offset,
                    limit
            );
        }
        return new PageImpl<VersionRes>(Collections.EMPTY_LIST, Pageable.unpaged(), 0);

    }

    @Override
    public CounterRes getInReviewCounterRequestById(Integer reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(reviewId));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Counter - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.COUNTER.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Counter - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        CreateCounterReq payload = this.objectMapper.convertValue(previewDetailRes.getData().getPayload(), CreateCounterReq.class);
        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );
        Business business = businessService.find(payload.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
        );
        List<RuleRes> ruleInReview = opsRuleService.getRuleInReview(payload.getProgramId(), payload.getRules(), payload.getCode());
        List<CounterServicesRes> lCounterServicesRes = new ArrayList<>();
        if (Objects.nonNull(payload.getId())) {
            EServiceType serviceType = payload.getServiceType();
            if (serviceType != null) {
                switch (serviceType) {
                    case TIER:
                        lCounterServicesRes = getLinkedProgramTierPolicies(payload.getId());
                        break;
                    case LIMITATION:
                        lCounterServicesRes = getLinkedLimitations(payload.getId(), payload.getProgramId());
                        break;
                    default:
                }
            }
        }
        return CounterRes.builder()
                .requestId(payload.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .programId(program.getId())
                .programName(program.getName())
                .programCode(program.getCode())
                .period(payload.getPeriod())
                .periodDisplay(payload.getPeriod().getDisplayName())
                .restoredFromVersion(previewDetailRes.getData().getRestoredFromVersion())
                .restoredFromId(previewDetailRes.getData().getRestoredFromId())
                .version(previewDetailRes.getData().getVersion())
                .endDate(payload.getEndDate())
                .counterType(payload.getCounterType())
                .counterTypeDisplay(payload.getCounterType().getDisplayName())
                .counterLevel(payload.getCounterLevel())
                .counterAttribute(payload.getCounterAttribute())
                .counterStatus(ECommonStatus.of(payload.getCounterStatus()))
                .name(payload.getName())
                .description(payload.getDescription())
                .enableRevert(payload.getEnableRevert())
                .code(payload.getCode())
                .startDate(payload.getStartDate())
                .serviceType(payload.getServiceType())
                .createdAt(Objects.nonNull(previewDetailRes.getData().getMadeDate()) ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getMadeDate()).toInstant()) : null)
                .createdBy(previewDetailRes.getData().getMadeByUserName())
                .approvedAt(Objects.nonNull(previewDetailRes.getData().getCheckedDate()) ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getCheckedDate()).toInstant()) : null)
                .approvedBy(previewDetailRes.getData().getCheckedByUserName())
                .approvalStatus(EApprovalStatus.of(previewDetailRes.getData().getStatus()))
                .reason(previewDetailRes.getData().getComment())
                .serviceType(payload.getServiceType())
                .enableRevert(payload.getEnableRevert())
                .counterServicesRes(lCounterServicesRes)
                .resetValue(payload.getResetValue())
//                .counterServicesRes(lCounterServicesRes)
                .rules(ruleInReview)
                .build();
    }

    @Override
    public CounterRes getAvailableCounterRequestById(Integer requestId) {
        return getAvailableCounterById(requestId);
    }

    private CounterRes getAvailableCounterById(Integer counterId) {
        Counter counter = counterService.findById(counterId)
                .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, null, null));


        Business business = businessRepository.findById(counter.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        Program program = programRepository.findByIdAndBusinessId(counter.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        EServiceType serviceType = counter.getServiceType();
        List<RuleRes> rules = opsRuleService.getRule(counter.getCode(), program.getId(), EServiceType.COUNTER);

        List<CounterServicesRes> lCounterServicesRes = null;
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    lCounterServicesRes = getLinkedProgramTierPolicies(counterId);
                    break;
                case LIMITATION:
                    lCounterServicesRes = getLinkedLimitations(counterId, business.getId());
                    break;
                default:
            }
        }
        return CounterRes.builder()
                .requestId(counter.getId())
                .counterStatusHeader(getCounterActiveStatus(counter))
                .businessId(business.getId())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .programId(program.getId())
                .programName(program.getName())
                .programCode(program.getCode())
                .endDate(counter.getEndDate())
                .period(counter.getPeriod())
                .periodDisplay(counter.getPeriod().getDisplayName())
                .counterType(counter.getType())
                .counterTypeDisplay(counter.getType().getDisplayName())
                .counterLevel(counter.getLevel())
                .counterAttribute(counter.getCounterAttribute())
                .counterStatus(counter.getStatus())
                .name(counter.getName())
                .description(counter.getDescription())
                .enableRevert(counter.getEnableRevert())
                .code(counter.getCode())
                .startDate(counter.getStartDate())
                .serviceType(counter.getServiceType())
                .updateAt(counter.getUpdatedAt())
                .updatedAt(counter.getUpdatedAt())
                .createdAt(counter.getCreatedAt())
                .updateBy(counter.getUpdatedBy())
                .updatedBy(counter.getUpdatedBy())
                .createdBy(counter.getCreatedBy())
                .approvedAt(counter.getApprovedAt())
                .approvedBy(counter.getApprovedBy())
                .serviceType(counter.getServiceType())
                .enableRevert(counter.getEnableRevert())
                .resetValue(counter.getResetValue())
                .counterServicesRes(lCounterServicesRes)
                .rules(rules)
                .build();
    }

    private List<CounterServicesRes> getLinkedProgramTierPolicies(Integer counterId) {
        List<ProgramTierPolicy> programTierPolicy =
                programTierPolicyService.findLikeCounterIds(counterId.toString());
        return programTierPolicy
                .stream()
                .map(tier -> CounterServicesRes.valueOf(tier, null))
                .collect(Collectors.toList());
    }

    private List<CounterServicesRes> getLinkedLimitations(Integer counterId, Integer businessId) {

        List<Limitation> limitations =
                limitationService.findAllByCounterId(counterId);

        return limitations
                .stream()
                .map(limitation -> {
                    CounterServicesRes counterServicesRes = CounterServicesRes.valueOf(null, limitation);
                    Optional<LimitationRequest> effectedVersion = limitationRequestRepository.findEffectedVersion(businessId, limitation.getProgramId(), counterServicesRes.getCode());
                    effectedVersion.ifPresent(limitationRequest -> counterServicesRes.setId(limitationRequest.getId()));
                    return counterServicesRes;
                })
                .collect(Collectors.toList());
    }

    @Override
    public CounterRes getEditCounterRequestSetting(Integer requestId) {
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        Program program = programService.findActive(counter.getProgramId());
        Business business = businessService.findActive(program.getBusinessId());
        List<RuleRes> rules = opsRuleService.getRule(counter.getCode(), program.getId(), EServiceType.COUNTER);
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.COUNTER.getType(), counter.getRequestCode());
        String editKey = opsReqPendingValidator.generateEditKey(counter.getRequestCode(), counter.getVersion());

        EServiceType serviceType = counter.getServiceType();
        List<CounterServicesRes> lCounterServicesRes = null;
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    lCounterServicesRes = getLinkedProgramTierPolicies(requestId);
                    break;
                case LIMITATION:
                    lCounterServicesRes = getLinkedLimitations(requestId, business.getId());
                    break;
                default:
            }
        }
        return CounterRes.builder()
                .requestId(counter.getId())
                .businessId(counter.getBusinessId())
                .programId(counter.getProgramId())
                .businessName(business.getName())
                .programName(program.getName())
                .nextVersion(counter.getVersion() + 1)
                .period(counter.getPeriod())
                .periodDisplay(counter.getPeriod().getDisplayName())
                .description(counter.getDescription())
                .enableRevert(counter.getEnableRevert())
                .counterStatus(counter.getStatus())
                .name(counter.getName())
                .code(counter.getCode())
                .startDate(counter.getStartDate())
                .serviceType(counter.getServiceType())
                .endDate(counter.getEndDate())
                .counterType(counter.getType())
                .counterTypeDisplay(counter.getType().getDisplayName())
                .counterLevel(counter.getLevel())
                .counterAttribute(counter.getCounterAttribute())
                .counterServicesRes(lCounterServicesRes)
                .rules(rules)
                .editKey(editKey)
                .build();
    }

    @Override
    public MakerCheckerInternalMakerRes requestEditingCounterRequest(Integer requestId, EditCounterReq req) {
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        Date currentTime = new Date();
        if (req.getEndDate().before(currentTime) || req.getEndDate().before(counter.getStartDate())) {
            throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);
        }
        opsReqPendingValidator.verifyEditKey(req.getEditKey(), counter.getRequestCode(), counter.getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.COUNTER.getType(), counter.getRequestCode());

        CreateCounterReq payload = new CreateCounterReq();
        payload.setId(requestId);
        payload.setName(req.getName());
        payload.setEndDate(req.getEndDate());
        payload.setCounterStatus(req.getCounterStatus());
        payload.setServiceType(req.getServiceType());
        payload.setDescription(req.getDescription());
        payload.setEnableRevert(req.getEnableRevert());
        payload.setCode(counter.getCode());
        payload.setBusinessId(counter.getBusinessId());
        payload.setProgramId(counter.getProgramId());
        payload.setStartDate(counter.getStartDate());
        payload.setPeriod(counter.getPeriod());
        payload.setCounterLevel(counter.getLevel());
        payload.setCounterType(counter.getType());
        payload.setCounterAttribute(counter.getCounterAttribute());
        payload.setResetValue(counter.getResetValue());
        payload.setRules(req.getRules());

        MakerCheckerInternalMakerReq<CreateCounterReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateCounterReq>builder()
                .requestCode(Objects.nonNull(counter.getRequestCode()) ? counter.getRequestCode() : UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.COUNTER.getName())
                .requestType(EMakerCheckerType.COUNTER.getType())
                .payload(payload)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> apiResponse = makerCheckerInternalFeignClient.maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != apiResponse.getMeta().getCode())
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Counter - request editing attribute request call checker error: " + apiResponse.getMeta().getMessage(), null);
        return apiResponse.getData();
    }

    @Override
    public Page<LinkedServiceRes> getLinkedServiceTypes(Integer requestId, Pageable pageable) {
        Counter counter = counterService.findById(requestId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
        EServiceType serviceType = counter.getServiceType();
        if (serviceType != null) {
            switch (serviceType) {
                case TIER:
                    return getLinkedTierPolicies(counter, pageable);

                case LIMITATION:
                    return getLinkedLimitations(counter, pageable);
                default:
            }
        }
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }

    private Page<LinkedServiceRes> getLinkedLimitations(Counter counter, Pageable pageable) {
        Page<Limitation> limitations = limitationService.find(counter.getId(), pageable);
        List<LinkedServiceRes> content = limitations.getContent()
                .stream()
                .map(request -> LinkedServiceRes.builder()
                        .requestId(request.getId())
                        .serviceCode(request.getCode())
                        .serviceName(request.getName())
                        .status(request.getStatus())
                        .serviceType(EServiceType.LIMITATION)
                        .startDate(request.getStartDate())
                        .endDate(request.getEndDate())
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(content, pageable, limitations.getTotalElements());
    }

    private CounterRequest editCounterRequest(Integer requestId, EditCounterReq req) {
        CounterRequest availableRequest = counterRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));

        EApprovalStatus approvalStatus = availableRequest.getApprovalStatus();

        if (approvalStatus.equals(EApprovalStatus.PENDING))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, null, null);

        if (approvalStatus.equals(EApprovalStatus.REJECTED))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_CAN_NOT_BE_EDITED, null, null);

        if (!availableRequest.getRequestStatus().equals(ECommonStatus.ACTIVE))
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, null, null);

        counterRequestRepository.findPendingVersion(availableRequest.getBusinessId(), availableRequest.getProgramId(), availableRequest.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, null, null);
                });

        int totalLinkedServiceTypes = countLinkedServiceTypes(availableRequest);

        if (req.getServiceType() != null
                && availableRequest.getServiceType() != null
                && !availableRequest.getServiceType().equals(req.getServiceType())
                && totalLinkedServiceTypes > 0)
            throw new BusinessException(ErrorCode.COUNTER_REQUEST_SERVICE_TYPE_CAN_NOT_BE_EDITED, null, null);

        if (req.getCounterStatus().equals(ECommonStatus.INACTIVE) && totalLinkedServiceTypes > 0)
            throw new BusinessException(ErrorCode.CAN_NOT_INACTIVATED_COUNTER_STATUS_LINKED_SERVICE_TYPES, null, null);

        CounterRequest newRequest = new CounterRequest();

        newRequest.setName(req.getName());
        newRequest.setDescription(req.getDescription());
        newRequest.setEndDate(req.getEndDate());
        newRequest.setStatus(ECommonStatus.of(req.getCounterStatus()));

        newRequest.setServiceType(req.getServiceType());
        newRequest.setVersion(availableRequest.getVersion() + 1);
        newRequest.setBusinessId(availableRequest.getBusinessId());
        newRequest.setProgramId(availableRequest.getProgramId());
        newRequest.setCode(availableRequest.getCode());
        newRequest.setStartDate(availableRequest.getStartDate());
        newRequest.setPeriod(availableRequest.getPeriod());
        newRequest.setLevel(availableRequest.getLevel());
        newRequest.setType(availableRequest.getType());
        newRequest.setRequestStatus(ECommonStatus.PENDING);
        newRequest.setApprovalStatus(EApprovalStatus.PENDING);
        newRequest.setCounterAttribute(availableRequest.getCounterAttribute());
        newRequest.setEnableRevert(EBoolean.NO);

        newRequest = counterRequestRepository.save(newRequest);

        Integer counterRequestId = newRequest.getId();

        Collection<ServiceCounterRequest> serviceCounterRequests = serviceCounterRequestRepository
                .findByCounterRequestIdAndActionTypeIn(requestId, List.of(ECommonActionType.Create, ECommonActionType.Update));

        Collection<ServiceCounterRequest> serviceCounterRequestUpdates = serviceCounterRequests.stream().map(
                serviceCounterRequest -> {
                    ServiceCounterRequest newServiceCounterRequest = new ServiceCounterRequest();
                    newServiceCounterRequest.setCounterRequestId(counterRequestId);
                    newServiceCounterRequest.setServiceRequestId(serviceCounterRequest.getServiceRequestId());
                    newServiceCounterRequest.setServiceType(serviceCounterRequest.getServiceType());
                    newServiceCounterRequest.setStatus(serviceCounterRequest.getStatus());
                    newServiceCounterRequest.setActionType(ECommonActionType.Update);
                    return newServiceCounterRequest;
                }
        ).collect(Collectors.toList());

        serviceCounterRequestRepository.saveAll(serviceCounterRequestUpdates);

        return newRequest;
    }

    private CounterRequest createRequest(CreateCounterReq req) {
        Date currentTime = new Date();

        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        if (business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, null, null);

        Program program = programRepository.findByIdAndBusinessId(req.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, null, null);

        counterRequestRepository.findPendingVersion(business.getId(), program.getId(), req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{req.getCode()});
                });

        counterRequestRepository.findEffectedVersion(business.getId(), program.getId(), req.getCode())
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{req.getCode()});
                });


        if (req.getServiceType() != null) {
            EServiceType serviceType = req.getServiceType();

            if (!EServiceType.TIER.equals(serviceType) && !EServiceType.LIMITATION.equals(serviceType))
                throw new BusinessException(ErrorCode.COUNTER_SERVICE_TYPE_IS_INVALID, null, null, new Object[]{req.getServiceType()});

            if (serviceType.equals(EServiceType.TIER) && !req.getCounterLevel().equals(ECounterLevel.MEMBER))
                throw new BusinessException(ErrorCode.COUNTER_LEVEL_IS_NOT_AVAILABLE_FOR_SEVICE_TYPE, null, null, new Object[]{req.getCounterLevel(), req.getServiceType()});
        }

        if (req.getStartDate().before(currentTime))
            throw new BusinessException(ErrorCode.COUNTER_START_DATE_INVALID, null, null);

        if (req.getEndDate().before(currentTime) || req.getEndDate().before(req.getStartDate()))
            throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);

        CounterRequest request = new CounterRequest();

        request.setVersion(1);
        request.setBusinessId(business.getId());
        request.setProgramId(program.getId());
        request.setCode(req.getCode());
        request.setName(req.getName());
        request.setDescription(req.getDescription());
        request.setStartDate(req.getStartDate());
        request.setEndDate(req.getEndDate());
        request.setPeriod(req.getPeriod());
        request.setLevel(req.getCounterLevel());
        request.setType(req.getCounterType());
        request.setStatus(ECommonStatus.of(req.getCounterStatus()));
        request.setEnableRevert(EBoolean.NO);
        request.setServiceType(req.getServiceType());
        request.setRequestStatus(ECommonStatus.PENDING);
        request.setApprovalStatus(EApprovalStatus.PENDING);

        if (req.getCounterType().equals(ECounterType.VALUE)) {
            request.setCounterAttribute(req.getCounterAttribute());
        }

        return counterRequestRepository.save(request);
    }

    private CounterRes getCounterRequestById(Integer requestId) {
        CounterRequest counterRequest = counterRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));

        Business business = businessRepository.findById(counterRequest.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        Program program = programRepository.findByIdAndBusinessId(counterRequest.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        List<RuleRes> rules = opsRuleRequestService.getRuleRequests(counterRequest.getId(), counterRequest.getProgramId(), EServiceType.COUNTER);

        return CounterRes.builder()
                .requestId(counterRequest.getId())
                .approvalStatus(counterRequest.getApprovalStatus())
                .version(counterRequest.getVersion())
                .businessName(business.getName())
                .businessCode(business.getCode())
                .programName(program.getName())
                .programCode(program.getCode())
                .endDate(counterRequest.getEndDate())
                .period(counterRequest.getPeriod())
                .counterType(counterRequest.getType())
                .counterLevel(counterRequest.getLevel())
                .counterAttribute(counterRequest.getCounterAttribute())
                .rejectedReason(counterRequest.getRejectedReason())
                .counterStatus(counterRequest.getStatus())
                .name(counterRequest.getName())
                .description(counterRequest.getDescription())
                .code(counterRequest.getCode())
                .startDate(counterRequest.getStartDate())
                .requestStatus(counterRequest.getRequestStatus())
                .serviceType(counterRequest.getServiceType())
                .rules(rules)
                .updateAt(counterRequest.getUpdatedAt())
                .createdAt(counterRequest.getCreatedAt())
                .updateBy(counterRequest.getUpdatedBy())
                .createdBy(counterRequest.getCreatedBy())
                .approvedAt(counterRequest.getApprovedAt())
                .approvedBy(counterRequest.getApprovedBy())
                .serviceType(counterRequest.getServiceType())
                .enableRevert(counterRequest.getEnableRevert())
                .build();
    }

    private List<CounterRes> transform(List<Object[]> entities, Map<Integer, ChangeRecordFeginRes> changeRecordFeginResMap) {
        List<CounterRes> content = new ArrayList<CounterRes>();

        Business business = null;
        Program program = null;
        Counter counterRequest = null;
        Integer reviewId = null;

        for (Object[] each : entities) {
            business = Business.class.cast(each[0]);
            program = Program.class.cast(each[1]);
            counterRequest = Counter.class.cast(each[2]);

            reviewId = changeRecordFeginResMap.containsKey(counterRequest.getId()) ? changeRecordFeginResMap.get(counterRequest.getId()).getChangeRequestId() : null;

            content.add(CounterRes.builder()
                    .reviewId(reviewId)
                    .requestId(counterRequest.getId())
                    .businessName(business.getName())
                    .programName(program.getName())
                    .counterStatus(counterRequest.getStatus())
                    .name(counterRequest.getName())
                    .code(counterRequest.getCode())
                    .startDate(counterRequest.getStartDate())
                    .endDate(counterRequest.getEndDate())
                    .serviceType(counterRequest.getServiceType())
                    .build());
        }

        return content.stream().sorted((o1, o2) -> o2.getRequestId().compareTo(o1.getRequestId()))
                .collect(Collectors.toList()); // order by id desc
    }

    private int countLinkedServiceTypes(CounterRequest availableRequest) {
        int total = 0;

        if (availableRequest.getServiceType() != null) {
            switch (availableRequest.getServiceType()) {
                case TIER:
                    total = serviceCounterRequestRepository.countTotalLinkedTierPolicies(availableRequest.getId(), EServiceType.TIER);
                    break;

                case LIMITATION:
                    Counter counter = counterRepository.findByProgramIdAndCode(availableRequest.getProgramId(), availableRequest.getCode());

                    total = limitationRequestRepository.countByCounterIdAndProgramIdAndBusinessId(counter.getId(), availableRequest.getProgramId(), availableRequest.getBusinessId());

                    break;

                default:
                    break;
            }
        }

        return total;
    }

    private Page<LinkedServiceRes> getLinkedTierPolicies(Counter counter, Pageable pageable) {
        List<ProgramTierPolicy> programTierPolicy =
                programTierPolicyService.findLikeCounterIds(counter.getId().toString());
        List<LinkedServiceRes> content = programTierPolicy
                .stream()
                .map(request -> LinkedServiceRes.builder()
                        .requestId(request.getId())
                        .serviceCode(request.getCode())
                        .serviceName(request.getName())
                        .serviceType(EServiceType.TIER)
                        .status(request.getStatus())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .startDate(request.getStartDate())
                        .endDate(request.getEndDate())
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(content, pageable, content.size());
    }

    private Page<LinkedServiceRes> getLinkedLimitations(Integer counterRequestId, Pageable pageable) {
        CounterRequest counterRequest = counterRequestRepository.findById(counterRequestId)
                .orElse(null);

        if (counterRequest == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        Counter counter = counterRepository.findByProgramIdAndCode(counterRequest.getProgramId(), counterRequest.getCode());

        if (counter == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        Page<LimitationRequest> page = limitationRequestRepository.findByCounterIdAndBusinessId(counter.getId(), counterRequest.getBusinessId(), pageable);

        if (page.getContent() == null)
            return new PageImpl<LinkedServiceRes>(Collections.emptyList(), pageable, 0);

        List<LinkedServiceRes> content = page.getContent()
                .stream()
                .map(request -> {
                    return LinkedServiceRes.builder()
                            .requestId(request.getId())
                            .serviceCode(request.getCode())
                            .serviceName(request.getName())
                            .serviceType(EServiceType.LIMITATION)
                            .startDate(request.getStartDate())
                            .endDate(request.getEndDate())
                            .status(request.getLimitationStatus())
                            .approvalStatus(request.getApprovalStatus())
                            .build();
                })
                .collect(Collectors.toList());

        return new PageImpl<LinkedServiceRes>(content, pageable, page.getTotalElements());
    }

    @Override
    public MakerCheckerInternalMakerRes createMaker(CreateCounterReq req) {
        Date currentTime = new Date();

        businessService.findActive(req.getBusinessId());
        programService.findByIdAndBusinessId(req.getProgramId(), req.getBusinessId());

        // Whether counter code is used in the available list?
        List<Counter> counters = counterService.findByProgramIdAndCode(req.getProgramId(), req.getCode());
        if (!counters.isEmpty()) {
            throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED, null, null, new Object[]{req.getCode()});
        }

        validationCodeExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getCode());

        if (Objects.nonNull(req.getServiceType())) {
            EServiceType serviceType = req.getServiceType();
            if (!EServiceType.TIER.equals(serviceType) && !EServiceType.LIMITATION.equals(serviceType))
                throw new BusinessException(ErrorCode.COUNTER_SERVICE_TYPE_IS_INVALID, null, null, new Object[]{req.getServiceType()});
            if (serviceType.equals(EServiceType.TIER) && !req.getCounterLevel().equals(ECounterLevel.MEMBER))
                throw new BusinessException(ErrorCode.COUNTER_LEVEL_IS_NOT_AVAILABLE_FOR_SEVICE_TYPE, null, null,
                        new Object[]{req.getCounterLevel(), req.getServiceType()});
        }

        if (req.getStartDate().before(currentTime))
            throw new BusinessException(ErrorCode.COUNTER_START_DATE_INVALID, null, null);
        if (req.getEndDate().before(currentTime) || req.getEndDate().before(req.getStartDate()))
            throw new BusinessException(ErrorCode.COUNTER_END_DATE_INVALID, null, null);

        MakerCheckerInternalMakerReq<CreateCounterReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateCounterReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.COUNTER.getName())
                .requestType(EMakerCheckerType.COUNTER.getType())
                .payload(req)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Counter call maker error: " + createMakerProgramCorporation.getMeta().getMessage(), null);
        }
        return createMakerProgramCorporation.getData();
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> response = makerCheckerInternalFeignClient.previewDetail(req.getId());
        if (response.getMeta().getCode() != 200) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
        CreateCounterReq payload = objectMapper.convertValue(response.getData().getPayload(), CreateCounterReq.class);
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();

        if (req.getStatus() == EApprovalStatus.APPROVED) {
            Counter counter;
            programService.findActive(payload.getProgramId());
            businessService.findActive(payload.getBusinessId());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(response.getData().getMadeByUserName());
            String createdBy = null;
            String updatedBy;

            if (payload.getId() != null) {
                counter = counterService.findById(payload.getId()).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_REQUEST_NOT_FOUND, null, null));
                convertCounter(counter, payload);
                updatedBy = response.getData().getMadeByUserName();
                resetRuleReqs = opsRuleService.editRule(counter, payload.getRules(), response.getData(), approvedBy);
            } else {
                counter = new Counter();
                convertCounter(counter, payload);
                counter.setRequestCode(response.getData().getRequestCode());
                resetRuleReqs = opsRuleService.createRule(counter, payload.getRules(), response.getData(), approvedBy);
                updatedBy = createdBy = response.getData().getMadeByUserName();
            }
            counter.setRequestCode(response.getData().getRequestCode());
            counter.setVersion(response.getData().getVersion());

            opsReqPendingValidator.updateInfoChecker(counter, response.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
            counterRepository.save(counter);

            // Check reset rule
            if (CollectionUtils.isNotEmpty(resetRuleReqs)) {
                APIFeignInternalResponse<?> ruleRes = oneloyaltyRuleFeignClient.checkReset(resetRuleReqs);
                if (ruleRes.getMeta().getCode() != 200) {
                    throw new BusinessException(ErrorCode.RESET_RULE_FAILED);
                }
            }
        }

        // Call to MakerChecker
        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plAprroved = makerCheckerInternalFeignClient.checker(checkerReq);
        if (plAprroved.getMeta().getCode() != 200) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS,
                    "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }

        if (EApprovalStatus.APPROVED == req.getStatus() && CollectionUtils.isNotEmpty(resetRuleReqs)) {
            ResetRuleEvent<?> ruleEvent = ResetRuleEvent.builder()
                    .id(UUID.randomUUID().toString())
                    .eventType(ResetRuleEvent.RESET_RULE_EVENT_TYPE)
                    .timeStamp(System.currentTimeMillis())
                    .payload(resetRuleReqs)
                    .build();
            messagePublisher.publish(resetRuleTopic, ruleEvent);
        }
    }

    private void convertCounter(Counter counter, CreateCounterReq payload) {
        counter.setId(payload.getId());
        counter.setBusinessId(payload.getBusinessId());
        counter.setCode(payload.getCode());
        counter.setProgramId(payload.getProgramId());
        counter.setName(payload.getName());
        counter.setDescription(payload.getDescription());
        counter.setServiceType(payload.getServiceType());
        counter.setPeriod(payload.getPeriod());
        counter.setLevel(payload.getCounterLevel());
        counter.setStatus(ECommonStatus.of(payload.getCounterStatus()));
        counter.setStartDate(payload.getStartDate());
        counter.setEndDate(payload.getEndDate());
        counter.setType(payload.getCounterType());
        counter.setEnableRevert(payload.getEnableRevert());
        if (payload.getCounterType().equals(ECounterType.VALUE)) {
            counter.setCounterAttribute(payload.getCounterAttribute());
        }
    }

    /**
     * Whether counter code is used
     *
     * @param businessId the business id
     * @param programId  the program id
     * @param code       the counter code
     */
    private void validationCodeExistInOtherReqPending(Integer businessId, Integer programId, String code) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.COUNTER.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        previewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), CreateCounterReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId) && ele.getCode().equals(code))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.COUNTER_CODE_IS_BEING_USED,
                            "[VALIDATION COUNTER CODE] counter code existed in other requests pending", code, new Object[]{code});
                });
    }

    @Override
    public CounterStatisticRes counterStatistic(Integer counterId, Date startedAt, Date endedAt, ECounterLevel counterLevel, String code, ECommonStatus status, Pageable pageable) {
        Counter counter = counterService.findById(counterId).orElseThrow(() -> new BusinessException(ErrorCode.COUNTER_NOT_FOUND, null, null));

        SpecificationBuilder specification = new SpecificationBuilder();

        specification.add(new SearchCriteria("counterId", counterId, SearchOperation.EQUAL));
        specification.add(new SearchCriteria("levelCode", counterLevel, SearchOperation.EQUAL));

        if (StringUtil.isNotEmpty(code))
            specification.add(new SearchCriteria("code", code, SearchOperation.EQUAL));

        if (!ObjectUtils.isEmpty(status))
            specification.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        if (!ECounterPeriod.NONE.equals(counter.getPeriod())) {
            if (startedAt == null || endedAt == null) {
                throw new BusinessException(
                        ErrorCode.COUNTER_STATISTIC_REQUIRED_FILTER, null, null,
                        new Object[]{String.format("started_at and ended_at for %s period", counter.getPeriod().getDisplayName().toLowerCase())}
                );
            }

            specification.add(new SearchCriteria("startDate", startedAt, SearchOperation.GREATER_THAN_EQUAL_DATE));
            specification.add(new SearchCriteria("endDate", new Date(endedAt.getTime() + (1000 * 60 * 60 * 24)), SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        List<CounterHistory> counterHistories = counterHistoryService.find(specification);
        Page<CounterHistory> counterHistoryPage = counterHistoryService.find(specification, pageable);

        BigDecimal sumCounting = BigDecimal.ZERO;
        Set<String> setCode = new HashSet<>();
        Date startDate = new Date();
        Date endDate = counterHistories.isEmpty() ? new Date() : counterHistories.get(0).getEndDate();

        for (CounterHistory c : counterHistories) {
            sumCounting = sumCounting.add(c.getCounting());
            setCode.add(c.getCode());
            if (c.getStartDate() != null && c.getStartDate().before(startDate))
                startDate = c.getStartDate();
            if (c.getEndDate() != null && c.getEndDate().after(endDate))
                endDate = c.getEndDate();
        }

        List<CounterStatisticRes.CounterHistory> histories = counterHistoryPage.stream()
                .map(c -> CounterStatisticRes.CounterHistory.builder()
                        .code(c.getCode())
                        .level(c.getLevelCode())
                        .startedAt(c.getStartDate())
                        .endedAt(c.getEndDate())
                        .totalCounted(c.getCounting())
                        .unit(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null)
                        .status(c.getStatus()).build()
                ).collect(Collectors.toList());

        CounterStatisticRes.Statistic statistic = new CounterStatisticRes().new Statistic();
        statistic.setTypePeriod(counter.getPeriod().getValue());
        statistic.setTypeCounted(counter.getCounterAttribute() != null ? counter.getCounterAttribute().getValue() : null);
        statistic.setTypeLevel(counter.getLevel().getValue());
        statistic.setTotalLevel(setCode.size());
        statistic.setTotalCounted(sumCounting);
        statistic.setTotalPeriod(mapPeriodToNumber(counter.getPeriod(), startDate, endDate));

        CounterStatisticRes counterStatisticRes = new CounterStatisticRes();
        counterStatisticRes.setCounterHistories(histories);
        counterStatisticRes.setStatistic(statistic);
        counterStatisticRes.setCounterStatusHeader(getCounterActiveStatus(counter));
        counterStatisticRes.setTotalHistories((int) counterHistoryPage.getTotalElements());

        return counterStatisticRes;
    }

    @Override
    public int getCounterActiveStatus(Counter counter) {
        if (counter.getEndDate() != null && counter.getEndDate().before(new Date())) {
            return 2;
        }
        Optional<CounterHistory> counterHistories = counterHistoryService.findFirstByCounterIdAndStatus(counter.getId(), ECommonStatus.ACTIVE);
        if (counterHistories.isEmpty()) {
            return 0;
        }
        return 1;
    }

    @Override
    public Integer mapPeriodToNumber(ECounterPeriod period, Date startDate, Date endDate) {
        if (ECounterPeriod.NONE.equals(period)) return null;

        Calendar startCalendar = Calendar.getInstance();
        Calendar endCalendar = Calendar.getInstance();
        endDate = new Date(endDate.getTime() + 1000); // Increase 1 second
        startCalendar.setTime(startDate);
        endCalendar.setTime(endDate);

        switch (period) {
            case YEARLY:
                return endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
            case MONTHLY:
                int diffYear = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
                return diffYear * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
            case DAILY:
                return (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            default:
                return 0;
        }
    }
}