package com.oneid.loyalty.accounting.ops.feign.model.req;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SAPSaleOrderCreateFeignReq {
    @JsonProperty("CUSTOMER")
    private String customer;

    @JsonProperty("POINT")
    private String point;

    @JsonProperty("POSTING_DATE")
    private String postingDate;

    @JsonProperty("REMARK")
    private String remark;
}
