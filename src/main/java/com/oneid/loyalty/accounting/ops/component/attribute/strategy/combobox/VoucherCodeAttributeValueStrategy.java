package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.component.service.AttributeValueService;
import com.oneid.loyalty.accounting.ops.feign.VoucherServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.VoucherRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class VoucherCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;
    @Autowired
    private AttributeValueService attributeValueService;

    @Autowired
    private VoucherServiceFeignClient voucherServiceFeignClient;
    private final static Logger LOGGER = LoggerFactory.getLogger(VoucherCodeAttributeValueStrategy.class);

    public VoucherCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getVoucherCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, Integer... programIds) {

        APIResponse<VoucherRequestPageFeignRes> voucherDetail = voucherServiceFeignClient.getVoucherPage("GIFT", null, value, 0, 1);
        if (voucherDetail.getMeta().getCode() != 200 || voucherDetail.getData().getContent().isEmpty()) {
            LOGGER.warn("Voucher {} not found", value);
            throw new IllegalArgumentException();
        }

        return AttributeCombobox.builder()
                .name(voucherDetail.getData().getContent().get(0).getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> voucherCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());
            OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(0, 1);
            return voucherCodes
                    .stream()
                    .map(ele -> {
                        Page<AttributeCombobox> voucherCodeAttributeValue = attributeValueService.getVoucherCodeAttributeValue(programIds[0], ele, pageRequest);
                        return voucherCodeAttributeValue.getContent();
                    }).flatMap(Collection::stream).collect(Collectors.toList());
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }

}
