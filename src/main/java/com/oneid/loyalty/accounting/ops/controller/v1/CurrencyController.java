package com.oneid.loyalty.accounting.ops.controller.v1;


import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.CurrencyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyEnum;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRes;
import com.oneid.loyalty.accounting.ops.service.OpsCurrencyService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.util.DateTimes;

@RestController
@RequestMapping(value = "v1/currencies")
@Validated
public class CurrencyController extends BaseController {
    @Autowired
    OpsCurrencyService opsCurrencyService;

    @GetMapping("enum")
    public ResponseEntity<?> getAllCurrency(@RequestParam(name = "business_id", required = true) Integer businessId) {
        List<CurrencyEnum> result = opsCurrencyService.getEnum(businessId);
        return success(result);
    }

    @GetMapping("/transactions")
    public ResponseEntity<?> getCurrenciesForTransaction(@RequestParam(name = "business_id") Integer businessId) {
        List<CurrencyEnum> result = opsCurrencyService.getEnum(businessId);
        return success(result);
    }

    @GetMapping("")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_CARD_BIN, permissions = {AccessPermission.VIEW }),
            @Authorize(role = AccessRole.CURRENCY, permissions = {AccessPermission.VIEW })
    }, any = true)
    public ResponseEntity<?> filterCurrencies(@RequestParam(name = "business_id", required = false) Integer businessId,
                                              @RequestParam(name = "code", required = false) String code,
                                              @RequestParam(name = "status", required = false)
                                              @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values") String status,
                                              @RequestParam(name = "created_date_from", required = false) Integer createdDateFrom,
                                              @RequestParam(name = "created_date_to", required = false) Integer createdDateTo,
                                              @Valid @RequestParam(name = "offset", defaultValue = "0") @Min(0) Integer offset,
                                              @Valid @RequestParam(name = "limit", defaultValue = "20") @Max(100) @Min(1) Integer limit,
                                              @Valid @RequestParam(value = "sort_direction", required = false) Sort.Direction sortDirection,
                                              @Valid @RequestParam(value = "sort_by", required = false) String sortBy
    ) {
        Date start = null, end = null;
        try {
            start = createdDateFrom != null ? DateTimes.parseDate("yyyyMMdd", String.valueOf(createdDateFrom)) : null;
            end = createdDateTo != null ? DateTimes.parseDate("yyyyMMdd", String.valueOf(createdDateTo)) : null;
        } catch (Exception ex) {
            return this.badRequest("create_date_from/ create_date_to is wrong format");
        }
        if (start != null && end != null && start.after(end)) {
            return this.badRequest("create_date_from > create_date_to");
        }
        if (start != null) {
            start.setHours(0);
            start.setMinutes(0);
            start.setSeconds(0);
        }
        if (end != null) {
            end.setHours(23);
            end.setMinutes(59);
            end.setSeconds(59);
        }
        //
        CurrencySearchReq currencySearchReq = new CurrencySearchReq();
        currencySearchReq.setBusinessId(businessId);
        currencySearchReq.setCode(code);
        currencySearchReq.setStatus(ECommonStatus.of(status));
        currencySearchReq.setCreatedDateFrom(start);
        currencySearchReq.setCreatedDateTo(end);
        if (sortBy != null) currencySearchReq.setSortBy(sortBy);
        if (sortDirection != null) currencySearchReq.setSortDirection(sortDirection);
        else currencySearchReq.setSortDirection(Sort.Direction.DESC);
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, currencySearchReq.getSort());
        Page<CurrencyRes> page = opsCurrencyService.filterCurrency(currencySearchReq, pageRequest);
        return success(page, offset, limit);
    }

    @GetMapping("{id}")
    @Authorize(role = AccessRole.CURRENCY, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getCurrency(@PathVariable(value = "id") Integer id) {
        CurrencyRes result = opsCurrencyService.getCurrency(id);
        return success(result);
    }

    @PostMapping("create")
    @Authorize(role = AccessRole.CURRENCY, permissions = {AccessPermission.CREATE })
    public ResponseEntity<?> createCurrency(@RequestBody @Valid CurrencyCreateReq currencyCreateReq) {
        opsCurrencyService.addCurrency(currencyCreateReq);
        return success(null);
    }

    @PostMapping("{id}/update")
    @Authorize(role = AccessRole.CURRENCY, permissions = {AccessPermission.EDIT })
    public ResponseEntity<?> updateCurrency(
            @PathVariable(value = "id") Integer id, @RequestBody @Valid CurrencyUpdateReq currencyUpdateReq) {
        this.opsCurrencyService.updateCurrency(id, currencyUpdateReq);
        return success(null);
    }

    private ResponseEntity<BaseResponseData> badRequest(String msg) {
        BaseResponseData respData = responseSupport.errorResponse(ErrorCode.BAD_REQUEST.getValue(), msg);
        return new ResponseEntity<BaseResponseData>(respData, HttpStatus.OK);
    }
}
