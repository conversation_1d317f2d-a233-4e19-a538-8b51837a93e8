package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VersionDetails {
    @JsonProperty("updated_at")
    private Long updatedAt;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("created_by")
    private String createdBy;
}
