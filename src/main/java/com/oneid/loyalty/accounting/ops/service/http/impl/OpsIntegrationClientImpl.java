package com.oneid.loyalty.accounting.ops.service.http.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.oneid.loyalty.accounting.ops.service.http.OpsIntegrationClient;

@Service
public class OpsIntegrationClientImpl implements OpsIntegrationClient {

    @Autowired
    @Qualifier("oneloyaltyService")
    private RestTemplate restTemplate;

    @Value("${1loyalty-ops-integration.url}")
    private String url;

    @Override
    public ResponseEntity<Resource> registerMember(String createdBy, MultipartFile[] files) {
        return makeRequest("/v1/api/member/bulk", files, createdBy, HttpMethod.POST);
    }

    private ResponseEntity<Resource> makeRequest(String path, MultipartFile[] files, String createdBy, HttpMethod method) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        String requestURl = url + path;

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<String, Object>();

        for (MultipartFile file : files) {
            body.add("files", file.getResource());
        }
        
        body.add("createdBy", createdBy);

        HttpEntity<MultiValueMap<String, Object>> requestEntity
                = new HttpEntity<>(body, headers);

        ResponseEntity<Resource> responseEntity = restTemplate.postForEntity(requestURl, requestEntity,
                Resource.class);
        
        return responseEntity;
    }
}
