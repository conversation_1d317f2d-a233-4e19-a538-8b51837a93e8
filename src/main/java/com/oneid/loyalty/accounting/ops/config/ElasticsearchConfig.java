package com.oneid.loyalty.accounting.ops.config;

import java.io.IOException;

import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.ClientConfiguration.MaybeSecureClientConfigurationBuilder;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import com.oneid.loyalty.accounting.ops.setting.ElasticsearchSetting;

@Configuration
public class ElasticsearchConfig extends AbstractElasticsearchConfiguration {
    @Autowired
    private ElasticsearchSetting elasticsearchSetting;

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        MaybeSecureClientConfigurationBuilder configurationBuilder = ClientConfiguration.builder()
                .connectedTo(elasticsearchSetting.getEndpoints());
        
        if(elasticsearchSetting.getEnableSsl()) {
            configurationBuilder.usingSsl();
        }
        
        ClientConfiguration clientConfiguration = configurationBuilder
                .withBasicAuth(elasticsearchSetting.getUsername(), elasticsearchSetting.getPassword())
                .build();
        
        RestHighLevelClient rhlClient = RestClients.create(clientConfiguration).rest();

        if(elasticsearchSetting.getPingable()) {
            try {
                if (!rhlClient.ping(RequestOptions.DEFAULT)) {
                    throw new RuntimeException("There are no reachable Elasticsearch nodes!");
                }
            } catch (IOException e) {
                throw new RuntimeException(e.getMessage());
            }
        }
        
        return rhlClient;
    }

    @Bean
    public ElasticsearchRestTemplate elasticsearchRestTemplate() {
        return new ElasticsearchRestTemplate(elasticsearchClient());
    }
}
