package com.oneid.loyalty.accounting.ops.support.web.acl;

import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccessPermission {
    VIEW("view", 1000, 1),
    CREATE("create", 1001, 1 << 1),
    EDIT("edit", 1002, 1 << 2),
    DELETE("delete", 1003, 1 << 3),
    
    SUSPEND("suspend", 1004, 1 << 4),
    IMPORT("import", 1005, 1 << 5),
    EXPORT("export", 1006, 1 << 6),
    APPROVE_OR_REJECT("approve_or_reject", 1007, 1 << 7),
    MAKER_ROLE("maker_role", 1, 1 << 8),
    CHECKER_ROLE("checker_role", 2, 1 << 9);

    String name;
    Integer mapping;
    Integer code;
    
    public static AccessPermission lookup(Integer mapping) {
        if (mapping == null) {
            return null;
        }
        
        return Stream.of(values())
                .filter(each -> each.getMapping().equals(mapping))
                .findFirst()
                .orElse(null);
    }
}
