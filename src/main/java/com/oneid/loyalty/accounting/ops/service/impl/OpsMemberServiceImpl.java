package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.MemberSortingField;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.exception.ServiceClientException;
import com.oneid.loyalty.accounting.ops.feign.CPMServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MemberServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.AddCardMemberFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CpmMemberInquiryReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MemberRegisterFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MembershipInquiryFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.VerifyMemberCardReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.AddCardMemberFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberInquiryRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberInquiryResData;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberProfileDetailFeignRes;
import com.oneid.loyalty.accounting.ops.kafka.event.CommonEventData;
import com.oneid.loyalty.accounting.ops.kafka.event.MemberData;
import com.oneid.loyalty.accounting.ops.kafka.producer.Producer;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.CustomerIdentifierDTO;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateMemberReq;
import com.oneid.loyalty.accounting.ops.model.res.AccountBalanceRes;
import com.oneid.loyalty.accounting.ops.model.res.AddCardWithMemberRegistrationRes;
import com.oneid.loyalty.accounting.ops.model.res.AttributeMemberRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberBalanceRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberProfileDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberProfileRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.TierHistoryRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberService;
import com.oneid.loyalty.accounting.ops.service.http.OpsIntegrationClient;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardTMP;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.MemberProductAccount;
import com.oneid.oneloyalty.common.entity.MessageEvent;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramProduct;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.Province;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TierHistory;
import com.oneid.oneloyalty.common.entity.UserProfile;
import com.oneid.oneloyalty.common.entity.VDCardReqLog;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.CountryRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.MemberAttributeRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import com.oneid.oneloyalty.common.repository.ProvinceRepository;
import com.oneid.oneloyalty.common.repository.RuleRepository;
import com.oneid.oneloyalty.common.repository.TierHistoryRepository;
import com.oneid.oneloyalty.common.repository.VDCardReqLogRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardTMPService;
import com.oneid.oneloyalty.common.service.MemberProductAccountService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.UserProfileService;
import com.oneid.oneloyalty.common.util.LogData;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class OpsMemberServiceImpl implements OpsMemberService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpsMemberServiceImpl.class);

    @Autowired
    private MemberService memberService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private MemberProductAccountService memberProductAccountService;

    @Autowired
    private ProgramProductService programProductService;

    @Autowired
    AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Autowired
    private CardServiceFeignClient cardClient;

    @Autowired
    private CardTMPService cardTMPService;

    @Value(value = "${app.vd-card-prefix-codes}")
    private List<String> vdCardPrefixCodes;

    @Autowired
    private VDCardReqLogRepository vdCardReqLogRepository;

    @Autowired
    private Producer producer;

    @Value("${kafka.event.code.register.member:202002}")
    private String EVENT_CODE_REGISTER_MEMBER;

    @Value("${kafka.event.type.member:MEMBER}")
    private String EVENT_TYPE_MEMBER;

    @Value("${kafka.service.code.member:OPS}")
    private String SERVICE_CODE;

    @Autowired
    private UserProfileService userProfileService;

    @Value("${app.format.date.pattern}")
    private String dateFormatPattern;

    @Autowired
    private OpsIntegrationClient opsIntegrationClient;

    @Autowired
    private OneloyaltyServiceFeignClient oneloyaltyServiceFeignClient;

    @Autowired
    private MemberServiceFeignClient memberServiceFeignClient;

    @Autowired
    private TierHistoryRepository tierHistoryRepository;

    @Autowired
    private MemberAttributeRepository memberAttributeRepository;
    
    @Autowired
    private RuleRepository ruleRepository;
    
    @Autowired
    private ProgramTierRepository programTierRepository;
    
    @Autowired
    private CountryRepository countryRepository;
    
    @Autowired
    private ProvinceRepository provinceRepository;
    
    @Autowired
    private DistrictRepository districtRepository;
    
    @Autowired
    private WardRepository wardRepository;
    
    @Autowired
    private CurrencyRepository currencyRepository;

    @Autowired
    private CPMServiceFeignClient cpmServiceFeignClient;

    @Override
    public MemberProfileDetailRes getProfileById(Long memberId) {
        return convert(memberServiceFeignClient.getProfileById(memberId).getData(), null);
    }

    private void validateCardTypeMatch(CreateMemberReq req) {
        Business business = businessService.findByCode(req.getBusinessCode());
        Store store = storeService.findByBusinessIdAndCodeWithoutStatus(business.getId(), req.getStoreCode());
        Program program = programService.findActive(business.getId(), req.getProgramCode());
        CardTMP cardTMP = cardTMPService.findPending(req.getCard().getCardNo(), business.getId(), program.getId(), store.getId());
        if (!req.getCard().getCardTypeId().equals(cardTMP.getCardTypeId())) {
            throw new OpsBusinessException(OpsErrorCode.CARD_TYPE_NOT_MATCHED, "card type not matched",
                    LogData.createLogData().append("card_no", req.getCard().getCardNo())
                            .append("card_type_id", req.getCard().getCardTypeId()));
        }
    }

    @Override
    public MemberProfileDetailRes addMember(CreateMemberReq req) {
        final String reqId = UUID.randomUUID().toString();

        validateCardTypeMatch(req);

        // Verify the card before add for member
        VerifyMemberCardReq cardInfo = new VerifyMemberCardReq();
        cardInfo.setProgramCode(req.getProgramCode());
        cardInfo.setCardNo(req.getCard().getCardNo());
        cardInfo.setBusinessCode(req.getBusinessCode());
        cardInfo.setCardTypeId(req.getCard().getCardTypeId());
        cardInfo.setStoreCode(req.getStoreCode());

        try {
            this.cardClient.verifyMemberCard(cardInfo);
        } catch (BusinessException ex) {
            throw ServiceClientException.builder()
                    .errorCode(ex.getErrCode())
                    .errorMessage(ex.getMessage())
                    .build();
        }

        Date registrationDate = null;

        if (StringUtils.isNotBlank(req.getRegistrationDate())) {
            LocalDate localDate = LocalDate.parse(req.getRegistrationDate(), DateTimeFormatter.ofPattern(dateFormatPattern));

            registrationDate = Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }

        // Create member
        MemberProfileDetailFeignRes memberRes = memberServiceFeignClient.addMember(MemberRegisterFeignReq.builder()
                .businessId(req.getBusinessCode())
                .programId(req.getProgramCode())
                .storeId(req.getStoreCode())
                .address(req.getAddress())
                .countryId(req.getCountryCode())
                .districtId(req.getDistrictCode())
                .provinceId(req.getProvinceCode())
                .wardId(req.getWardCode())
                .dob(req.getDob())
                .email(req.getEmail())
                .firstName(req.getFirstName())
                .fullName(req.getFullName())
                .gender(req.getGender())
                .homePhone(req.getHomePhone())
                .houseNumber(req.getHouseNumber())
                .identifyNo(req.getIdentifyNo())
                .identifyType(req.getIdentifyType())
                .job(req.getJob())
                .midName(req.getMidName())
                .officePhone(req.getOfficePhone())
                .phoneNo(req.getPhoneNo())
                .referralCode(req.getReferralCode())
                .registrationDate(registrationDate)
                .tierId(req.getTierId())
                .updatedBy(auditorAware.getCurrentAuditor().get().getUserName())
                .street(req.getStreet())
                .status(ECommonStatus.ACTIVE.getValue()) // Create member, status is active
                .build()).getData();


        // Add card to member
        AddCardWithMemberRegistrationRes addedMemberCard = null;
        try {
            AddCardMemberFeignReq addCardPayload = AddCardMemberFeignReq.builder()
                    .businessCode(req.getBusinessCode())
                    .cardNo(req.getCard().getCardNo())
                    .cardTypeId(req.getCard().getCardTypeId())
                    .memberCode(memberRes.getMemberCode())
                    .programCode(req.getProgramCode())
                    .storeCode(req.getStoreCode())
                    .userId(auditorAware.getCurrentAuditor().get().getUserName())
                    .build();

            APIResponse<AddCardMemberFeignRes> addCardRes = (APIResponse<AddCardMemberFeignRes>) cardClient
                    .addMemberCard(addCardPayload);
            if (addCardRes.getMeta().getCode() == 200) {
                addedMemberCard = AddCardWithMemberRegistrationRes.builder().success(true).build();
            } else {
                addedMemberCard = AddCardWithMemberRegistrationRes.builder().success(false).build();
            }
        } catch (Exception be) {
            LOGGER.error("An exception occurred when adding member card with member registration", be);
            addedMemberCard = AddCardWithMemberRegistrationRes.builder().success(false).build();
        }

        MemberProfileDetailRes res = convert(memberRes, addedMemberCard);

        UserProfile userProfile = userProfileService.findActiveByPhoneNoAndBusiness(
                res.getPhoneNo(),
                res.getBusinessId()
        );

        /* PUBLISH MESSAGE */
        MemberData.Profile profile = MemberData.Profile.builder()
                .fullName(res.getFullName())
                .gender(res.getGender())
                .memberStatus(res.getMemberStatus())
                .registrationDate(String.valueOf((new Date()).getTime()))
                .phoneNo(res.getPhoneNo())
                .status(res.getStatus())
                .address(res.getAddress())
                .dob(res.getDob())
                .email(res.getEmail())
                .firstName(res.getFirstName())
                .lastName(res.getLastName())
                .houseNumber(res.getHouseNumber())
                .street(res.getStreet())
                .wardCode(res.getWardCode())
                .districtCode(res.getDistrictCode())
                .provinceCode(res.getProvinceCode())
                .countryCode(res.getCountryCode())
                .homePhone(res.getHomePhone())
                .officePhone(res.getOfficePhone())
                .identifyNo(res.getIdentifyNo())
                .identifyType(res.getIdentifyType())
                .level(null)
                .build();

        List<MemberProductAccount> memberProductAccounts = memberProductAccountService.findByMemberId(res.getMemberId());
        List<MemberData.ProductAccount> productAccounts = memberProductAccounts.stream()
                .map(memberProductAccount -> {
                            if (memberProductAccount != null) {
                                ProgramProduct programProduct = programProductService
                                        .findActive(memberProductAccount.getProgramProductId());

                                return MemberData.ProductAccount.builder()
                                        .productCode(programProduct.getCode())
                                        .productAccountCode(memberProductAccount.getAccountCode())
                                        .issueDate(memberProductAccount.getIssueDate())
                                        .productStatus(memberProductAccount.getStatus())
                                        .expiredTime(DateTimes.toEpochSecond(memberProductAccount.getExpiredAt()))
                                        .build();
                            } else {
                                return null;
                            }
                        }
                )
                .collect(Collectors.toList());

        MemberData.Tier tier = null;
        if (res.getTierId() != null) {
            ProgramTier programTier = programTierService.findActive(res.getTierId());
            tier = MemberData.Tier.builder()
                    .tierCode(programTier.getTierCode())
                    .tierName(programTier.getName())
                    .description(programTier.getDescription())
                    .cardThumbnailUrl(programTier.getCardThumbnailUrl())
                    .cardBackgroundUrl(programTier.getCardBackgroundUrl())
                    .badgeIconUrl(programTier.getBadgeIconUrl())
                    .build();
        }

        MemberData memberData = MemberData.builder()
                .profile(profile)
                .productAccounts(productAccounts)
                .tier(tier)
                .attributes(new LinkedList<>())
                .build();

        CommonEventData<MemberData> memberDataPublish = CommonEventData.<MemberData>builder()
                .id(UUID.randomUUID().toString())
                .event(EVENT_CODE_REGISTER_MEMBER)
                .eventType(EVENT_TYPE_MEMBER)
                .serviceCode(SERVICE_CODE)
                .timeStamp(new Date().getTime())
                .businessCode(res.getBusinessCode())
                .programCode(res.getProgramCode())
                .memberCode(res.getMemberCode())
                .userProfileCode(userProfile.getProfileId())
                .masterUserId(userProfile.getMasterUserId())
                .requestId(UUID.randomUUID().toString())
                .payload(memberData)
                .build();
        producer.send(memberDataPublish);
        return res;
    }

    private String getDefaultUpdatingMemberStatus(String status) {
        if (status == null || !(ECommonStatus.of(status).equals(ECommonStatus.ACTIVE) || ECommonStatus.of(status).equals(ECommonStatus.INACTIVE)))
            status = ECommonStatus.ACTIVE.getValue();

        return status;
    }

    @Override
    public MemberProfileDetailRes updateMember(Long memberId, UpdateMemberReq req) {
        String status = getDefaultUpdatingMemberStatus(req.getStatus());

        MemberProfileDetailFeignRes memberRes = memberServiceFeignClient.updateMember(memberId, MemberRegisterFeignReq.builder()
                .address(req.getAddress())
                .countryId(req.getCountryCode())
                .districtId(req.getDistrictCode())
                .provinceId(req.getProvinceCode())
                .wardId(req.getWardCode())
                .dob(req.getDob())
                .email(req.getEmail())
                .firstName(req.getFirstName())
                .fullName(req.getFullName())
                .gender(req.getGender())
                .homePhone(req.getHomePhone())
                .houseNumber(req.getHouseNumber())
                .job(req.getJob())
                .lastName(req.getLastName())
                .midName(req.getMidName())
                .officePhone(req.getOfficePhone())
                .updatedBy(auditorAware.getCurrentAuditor().get().getUserName())
                .street(req.getStreet())
                .status(status)
                .identifyNo(req.getIdentifyNo())
                .identifyType(req.getIdentifyType())
                .build()).getData();

        return convert(memberRes, null);
    }

    private MemberProfileDetailRes convert(MemberProfileDetailFeignRes res, AddCardWithMemberRegistrationRes addedMemberCard) {
        String storeCode = null;

        if (res.getStoreRegisterId() != null) {
            Store store = storeService.find(res.getStoreRegisterId());
            storeCode = store.getCode();
        }
        Business business = businessService.findByCode(res.getBusinessId());

        Program program = programService.find(business.getId(), res.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", res));
        
        ProgramTier tier = null;
        
        if(res.getTierId() != null) {
            tier = programTierRepository.findById(res.getTierId()).orElse(null);
        }
        
        Country country = countryRepository.findByCode(res.getCountryId()).orElse(null);
        Province province = null;
        District district = null;
        Ward ward = null;
        
        if (country != null)
            province = provinceRepository.findByCountryIdAndCode(country.getId(), res.getProvinceId()).orElse(null);
        
        if (province != null)
            district = districtRepository.findByProvinceIdAndCode(province.getId(), res.getDistrictId()).orElse(null);
        
        if (district != null)
            ward = wardRepository.findByDistrictIdAndCode(district.getId(), res.getWardId()).orElse(null);
        
        return MemberProfileDetailRes.builder()
                .memberId(res.getId())
                .businessId(business.getId())
                .businessCode(business.getCode())
                .businessName(business.getName())
                .programId(program.getId())
                .programCode(res.getProgramId())
                .programName(program.getName())
                .memberCode(res.getMemberCode())
                .memberStatus(res.getMemberStatus())
                .address(res.getAddress())
                .dob(res.getDob())
                .email(res.getEmail())
                .firstName(res.getFirstName())
                .lastName(res.getLastName())
                .fullName(res.getFullName())
                .gender(res.getGender())
                .homePhone(res.getHomePhone())
                .houseNumber(res.getHouseNumber())
                .identifyNo(res.getIdentifyNo())
                .identifyType(res.getIdentifyType())
                .officePhone(res.getOfficePhone())
                .phoneNo(res.getPhoneNo())
                .street(res.getStreet())
                .countryCode(res.getCountryId())
                .countryName(country == null ? null : country.getName())
                .provinceCode(res.getProvinceId())
                .provinceName(province == null ? null : province.getName())
                .districtCode(res.getDistrictId())
                .districtName(district == null ? null : district.getName())
                .wardCode(res.getWardId())
                .wardName(ward == null ? null : ward.getName())
                .status(res.getStatus())
                .storeRegisterCode(storeCode)
                .registrationDate(res.getRegistrationDate())
                .referralCode(res.getReferralCode())
                .job(res.getJob())
                .tierId(res.getTierId())
                .tierName(tier == null ? null : tier.getName())
                .addedMemberCard(addedMemberCard)
                .build();
    }

    @Override
    public Page<MemberProfileRes> getMemberProfiles(Integer businessId, Integer programId, String memberCode, String phoneNo, String fullName, String identifyNo,
                                                    Integer limit, Integer offset, Direction direction, MemberSortingField sortBy, String cardNo) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        Business business = businessService.findActive(businessId);

        Program program = programService.findByIdAndBusinessId(programId, businessId);

        List<MemberProfileRes> memberProfileRes = new ArrayList<>();
        List<Member> members = new ArrayList<Member>();
        long total = 0;

        if (StringUtils.isBlank(cardNo)) {
            Page<Member> page = memberService.searchMember(programId, memberCode, phoneNo, fullName, identifyNo, pageRequest);

            members = page.getContent();
            total = page.getTotalElements();
        } else {
            Member member = null;

            if (vdCardPrefixCodes.stream()
                    .filter(prefix -> cardNo.startsWith(prefix))
                    .count() > 0) {
                VDCardReqLog vdCardReqLog = vdCardReqLogRepository.findByVDCardAndProgramId(programId, cardNo);

                if (vdCardReqLog != null) {
                    member = memberService.find(vdCardReqLog.getMemberId()).orElse(null);
                }
            } else {
                Map<Integer, ProgramProduct> programProducts = programProductService.getAllActiveByProgramId(programId)
                        .stream()
                        .collect(Collectors.toMap(ProgramProduct::getId, each -> each));

                MemberProductAccount memberProductAccount = memberProductAccountService.find(cardNo, programId).stream()
                        .filter(programProduct -> {
                            EIdType eIdType = programProducts.get(programProduct.getProgramProductId()).getIdType();
                            return EIdType.VD_CARD.equals(eIdType) || EIdType.CARD.equals(eIdType);
                        })
                        .findAny()
                        .orElse(null);

                if (memberProductAccount != null) {
                    member = memberService.find(memberProductAccount.getMemberId()).orElse(null);
                }
            }

            if (member != null) {
                members.add(member);
                total = 1;
            }
        }

        List<Integer> tierIds = members.stream()
                .map(Member::getTierId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Integer, ProgramTier> programTiers;

        if (!CollectionUtils.isEmpty(tierIds)) {
            SpecificationBuilder<ProgramTier> specificationBuilder = new SpecificationBuilder<ProgramTier>();

            specificationBuilder.add(new SearchCriteria("id", tierIds, SearchOperation.IN));

            programTiers = programTierService.searchAll(specificationBuilder)
                    .stream()
                    .collect(Collectors.toMap(ProgramTier::getId, tier -> tier));
        } else {
            programTiers = Collections.emptyMap();
        }

        memberProfileRes = members.stream()
                .map(member -> MemberProfileRes.builder()
                        .businessCode(business.getCode())
                        .businessId(business.getId())
                        .fullName(member.getFullName())
                        .gender(member.getGender())
                        .identifyNo(member.getIdentifyNo())
                        .memberId(member.getId())
                        .memberCode(member.getMemberCode())
                        .phoneNo(member.getPhoneNo())
                        .programId(program.getId())
                        .registrationDate(member.getRegistrationDate())
                        .status(member.getStatus())
                        .tierName(programTiers.containsKey(member.getTierId()) ? programTiers.get(member.getTierId()).getName() : null).build())
                .collect(Collectors.toList());

        return new PageImpl<MemberProfileRes>(memberProfileRes, pageRequest, total);
    }


    @Override
    public APIResponse<?> getMemberBalanceById(Long memberId) {
        Member member = memberService.findActive(memberId);
        Program program = programService.findActive(member.getProgramId());
        Business business = businessService.findActive(program.getBusinessId());
        
        APIResponse<MemberBalanceRes> res = oneloyaltyServiceFeignClient.inquiry(MembershipInquiryFeignReq.builder()
                .businessId(business.getCode())
                .programId(program.getCode())
                .customerIdentifier(CustomerIdentifierDTO.builder()
                        .idType(EIdType.MEMBER_ID)
                        .id(member.getMemberCode())
                        .build())
                .balanceRequired(true)
                .profileRequired(false)
                .vdCardRequired(false)
                .build());
        
        if (CollectionUtils.isEmpty(res.getData().getAccountPoolBalances()) == false) {
            Set<Integer> currencyIds = res.getData().getAccountPoolBalances().stream().map(e -> e.getCurrencyId()).collect(Collectors.toSet());
            List<Currency> currencies = currencyRepository.findAllByIdIn(currencyIds);
            Map<Integer, Currency> currencyMap = currencies.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
            
            res.getData().getAccountPoolBalances().forEach(e -> {
                e.setCurrencyName(currencyMap.get(e.getCurrencyId()).getName());
            });
            
            res.getData().getAccountPoolBalances().sort(
                    Comparator.comparing(AccountBalanceRes::getPoolCode).thenComparing(AccountBalanceRes::getExpiredTime));
        }
        
        return res;
    }

    @Override
    public ResponseEntity<Resource> registerMemberByBatchFile(OPSAuthenticatedPrincipal principal, MultipartFile[] multipartFiles) {
        if (multipartFiles.length == 0)
            throw new BusinessException(ErrorCode.BAD_REQUEST, "File not found", multipartFiles);

        return opsIntegrationClient.registerMember(principal.getUserName(), multipartFiles);
    }

    @Override
    public Page<TierHistoryRes> getMemberTierHistory(Long memberId, Pageable pageRequest) {
        pageRequest = PageRequest.of(pageRequest.getPageNumber(), pageRequest.getPageSize(),
                Sort.by(Direction.DESC, "updatedAt")); // default

        Member member = memberService.find(memberId).orElseThrow(
                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", memberId)
        );
        Page<Object[]> pageTierHistory = tierHistoryRepository.findByMemberId(member.getId(), pageRequest);
        
        Set<Integer> unionRuleIds = pageTierHistory.getContent().stream()
                .map(e -> ((TierHistory) e[0]).getRuleId())
                .filter(StringUtils::isNotBlank)
                .flatMap(ruleIds -> Stream.of(ruleIds.split("\\|")))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        
        List<Rule> unionRules = ruleRepository.findByIdIn(unionRuleIds);
        
        Map<Integer, Rule> ruleMap = unionRules.stream()
                .collect(Collectors.toMap(Rule::getId, e -> e));
        
        List<TierHistoryRes> content = pageTierHistory.getContent().stream()
                .map(objects -> {
                    TierHistory tierHistory = (TierHistory) objects[0];
                    ProgramTier programTier = (ProgramTier) objects[1];
                    MessageEvent messageEvent = (MessageEvent) objects[2];
                    ReasonCode reasonCode = (ReasonCode) objects[3];
                    Long tierExpiredDate = tierHistory.getTierExpiredDate() == null ? 
                            null : DateTimes.toEpochSecond(tierHistory.getTierExpiredDate());
                    
                    List<RuleRes> rulesRes = Collections.emptyList();
                    if (StringUtils.isNotBlank(tierHistory.getRuleId())) {
                        rulesRes = Stream.of(tierHistory.getRuleId().split("\\|"))
                                .map(Integer::parseInt)
                                .map(id -> ruleMap.get(id))
                                .filter(Objects::nonNull)
                                .map(rule -> RuleRes.builder()
                                        .code(rule.getCode())
                                        .name(rule.getName())
                                        .build())
                                .collect(Collectors.toList());
                    }
                    
                    return TierHistoryRes.builder()
                            .tierName(programTier == null ? null : programTier.getName())
                            .tierCode(programTier == null ? null :programTier.getTierCode())
                            .qualificationPoint(tierHistory.getQualifyingPoint())
                            .eventTriggerCode(tierHistory.getEventTrigger())
                            .eventTriggerName(messageEvent == null ? null :messageEvent.getName())
                            .description(tierHistory.getDescription())
                            .reasonCode(tierHistory.getReasonCode())
                            .reasonName(reasonCode == null ? null : reasonCode.getName())
                            .updatedAt(DateTimes.toEpochSecond(tierHistory.getUpdatedAt()))
                            .tierExpiredDate(tierExpiredDate)
                            .rules(rulesRes)
                            .build();
                })
                .collect(Collectors.toList());
        
        return new PageImpl<>(content, pageRequest, pageTierHistory.getTotalElements());
    }

    @Override
    public Collection<AttributeMemberRes> getMemberAttributes(Long memberId) {
        return memberAttributeRepository.getInfoAttributeOfMember(memberId)
                .stream()
                .map(objs -> AttributeMemberRes.builder()
                        .attributeName((String) objs[2])
                        .attributeValue((String) objs[0])
                        .lastUpdatedAt(DateTimes.toEpochSecond((Date) objs[1]))
                        .build())
                .sorted(Comparator.comparing(AttributeMemberRes::getAttributeName))
                .collect(Collectors.toList());
    }

    @Override
    public MemberInquiryRes getLinkAccountInfo(Long memberId) {
        Member member = memberService.find(memberId).orElseThrow(
                () -> new BusinessException(ErrorCode.MEMBER_NOT_FOUND, "Member not found", memberId)
        );

        CustomerIdentify customerIdentify = new CustomerIdentify();
        customerIdentify.setIdType(EIdType.MEMBER_ID);
        customerIdentify.setId(member.getMemberCode());

        Program program = programService.findActive(member.getProgramId());

        Business business = businessService.findActive(program.getBusinessId());

        CpmMemberInquiryReq cpmMemberInquiryReq = CpmMemberInquiryReq.builder()
                .currencyCode(null)
                .businessCode(business.getCode())
                .customerIdentifier(customerIdentify)
                .programCode(program.getCode())
                .userId(null)
                .build();

        MemberInquiryResData memberInquiryResData = cpmServiceFeignClient.inquiry(cpmMemberInquiryReq);
        if (!memberInquiryResData.getMeta().getCode().equals(ErrorCode.SUCCESS.getValue())) {
            throw new BusinessException(ErrorCode.of(memberInquiryResData.getMeta().getCode()));
        }

        return memberInquiryResData.getData();
    }
}