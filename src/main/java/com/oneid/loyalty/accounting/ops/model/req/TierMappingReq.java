package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotNull;

import com.oneid.oneloyalty.common.constant.ECommonStatus;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class TierMappingReq {
    private Integer id;
    
    @NotNull(message = "'base_tier_id' must not be null")
    private Integer baseTierId;
    
    private Integer matchTierId;
    
    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;
    
}
