package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;

@Data
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransactionStatisticRes {
    private Integer total;
    private Integer totalSuccess;
    private BigDecimal awardSuccessPoint;
    private BigDecimal redeemSuccessPoint;
    private Integer totalFail;
    private Integer totalPending;
}
