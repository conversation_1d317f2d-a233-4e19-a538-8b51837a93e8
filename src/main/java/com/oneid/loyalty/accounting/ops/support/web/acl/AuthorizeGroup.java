package com.oneid.loyalty.accounting.ops.support.web.acl;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthorizeGroup {
    Authorize[] authorize();

    boolean any() default false;

    OpsErrorCode errorCode() default OpsErrorCode.ACCESS_DENIED;
}