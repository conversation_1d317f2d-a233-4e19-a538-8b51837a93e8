package com.oneid.loyalty.accounting.ops.feign.model.req;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberRegisterFeignReq {
    private String firstName;
    private String midName;
    private String lastName;
    private String fullName;
    private String programId;
    private String businessId;
    private String storeId;
    private String phoneNo;
    private EIdentifyType identifyType;
    private String identifyNo;
    private String gender;
    private String dob;
    private String email;
    private String houseNumber;
    private String homePhone;
    private String officePhone;
    private String referralCode;
    private String countryId;
    private String provinceId;
    private String districtId;
    private String wardId;
    private String address;
    private String street;
    private String job;
    private String status;
    private Integer tierId;
    private Date registrationDate;
    private String updatedBy;
}
