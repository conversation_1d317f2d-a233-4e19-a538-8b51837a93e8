package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.Searchable;
import com.oneid.oneloyalty.common.search.SearchableFieldExtractor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class FilterLimitationAvailableReq extends SearchableFieldExtractor<Limitation> {

    private Integer businessId;

    @Searchable(key = "programId")
    private Integer programId;

    @Searchable(key = "status")
    private ECommonStatus status;

    @Searchable(key = "approvalStatus")
    private EApprovalStatus approvalStatus;

    @Searchable(key = "counterId")
    private Integer counterId;

    @Searchable(key = "code", operation = SearchOperation.MATCH)
    private String code;

}
