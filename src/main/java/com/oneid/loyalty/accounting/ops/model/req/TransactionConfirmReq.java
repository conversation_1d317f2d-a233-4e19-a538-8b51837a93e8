package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TransactionConfirmReq {

    @JsonProperty("transaction_id")
    private String transactionId;

    @NotBlank(message = "transaction_status is missing")
    @Pattern(regexp="^(S|C){1}$", message = "transaction_status is not valid")
    @JsonProperty("transaction_status")
    private String txnStatus;
    
}
