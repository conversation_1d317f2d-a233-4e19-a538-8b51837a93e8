package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
public class CurrencyUpdateReq {
    @NotNull(message = "'BusinessId' cannot be null")
    @JsonProperty("business_id")
    private Integer businessId;

    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @NotBlank(message = "'Name' cannot be empty")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    private String description;

    @NotBlank(message = "'Status' cannot be empty")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' is invalid")
    private String status;
}
