package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.EditCounterReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.loyalty.accounting.ops.model.res.LinkedServiceRes;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.service.CounterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Date;

@RestController
@RequestMapping("v1/counters")
@Validated
public class CounterController extends BaseController {
    @Autowired
    private OpsCounterService opsCounterService;

    @Autowired
    private CounterService counterService;

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableCounterRequests(
            @RequestParam(value = "business_id", required = false) Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "counter_status", required = false) ECommonStatus counterStatus,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, sort);
        Page<CounterRes> page = opsCounterService.getAvailableCounterRequests(businessId, programId, code, counterStatus, pageRequest);

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/{id}/requests/version-list")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getVersionList(
            @PathVariable("id") Integer counterId,
            @RequestParam(value = "approval_status", required = false) List<EApprovalStatus> approvalStatus,
            @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
            @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
            @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
            @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        Page<VersionRes> page = opsCounterService.getVersionList(
                counterId,
                approvalStatus,
                fromCreatedAt,
                toCreatedAt,
                fromReviewedAt,
                toReviewedAt,
                createdBy,
                reviewedBy,
                offset,
                limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewCounterRequests(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "from_created_at", required = false) String fromCreatedAt,
            @RequestParam(value = "to_created_at", required = false) String toCreatedAt,
            @RequestParam(value = "from_reviewed_at", required = false) String fromReviewedAt,
            @RequestParam(value = "to_reviewed_at", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "reviewed_by", required = false) String reviewedBy,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        Page<CounterRes> page = opsCounterService.getInReviewCounterRequests(approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy, offset, limit);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review/{id}")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviewCounterRequestById(@PathVariable("id") Integer reviewId) {
        return success(opsCounterService.getInReviewCounterRequestById(reviewId));
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailableCounterRequestById(@PathVariable("id") Integer requestId) {
        return success(opsCounterService.getAvailableCounterRequestById(requestId));
    }

    @GetMapping("/request/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> getEditCounterRequestSetting(@PathVariable("id") Integer requestId) {
        return success(opsCounterService.getEditCounterRequestSetting(requestId));
    }

    @PostMapping("/request/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> requestEditingCounterRequest(
            @PathVariable("id") Integer requestId,
            @Valid @RequestBody EditCounterReq req) {
        return success(opsCounterService.requestEditingCounterRequest(requestId, req));
    }

    @PostMapping("/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    public ResponseEntity<?> requestCreatingCounterRequest(@Valid @RequestBody CreateCounterReq req) {
        return success(opsCounterService.createMaker(req));
    }

    @GetMapping("/request/{id}/linked-service-types")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getLinkedServiceTypes(
            @PathVariable("id") Integer requestId,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", required = false) Integer limit) {
        Pageable pageable = Pageable.unpaged();
        if (Objects.nonNull(limit) && limit > 0) {
            pageable = new OffsetBasedPageRequest(offset, limit, null);
        }
        Page<LinkedServiceRes> page = opsCounterService.getLinkedServiceTypes(requestId, pageable);
        return success(page.getContent(), offset, Objects.nonNull(limit) ? limit : -1, (int) page.getTotalElements());
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsCounterService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/statistic")
    @Authorize(role = AccessRole.COUNTER, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> statisticCounter(
            @RequestParam(value = "counter_id", required = true) Integer counterId,
            @RequestParam(value = "started_at", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date startedAt,
            @RequestParam(value = "ended_at", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date endedAt,
            @RequestParam(value = "counter_level", required = true) ECounterLevel counterLevel,
            @RequestParam(value = "code", required = true) String code,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit) {

        Pageable pageable = new OffsetBasedPageRequest(offset, limit, Sort.by("id").descending());

        CounterStatisticRes res = opsCounterService.counterStatistic(counterId, startedAt, endedAt, counterLevel,  code, status, pageable);

        return success(res, offset, limit, res.getTotalHistories());
    }
}
