package com.oneid.loyalty.accounting.ops.feign.config;

import com.oneid.loyalty.accounting.ops.support.feign.interceptor.LOCInterceptor;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

public class LOCFeignConfig {
    @Value("${loc.basic.auth}")
    private String basicAuth;

    @Bean
    public RequestInterceptor locRequestInterceptor() {
        return new LOCInterceptor(basicAuth);
    }
}
