package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.util.EpochDateDeserializer;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeTransactionCode;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ResetSchemeRuleReq {
    private Integer id;

    private Integer programId;

    private Integer poolId;

    private String code;

    private ESchemeType schemeType;

    private EConditionType ruleLogic;

    private ECommonStatus status;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date endDate;

    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date createdAt;

    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date updatedAt;

    private String approvedBy;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
    private Date approvedAt;

    private List<Rule> rules;

    private List<TransactionCode> schemeTransactionCodes;

    public static Rule buildRuleReq(RuleRecordRes ruleRecordRes) {

        Rule rule = Rule.builder()
                .id(ruleRecordRes.getRuleId())
                .schemeId(ruleRecordRes.getSchemeId())
                .conditionLogic(ruleRecordRes.getConditionLogic())
                .sequenceNo(ruleRecordRes.getSeqNo())
                .status(ruleRecordRes.getStatus())
                .createdBy(ruleRecordRes.getCreatedBy())
                .createdAt(ruleRecordRes.getCreatedAt())
                .updatedBy(ruleRecordRes.getUpdatedBy())
                .updatedAt(ruleRecordRes.getUpdatedAt())
                .approvedBy(ruleRecordRes.getApprovedBy())
                .approvedAt(ruleRecordRes.getApprovedAt())
                .build();

        List<Rule.Condition> conditions = new ArrayList<>();

        for (ConditionRecordRes conditionRecordRes : ruleRecordRes.getListCondition()) {
            Rule.Condition condition = Rule.Condition.builder()
                    .id(conditionRecordRes.getId())
                    .ruleId(conditionRecordRes.getRuleId())
                    .attributeId(conditionRecordRes.getAttribute())
                    .operator(conditionRecordRes.getOperator())
                    .value(conditionRecordRes.getValueString())
                    .isIgnoreRefund(conditionRecordRes.getIsIgnoreRefund())
                    .dataType(conditionRecordRes.getDataType())
                    .status(conditionRecordRes.getStatus())
                    .createdBy(conditionRecordRes.getCreatedBy())
                    .createdAt(conditionRecordRes.getCreatedAt())
                    .updatedBy(conditionRecordRes.getUpdatedBy())
                    .updatedAt(conditionRecordRes.getUpdatedAt())
                    .approvedBy(conditionRecordRes.getApprovedBy())
                    .approvedAt(conditionRecordRes.getApprovedAt())
                    .build();
            conditions.add(condition);
        }

        rule.setConditions(conditions);

        return rule;
    }

    public static ResetSchemeRuleReq buildSchemeRuleReq(Scheme scheme) {
        return ResetSchemeRuleReq.builder()
                .id(scheme.getId())
                .programId(scheme.getProgramId())
                .poolId(scheme.getPoolId())
                .code(scheme.getCode())
                .schemeType(scheme.getSchemeType())
                .ruleLogic(scheme.getRuleLogic())
                .status(scheme.getStatus())
                .startDate(scheme.getStartDate())
                .endDate(scheme.getEndDate())
                .createdBy(scheme.getCreatedBy())
                .createdAt(scheme.getCreatedAt())
                .updatedBy(scheme.getUpdatedBy())
                .updatedAt(scheme.getUpdatedAt())
                .approvedBy(scheme.getApprovedBy())
                .approvedAt(scheme.getApprovedAt())
                .build();
    }

    public static TransactionCode buildTransactionCodeReq(SchemeTransactionCode schemeTransactionCode) {
        return TransactionCode.builder()
                .id(schemeTransactionCode.getId())
                .schemeId(schemeTransactionCode.getSchemeId())
                .transactionCodeId(schemeTransactionCode.getTransactionCodeId())
                .status(schemeTransactionCode.getStatus())
                .createdBy(schemeTransactionCode.getCreatedBy())
                .createdAt(schemeTransactionCode.getCreatedAt())
                .updatedBy(schemeTransactionCode.getUpdatedBy())
                .updatedAt(schemeTransactionCode.getUpdatedAt())
                .approvedBy(schemeTransactionCode.getApprovedBy())
                .approvedAt(schemeTransactionCode.getApprovedAt())
                .build();
    }

    @Getter
    @Setter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Rule {
        private Integer id;

        private Integer schemeId;

        private EConditionType conditionLogic;

        private Integer sequenceNo;

        private List<Condition> conditions;

        private ECommonStatus status;

        private String createdBy;

        @JsonDeserialize(using = EpochDateDeserializer.class)
        private Date createdAt;

        private String updatedBy;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date updatedAt;

        private String approvedBy;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date approvedAt;

        @Getter
        @Builder
        @AllArgsConstructor(access = AccessLevel.PRIVATE)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class Condition {
            private Integer id;

            private Integer ruleId;

            private String attributeId;

            private String operator;

            private String value;

            private EBoolean isIgnoreRefund;

            private ECommonStatus status;

            private String dataType;

            private String createdBy;

            @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
            private Date createdAt;

            private String updatedBy;

            @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
            private Date updatedAt;

            private String approvedBy;

            @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
            private Date approvedAt;
        }
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TransactionCode {
        private Integer id;

        private Integer schemeId;

        private Integer transactionCodeId;

        private ECommonStatus status;

        private String createdBy;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date createdAt;

        private String updatedBy;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date updatedAt;

        private String approvedBy;

        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
        private Date approvedAt;
    }
}