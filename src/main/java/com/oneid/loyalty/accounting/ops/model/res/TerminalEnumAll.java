package com.oneid.loyalty.accounting.ops.model.res;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Pos;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class TerminalEnumAll {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    public static TerminalEnumAll of(Pos terminal) {
        return new TerminalEnumAll(
                terminal.getId(),
                terminal.getCode(),
                terminal.getName(),
                terminal.getStatus().getValue()
        );
    }
}
