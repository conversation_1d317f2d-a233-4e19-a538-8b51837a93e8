package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.dto.ActionOnCounterDto;
import com.oneid.loyalty.accounting.ops.service.OpsServiceCounterRequestService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonActionType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.CounterRequest;
import com.oneid.oneloyalty.common.entity.ServiceCounterRequest;
import com.oneid.oneloyalty.common.repository.CounterRequestRepository;
import com.oneid.oneloyalty.common.repository.ServiceCounterRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpsServiceCounterRequestServiceImpl implements OpsServiceCounterRequestService {

    @Autowired
    private ServiceCounterRequestRepository requestRepository;

    @Autowired
    private CounterRequestRepository counterRequestRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveAllNewRequest(EServiceType type, Integer serviceRequestId, List<ActionOnCounterDto> idWithActions) {
        List<ServiceCounterRequest> serviceCounterRequests = idWithActions.stream()
                .map(dto -> {
                    ECommonActionType actionType = Boolean.TRUE.equals(dto.getArchive())
                            ? ECommonActionType.Delete : ECommonActionType.Create;

                    ServiceCounterRequest newRequest = new ServiceCounterRequest();
                    newRequest.setCounterRequestId(dto.getId());
                    newRequest.setServiceRequestId(serviceRequestId);
                    newRequest.setServiceType(type);
                    newRequest.setStatus(ECommonStatus.ACTIVE);
                    newRequest.setActionType(actionType);
                    return newRequest;
                })
                .collect(Collectors.toList());
        requestRepository.saveAll(serviceCounterRequests);
    }

    @Override
    public List<CounterRequest> getCounterRequestsByServiceRequestIdAndType(EServiceType serviceType, Integer serviceRequestId) {
        List<Integer> counterIds = requestRepository.findByServiceTypeAndServiceRequestId(
                serviceType, 
                serviceRequestId,
                ECommonStatus.ACTIVE, 
                EApprovalStatus.APPROVED)
                .stream()
                .map(ServiceCounterRequest::getCounterRequestId)
                .collect(Collectors.toList());
        
        return counterRequestRepository.findByIdIn(counterIds);
    }
}
