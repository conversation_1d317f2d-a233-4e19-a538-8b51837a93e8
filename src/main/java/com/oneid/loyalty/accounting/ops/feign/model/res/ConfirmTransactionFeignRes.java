package com.oneid.loyalty.accounting.ops.feign.model.res;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConfirmTransactionFeignRes {

    @JsonProperty("transaction")
    TransactionRes transactionRes;

    @JsonProperty("business_id")
    private String businessId;

    @JsonProperty("program_id")
    private String programId;

    @JsonProperty("channel_id")
    private String channelId;

    @JsonProperty("service_id")
    private String serviceCode;

    public class TransactionRes {
        @JsonProperty("txn_ref_no")
        private String txnRefNo;
    
        @JsonProperty("invoice_no")
        private String invoiceNo;
    
        @JsonProperty("balance_before")
        private BigDecimal balanceBefore;
    
        @JsonProperty("balance_after")
        private BigDecimal balanceAfter;
    
        @JsonProperty("store_id")
        private String storeId;
    
        @JsonProperty("terminal_id")
        private String terminalId;
    
        @JsonProperty("gross_amount")
        private BigDecimal grossAmount;
    
        @JsonProperty("gmv")
        private BigDecimal gmv;
    
        @JsonProperty("nett_amount")
        private BigDecimal nettAmount;
    
        @JsonProperty("awarded_point")
        private BigDecimal awardedPoint;
    
        @JsonProperty("redeemed_point")
        private BigDecimal redeemedPoint;
    
        @JsonProperty("transaction_time")
        private Long transactionTime;
    }
}
