package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.TransactionAuditTrail;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Builder
public class TransactionCheckLogRes {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("transaction_ref")
    private String transactionRef;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("payload")
    private Object payload;

    @JsonProperty("source")
    private String sourse;

    @JsonProperty("additional_source_info")
    private String additionalSourceInfo;

    @JsonProperty("created_at")
    private Long createdAt;
}
