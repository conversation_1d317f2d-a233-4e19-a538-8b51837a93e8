package com.oneid.loyalty.accounting.ops.support.web.acl;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AccessRole {
    MEMBER("LoyaltyAccounting_User", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT,
            AccessPermission.IMPORT
    }),

    MEMBER_TRANSACTION("LoyaltyAccounting_Transaction", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    MEMBER_BALANCE("LoyaltyAccounting_User_Balance", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    BUSINESS("LoyaltyAccounting_Business", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CURRENCY("LoyaltyAccounting_Currency", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CURRENCY_RATE("LoyaltyAccounting_CurrencyRate", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    PROGRAM("LoyaltyAccounting_Program", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    POOL("LoyaltyAccounting_Pool", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    TIER("LoyaltyAccounting_Tier", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    REFUNDING_TRANSACTION("LoyaltyAccounting_Refunding_Transaction", new AccessPermission[]{
            AccessPermission.EDIT
    }),

    REFUNDING_CRATE_NEW_TRANSACTION("LoyaltyAccounting_Refunding_CreateNew_Transaction", new AccessPermission[]{
            AccessPermission.EDIT
    }),

    TRANSACTION("LoyaltyAccounting_Transaction_Management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EXPORT
    }),

    TRANSACTION_REQUEST("LoyaltyAccounting_Transaction_Request", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EXPORT
    }),

    TRANSACTION_FILE_PROCESS("LoyaltyAccounting_Transaction_File_Process", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE
    }),

    MEMBER_PRODUCT_ACCOUNT("LoyaltyAccounting_Card", new AccessPermission[]{
            AccessPermission.VIEW
    }),

    MEMBER_CARD("LoyaltyAccounting_MemberCard", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CARD("LoyaltyAccounting_Card", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CORPORATION("LoyaltyAccounting_Corporation", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CHAIN("LoyaltyAccounting_Chain", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.CREATE
    }),

    STORE("LoyaltyAccounting_Store", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT,
            AccessPermission.CREATE
    }),

    TERMINAL("LoyaltyAccounting_Terminal", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    SCHEME("LoyaltyAccounting_Scheme", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    CARD_POLICY("LoyaltyAccounting_CardPolicy", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_BIN("LoyaltyAccounting_CardBin", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_TYPE("LoyaltyAccounting_CardType", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_CARD_PR("LoyaltyAccounting_cpr", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),

    GIFT_CARD_BIN("LoyaltyAccounting_GiftCard_CardBin", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_TYPE("LoyaltyAccounting_GiftCard_CardType", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_PR("LoyaltyAccounting_GiftCard_cpr", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    
    GIFT_CARD("LoyaltyAccounting_gift_card", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    GIFT_CARD_TRANSFER("LoyaltyAccounting_GiftCard_Transferring", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),

    MEMBER_CARD_TRANSFER("LoyaltyAccounting_MemberCardTransfer", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    OPERATOR("LoyaltyAccounting_Operator", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MAKER_CHECKER("MakerChecker", new AccessPermission[]{
            AccessPermission.MAKER_ROLE,
            AccessPermission.CHECKER_ROLE
    }),
    
    SCHEME_SEQUENCE("LoyaltyAccounting_SchemeSequence", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),
    
    COUNTER("LoyaltyAccounting_Counter", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    LIMITATION("LoyaltyAccounting_Limit", new AccessPermission[]{
        AccessPermission.VIEW,
                AccessPermission.CREATE,
                AccessPermission.EDIT,
                AccessPermission.APPROVE_OR_REJECT
    }),

    TIER_POLICY("LoyaltyAccounting_TierPolicy", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),
    
    BATCH_ADJUST_PARTNER_TRANSACTION("LoyaltyAccounting_tcb_batch_adj_partner_txn", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EXPORT,
    }),
    
    ADJUST_MEMBER_TIER("LoyaltyAccounting_adjust_member_tier", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.EXPORT
    }),
    
    MEMBER_CARD_LISTING("LoyaltyAccounting_member_card", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    
    SKU_MANAGEMENT("LoyaltyAccounting_sku_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.EDIT
    }),
    
    PROGRAM_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_program_attribute_management", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    MEMBER_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_member_attribute_management", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    PROGRAM_TRANSACTION_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_transaction_attribute_management", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    SYSTEM_ATTRIBUTE_MANAGEMENT("LoyaltyAccounting_system_attribute_management", new AccessPermission[] {
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    
    TIER_MATCHING_MANAGEMENT("LoyaltyAccounting_tier_matching_management", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),
    PROGRAM_CORPORATION("LoyaltyAccounting_ProgramCorporation", new AccessPermission[]{
        AccessPermission.VIEW,
        AccessPermission.CREATE,
        AccessPermission.EDIT
    }),
    PROGRAM_FUNCTION("LoyaltyAccounting_ProgramFunction", new AccessPermission[]{
        AccessPermission.VIEW,
        AccessPermission.CREATE,
        AccessPermission.EDIT
    }),
    PROGRAM_LEVEL("LoyaltyAccounting_ProgramLevel", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT
    }),

    MEMBER_STATUS("LoyaltyAccounting_User_Status_Config", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
            AccessPermission.EDIT,
            AccessPermission.APPROVE_OR_REJECT
    }),

    CPM_MANAGEMENT("LoyaltyAccounting_CPM_Transaction", new AccessPermission[]{
            AccessPermission.VIEW,
            AccessPermission.CREATE,
    }),

    MEMBER_STATUS_TRANSITION("LoyaltyAccounting_User_Status_Transition", new AccessPermission[]{
        AccessPermission.VIEW,
                AccessPermission.CREATE,
                AccessPermission.EDIT,
                AccessPermission.APPROVE_OR_REJECT
    });
    

    String name;
    AccessPermission[] permissions;

    public static AccessRole lookup(String name) {
        if (name == null) {
            return null;
        }

        return Stream.of(values())
                .filter(each -> each.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

}
