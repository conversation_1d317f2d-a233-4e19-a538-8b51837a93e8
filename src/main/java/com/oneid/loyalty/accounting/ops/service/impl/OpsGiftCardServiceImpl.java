package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTypeRes;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardService;
import com.oneid.loyalty.accounting.ops.util.excel.GiftCardFileFormatUtil;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.GiftCard;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.SingleGiftCardRequest;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.GiftCardRepository;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.repository.GiftCardTypeRepository;
import com.oneid.oneloyalty.common.repository.MemberRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.SingleGiftCardRequestRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.service.GiftCardService;
import com.oneid.oneloyalty.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
public class OpsGiftCardServiceImpl implements OpsGiftCardService {
    private final static String PREFIX_GIFT_CARD = "OLSGiftcard";

    private final static String PREFIX_GIFT_CODE = "OLSGiftcode";

    @Value("${maker-checker.module.gift-card}")
    private String moduleId;
    
    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @Autowired
    private ProgramRepository programRepository;
    
    @Autowired
    private GiftCardRepository giftCardRepository;
    
    @Autowired
    private SingleGiftCardRequestRepository singleGiftCardRequestRepository;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private GiftCardTypeRepository giftCardTypeRepository;
    
    @Autowired
    private CorporationRepository corporationRepository;
    
    @Autowired
    private ChainRepository chainRepository;
    
    @Autowired
    private StoreRepository storeRepository;
    
    @Autowired
    private MemberRepository memberRepository;
    
    @Autowired
    private GiftCardRequestRepository giftCardRequestRepository;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private GiftCardService giftCardService;
    
    private TransactionTemplate transactionTemplate;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }
    
    @Override
    public Page<GiftCardRes> getPage(int businessId, Integer corporationId, Integer chainId, Integer storeId,
            Integer programId, String serial, Long batchNo, Long point,
            EGiftCardStatus status, EApprovalStatus approvalStatus,
            LocalDate topupDate, LocalDate expirationDate, Pageable pageable
    ) {
        ZoneId systemZoneId = ZoneId.systemDefault();
        Date topupDateUtil = topupDate == null ? 
                null : Date.from(topupDate.atStartOfDay().atZone(systemZoneId).toInstant());
        Date expirationDateUtil = expirationDate == null ? 
                null : Date.from(expirationDate.atStartOfDay().atZone(systemZoneId).toInstant());
        
        Business business = businessRepository.findById(businessId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        
        if (approvalStatus == EApprovalStatus.APPROVED) {
            Page<Object[]> page = giftCardRepository.findPage(businessId, corporationId, chainId, storeId, programId, 
                    serial, batchNo, point, status, topupDateUtil, expirationDateUtil, pageable);
            List<GiftCardRes> content = this.convertForApprovedList(page.getContent(), business);
            
            return new PageImpl<>(content, pageable, page.getTotalElements());
        } else {
            Page<Object[]> page = singleGiftCardRequestRepository.findPage(businessId, corporationId, chainId, storeId, programId, 
                    serial, batchNo, point, status, approvalStatus, topupDateUtil, expirationDateUtil, pageable);
            List<GiftCardRes> content = this.convertForRequestList(page.getContent(), business);
            
            return new PageImpl<>(content, pageable, page.getTotalElements());
        }
    }
    
    private List<GiftCardRes> convertForApprovedList(List<Object[]> records, Business business) {
        return records.stream()
                .map(record -> {
                    GiftCard giftCard = GiftCard.class.cast(record[0]);
                    GiftCardType giftCardType = GiftCardType.class.cast(record[1]);
                    Store store = Store.class.cast(record[2]);
                    Chain chain = Chain.class.cast(record[3]);
                    Corporation corporation = Corporation.class.cast(record[4]);
                    Program program = Program.class.cast(record[5]);
        
                    GiftCardRes res = this.convertToRes(giftCard, giftCardType, store, chain, corporation, program, business);
                    res.setApprovalStatus(EApprovalStatus.APPROVED);    // default for approved request
                    return res;
                })
                .collect(Collectors.toList());
    }
    
    private List<GiftCardRes> convertForRequestList(List<Object[]> records, Business business) {
        return records.stream()
                .map(record -> {
                    GiftCard giftCard = GiftCard.class.cast(record[0]);
                    GiftCardType giftCardType = GiftCardType.class.cast(record[1]);
                    Store store = Store.class.cast(record[2]);
                    Chain chain = Chain.class.cast(record[3]);
                    Corporation corporation = Corporation.class.cast(record[4]);
                    Program program = Program.class.cast(record[5]);
                    SingleGiftCardRequest sgcRequest = SingleGiftCardRequest.class.cast(record[6]);
        
                    GiftCardRes res = this.convertToRes(giftCard, giftCardType, store, chain, corporation, program, business);
                    res.setRequestId(sgcRequest.getId());
                    res.setApprovalStatus(sgcRequest.getApprovalStatus());
                    res.setRejectedReason(sgcRequest.getRejectedReason());
                    if (sgcRequest.getStatus() != null) {
                        res.setStatus(sgcRequest.getStatus());
                    }
                    if (sgcRequest.getExpirationDate() != null) {
                        res.setExpirationDate(
                                sgcRequest.getExpirationDate().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime());
                    }
                    return res;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public GiftCardRes getApprovedDetail(int programId, String serial) {
        GiftCard giftCard = giftCardRepository.findByProgramIdAndSerial(programId, serial)
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_NOT_FOUND));
        
        GiftCardRes res = this.getDetailRes(giftCard);
        res.setApprovalStatus(EApprovalStatus.APPROVED);    // default for approved request
        return res;
    }
    
    @Override
    public GiftCardRes getRequestDetail(int programId, String serial, int requestId) {
        GiftCard giftCard = giftCardRepository.findByProgramIdAndSerial(programId, serial)
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_NOT_FOUND));
        
        SingleGiftCardRequest sgcRequest = singleGiftCardRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.SINGLE_GIFT_CARD_REQUEST_NOT_FOUND));
        
        GiftCardRes res = this.getDetailRes(giftCard);
        
        res.setRequestId(sgcRequest.getId());
        res.setApprovalStatus(sgcRequest.getApprovalStatus());
        res.setRejectedReason(sgcRequest.getRejectedReason());
        if (sgcRequest.getStatus() != null) {
            res.setStatus(sgcRequest.getStatus());
        }
        if (sgcRequest.getExpirationDate() != null) {
            res.setExpirationDate(
                    sgcRequest.getExpirationDate().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime());
        }
        return res;
    }
    
    @Override
    public GiftCardRes getChangeable(int programId, String serial) {
        GiftCard giftCard = this.getChangeableGiftCard(programId, serial);
        GiftCardRes res = this.getDetailRes(giftCard);
        res.setApprovalStatus(EApprovalStatus.APPROVED);    // default for approved request
        res.setTransitionStatuses(res.getStatus().getTransformableList());
        return res;
    }
    
    private GiftCardRes getDetailRes(GiftCard giftCard) {
        Program program = programRepository.findById(giftCard.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
        
        GiftCardType giftCardType = giftCardTypeRepository.findById(giftCard.getGcTypeId())
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_TYPE_NOT_FOUND));
        
        Store store = storeRepository.findById(giftCard.getStoreId())
                .orElseThrow(() -> new BusinessException(ErrorCode.STORE_NOT_FOUND));
        
        Chain chain = chainRepository.findById(store.getChainId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CHAIN_NOT_FOUND));
        
        Corporation corporation = corporationRepository.findById(store.getCorporationId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND));
        
        Business business = businessRepository.findById(giftCard.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND));
        
        GiftCardRes res = this.convertToRes(giftCard, giftCardType, store, chain, corporation, program, business);
        
        GiftCardRequest giftCardRequest = giftCardRequestRepository.findByCprBatchNoAndBusinessIdAndProgramId(
                giftCard.getCprBatchNo(), giftCard.getBusinessId(), giftCard.getProgramId());
        
        res.setBatchType(giftCardRequest.getGcBatchType());
        
        if (giftCard.getMemberId() != null) {
            memberRepository.findById(giftCard.getMemberId())
                    .ifPresent(member -> {
                        res.setMemberCode(member.getMemberCode());
                    });
        }
        
        return res;
    }
    
    @Override
    public GiftCardRes requestUpdate(GiftCardUpdateReq req) {
        SingleGiftCardRequest reqEntity = transactionTemplate
                .execute(status -> this.createUpdateRequest(req));
        
        ChangeRequestFeignReq changeReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(moduleId)
                .objectId(reqEntity.getId().toString())
                .payload(RequestFeignReq.builder()
                        .requestId(reqEntity.getId())
                        .build())
                .build();
        
        makerCheckerServiceClient.changes(changeReq);
        
        return GiftCardRes.builder()
                .requestId(reqEntity.getId())
                .build();
    }
    
    private SingleGiftCardRequest createUpdateRequest(GiftCardUpdateReq req) {
        Integer programId = req.getProgramId();
        String serial = req.getSerial();
        
        Program program = programRepository.findById(programId)
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND));
        
        if (program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE);
        
        GiftCard giftCard = this.getChangeableGiftCard(programId, serial);
        
        EGiftCardStatus currentStatus = giftCard.getStatus();
        EGiftCardStatus newStatus = req.getStatus();
        if (newStatus != null) {
            boolean isValidChangedStatus = currentStatus.getTransformableList().contains(newStatus);
            if (isValidChangedStatus == false)
                throw new BusinessException(ErrorCode.GIFT_CARD_STATUS_CAN_NOT_UPDATE);
        }
        
        if (req.getExpirationDate() != null) {
            if (currentStatus != EGiftCardStatus.ACTIVE)
                throw new BusinessException(ErrorCode.GIFT_CARD_NOT_ACTIVE);
        }
        
        int currentVersion = singleGiftCardRequestRepository.findEffectedVersion(programId, serial)
                .map(SingleGiftCardRequest::getVersion)
                .orElse(0);
        
        Date expirationDate = req.getExpirationDate() == null ? null : Date.from(req.getExpirationDate().toInstant());
        
        SingleGiftCardRequest reqEntity = SingleGiftCardRequest.builder()
                .programId(programId)
                .serial(serial)
                .status(newStatus)
                .expirationDate(expirationDate)
                .version(currentVersion + 1)
                .requestStatus(ECommonStatus.PENDING)
                .approvalStatus(EApprovalStatus.PENDING)
                .changedReason(req.getReason())
                .build();
        
        singleGiftCardRequestRepository.save(reqEntity);
        return reqEntity;
    }
    
    private GiftCardRes convertToRes(GiftCard giftCard, GiftCardType giftCardType, 
            Store store, Chain chain, Corporation corporation, Program program, Business business
    ) {
        ZoneId systemZoneId = ZoneId.systemDefault();
        OffsetDateTime expirationDate = giftCard.getExpirationDate().toInstant().atZone(systemZoneId).toOffsetDateTime();
        OffsetDateTime startDate = giftCard.getStartDate() == null ?
                null : giftCard.getStartDate().toInstant().atZone(systemZoneId).toOffsetDateTime();
        OffsetDateTime topupAt = giftCard.getTopupAt() == null ?
                null : giftCard.getTopupAt().toInstant().atZone(systemZoneId).toOffsetDateTime();
        OffsetDateTime createdAt = giftCard.getCreatedAt() == null ?
                null : giftCard.getCreatedAt().toInstant().atZone(systemZoneId).toOffsetDateTime();
        
        GiftCardTypeRes giftCardTypeRes = GiftCardTypeRes.builder()
                .id(giftCardType.getId())
                .code(giftCardType.getCode())
                .point(giftCardType.getPoint())
                .price(giftCardType.getPrice())
                .build();
        
        return GiftCardRes.builder()
                .id(giftCard.getId())
                .cprBatchNo(giftCard.getCprBatchNo())
                // .batchType(...) // TODO
                .serial(giftCard.getSerial())
                .businessId(business.getId())
                .businessName(business.getName())
                .corporationId(corporation.getId())
                .corporationName(corporation.getName())
                .chainId(chain.getId())
                .chainName(chain.getName())
                .storeId(store.getId())
                .storeName(store.getName())
                .programId(program.getId())
                .programName(program.getName())
                .expirationDate(expirationDate)
                .startDate(startDate)
                .topupAt(topupAt)
                .status(giftCard.getStatus())
                .memberId(giftCard.getMemberId())
                .createdAt(createdAt)
                .createdBy(giftCard.getCreatedBy())
                .giftCardType(giftCardTypeRes)
                .build();
    }
    
    private GiftCard getChangeableGiftCard(int programId, String serial) {
        GiftCard giftCard = giftCardRepository.findByProgramIdAndSerial(programId, serial)
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_NOT_FOUND));
        
        if (CollectionUtils.isEmpty(giftCard.getStatus().getTransformableList())) {
            throw new BusinessException(ErrorCode.GIFT_CARD_STATUS_CAN_NOT_UPDATE);
        }
        
        singleGiftCardRequestRepository.findPendingVersion(programId, serial)
                .ifPresent(entity -> {
                    throw new BusinessException(ErrorCode.SINGLE_GIFT_CARD_REQUEST_IS_ALREADY_REQUESTED);
                });
        return giftCard;
    }
    
    @Override
    public Page<GiftCardRes> getInReviewPage(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable) {
        Integer fromEpochSecond = null;
        Integer toEpochSecond = null;
        if (fromDate != null) {
            fromEpochSecond = (int) fromDate.atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond();
        }
        if (toDate != null) {
            toEpochSecond = (int) toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toEpochSecond();
        }
        APIResponse<ChangeRequestPageFeignRes> mcResponse = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                fromEpochSecond,
                toEpochSecond);

        ChangeRequestPageFeignRes mcRequestPage = mcResponse.getData();
        
        if (mcRequestPage.getTotalRecordCount() <= 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
        
        List<Integer> requestIds = mcRequestPage.getRecords().stream()
                .map(ChangeRecordFeginRes::getObjectId)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        
        List<Object[]> requestRecords = singleGiftCardRequestRepository.findDataByIds(requestIds);
        
        List<GiftCardRes> requests = requestRecords.stream()
                .map(record -> {
                    GiftCard giftCard = GiftCard.class.cast(record[0]);
                    GiftCardType giftCardType = GiftCardType.class.cast(record[1]);
                    Store store = Store.class.cast(record[2]);
                    Chain chain = Chain.class.cast(record[3]);
                    Corporation corporation = Corporation.class.cast(record[4]);
                    Program program = Program.class.cast(record[5]);
                    SingleGiftCardRequest sgcRequest = SingleGiftCardRequest.class.cast(record[6]);
                    Business business = Business.class.cast(record[7]);
        
                    GiftCardRes res = this.convertToRes(giftCard, giftCardType, store, chain, corporation, program, business);
                    res.setRequestId(sgcRequest.getId());
                    res.setApprovalStatus(sgcRequest.getApprovalStatus());
                    res.setRejectedReason(sgcRequest.getRejectedReason());
                    if (sgcRequest.getStatus() != null) {
                        res.setStatus(sgcRequest.getStatus());
                    }
                    if (sgcRequest.getExpirationDate() != null) {
                        res.setExpirationDate(
                                sgcRequest.getExpirationDate().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime());
                    }
                    return res;
                })
                .collect(Collectors.toList());
        
        Map<Integer, GiftCardRes> requestMap = requests.stream()
                .collect(Collectors.toMap(GiftCardRes::getRequestId, e -> e));
        
        List<GiftCardRes> content = mcRequestPage.getRecords().stream()
                .map(mcRequest -> {
                    Integer reviewId = mcRequest.getChangeRequestId();
                    Integer requestId = Integer.parseInt(mcRequest.getObjectId());
                    
                    if (requestMap.containsKey(requestId) == false) // handle mismatched data with makerchecker
                        return null;
                    
                    GiftCardRes request = requestMap.get(requestId);
                    request.setReviewId(reviewId);
                    return request;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        return new PageImpl<>(content, pageable, mcRequestPage.getTotalRecordCount());
    }
    
    @Override
    public GiftCardRes getInReviewDetail(int reviewId) {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> mcResponse = makerCheckerServiceClient.getChangeRequestById(String.valueOf(reviewId));
        
        Integer requestId = Integer.parseInt(mcResponse.getData().getObjectId());
        SingleGiftCardRequest sgcRequest = singleGiftCardRequestRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.SINGLE_GIFT_CARD_REQUEST_NOT_FOUND));
        
        GiftCard giftCard = giftCardRepository.findByProgramIdAndSerial(sgcRequest.getProgramId(), sgcRequest.getSerial())
                .orElseThrow(() -> new BusinessException(ErrorCode.GIFT_CARD_NOT_FOUND));
        
        GiftCardRes res = this.getDetailRes(giftCard);
        res.setReviewId(reviewId);
        res.setRequestId(sgcRequest.getId());
        res.setApprovalStatus(sgcRequest.getApprovalStatus());
        res.setRejectedReason(sgcRequest.getRejectedReason());
        if (sgcRequest.getStatus() != null) {
            res.setStatus(sgcRequest.getStatus());
        }
        if (sgcRequest.getExpirationDate() != null) {
            res.setExpirationDate(
                    sgcRequest.getExpirationDate().toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime());
        }
        return res;
    }

    @Override
    public ResourceDTO exportGiftCard(int giftCardRequestId) {
        List<Object[]> obj = giftCardRequestRepository.findAllByRequestId(giftCardRequestId);

        if (ObjectUtils.isEmpty(obj)) {
            throw new BusinessException(
                    ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND,
                    "Gift card production request not found",
                    null
            );
        }
        Object[] objectGiftCardRequest = obj.get(0);
        GiftCardRequest giftCardRequest = (GiftCardRequest) objectGiftCardRequest[0];
        Business business = (Business) objectGiftCardRequest[1];
        Program program = (Program) objectGiftCardRequest[2];
        Store store = (Store) objectGiftCardRequest[3];
        GiftCardType gcType = (GiftCardType) objectGiftCardRequest[4];

        if (!giftCardRequest.getApprovalStatus().equals(EApprovalStatus.APPROVED)
                || !giftCardRequest.getGenerateInd().equals(EGiftCardIndicator.YES)) {
            throw new BusinessException(ErrorCode.GIFT_CARD_PRODUCTION_IN_PROGRESS, "Gift card production request is not valid", null);
        }

        String fileName = PREFIX_GIFT_CARD;
        if (giftCardRequest.getGcBatchType().equals(EGiftCardBatchType.GIFT_CODE))
            fileName = PREFIX_GIFT_CODE;


        int ddMMyyyy = DateUtil.formatDDMMYYYY(new Date());
        fileName = String.format("%s_%s_%s_%s.xlsx", fileName, ddMMyyyy, giftCardRequest.getCprBatchNo(), store.getId());

        return saveFile(fileName, giftCardRequest, business.getCode(), program.getCode(), store.getCode(), gcType);
    }

    private ResourceDTO saveFile(String fileName, GiftCardRequest giftCardRequest, String  businessCode, String  programCode, String storeCode, GiftCardType giftCardType) {
        Pageable pageable = PageRequest.of(0, GiftCardFileFormatUtil.PAGE_SIZE, Sort.by("id"));

        SXSSFWorkbook workbook = new SXSSFWorkbook(GiftCardFileFormatUtil.BATCH_SIZE_FLUSH);
        Sheet sheet = workbook.createSheet("sheetName");

        // Create Header Row
        CellStyle headerCellStyle = GiftCardFileFormatUtil.getCellStyleHeader(workbook);
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < GiftCardFileFormatUtil.COLUMN.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(GiftCardFileFormatUtil.COLUMN[i]);
            cell.setCellStyle(headerCellStyle);
        }
        // Create Data Rows
        AtomicInteger rowNum = new AtomicInteger(1);
        do {
            Page<Object[]> giftCardPage = giftCardService
                    .searchGiftCardByBusinessBatchNo(giftCardRequest.getBusinessId(),
                            giftCardRequest.getProgramId(),
                            giftCardRequest.getStoreId(),
                            giftCardRequest.getCprBatchNo(),
                            pageable);
            Consumer<Object[]> consumer = (obj) -> {
                Row row = sheet.createRow(rowNum.getAndIncrement());
                GiftCard gc = (GiftCard) obj[0];
                Member m = (Member) obj[1];
                row.createCell(0).setCellValue(businessCode);
                row.createCell(1).setCellValue(programCode);
                row.createCell(2).setCellValue(storeCode);
                row.createCell(3).setCellValue(giftCardRequest.getCprBatchNo());
                row.createCell(4).setCellValue(giftCardType.getPoint());
                row.createCell(5).setCellValue(gc.getSerial());
                row.createCell(6).setCellValue(gc.getStatus().getValue());
                row.createCell(7).setCellValue(gc.getCreatedAt() != null ? DateUtil.formatDD_MM_yy(gc.getCreatedAt()) : "");
                row.createCell(8).setCellValue(gc.getExpirationDate() != null ? DateUtil.formatDD_MM_yy(gc.getExpirationDate()) : "");
                row.createCell(9).setCellValue(gc.getTopupAt() != null ? DateUtil.formatDD_MM_yy(gc.getTopupAt()) : "");
                if (m != null) {
                    row.createCell(10).setCellValue(m.getMemberCode());
                }
            };
            giftCardPage.getContent().forEach(consumer);

            pageable = giftCardPage.nextPageable();
        } while (!pageable.isUnpaged());


        // Write file
        String localPath = Paths.get("/tmp", fileName).toString();
        File file = new File(localPath);

        try {
            file.createNewFile();
            FileOutputStream fileOut = new FileOutputStream(localPath, false);
            workbook.write(fileOut);
            workbook.close();
            fileOut.close();
            return ResourceDTO.builder()
                    .filename(fileName)
                    .resource(new InputStreamResource(new FileInputStream(file))).build();
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.FILE_TRANSACTION_NOT_FOUND, e.getMessage(), null);
        }

    }
}
