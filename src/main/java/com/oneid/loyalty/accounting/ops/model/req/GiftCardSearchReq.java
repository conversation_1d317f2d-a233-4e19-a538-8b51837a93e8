package com.oneid.loyalty.accounting.ops.model.req;

import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class GiftCardSearchReq {
    private Integer businessId;
    private Integer corporationId;
    private Integer programId;
    private Integer chainId;
    private Long cprBatchNo;
    private Integer storeId;
    private EGiftCardIndicator generationStatus;
    private EGiftCardStatus giftCardStatus;
    private EApprovalStatus approvalStatus;
    private EGiftCardBatchType batchType;
}