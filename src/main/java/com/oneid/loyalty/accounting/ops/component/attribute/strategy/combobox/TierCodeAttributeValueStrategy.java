package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.ProgramTierRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class TierCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private ProgramTierRepository programTierRepository;

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(TierCodeAttributeValueStrategy.class);

    public TierCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getTierCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {

        ProgramTier programTier = programTierRepository.findByProgramIdAndTierCode(programIds[0], value);

        if (programTier == null) {
            LOGGER.warn("Tier {} not found", value);
            throw new IllegalArgumentException();
        }

        return AttributeCombobox.builder()
                .name(programTier.getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> programTierCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return programTierRepository.findByProgramIdAndTierCodes(programIds[0], programTierCodes)
                    .stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(s.getName())
                            .value(s.getTierCode())
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}