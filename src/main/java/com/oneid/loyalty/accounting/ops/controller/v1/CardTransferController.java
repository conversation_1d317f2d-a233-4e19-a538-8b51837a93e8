package com.oneid.loyalty.accounting.ops.controller.v1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.oneid.loyalty.accounting.ops.model.req.RejectReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferFilterReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.ListCardTransfer;
import com.oneid.loyalty.accounting.ops.model.res.RequestTransferRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardTransferService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import com.oneid.oneloyalty.common.controller.BaseController;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;

@RestController
@RequestMapping("v1/card-transfers")
public class CardTransferController extends BaseController {
    @Autowired
    OpsCardTransferService opsCardTransferService;

    @PostMapping
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.MEMBER_CARD_TRANSFER, permissions = { AccessPermission.CREATE }),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.MAKER_ROLE })
    })
    public ResponseEntity<?> createRequestCardTransfer(@RequestBody RequestTransferReq req) {
        return success(opsCardTransferService.createRequestCardTransfer(req));
    }

    @GetMapping(value = "{id}")
    @Authorize(role = AccessRole.MEMBER_CARD_TRANSFER, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getDetails(@PathVariable("id") Integer id) {
        return success(opsCardTransferService.getDetails(id));
    }

    @GetMapping("/{id}/cards")
    @Authorize(role = AccessRole.MEMBER_CARD_TRANSFER, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getListCardNo(@RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                           @RequestParam(value = "limit", defaultValue = "20") Integer limit,
                                           @PathVariable("id") Integer id) {
        ListCardTransfer listCardTransfer = opsCardTransferService
                .getListCardByTransferId(id,new OffsetBasedPageRequest(offset, limit));
        return success(listCardTransfer,
                (int)listCardTransfer.getOffset(),
                (int)listCardTransfer.getLimit(),
                (int)listCardTransfer.getTotal()
        );
    }

    @GetMapping
    @Authorize(role = AccessRole.MEMBER_CARD_TRANSFER, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> filter(
            @RequestParam(value = "card_transfer_no", required = false) Long cardTransferNo,
            @RequestParam(value = "indicator", required = true) ECardTransferIndicatorStatus indicator,
            @RequestParam(value = "status", required = false) ECardTransferStatus status,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "batch_no", required = false) Integer batchNo,
            @RequestParam(value = "from_store_id", required = false) Integer fromStoreId,
            @RequestParam(value = "to_store_id", required = false) Integer toStoreId,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit
    ) {

        RequestTransferFilterReq req = RequestTransferFilterReq.builder()
                .cardTransferNo(cardTransferNo)
                .indicator(indicator)
                .status(status)
                .programId(programId)
                .batchNo(batchNo)
                .fromStoreId(fromStoreId)
                .toStoreId(toStoreId)
                .pageRequest(new OffsetBasedPageRequest(offset, limit, Sort.by("createdAt").descending()))
                .build();

        Page<RequestTransferRes> pageRes = opsCardTransferService.filter(req);

        return success(pageRes.getContent(), offset, limit, (int) pageRes.getTotalElements());
    }

    @PostMapping("{id}/approve")
    @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.CHECKER_ROLE })
    public ResponseEntity<?> approve(@PathVariable("id") Integer id) {
        return success(opsCardTransferService.approve(id));
    }

    @PostMapping("{id}/reject")
    @Authorize(role = AccessRole.MAKER_CHECKER, permissions = { AccessPermission.CHECKER_ROLE })
    public ResponseEntity<?> processChangeRequest(
            @PathVariable("id") Integer id,
            @RequestBody RejectReq req) {
        return success(opsCardTransferService.reject(id, req));
    }
}