package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateStoreReq extends StoreUpdateReq {
    @Size(max = 15, message = "'Code' cannot exceed 15 characters")
    @NotBlank(message = "'Code' cannot be empty")
    @Pattern(regexp = "^[a-zA-Z0-9]{0,15}$", message = "'Code' is invalid")
    private String code;
}