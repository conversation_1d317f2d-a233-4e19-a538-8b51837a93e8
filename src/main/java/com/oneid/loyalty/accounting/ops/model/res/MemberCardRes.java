package com.oneid.loyalty.accounting.ops.model.res;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECardStatus;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberCardRes extends VersioningRes {
    @Setter
    private Integer reviewId;
    
    private Integer requestId;
    
    private Integer programId;
    private Long batchNo;
    private String cardNo;
    private String cardType;
    private ECardStatus currentCardStatus;
    
    @Setter
    private ECardStatus changedCardStatus;
    
    private List<TransitionCardStatusRes> transitionStatuses;
    private String businessName;
    private String programName;
    private String storeName;
    private String chainName;
    private String corporationName;
    private EApprovalStatus approvalStatus;
}
