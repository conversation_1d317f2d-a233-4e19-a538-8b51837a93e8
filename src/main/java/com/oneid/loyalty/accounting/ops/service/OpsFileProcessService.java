package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.SearchControlFileHistoryReq;
import com.oneid.loyalty.accounting.ops.model.res.ControlFileHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.DataFileHistoryRes;
import com.oneid.loyalty.accounting.ops.model.res.AuditTrailAutoEarningRes;
import com.oneid.loyalty.accounting.ops.model.res.RetryFileRequestHistoryRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OpsFileProcessService {

    Page<ControlFileHistoryRes> getControlFileHistoryList(SearchControlFileHistoryReq req, Pageable page);

    ControlFileHistoryRes getControlFileHistoryDetail(Long id);

    Page<DataFileHistoryRes> getDataFileHistoryList(Long id, Pageable pageable);

    Page<AuditTrailAutoEarningRes> getFailedTransactionList(Long id, Pageable pageable);

    void ignoreControlFile(Long id);

    ResourceDTO exportFailedTransaction(Long controlFileId);

    List<RetryFileRequestHistoryRes> getRetryHistoryList(Long id);

    void retryControlFile(Long id);
}
