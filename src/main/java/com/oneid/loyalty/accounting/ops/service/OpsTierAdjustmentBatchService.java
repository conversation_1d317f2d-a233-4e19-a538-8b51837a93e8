package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentBatchRequestRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentBatchProcessStatus;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.text.ParseException;
import java.util.Date;

public interface OpsTierAdjustmentBatchService {

    Page<TierAdjustmentBatchRequestRes> filterAdjustment(Integer businessId, Integer programId, Integer batchNo, ETierAdjustmentBatchProcessStatus processStatus,
                                                         ETierAdjustmentType type, EApprovalStatus approvalStatus, String createdBy, Date createdStart, Date createdEnd,
                                                         String approvedBy, Date approvedStart, Date approvedEnd, Boolean failedRecords, Pageable pageable) throws ParseException;

    TierAdjustmentBatchRequestRes inReviewDetail(Integer inReviewId);

    TierAdjustmentBatchRequestRes approveBatchRequest(ApprovalReq req);

    TierAdjustmentBatchRequestRes retryBatchRequest(Integer id);

    TierAdjustmentBatchRequestRes availableDetail(Integer requestId);
}
