package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VerifyMemberCardReq {
    @JsonProperty("business_code")
    private String businessCode;
    @JsonProperty("program_code")
    private String programCode;
    @JsonProperty("card_type_id")
    private Integer cardTypeId;
    @JsonProperty("store_code")
    private String storeCode;
    @JsonProperty("card_no")
    private String cardNo;
}