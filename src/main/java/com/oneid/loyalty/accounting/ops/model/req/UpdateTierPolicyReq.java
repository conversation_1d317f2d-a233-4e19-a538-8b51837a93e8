package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.model.dto.ActionOnCounterDto;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.ValidatorUtil;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpdateTierPolicyReq implements Serializable {
    private static final long serialVersionUID = 1445546911338647779L;

    private Integer tierPolicyId;

    private Integer businessId;

    private Integer programId;

    private String code;

    @OpsName
    @NotBlank(message = "'name' must not be blank")
    private String name;

    private String description;

    private List<Integer> counterIds;

    @NotNull(message = "'period_value' must not be null")
    private Integer periodValue;

    private Integer tierDefaultId;

    @NotNull(message = "'expirations' must not be null")
    @Size(max = 100, min = 1, message = "'expirations' size in range [1, 100]")
    private List<String> expirations;

    @NotNull(message = "'event_qualification_policy' must not be null")
    private Boolean eventQualificationPolicy;

    private List<String> qualificationEventCodes;

    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;

    @NotNull(message = "'counter' must not be null")
    @Size(max = 100, min = 1, message = "'counter_ids' size in range [1, 100]")
    private List<ActionOnCounterDto> counters;

    @JsonDeserialize(using = DateDeserializer.class)
    private Date startDate;

    @JsonDeserialize(using = DateDeserializer.class)
    private Date endDate;

    private String editKey;

    @AssertTrue(message = "'end_date' invalid")
    public boolean isValidEndDate() {
        Date startDateTmp = startDate != null ? startDate : new Date();
        return (endDate == null) || (
                endDate.after(startDateTmp)
                        && endDate.getTime() / DateTimes.DAY_IN_MILLIS != startDateTmp.getTime() / DateTimes.DAY_IN_MILLIS
        );
    }

    @AssertTrue(message = "'qualification_event_ids' must not empty")
    public boolean isValidQualificationEventIds() {
        return !Boolean.TRUE.equals(this.eventQualificationPolicy)
                || (qualificationEventCodes != null && qualificationEventCodes.size() > 0);
    }

    @AssertTrue(message = "'expirations' invalid")
    public boolean isValidExpirations() {
        boolean ok = true;
        for (String expiration : this.expirations) {
            ok &= ValidatorUtil.isValidFormatMMMdd(expiration);
        }
        return ok;
    }

    @AssertTrue(message = "'counter' cannot empty when archive")
    public boolean isValidCounter() {
        return this.counters.stream().anyMatch(c -> !Boolean.TRUE.equals(c.getArchive()));
    }
}
