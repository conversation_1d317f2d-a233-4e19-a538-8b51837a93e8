package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.Oauth2FeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.res.Oauth2GetTokenRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@FeignClient(name = "oauth2", url = "${oauth2.url}", configuration = Oauth2FeignConfig.class)
public interface Oauth2FeignClient {
    @RequestMapping(method = RequestMethod.POST, value = "/oauth2/token",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Headers(
            "Content-Type:application/x-www-form-urlencoded"

    )
    Oauth2GetTokenRes getToken(@RequestHeader(value = "Authorization", required = true) String authorizationHeader,  @RequestBody Map<String, ?> formParams);
}
