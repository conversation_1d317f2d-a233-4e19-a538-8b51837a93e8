package com.oneid.loyalty.accounting.ops.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.SchemeSequenceFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.SchemeSequenceReq;
import com.oneid.loyalty.accounting.ops.model.req.SchemeSequenceSettingReq;
import com.oneid.loyalty.accounting.ops.model.res.SchemeRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeSequenceRequestSettingRes;
import com.oneid.loyalty.accounting.ops.model.res.SchemeSequenceRes;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeSequenceRequestService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeSequence;
import com.oneid.oneloyalty.common.entity.SchemeSequenceRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.SchemeRepository;
import com.oneid.oneloyalty.common.repository.SchemeSequenceRepository;
import com.oneid.oneloyalty.common.repository.SchemeSequenceRequestRepository;

@Service
public class OpsSchemeSequenceRequestServiceImpl implements OpsSchemeSequenceRequestService {
    @Value("${maker-checker.module.scheme_seq}")
    private String moduleId;
    
    @Value("${app.format.date.pattern}")
    private String dateFormatPattern;
    
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    
    @Autowired
    private SchemeSequenceRequestRepository schemeSequenceRequestRepository;
    
    @Autowired
    private SchemeSequenceRepository schemeSequenceRepository;
    
    @Autowired
    private BusinessRepository businessRepository;
    
    @Autowired
    private ProgramRepository programRepository;
    
    @Autowired
    private SchemeRepository schemeRepository;
    
    @Autowired
    MakerCheckerFeignClient makerCheckerServiceClient;
    
    private TransactionTemplate transactionTemplate ;
    
    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }
    
    @Override
    public Integer sendRequestForApproval(Integer businessId, Integer programId, ESchemeType schemeType, SchemeSequenceSettingReq req) {
        SchemeSequenceRequest sequenceRequest = transactionTemplate.execute(status -> {
            return createRequest(businessId, programId, schemeType, req);
        });
        
        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
        .actionType(EMakerCheckerActionType.CREATE)
        .module(moduleId)
        .objectId(sequenceRequest.getId().toString())
        .payload(SchemeSequenceFeignReq.builder()
                .schemeSeqRequestId(sequenceRequest.getId())
                .build())
        .build();
        
        makerCheckerServiceClient.changes(feignReq);
        
        return sequenceRequest.getId();
    }


    @Override
    public Page<SchemeSequenceRes> getChangeRequests(EApprovalStatus approvalStatus, Integer fromDate, Integer toDate, Pageable pageable) {
        approvalStatus = (approvalStatus == null ? EApprovalStatus.PENDING : approvalStatus);
        
        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                moduleId, 
                null, 
                null, 
                approvalStatus.getDisplayName().toUpperCase()
                , pageable.getPageNumber(),
                pageable.getPageSize(), 
                fromDate, 
                toDate);
        
        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        
        List<SchemeSequenceRes> schemeSequenceRes = null;
        
        if(changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRecordFeginRes> changeRecordFeginResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each, (first, second) -> second));
            
            
            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
            .stream()
            .map(record -> Integer.parseInt(record.getObjectId()))
            .collect(Collectors.toList());
            
           Object[][] objects = schemeSequenceRequestRepository.findBy(requestIds);
           
           schemeSequenceRes = Arrays.stream(objects)
           .map(each -> {
               SchemeSequenceRequest schemeSequenceRequest = SchemeSequenceRequest.class.cast(each[0]);
               Business business = Business.class.cast(each[1]);
               Program program = Program.class.cast(each[2]);
               
               return SchemeSequenceRes.builder()
                       .id(schemeSequenceRequest.getId())
                       .changeRequestId(changeRecordFeginResMap.get(schemeSequenceRequest.getId()).getChangeRequestId())
                       .schemeType(schemeSequenceRequest.getSchemeType())
                       .version(schemeSequenceRequest.getVersion())
                       .businessId(business.getId())
                       .businessName(business.getName())
                       .programId(program.getId())
                       .programName(program.getName())
                       .approvalStatus(schemeSequenceRequest.getApprovalStatus())
                       .rejectedReason(schemeSequenceRequest.getRejectedReason())
                       .status(schemeSequenceRequest.getStatus())
                       .createdAt(schemeSequenceRequest.getCreatedAt())
                       .build(); 
           })
           .collect(Collectors.toList());
        } else {
            schemeSequenceRes = Collections.emptyList();
        }
        
        return new PageImpl<SchemeSequenceRes>(schemeSequenceRes, pageable, changeRequestPageFeignRes.getTotalRecordCount());
    }

    @Override
    public SchemeSequenceRequestSettingRes getRequestApprovalSetting(OPSAuthenticatedPrincipal principal, Integer businessId, Integer programId, ESchemeType schemeType) {
        Business business = businessRepository.findById(businessId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));
        
        if(business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);
        
        Program program = programRepository.findByIdAndBusinessId(programId, business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));
        
        if(program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);
        
        SchemeSequenceRequest pendingRequest = schemeSequenceRequestRepository.findPendingVersion(
                business.getId(), 
                program.getId(), 
                schemeType)
        .orElse(null);
        
        if(pendingRequest != null) {
            int pendingRequestId = pendingRequest.getId();
            
            if(!pendingRequest.getCreatedBy().equals(principal.getUserName()))
                throw new BusinessException(ErrorCode.SCHEME_SEQUENCE_REQUEST_ALREADY_REQUESTED, null, null);
            
            APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                    moduleId, 
                    pendingRequestId, 
                    null, 
                    EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                    null,
                    null, 
                    null, 
                    null);
            
            Integer changeRequestId = response.getData().getRecords()
            .stream()
            .findFirst()
            .map(ChangeRecordFeginRes::getChangeRequestId)
            .orElseThrow(() -> new BusinessException(ErrorCode.SCHEME_SEQUENCE_PENDING_CHANGE_REQUEST_NOT_FOUND, null, null, new Object[] { pendingRequestId }));
            
            return SchemeSequenceRequestSettingRes.builder()
                    .pendingRequestId(pendingRequestId)
                    .changeRequestId(changeRequestId)
                    .build();
        }
        
        Integer nextVersion = schemeSequenceRequestRepository.findEffectedVersion(business.getId(), program.getId(), schemeType)
                .map(entity -> entity.getVersion() + 1)
                .orElse(1);
        
        List<SchemeRes> schemeRes = schemeRepository.findActivedSchemes(program.getId(), ECommonStatus.ACTIVE, schemeType)
        .stream()
        .map(entity -> {
            return SchemeRes.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .code(entity.getCode())
                    .sequenceNo(entity.getSequenceNo())
                    .schemeType(entity.getSchemeType())
                    .status(entity.getStatus())
                    .build();
        })
        .collect(Collectors.toList());
        
        return SchemeSequenceRequestSettingRes.builder()
                .nextVersion(nextVersion)
                .schemes(schemeRes)
                .schemeType(schemeType)
                .build();
    }

    @Override
    public SchemeSequenceRequestSettingRes getApprovedRequestById(Integer requestId) {
        SchemeSequenceRequest schemeSequenceRequest = schemeSequenceRequestRepository.findById(requestId)
        .orElseThrow(() -> new BusinessException(ErrorCode.SCHEME_SEQUENCE_REQUEST_NOT_FOUND, "Scheme Sequence not found", null));
        
        Business business = businessRepository.findById(schemeSequenceRequest.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));
        
        Program program = programRepository.findByIdAndBusinessId(schemeSequenceRequest.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));
        
        List<SchemeRes> schemeRes = Arrays.stream(schemeSequenceRepository.findByRequestId(requestId))
        .map(entity -> {
            SchemeSequence schemeSequence = SchemeSequence.class.cast(entity[0]);
            Scheme scheme = Scheme.class.cast(entity[1]);
            
            return SchemeRes.builder()
                    .id(scheme.getId())
                    .name(scheme.getName())
                    .code(scheme.getCode())
                    .sequenceNo(schemeSequence.getSequenceNo())
                    .schemeType(scheme.getSchemeType())
                    .status(scheme.getStatus())
                    .endDate(scheme.getEndDate())
                    .build();
        })
        .collect(Collectors.toList());
        
        return SchemeSequenceRequestSettingRes.builder()
                .id(schemeSequenceRequest.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .programId(program.getId())
                .programName(program.getName())
                .version(schemeSequenceRequest.getVersion())
                .createdAt(schemeSequenceRequest.getCreatedAt())
                .status(schemeSequenceRequest.getStatus())
                .schemes(schemeRes)
                .build();
    }
    
    @Override
    public SchemeSequenceRequestSettingRes getChangeRequestDetailById(Integer changeRequestId) {
        APIResponse<ChangeRecordFeginRes> apiResponse = makerCheckerServiceClient.getChangeRequestById(changeRequestId.toString());
        Integer id = Integer.parseInt(apiResponse.getData().getObjectId());
        
        SchemeSequenceRequest schemeSequenceRequest = schemeSequenceRequestRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ErrorCode.SCHEME_SEQUENCE_REQUEST_NOT_FOUND, "Scheme Sequence not found", null));
        
        Business business = businessRepository.getOne(schemeSequenceRequest.getBusinessId());
        
        Program program = programRepository.getOne(schemeSequenceRequest.getProgramId());
        
        List<SchemeRes> schemeRes = Arrays.stream(schemeSequenceRepository.findByRequestId(id))
                .map(entity -> {
                    SchemeSequence schemeSequence = SchemeSequence.class.cast(entity[0]);
                    Scheme scheme = Scheme.class.cast(entity[1]);
                    
                    return SchemeRes.builder()
                            .id(scheme.getId())
                            .name(scheme.getName())
                            .code(scheme.getCode())
                            .sequenceNo(schemeSequence.getSequenceNo())
                            .schemeType(scheme.getSchemeType())
                            .status(scheme.getStatus())
                            .endDate(scheme.getEndDate())
                            .build();
                })
                .collect(Collectors.toList());
        
        return SchemeSequenceRequestSettingRes.builder()
                .id(schemeSequenceRequest.getId())
                .businessId(business.getId())
                .businessName(business.getName())
                .programName(program.getName())
                .version(schemeSequenceRequest.getVersion())
                .createdAt(schemeSequenceRequest.getCreatedAt())
                .approvalStatus(schemeSequenceRequest.getApprovalStatus())
                .schemes(schemeRes)
                .build();
    }

    @Override
    public Page<SchemeSequenceRes> getApprovedRequests(
            Integer businessId, 
            Integer programId, 
            ESchemeType schemeType, 
            Integer version,
            ECommonStatus status, 
            Integer fromDateInt, Integer toDateInt, 
            Pageable pageable) {
        
        Date fromDate = null;
        Date toDate = null;
        
        if(fromDateInt != null && toDateInt != null) {
            LocalDate fromLocalDate = LocalDate.parse(fromDateInt.toString(), DateTimeFormatter.ofPattern(dateFormatPattern));
            LocalDate toLocalDate = LocalDate.parse(fromDateInt.toString(), DateTimeFormatter.ofPattern(dateFormatPattern));
            
            fromDate = Date.from(fromLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            toDate = Date.from(toLocalDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        }
        
        Page<Object[][]> page = schemeSequenceRequestRepository.filter(
                businessId, 
                programId, 
                schemeType, 
                version, status, 
                EApprovalStatus.APPROVED,
                fromDate, 
                toDate, 
                Direction.DESC, 
                null, 
                pageable);
        
        List<SchemeSequenceRes> content = new ArrayList<SchemeSequenceRes>(page.getNumberOfElements());
        
        for (Object[] each : page.getContent()) {
            SchemeSequenceRequest schemeSequenceRequest = SchemeSequenceRequest.class.cast(each[0]);
            Business business = Business.class.cast(each[1]);
            Program program = Program.class.cast(each[2]);
            
            content.add(SchemeSequenceRes.builder()
                    .id(schemeSequenceRequest.getId())
                    .schemeType(schemeSequenceRequest.getSchemeType())
                    .version(schemeSequenceRequest.getVersion())
                    .businessId(business.getId())
                    .businessName(business.getName())
                    .programId(program.getId())
                    .programName(program.getName())
                    .rejectedReason(schemeSequenceRequest.getRejectedReason())
                    .status(schemeSequenceRequest.getStatus())
                    .createdAt(schemeSequenceRequest.getCreatedAt())
                    .build()); 
        }
        
        return new PageImpl<SchemeSequenceRes>(content, pageable, page.getTotalElements());
    }
    
    private SchemeSequenceRequest createRequest(Integer businessId, Integer programId, ESchemeType schemeType, SchemeSequenceSettingReq req) {
        Business business = businessRepository.findById(businessId)
        .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));
        
        if(business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, "Business not active", null);
        
        Program program = programRepository.findByIdAndBusinessId(programId, business.getId())
        .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));
        
        if(program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, "Program not active", null);
        
        if(schemeSequenceRequestRepository.findPendingVersion(business.getId(), program.getId(), schemeType).isPresent()) {
            throw new BusinessException(ErrorCode.SCHEME_SEQUENCE_REQUEST_ALREADY_REQUESTED, "Scheme Sequence is already requested for approval", null);
        }
        
       Integer nextVersion = schemeSequenceRequestRepository.findEffectedVersion(business.getId(), program.getId(), schemeType)
                .map(entity -> entity.getVersion() + 1)
                .orElse(1);
        
        Map<Integer, SchemeSequenceReq> sequenceMap = req.getSchemeSequences()
                .stream()
                .collect(Collectors.toMap(SchemeSequenceReq::getId, schemeSequenceReq -> schemeSequenceReq, (first, second) -> second));
        
        ArrayList<Integer> givenSchemeIds = new ArrayList<Integer>(sequenceMap.keySet());
        
        List<Scheme> schemes = schemeRepository.findActivedSchemes(program.getId(), ECommonStatus.ACTIVE, schemeType);
        
        if(givenSchemeIds.size() != schemes.size())
            throw new BusinessException(ErrorCode.SCHEME_SEQUENCE_PAYLOAD_IS_INVALID, "Scheme Sequence payload is invalid", null);
        
        schemes
        .stream()
        .filter(entity -> !sequenceMap.containsKey(entity.getId()))
        .findAny()
        .ifPresent(scheme -> { 
            throw new BusinessException(ErrorCode.SCHEME_SEQUENCE_PAYLOAD_IS_INVALID, "Scheme Sequence payload is invalid", null); 
         });
        
        SchemeSequenceRequest schemeSequenceRequest = new SchemeSequenceRequest();
        
        schemeSequenceRequest.setVersion(nextVersion);
        schemeSequenceRequest.setBusinessId(business.getId());
        schemeSequenceRequest.setProgramId(program.getId());
        schemeSequenceRequest.setSchemeType(schemeType);
        schemeSequenceRequest.setStatus(ECommonStatus.PENDING);
        schemeSequenceRequest.setApprovalStatus(EApprovalStatus.PENDING);
        
        schemeSequenceRequestRepository.save(schemeSequenceRequest);
        
        List<SchemeSequence> schemeSequences = schemes.stream()
        .map(scheme -> {
            SchemeSequenceReq sequenceReq = sequenceMap.get(scheme.getId());
            
            SchemeSequence schemeSequence = new SchemeSequence();
            schemeSequence.setRequestId(schemeSequenceRequest.getId());
            schemeSequence.setSchemeId(scheme.getId());
            schemeSequence.setSequenceNo(sequenceReq.getSequenceNo());
            
            return schemeSequence;
        })
        .collect(Collectors.toList());
        
        schemeSequenceRepository.saveAll(schemeSequences);
        
        return schemeSequenceRequest;
    }

}
