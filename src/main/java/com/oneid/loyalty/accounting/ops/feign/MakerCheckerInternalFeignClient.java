package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.config.MakerCheckerFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@FeignClient(name = "maker-checker-internal-system", url = "${maker-checker-internal.url}", configuration = MakerCheckerFeignConfig.class)
public interface MakerCheckerInternalFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/maker")
    APIFeignInternalResponse<MakerCheckerInternalMakerRes> maker(@RequestBody MakerCheckerInternalMakerReq<?> request);

    default MakerCheckerInternalMakerRes makerDefault(
            EMakerCheckerType eMakerCheckerType,
            String requestCode,
            Object payload
    ) {
        if (requestCode == null) {
            requestCode = UUID.randomUUID().toString();
        }
        MakerCheckerInternalMakerReq<Object> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<Object>builder()
                .requestCode(requestCode)
                .requestName(eMakerCheckerType.getName())
                .requestType(eMakerCheckerType.getType())
                .payload(payload)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> makerRes = this.maker(createFeignInternalReq);

        if (ErrorCode.SUCCESS.getValue() != makerRes.getMeta().getCode()) {
            throw new BusinessException(
                    ErrorCode.SERVER_ERROR,
                    "Call maker error: " + makerRes.getMeta().getMessage(),
                    null
            );
        }

        return makerRes.getData();
    }

    @RequestMapping(method = RequestMethod.POST, value = "/preview")
    APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> preview(
            @RequestBody MakerCheckerInternalPreviewReq request,
            @RequestParam(value = "offset", required = false) Integer offset,
            @RequestParam(value = "limit", required = false) Integer limit
    );

    @RequestMapping(method = RequestMethod.GET, value = "/preview-detail/{id}")
    APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetail(@PathVariable("id") Long id);

    default MakerCheckerInternalDataDetailRes previewDetailDefault(Long id, EMakerCheckerType type) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                this.previewDetail(id);

        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode()) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Preview detail call maker checker id not found",
                    null
            );
        }
        if (!type.getType().equals(previewDetailRes.getData().getRequestType())) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Preview detail type is not match",
                    null
            );
        }

        return previewDetailRes.getData();
    }


    @RequestMapping(method = RequestMethod.POST, value = "/checker")
    APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checker(@RequestBody MakerCheckerInternalCheckerReq data);

    default MakerCheckerInternalDataDetailRes previewChecker(@PathVariable("id") Long id, EMakerCheckerType type) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> detailRes = this.previewDetail(id);
        if (ObjectUtils.isEmpty(detailRes.getData()) ||
                !type.getType().equals(detailRes.getData().getRequestType())) {
            throw new BusinessException(
                    ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND,
                    "[Checker] request id not found",
                    LogData.createLogData()
                            .append("id", id)
            );
        }
        MakerCheckerInternalDataDetailRes detailResData = detailRes.getData();

        if (!EApprovalStatus.PENDING.getValue().equals(detailRes.getData().getStatus())) {
            throw new BusinessException(
                    ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", detailResData.getId())
                            .append("approve_status", detailResData.getStatus())
            );
        }

        return detailResData;
    }

    default APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerDefault(ApprovalReq req) {
        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes = this.checker(checkerReq);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(
                    ErrorCode.SERVER_ERROR,
                    "Call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData()
                            .append("reviewId", req.getId())
            );
        }
        return checkerRes;
    }

    default void validateRequestPending(EMakerCheckerType requestType, String requestCode, ErrorCode errorCode) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(requestType.getType())
                .requestCode(requestCode)
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> response = this.preview(previewReq, 0, 10);

        if (ErrorCode.SUCCESS.getValue() != response.getMeta().getCode()) {
            throw new BusinessException(
                    ErrorCode.SERVER_ERROR,
                    "Request check pending error: " + response.getMeta().getMessage(),
                    null
            );
        }

        if (response.getMeta().getTotal() > 0) {
            throw new BusinessException(
                    errorCode,
                    null,
                    null
            );
        }
    }
}