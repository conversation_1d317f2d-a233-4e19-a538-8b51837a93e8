package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Getter
@Setter
@Builder
@JsonDeserialize(builder = TransactionSearchReq.TransactionSearchReqBuilder.class)
public class TransactionSearchReq {
    @NotNull(message = "business_id must not be null")
    @JsonProperty("business_id")
    Integer businessId;

    @JsonProperty("corporation_id")
    Integer corporationId;

    @JsonProperty("chain_id")
    Integer chainId;

    @JsonProperty("store_id")
    Integer storeId;

    @JsonProperty("terminal_id")
    Integer terminalId;

    @NotNull(message = "program_id must not be null")
    @JsonProperty("program_id")
    Integer programId;

    @JsonProperty("cancellation_type")
    EOpsCancellationType cancellationType;

    @JsonProperty("invoice_number")
    String invoiceNumber;

    @JsonProperty("tnx_ref_no")
    String tnxRefNo;

    @JsonProperty("member_id")
    Long memberId;

    @JsonProperty("member_code")
    String memberCode;

    @NotNull(message = "transaction_from must not be null")
    @JsonProperty("transaction_from")
    Integer transactionFrom;

    @NotNull(message = "transaction_to must not be null")
    @JsonProperty("transaction_to")
    Integer transactionTo;

    @JsonProperty("user_id")
    String userId;

    @JsonProperty("status")
    ETransactionStatus status;

    @JsonProperty("account_type")
    EOpsIdType accountType;

    @JsonProperty("account_code")
    String accountCode;

    @JsonProperty("transaction_type")
    EOpsTransactionType transactionType;

    @JsonProperty("attributes")
    Map<String, String> attributes;

    @JsonProperty("pool_id")
    Integer poolId;

    @JsonPOJOBuilder(withPrefix = "")
    public static class TransactionSearchReqBuilder {
    }
}