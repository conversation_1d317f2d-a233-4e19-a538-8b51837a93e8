package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Data
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusDetailAvaiRes extends MemberStatusAvaiRes{

    private String enName;

    private String enDescription;

    private String viDescription;

    private EBoolean defaultStatus;

    private EBoolean revertLatestStatus;

    private EBoolean expiredAllPoints;

    private List<Function> functions;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Function {

        private String functionCode;

        private String functionName;

        private String group;
    }
}
