package com.oneid.loyalty.accounting.ops.component;

import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public abstract class AttributeValueStrategy<T> {

    public static final String DELIMITER = "|";

    protected ConditionAttributeDto conditionAttributeDto;

    private final AttributeMasterDataRepository attributeMasterDataRepository;

    public AttributeValueStrategy(AttributeMasterDataRepository attributeMasterDataRepository) {
        this.attributeMasterDataRepository = attributeMasterDataRepository;
    }

    protected void setConditionAttributeDto(ConditionAttributeDto dto) {
        this.conditionAttributeDto = dto;
    }

    public String getWriteValue(
            String attribute,
            EAttributeOperator operator,
            Object value, final Integer... programIds
    ) {
        if (value == null) {
            return null;
        }

        if (operator.isMultiple()) {
            if (!(value instanceof List)) {
                throw new IllegalArgumentException("invalid format");
            }

            List<?> values = (List<?>) value;

            return getWriteValue(
                    attribute,
                    values
                            .stream()
                            .map(iterator -> this.serialize(iterator, programIds))
                            .collect(Collectors.toList()), programIds);
        } else {
            return getWriteValue(attribute, this.serialize(value, programIds), programIds);
        }
    }

    public void verifyMasterData(String value) {
        if (conditionAttributeDto.isExistMasterData() && EBoolean.YES.equals(conditionAttributeDto.getEnableValidateMasterData())) {
            if (!attributeMasterDataRepository.existsByAttributeCodeAndValue(conditionAttributeDto.getAttribute(), value)) {
                throw new BusinessException(
                        ErrorCode.INVALID_ATTRIBUTE_MASTER_DATA,
                        null,
                        null,
                        new Object[]{this.conditionAttributeDto.getAttribute(), this.conditionAttributeDto.getValueValidationPattern()}
                );
            }
        }
    }

    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds
    ) {
        if (value == null) {
            return null;
        }

        if (operator.isMultiple()) {
            return Arrays.stream(value.split("\\" + DELIMITER, -1))
                    .map(o -> this.deserialize(attribute, o, programIds))
                    .collect(Collectors.toList());
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }

    protected String getWriteValue(String attribute, List<T> values, final Integer... programIds) {
        return values.stream()
                .map(f -> this.getWriteValue(attribute, f, programIds))
                .collect(Collectors.joining(DELIMITER));
    }

    protected String getWriteValue(String attribute, T value, final Integer... programIds) {
        return String.valueOf(value);
    }

    public abstract boolean isApplicable(EAttributeDataDisplayType type);

    public abstract T serialize(Object value, final Integer... programIds);

    public abstract T deserialize(String attribute, String value, final Integer... programIds);
}