package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
public class TierHistoryRes {
    private String tierName;
    private String tierCode;
    private BigDecimal qualificationPoint;
    private String eventTriggerCode;
    private String eventTriggerName;
    private String description;
    private String reasonCode;
    private String reasonName;
    private Long updatedAt;
    private Long tierExpiredDate;
    private List<RuleRes> rules;
}
