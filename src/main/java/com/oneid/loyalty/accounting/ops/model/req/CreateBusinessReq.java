package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.validation.*;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Setter
@Getter
public class CreateBusinessReq {

    @NotBlank(message = "'Code' cannot be blank")
    @Pattern(regexp = "^[a-zA-Z0-9]{1,16}$", message = "'Code' is invalid")
    private String code;

    @NotBlank(message = "'Name' cannot be blank")
    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @ValidMeta(message = "'Name' is invalid")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    @ValidMeta(message = "'Description' is invalid")
    private String description;

    @NotNull(message = "'Status' is required")
    @Pattern(regexp = "^(A|I)$", message = "'Status' is invalid")
    private String status;

    @NotNull(message = "'Start date' cannot be null")
    @JsonProperty("start_date")
    private Long startDate;

    @NotNull(message = "'End date' cannot be null")
    @JsonProperty("end_date")
    private Long endDate;

    @NotNull(message = "'Contact person' is required")
    @PersonName(message = "'Contact person' is invalid")
    @JsonProperty("contact_person")
    private String contactPerson;

    @ValidEmail(message = "'Email' is invalid")
    private String email;

    @NotNull(message = "'Address 1' is required")
    @Address(message = "'Address 1' is invalid")
    @JsonProperty("address_1")
    private String address1;

    @Address(message = "'Address 2' is invalid")
    @JsonProperty("address_2")
    private String address2;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("province_id")
    private Integer provinceId;

    @JsonProperty("district_id")
    private Integer districtId;

    @JsonProperty("ward_id")
    private Integer wardId;

    @JsonProperty("postal_code")
    @Size(max = 16, message = "'Postal Code' cannot exceed 16 characters")
    private String postalCode;

    @NotNull(message = "'Phone' is required")
    @Phone(message = "Phone is invalid")
    private String phone;

    @Website(message = "'Website' is invalid")
    private String website;

    @AssertTrue(message = "End date must be greater than start date")
    public boolean isValidStartAndEndDate() {
        if (this.startDate == null || this.endDate == null) {
            return true;
        }
        return this.endDate.compareTo(this.startDate) > 0;
    }

    @JsonProperty("request_created_at")
    private Long requestCreatedAt;
}
