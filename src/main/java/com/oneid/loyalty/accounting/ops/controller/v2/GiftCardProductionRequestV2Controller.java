package com.oneid.loyalty.accounting.ops.controller.v2;

import com.oneid.loyalty.accounting.ops.service.v2.OpsGiftCardProductionRequestV2Service;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("v2/gift-card-requests")
public class GiftCardProductionRequestV2Controller extends BaseController {
    @Autowired
    private OpsGiftCardProductionRequestV2Service giftCardProductionRequestV2Service;

    @GetMapping("available/{request_id}")
    @Authorize(role = AccessRole.GIFT_CARD_PR, permissions = {AccessPermission.VIEW })
    public ResponseEntity<?> getDetailAvailable(@PathVariable("request_id") Integer requestId) {
        return success(giftCardProductionRequestV2Service.getDetailAvailable(requestId));
    }
}
