package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.model.req.FormulaRecordReq;
import com.oneid.oneloyalty.common.constant.EFormulaUnitType;
import com.oneid.oneloyalty.common.entity.SchemeFormula;
import com.oneid.oneloyalty.common.entity.SchemeFormulaRange;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class FormulaRecordRes extends FormulaRecordReq {
    @JsonProperty("formula_id")
    private Integer formulaId;

    public static FormulaRecordRes toF2(SchemeFormula schemeFormula, SchemeFormulaRange schemeFormulaRange) {
        FormulaRecordRes res = new FormulaRecordRes();
        res.setSchemeId(schemeFormula.getSchemeId());
        res.setFormulaId(schemeFormula.getId());
        res.setFormulaType(schemeFormula.getFormulaType());
        res.setFormulaUnitType(schemeFormula.getUnitType());
        res.setD(schemeFormulaRange.getDValue());
        res.setN(schemeFormulaRange.getNValue());
        return res;
    }

    public static FormulaRecordRes toF4(SchemeFormula schemeFormula, List<SchemeFormulaRange> schemeFormulaRanges) {
        FormulaRecordRes res = new FormulaRecordRes();
        res.setSchemeId(schemeFormula.getSchemeId());
        res.setFormulaId(schemeFormula.getId());
        res.setFormulaType(schemeFormula.getFormulaType());
        res.setFormulaUnitType(schemeFormula.getUnitType());
        for (SchemeFormulaRange schemeFormulaRange : schemeFormulaRanges) {
            res.getFactorList().add(
                new FormulaFactor(schemeFormulaRange.getAmountFrom(), schemeFormulaRange.getAmountTo(),
                        EFormulaUnitType.POINT.equals(schemeFormula.getUnitType()) ?
                                Double.valueOf(schemeFormulaRange.getPointValue()) :
                                schemeFormulaRange.getPercentValue())
            );
        }
        res.getFactorList().sort((o1, o2) -> {
            if (o1.getAmountFrom() == o2.getAmountFrom()) {
                return 0;
            }
            return o1.getAmountFrom() > o2.getAmountFrom() ? 1 : -1;
        });
        return res;
    }
}