package com.oneid.loyalty.accounting.ops.util;

import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;

public class Base64OutputStream extends FilterOutputStream {
    private static final byte[] a;
    private static byte[] b = new byte[]{13, 10};
    private byte[] c;
    private int d;
    private byte[] e;
    private int f;
    private int g;
    private byte[] h;

    public static void setLineBreak(byte[] var0) {
        if (var0 == null) {
            throw new IllegalArgumentException("Line break must not be null!");
        } else {
            b = (byte[])((byte[])var0.clone());
        }
    }

    public static byte[] getLineBreak() {
        return (byte[])((byte[])b.clone());
    }

    public Base64OutputStream(OutputStream var1) {
        this(var1, b);
    }

    public Base64OutputStream(OutputStream var1, byte[] var2) {
        super(var1);
        this.c = new byte[3];
        this.e = new byte[512];
        this.h = var2 != null ? var2 : b;
    }

    public byte[] getInstanceLineBreak() {
        return (byte[])((byte[])this.h.clone());
    }

    public synchronized void write(int var1) throws IOException {
        this.write(new byte[]{(byte)var1}, 0, 1);
    }

    public synchronized void write(byte[] var1, int var2, int var3) throws IOException {
        if ((var2 | var3 | var1.length - (var3 + var2) | var2 + var3) < 0) {
            throw new IndexOutOfBoundsException();
        } else {
            int var4 = var2;
            int var5;
            if (this.d > 0 && this.d + var3 >= 3) {
                var5 = 3 - this.d;
                System.arraycopy(var1, var2, this.c, this.d, var5);
                var4 = var2 + var5;
                this.a(this.c, 0);
                this.d = 0;
            }

            for(var5 = var2 + var3 - 2; var4 < var5; var4 += 3) {
                this.a(var1, var4);
            }

            this.a();
            int var6 = var2 + var3 - var4;
            System.arraycopy(var1, var4, this.c, this.d, var6);
            this.d += var6;
        }
    }

    private void a(byte[] var1, int var2) throws IOException {
        if (this.h != null) {
            int var3 = this.h.length;
            if (this.f + var3 + 3 >= 512) {
                this.a();
            }

            if (this.g >= 64) {
                System.arraycopy(this.h, 0, this.e, this.f, var3);
                this.f += var3;
                this.g = 0;
            }
        }

        this.e[this.f++] = a[var1[var2 + 0] >>> 2 & 63];
        this.e[this.f++] = a[var1[var2 + 0] << 4 & 48 | var1[var2 + 1] >>> 4 & 15];
        this.e[this.f++] = a[var1[var2 + 1] << 2 & 60 | var1[var2 + 2] >>> 6 & 3];
        this.e[this.f++] = a[var1[var2 + 2] & 63];
        this.g += 4;
    }

    private void a() throws IOException {
        this.out.write(this.e, 0, this.f);
        this.f = 0;
    }

    public synchronized void flush() throws IOException {
        this.a(true);
    }

    void a(boolean var1) throws IOException {
        if (this.d > 0) {
            this.out.write(a[this.c[0] >>> 2 & 63]);
            if (this.d > 1) {
                this.out.write(a[this.c[0] << 4 & 48 | this.c[1] >>> 4 & 15]);
                this.out.write(a[this.c[1] << 2 & 60]);
            } else {
                this.out.write(a[this.c[0] << 4 & 48]);
                this.out.write(61);
            }

            this.out.write(61);
        }

        if (var1) {
            this.out.flush();
        }

        this.d = 0;
    }

    static {
        try {
            a = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".getBytes("ASCII");
        } catch (UnsupportedEncodingException var1) {
            throw new RuntimeException("ASCII encoding unsupported");
        }
    }
}
