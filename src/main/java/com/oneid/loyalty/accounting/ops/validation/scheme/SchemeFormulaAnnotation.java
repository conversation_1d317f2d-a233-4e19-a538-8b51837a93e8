package com.oneid.loyalty.accounting.ops.validation.scheme;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {SchemeFormulaValidator.class})
public @interface SchemeFormulaAnnotation {

    String message() default "Formula invalid";

    int maxPercentValue() default 999;

    int minPercentValue() default 0;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}