package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.CardTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardTypeUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.CardType;

public class CardTypeMapper {
    public static CardType toCardTypeOne(CardTypeCreateReq request) {
        CardType cardType = new CardType();
        cardType.setBusinessId(request.getBusinessId());
        cardType.setProgramId(request.getProgramId());
        cardType.setCardPolicyId(request.getCardPolicyId());
        cardType.setCardType(request.getCardType());
        cardType.setDescription(request.getDescription());
        cardType.setStatus(ECommonStatus.of(request.getStatus()));
        return cardType;
    }

    public static CardType toCardTypeOne(CardType cardType, CardTypeUpdateReq request) {
//        cardType.setBusinessId(request.getBusinessId());
//        cardType.setProgramId(request.getProgramId());
//        cardType.setCardPolicyId(request.getCardPolicyId());
//        cardType.setCardType(request.getCardType());
        cardType.setDescription(request.getDescription());
        cardType.setStatus(ECommonStatus.of(request.getStatus()));
        return cardType;
    }
}
