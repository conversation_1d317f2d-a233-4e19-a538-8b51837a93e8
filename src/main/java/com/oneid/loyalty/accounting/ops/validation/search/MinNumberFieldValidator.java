package com.oneid.loyalty.accounting.ops.validation.search;


import lombok.SneakyThrows;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;
import java.lang.reflect.Field;
import java.util.stream.Collectors;

public class MinNumberFieldValidator implements ConstraintValidator<MinFieldRequiredForSearch, Object> {

    private String dateFormat;

    private int count;
    private List<String> ignores;

    @Override
    public void initialize(MinFieldRequiredForSearch constraintAnnotation) {
        ignores = Arrays.asList(constraintAnnotation.ignores());
        count = constraintAnnotation.count();
    }

    @SneakyThrows
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        int countTmp = count;
        Class superClazz = value.getClass().getSuperclass();

        Field[] fieldsSupers = {};
        if(superClazz != null) fieldsSupers = superClazz.getDeclaredFields();
        List<String> ignoresBySuperClass = Arrays.stream(fieldsSupers).map(f->f.getName()).collect(Collectors.toList());

        for (Field field : value.getClass().getDeclaredFields()) {
            if(ignores.contains(field.getName()) || ignoresBySuperClass.contains(field.getName()))
                continue;
            field.setAccessible(true);
            if (field.get(value) != null) countTmp--;
        }
        return countTmp <= 0;
    }
}