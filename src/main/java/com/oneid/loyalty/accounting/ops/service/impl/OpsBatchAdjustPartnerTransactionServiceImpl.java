package com.oneid.loyalty.accounting.ops.service.impl;

import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.RequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.BatchAdjustPartnerTransactionReq;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionItemRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionSettingRes;
import com.oneid.loyalty.accounting.ops.model.res.BatchAdjustPartnerTransactionStatisticRes;
import com.oneid.loyalty.accounting.ops.service.OpsBatchAdjustPartnerTransactionService;
import com.oneid.loyalty.accounting.ops.setting.BatchAdjustPartnerTransactionSetting;
import com.oneid.loyalty.accounting.ops.setting.BatchAdjustTransactionSetting;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;
import com.oneid.oneloyalty.common.constant.ECardTypeTCB;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EMessageTcb;
import com.oneid.oneloyalty.common.constant.EProgressStatus;
import com.oneid.oneloyalty.common.constant.ETransactionTypeTCB;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.BatchAdjustPartnerTransaction;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.entity.TcbFileTransaction;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BatchAdjustPartnerTransactionRepository;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramCorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.TcbFileTransactionRepository;
import com.oneid.oneloyalty.common.util.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.PathResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.io.Writer;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class OpsBatchAdjustPartnerTransactionServiceImpl implements OpsBatchAdjustPartnerTransactionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpsBatchAdjustPartnerTransactionServiceImpl.class);

    private static final String TMP_FILE_EXTENSION = ".tmp";

    @Value("${maker-checker.module.tcb-batch-adj-txn}")
    private String moduleId;

    @Value("${gcp.storagebucket-name}")
    private String gcpStorageBucketName;

    @Value("${app.batch-adjust-transaction.vgc_code}")
    private String vgcBusinessCode;

    @Value("${app.batch-adjust-transaction.tcb_corporation_code}")
    private String tcbCorporationCode;

    @Value("${app.format.tcb-export-date-pattern}")
    private String tcbExportDatePattern;

    @Value("${app.format.date.pattern}")
    private String ymdFormatPattern;

    @Autowired
    private BatchAdjustTransactionSetting batchAdjustTransactionSetting;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @Autowired
    private BatchAdjustPartnerTransactionRepository batchAdjustPartnerTransactionRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private ProgramRepository programRepository;

    @Autowired
    private CorporationRepository corporationRepository;

    @Autowired
    private TcbFileTransactionRepository tcbFileTransactionRepository;

    @Autowired
    private ProgramCorporationRepository programCorporationRepository;

    private TransactionTemplate transactionTemplate;

    @Autowired
    private Storage storage;

    @PostConstruct
    private void postConstruct() {
        transactionTemplate = new TransactionTemplate(platformTransactionManager);
    }

    @Override
    public BatchAdjustPartnerTransactionSettingRes getTcbPartnerSetting() {
        Business business = businessRepository.findByCode(vgcBusinessCode);

        Corporation corporation = corporationRepository.findByBusinessId(business.getId(), tcbCorporationCode);

        List<Integer> programIds = programCorporationRepository.findByCorporationIdAndStatus(corporation.getId(), ECommonStatus.ACTIVE)
        .stream()
        .map(ProgramCorporation::getProgramId)
        .collect(Collectors.toList());

        return BatchAdjustPartnerTransactionSettingRes.builder()
                .businessId(business.getId())
                .programIds(programIds)
                .corporationId(corporation.getId())
                .build();
    }

    @Override
    public ResourceDTO exportBatchFileTemplate(Integer corporationId) {
        Corporation corporation = corporationRepository.findById(corporationId)
                .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, null, null));

        BatchAdjustPartnerTransactionSetting batchAdjustPartnerTransactionSetting = batchAdjustTransactionSetting.getPartners()
        .stream()
        .filter(each -> each.getCorporationCode().equals(corporation.getCode()))
        .findFirst()
        .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_SUPPORTED_YET, null, null));

        return ResourceDTO.builder()
                .filename(batchAdjustPartnerTransactionSetting.getBatchFileName())
                .resource(new ClassPathResource(batchAdjustPartnerTransactionSetting.getBatchFileDir()))
                .build();
    }

    @Override
    public Integer requestCreatingBatchRequest(BatchAdjustPartnerTransactionReq req, MultipartFile multipartFile) {
        Corporation corporation = corporationRepository.findById(req.getCorporationId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, null, null));

        if(corporation.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.CORPORATION_NOT_ACTIVE, null, null);

        BatchAdjustPartnerTransactionSetting batchAdjustPartnerTransactionSetting = batchAdjustTransactionSetting.getPartners()
                .stream()
                .filter(each -> each.getCorporationCode().equals(corporation.getCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_SUPPORTED_YET, null, null));

        if (!FilenameUtils.getExtension(multipartFile.getOriginalFilename()).equals("xlsx"))
           throw new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_BATCH_FILE_EXTENSION_IS_INVALID, null, null);

        try (XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream())) {
           XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(0);
           int totalRows = xssfSheet.getLastRowNum();

            if (totalRows > batchAdjustPartnerTransactionSetting.getMaxRowSize())
               throw new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_ROW_SIZE_IS_INVALID, "Invalid batch file format", null, new Object[] { batchAdjustPartnerTransactionSetting.getMaxRowSize() });

            Row headerRow = xssfSheet.getRow(4);
           int totalCells = headerRow.getPhysicalNumberOfCells();
            Log.info("Total_cell: " + totalCells);

            if (totalCells != 26)
               throw new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_COLUMN_SIZE_IS_INVALID, "Invalid batch file format", null, new Object[] { 26 });

            int startRowNum = 8;
           int actualRowData = totalRows - startRowNum;
            Log.info("Actual_cell: " + totalCells);
           if(actualRowData < 0)
               throw new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_DATA_IS_EMPTY, null, null);

            Row row = null;
           Cell cell = null;
           String cellValue = null;
           DataFormatter formatter = new DataFormatter();
           
           List<Integer> nulableCellNums = Arrays.asList(11, 12, 15, 19, 21, 22, 23, 25);
           
           for(int rownum = 0; rownum <= actualRowData; rownum ++) {
               Log.info("Process row: " + startRowNum + rownum);
               row = xssfSheet.getRow(startRowNum + rownum);
               if (row == null) {
                   Log.info("Row is empty - row num: " + startRowNum + rownum);
                   continue;
               }
               for(int cellNum = 0; cellNum < totalCells; cellNum ++) {
                   cell = row.getCell(cellNum);
                   cellValue = formatter.formatCellValue(cell);

                   if(StringUtils.isBlank(cellValue) && !nulableCellNums.contains(cellNum))
                       throw new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_FIELD_MUST_NOT_BE_BLANK, "Invalid batch file format", null, new Object[] { row.getRowNum() + 1, cellNum + 1 });
               }
           }
       } catch (IOException e) {
           throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
       }

        BatchAdjustPartnerTransaction request = transactionTemplate.execute(status -> {
           return createRequest(req, multipartFile, batchAdjustPartnerTransactionSetting);
       });

        ChangeRequestFeignReq feignReq = ChangeRequestFeignReq.builder()
               .actionType(EMakerCheckerActionType.CREATE)
               .module(moduleId)
               .objectId(request.getId().toString())
               .payload(RequestFeignReq.builder()
                       .requestId(request.getId())
                       .build())
               .build();

       makerCheckerServiceClient.changes(feignReq);

        return request.getId();
    }

    private BatchAdjustPartnerTransaction createRequest(
            BatchAdjustPartnerTransactionReq req,
            MultipartFile multipartFile,
            BatchAdjustPartnerTransactionSetting batchAdjustPartnerTransactionSetting) {
        Business business = businessRepository.findById(req.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        if(business.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.BUSINESS_NOT_ACTIVE, null, null);

        Program program = programRepository.findByIdAndBusinessId(req.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        if(program.getStatus() != ECommonStatus.ACTIVE)
            throw new BusinessException(ErrorCode.PROGRAM_NOT_ACTIVE, null, null);

        ProgramCorporation programCorporation = programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), req.getCorporationId())
        .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_CORPORATION_NOT_FOUND, null, null));

        String filename = multipartFile.getOriginalFilename();
        String resourcePath = String.format("%s/%s/%s", batchAdjustPartnerTransactionSetting.getBucketDir(), UUID.randomUUID(), filename);

        BatchAdjustPartnerTransaction request = new BatchAdjustPartnerTransaction();

        request.setBatchFileName(filename);
        request.setBatchName(req.getBatchFileName());
        request.setBusinessId(business.getId());
        request.setProgramId(program.getId());
        request.setCorporationId(programCorporation.getCorporationId());
        request.setResourcePath(resourcePath);
        request.setVersion(1);
        request.setBatchProcessStatus(EBatchProcessStatusType.PENDING);
        request.setRequestStatus(ECommonStatus.PENDING);
        request.setApprovalStatus(EApprovalStatus.PENDING);

        batchAdjustPartnerTransactionRepository.save(request);

        BlobInfo blobInfo = BlobInfo.newBuilder(gcpStorageBucketName, resourcePath)
                .setContentType(multipartFile.getContentType())
                .build();

        try(InputStream inputstream = multipartFile.getInputStream()) {
            storage.createFrom(blobInfo, multipartFile.getInputStream());
        } catch (IOException e) {
            throw new BusinessException(ErrorCode.SERVER_ERROR, e.getMessage(), null);
        }

        return request;
    }

    @Override
    public URL getDownloadedBatchFileLink(Integer requestId) {
        BatchAdjustPartnerTransaction request = batchAdjustPartnerTransactionRepository.findById(requestId)
        .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND, null, null));

        return storage.signUrl(
                BlobInfo.newBuilder(gcpStorageBucketName, request.getResourcePath()).build(),
                1,
                TimeUnit.MINUTES);
    }

    @Override
    public Page<BatchAdjustPartnerTransactionRes> getAvailableRequests(
            String batchName,
            Integer batchNo,
            Integer corporationId,
            Integer businessId, Integer programId,
            EBatchProcessStatusType
                    batchProcessStatusType,
            EApprovalStatus approvalStatus,
            LocalDate fromLocalDate,
            LocalDate toLocalDate,
            Pageable pageable) {

        Date fromDate = null;
        Date toDate = null;

        if(fromLocalDate != null) {
            fromDate = Date.from(fromLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        }

        if(toLocalDate != null) {
            toDate = Date.from(toLocalDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        }

        ECommonStatus requestStatus = null;

        if(approvalStatus != null) {
            switch (approvalStatus) {
            case APPROVED:
                requestStatus = ECommonStatus.ACTIVE;

                break;

            case REJECTED:
                requestStatus = ECommonStatus.INACTIVE;

                break;

            default:
                requestStatus = ECommonStatus.PENDING;

                break;
            }
        }

        Page<Object[]> page = batchAdjustPartnerTransactionRepository.filter(
                batchName,
                batchNo,
                businessId,
                programId,
                null,
                batchProcessStatusType,
                requestStatus,
                approvalStatus,
                fromDate,
                toDate,
                Direction.DESC,
                null,
                pageable);

        return new PageImpl<BatchAdjustPartnerTransactionRes>(transform(page.getContent(), Collections.emptyMap()), pageable, page.getTotalElements());
    }

    @Override
    public BatchAdjustPartnerTransactionRes getAvailableRequestById(Integer requestId) {
        return getRequestById(requestId);
    }

    @Override
    public Page<BatchAdjustPartnerTransactionRes> getInReviewRequests(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate,
            Pageable pageable) {

        APIResponse<ChangeRequestPageFeignRes> response = makerCheckerServiceClient.getChangeRequests(
                moduleId,
                null,
                null,
                approvalStatus != null ? approvalStatus.getDisplayName().toUpperCase() : EApprovalStatus.PENDING.getDisplayName().toUpperCase(),
                pageable.getPageNumber(),
                pageable.getPageSize(),
                null,
                null);

        ChangeRequestPageFeignRes changeRequestPageFeignRes = response.getData();
        List<BatchAdjustPartnerTransactionRes> batchRes = Collections.emptyList();

        if(changeRequestPageFeignRes.getTotalRecordCount() > 0) {
            Map<Integer, ChangeRecordFeginRes> changeRecordFeginResMap = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .collect(Collectors.toMap(each -> Integer.parseInt(each.getObjectId()), each -> each));

            List<Integer> requestIds = changeRequestPageFeignRes.getRecords()
                    .stream()
                    .map(record -> Integer.parseInt(record.getObjectId()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(requestIds)) {
                Map<Integer, BatchAdjustPartnerTransactionRes> mapRes = transform(batchAdjustPartnerTransactionRepository.findBy(requestIds), changeRecordFeginResMap)
                        .stream()
                        .collect(Collectors.toMap(BatchAdjustPartnerTransactionRes::getRequestId, each -> each));

                batchRes = requestIds.stream()
                .map(id -> mapRes.get(id))
                .collect(Collectors.toList());
            }
        }

        return new PageImpl<BatchAdjustPartnerTransactionRes>(batchRes, pageable, changeRequestPageFeignRes.getTotalRecordCount());
    }

    @Override
    public BatchAdjustPartnerTransactionRes getInReviewRequestById(Integer reviewId) {
        APIResponse<ChangeRecordFeginRes> apiResponse = makerCheckerServiceClient.getChangeRequestById(reviewId.toString());

        return getRequestById(Integer.parseInt(apiResponse.getData().getObjectId()));
    }

    @Override
    public Page<BatchAdjustPartnerTransactionItemRes> getTransactionsById(Integer requestId, EProgressStatus progressStatus, Pageable pageable) {
        BatchAdjustPartnerTransaction request = batchAdjustPartnerTransactionRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND, null, null));

        Page<TcbFileTransaction> page = tcbFileTransactionRepository.filter(request.getId(), progressStatus, Direction.DESC, null, pageable);

        List<BatchAdjustPartnerTransactionItemRes> response = page.getContent()
        .stream()
        .map(entity -> {
            EMessageTcb error = entity.getError();

            return BatchAdjustPartnerTransactionItemRes.builder()
            .id(entity.getId())
            .pointTransactionId(entity.getPointTransactionId())
            .tcbTransactionId(entity.getTcbTransactionId())
            .pointAward(entity.getPointAward())
            .pointExpiryTime(entity.getPointExpiryTime())
            .transactionTime(entity.getTcbTransactionTime())
            .status(entity.getSuccess())
            .resultCode(error != null ? error.getDisplayName() : null)
            .errorMessage(error != null ? error.getValue() : null)
            .industry(entity.getAdjCode() != null ? entity.getAdjIndustry() : entity.getIndustry())
            .oldIndustry(entity.getAdjCode() != null ? entity.getIndustry() : null)
            .build();
        })
        .collect(Collectors.toList());

        return new PageImpl<BatchAdjustPartnerTransactionItemRes>(response, pageable, page.getTotalElements());
    }

    @Override
    public BatchAdjustPartnerTransactionStatisticRes getTransactionStatisticById(Integer requestId) {
        BatchAdjustPartnerTransaction request = batchAdjustPartnerTransactionRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND, null, null));

        BatchAdjustPartnerTransactionStatisticRes response = new BatchAdjustPartnerTransactionStatisticRes();

        tcbFileTransactionRepository.countTransactionBySuccess(request.getId())
        .stream()
        .forEach(entity -> {
            EProgressStatus progressStatus = EProgressStatus.class.cast(entity[1]);
            long total = Long.class.cast(entity[0]);

            switch (progressStatus) {
            case PENDING:
                response.setTotalPending(total);

                break;

                case YES:
                response.setTotalSuccess(total);

                    break;

                case NO:
                response.setTotalFail(total);

                    break;

            default:
                break;
            }
        });

        response.setTotal(response.getTotalPending() + response.getTotalSuccess() + response.getTotalFail());

        return response;
    }

    @Override
    public ResourceDTO exportAllTransactionsByRequestId(Integer requestId) {
       BatchAdjustPartnerTransaction request = batchAdjustPartnerTransactionRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND, null, null));

        Corporation corporation = corporationRepository.findById(request.getCorporationId())
                .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, null, null));

        BatchAdjustPartnerTransactionSetting batchAdjustPartnerTransactionSetting = batchAdjustTransactionSetting.getPartners()
                .stream()
                .filter(each -> each.getCorporationCode().equals(corporation.getCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_SUPPORTED_YET, null, null));

        DateTimeFormatter tcbExportTimeFormatter = DateTimeFormatter.ofPattern(tcbExportDatePattern);

        List<List<?>> csvRowData = new ArrayList<List<?>>();
        csvRowData.add(batchAdjustPartnerTransactionSetting.getCsvExportHeaders());

        List<TcbFileTransaction> tcbFileTransactions = tcbFileTransactionRepository.findByAdjBatchNo(requestId);

        Path tmpCsvPath = null;

        try {
            tmpCsvPath = Files.createTempFile("", TMP_FILE_EXTENSION);
        } catch (IOException ioe) {
            LOGGER.error("Unexpected exception handling during creating tmp files", ioe);
            throw new BusinessException(ErrorCode.SERVER_ERROR, ioe.getMessage(), null);
        }

        try(Writer writer = Files.newBufferedWriter(tmpCsvPath)){
            CSVPrinter printer = CSVFormat.DEFAULT.print(writer);

            printer.printRecord(batchAdjustPartnerTransactionSetting.getCsvExportHeaders());

            ECardTypeTCB bankCardType = null;
            EMessageTcb error = null;
            ETransactionTypeTCB type = null;
            Date pointExpiryTime = null;

            for(TcbFileTransaction entity: tcbFileTransactions) {
                bankCardType = entity.getBankCardType();
                error = entity.getError();
                type = entity.getType();
                pointExpiryTime = entity.getPointExpiryTime();

                printer.printRecord(Arrays.asList(
                        entity.getCustomerId(),
                        entity.getName(),
                        entity.getTcbTransactionId(),
                        entity.getProduct(),
                        entity.getSubProduct(),
                        bankCardType != null ? bankCardType.getValue() : "",
                        entity.getPan(),
                        entity.getAmount(),
                        entity.getAmountOrig(),
                        entity.getCurrencyOrig(),
                        entity.getAppCode(),
                        type != null ? type.getValue() : "",
                        entity.getRevRequestId(),
                        entity.getTimeOrig(),
                        entity.getTime(),
                        entity.getChannel(),
                        entity.getTermOwner(),
                        entity.getTermLocation(),
                        entity.getMcc(),
                        entity.getDescription(),
                        entity.getCustomerGroup(),
                        entity.getIndustry(),
                        entity.getAdjNote(),
                        entity.getAdjCodeNote(),
                        error != null ? error.getDisplayName() : "",
                        error != null ? error.getValue() : "",
                        entity.getPointTransactionId(),
                        entity.getPointAward(),
                        pointExpiryTime != null ? pointExpiryTime.toInstant().atZone(ZoneId.systemDefault()).format(tcbExportTimeFormatter): ""));
            }

            printer.flush();

            return ResourceDTO.builder()
                    .filename(String.format(
                            "COR_TXN_OUTPUT_%s.csv",
                            request.getCreatedAt().toInstant().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern(ymdFormatPattern))))
                    .resource(new PathResource(tmpCsvPath))
                    .build();
        } catch (IOException ioe) {
            LOGGER.error("Unexpected exception handling during generating csv file", ioe);
            throw new BusinessException(ErrorCode.SERVER_ERROR, ioe.getMessage(), null);
        }
    }

    private List<BatchAdjustPartnerTransactionRes> transform(List<Object[]> entities, Map<Integer, ChangeRecordFeginRes> changeRecordFeginResMap){
        Business business = null;
        Program program = null;
        Corporation corporation = null;
        BatchAdjustPartnerTransaction request = null;
        Integer reviewId = null;

        List<BatchAdjustPartnerTransactionRes> response = new ArrayList<BatchAdjustPartnerTransactionRes>();

        for (Object[] each : entities) {
            business = Business.class.cast(each[0]);
            program = Program.class.cast(each[1]);
            corporation = Corporation.class.cast(each[2]);
            request = BatchAdjustPartnerTransaction.class.cast(each[3]);

            reviewId = changeRecordFeginResMap.containsKey(request.getId()) ? changeRecordFeginResMap.get(request.getId()).getChangeRequestId() : null;

            response.add(BatchAdjustPartnerTransactionRes.builder()
                    .requestId(request.getId())
                    .reviewId(reviewId)
                    .version(request.getVersion())
                    .businessName(business.getName())
                    .programName(program.getName())
                    .corporationName(corporation.getName())
                    .batchName(request.getBatchName())
                    .createdAt(request.getCreatedAt())
                    .batchProcessStatus(request.getBatchProcessStatus())
                    .approvalStatus(request.getApprovalStatus())
                    .createdBy(request.getCreatedBy())
                    .build());
        }

        return response;
    }

    private BatchAdjustPartnerTransactionRes getRequestById(Integer requestId) {
        BatchAdjustPartnerTransaction request = batchAdjustPartnerTransactionRepository.findById(requestId)
                .orElseThrow(() -> new BusinessException(ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND, null, null));

        Business business = businessRepository.findById(request.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, null, null));

        Program program = programRepository.findByIdAndBusinessId(request.getProgramId(), business.getId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, null, null));

        Corporation corporation = corporationRepository.findById(request.getCorporationId())
        .orElseThrow(() -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, null, null));

        return BatchAdjustPartnerTransactionRes.builder()
                .requestId(request.getId())
                .version(request.getVersion())
                .businessName(business.getName())
                .programName(program.getName())
                .corporationName(corporation.getName())
                .batchFileName(request.getBatchFileName())
                .batchName(request.getBatchName())
                .createdAt(request.getCreatedAt())
                .rejectedReason(request.getRejectedReason())
                .batchProcessStatus(request.getBatchProcessStatus())
                .approvalStatus(request.getApprovalStatus())
                .createdAt(request.getCreatedAt())
                .createdBy(request.getCreatedBy())
                .approvedAt(request.getApprovedAt())
                .approvedBy(request.getApprovedBy())
                .build();
    }
}
