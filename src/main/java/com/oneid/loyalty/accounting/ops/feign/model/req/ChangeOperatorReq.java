package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonDeserialize(builder = ChangeOperatorReq.ChangeOperatorReqBuilder.class)
public class ChangeOperatorReq {
    @JsonProperty("request_id")
    private Integer requestId;

    @JsonPOJOBuilder(withPrefix = "")
    public static class ChangeOperatorReqBuilder {
    }
}
