package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramLevelReq;
import com.oneid.loyalty.accounting.ops.service.OpsProgramLevelService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ProgramLevelService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpsProgramLevelServiceImpl implements OpsProgramLevelService {

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramLevelService programLevelService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public Page<ProgramLevel> search(Integer businessId, Integer programId, String level, Pageable pageable) {
        SpecificationBuilder<ProgramLevel> specificationBuilder = new SpecificationBuilder<>();
        if (businessId != null) {
            Business business = businessService.find(businessId).orElseThrow(() -> new BusinessException(
                    ErrorCode.BUSINESS_NOT_FOUND, "Business not found",
                    LogData.createLogData().append("business_id", businessId))
            );
            List<Program> programList = programService.findByBusinessId(business.getId());
            List<Integer> programIds = programList.stream().map(Program::getId).collect(Collectors.toList());
            specificationBuilder.add(new SearchCriteria("programId", programIds, SearchOperation.IN));
        }
        if (programId != null) {
            programService.findActive(programId);
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));
        }

        if (level != null) {
            specificationBuilder.add(new SearchCriteria("level", level, SearchOperation.MATCH));
        }
        return programLevelService.find(specificationBuilder, pageable);
    }

    @Override
    @Transactional
    public void approval(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewProgramLevel = makerCheckerInternalFeignClient.previewDetail(req.getId());
        if (previewProgramLevel.getMeta().getCode() != 200) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
        if (!EApprovalStatus.PENDING.getValue().equals(previewProgramLevel.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }
        UpdateProgramLevelReq programLevelReq = jsonMapper.convertValue(previewProgramLevel.getData().getPayload(), UpdateProgramLevelReq.class);
        programService.findActive(programLevelReq.getProgramId());
        ProgramLevel programLevel = null;
        if (req.getStatus() == EApprovalStatus.APPROVED) {
            String createdBy = null;
            String updatedBy;
            if (programLevelReq.getId() != null) {
                programLevel = programLevelService.findById(programLevelReq.getId());
                programLevel.setDescription(programLevelReq.getDescription());
                programLevel.setDescriptionEn(programLevelReq.getDescriptionEn());
                programLevel.setLevel(programLevelReq.getLevel());
                programLevel.setRank(programLevelReq.getRank());
                programLevel.setProgramId(programLevelReq.getProgramId());
                updatedBy = previewProgramLevel.getData().getMadeByUserName();
            } else {
                programLevel = programLevelReq.toProgramLevel();
                programLevel.setRequestCode(previewProgramLevel.getData().getRequestCode());
                updatedBy = createdBy = previewProgramLevel.getData().getMadeByUserName();
            }
            programLevel.setVersion(previewProgramLevel.getData().getVersion());
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(previewProgramLevel.getData().getMadeByUserName());
            opsReqPendingValidator.updateInfoChecker(programLevel, previewProgramLevel.getData().getMadeDate(), createdBy, updatedBy, approvedBy);
            programLevelService.save(programLevel);
        }

        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> plApproved = makerCheckerInternalFeignClient.checker(checkerReq);
        if (plApproved.getMeta().getCode() != 200) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CANNOT_CHANGE_STATUS, "Checker fail",
                    LogData.createLogData()
                            .append("id", req.getId()));
        }
    }
}