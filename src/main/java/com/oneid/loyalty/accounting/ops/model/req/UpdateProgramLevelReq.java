package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Convert;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class UpdateProgramLevelReq extends CreateProgramLevelReq {
    @NotNull(message = "Id must not be blank")
    @JsonProperty("id")
    private Integer id;

    @NotNull(message = "Edit Key must not be blank")
    @JsonProperty("edit_key")
    private String editKey;
}
