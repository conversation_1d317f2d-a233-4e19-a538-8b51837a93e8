package com.oneid.loyalty.accounting.ops.model.req;

import java.util.Set;

import javax.validation.constraints.NotNull;

import com.oneid.oneloyalty.common.constant.EServiceType;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class ProgramAttributeServiceTypeCreateReq {
    
    @NotNull(message = "program_id is missing")
    private Integer programId;
    
    @NotNull(message = "service_type is missing")
    private EServiceType serviceType;
    
    private Set<String> systemAttributeCodes;
    
    private Set<String> programAttributeCodes;
    
    private Set<String> memberAttributeCodes;
    
}
