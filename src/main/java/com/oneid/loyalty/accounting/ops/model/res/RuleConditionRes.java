package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.model.req.RuleConditionReq;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RuleConditionRes {
    private Integer id;

    private String attribute;

    private Object value;

    private EAttributeOperator operator;

    private EAttributeDataDisplayType dataTypeDisplay;

    public static RuleConditionRes valueOf(RuleConditionReq ruleConditionReq) {
        return RuleConditionRes.builder()
                .id(ruleConditionReq.getId())
                .attribute(ruleConditionReq.getAttribute())
                .value(ruleConditionReq.getValue())
                .operator(ruleConditionReq.getOperator())
                .dataTypeDisplay(ruleConditionReq.getDataTypeDisplay())
                .build();
    }
}
