package com.oneid.loyalty.accounting.ops.model.res;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AccumulationDetailDTO implements Serializable {
    private Integer id;
    private String name;
    private String description;
    private String type;
    private String status;
    private Business business;
    private Program program;

    @Getter
    @Setter
    public static class Business {
        private Integer id;
        private String name;

        public Business(Integer id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Getter
    @Setter
    public static class Program {
        private Integer id;
        private String code;

        public Program(Integer id, String code) {
            this.id = id;
            this.code = code;
        }
    }
}
