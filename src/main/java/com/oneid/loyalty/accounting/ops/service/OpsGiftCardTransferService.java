package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.GiftCardTransferAvailableListRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.BasicRecipientInfo;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferInReviewDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferringAvailableRes;
import com.oneid.loyalty.accounting.ops.model.res.GiftCardTransferringInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.SendSmsPasswordFolderRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EProcessStatus;
import com.oneid.oneloyalty.common.constant.ESendVia;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface OpsGiftCardTransferService {
    MakerCheckerInternalMakerRes createNewChangeRequest(CreateGiftCardTransferReq req);

    BasicRecipientInfo findRecipientCode(String code);

    List<BasicRecipientInfo> findRecipientLikeCode(String code);

    List<BasicRecipientInfo> findRecipientLikeCodeAndName(String value);

    void approve(ApprovalReq req);

    ResourceDTO exportFeature(int id);

    Page<GiftCardTransferringInReviewRes> getInReview(EApprovalStatus approvalStatus, String fromCreatedAt, String toCreatedAt, String fromReviewedAt,
                                                      String toReviewedAt, String createdBy, String reviewedBy, Pageable pageable);

    GiftCardTransferInReviewDetailRes giftCardTransferInReviewDetail(Integer inReviewId);

    Page<GiftCardTransferAvailableListRes> searchGiftCardTransferAvailable(Integer businessId,
                                                                           Integer programId,
                                                                           Integer batchNo,
                                                                           Integer transferNo,
                                                                           Date createdStart,
                                                                           Date createdEnd,
                                                                           EProcessStatus processStatus,
                                                                           String recipientCode,
                                                                           ESendVia sendVia,
                                                                           Integer offset,
                                                                           Integer limit);

    GiftCardTransferringAvailableRes giftCardTransferAvailableDetail(Integer id);

    SendSmsPasswordFolderRes sendSmsPasswordFolder(Integer id);
}
