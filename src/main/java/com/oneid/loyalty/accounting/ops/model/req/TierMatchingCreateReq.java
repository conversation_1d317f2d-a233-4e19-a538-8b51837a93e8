package com.oneid.loyalty.accounting.ops.model.req;

import java.time.OffsetDateTime;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.oneid.loyalty.accounting.ops.validation.ASCIICode;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EDurationType;
import com.oneid.oneloyalty.common.constant.ETierMappingExpireType;
import com.oneid.oneloyalty.common.constant.EVerificationType;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class TierMatchingCreateReq {
    
    @NotNull(message = "'business_id' must not be null")
    private Integer businessId;
    
    @NotNull(message = "'base_program_id' must not be null")
    private Integer baseProgramId;
    
    @NotNull(message = "'matched_program_id' must not be null")
    private Integer matchedProgramId;
    
    @NotBlank(message = "'code' must not be blank")
    @Size(max = 50)
    @ASCIICode
    private String code;
    
    @NotBlank(message = "'name' must not be blank")
    @Size(max = 255)
    private String name;
    
    @Size(max = 512)
    private String description;
    
    @NotNull(message = "'start_date' must not be null")
    private OffsetDateTime startDate;
    
    @NotNull(message = "'end_date' must not be null")
    private OffsetDateTime endDate;
    
    @NotNull(message = "'auto_matching' must not be null")
    private Boolean autoMatching;
    
    @NotNull(message = "'auto_verify' must not be null")
    private Boolean autoVerify;
    
    @NotNull(message = "'status' must not be null")
    private ECommonStatus status;
    
    @NotNull(message = "'expire_type' must not be null")
    private ETierMappingExpireType expireType;
    
    private EDurationType durationType;
    
    @Min(value = 0)
    private Integer durationValue;
    
    @NotEmpty(message = "'expire_type' must not be empty")
    private List<EVerificationType> verificationType;
    
    @Valid
    @NotEmpty(message = "'tier_mappings' must not be empty")
    private List<TierMappingReq> tierMappings;
    
    @AssertTrue(message="'base_program_id' and 'matched_program_id' must not be the same")
    public boolean isValidMatchedProgramId() {
        return baseProgramId != matchedProgramId;
    }
    
    @AssertTrue(message="'start_date' must be greater than current time")
    public boolean isValidStartDate() {
        return startDate.isAfter(OffsetDateTime.now());
    }
    
    @AssertTrue(message="'end_date' must be greater than 'start_date' at least 24 hours")
    public boolean isValidEndDate() {
        return endDate.isAfter(startDate.plusHours(24))
                || endDate.isEqual(startDate.plusHours(24));
    }
    
}
