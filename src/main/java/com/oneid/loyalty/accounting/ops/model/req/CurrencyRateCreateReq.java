package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;

@Getter
@Setter
public class CurrencyRateCreateReq {
    @NotNull(message = "'BaseCurrencyId' cannot be null")
    @JsonProperty("base_currency_id")
    private Integer baseCurrencyId;

    @NotNull(message = "'CurrencyId' cannot be null")
    @JsonProperty("currency_id")
    private Integer currencyId;

    @NotNull(message = "'BuyRate' is invalid")
    @JsonProperty("buy_rate")
    @PositiveOrZero
    private Double buyRate;

    @NotNull(message = "'SellRate' is invalid")
    @JsonProperty("sell_rate")
    @PositiveOrZero
    private Double sellRate;

    @NotBlank(message = "'Status' cannot be empty")
    @JsonProperty("status")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;

    @NotNull(message = "'BusinessId' cannot be null")
    @JsonProperty("business_id")
    private Integer businessId;
}
