package com.oneid.loyalty.accounting.ops.model.res;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class ProgramAttributeRes {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("value_validation_pattern")
    private String valueValidationPattern;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private String status;
}
