package com.oneid.loyalty.accounting.ops.support.web.acl;

import java.lang.reflect.Method;

import org.aopalliance.aop.Advice;
import org.springframework.aop.Pointcut;
import org.springframework.aop.support.AbstractPointcutAdvisor;
import org.springframework.aop.support.StaticMethodMatcherPointcut;
import org.springframework.beans.factory.config.BeanPostProcessor;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ACLMethodSecurityAdvisor extends AbstractPointcutAdvisor implements BeanPostProcessor {
    private static final long serialVersionUID = 602314835198715371L;
    
    private ACLMethodSecurityInterceptor aclMethodSecurityAdvisor;

    private final StaticMethodMatcherPointcut pointcut = new StaticMethodMatcherPointcut() {
        @Override
        public boolean matches(Method method, Class<?> clss) {
            return method.isAnnotationPresent(Authorize.class) || method.isAnnotationPresent(AuthorizeGroup.class);
        }
    };

    @Override
    public Pointcut getPointcut() {
        return pointcut;
    }

    @Override
    public Advice getAdvice() {
        return aclMethodSecurityAdvisor;
    }
}
