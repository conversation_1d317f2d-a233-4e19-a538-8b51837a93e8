package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EProgressStatus;

import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
@JsonInclude(value = Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BatchAdjustPartnerTransactionItemRes {
    private Long id;
    private String pointTransactionId;
    private String tcbTransactionId;
    private EProgressStatus status;
    private String resultCode;
    private String errorMessage;
    private BigDecimal pointAward;
    private Date pointExpiryTime;
    private String industry;
    private String oldIndustry;
    private Date transactionTime;
}
