package com.oneid.loyalty.accounting.ops.datasource.cpm.repository;

import com.oneid.loyalty.accounting.ops.datasource.cpm.entity.MasterTransactionHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MasterTransactionHistoryRepository
        extends JpaRepository<MasterTransactionHistory, Long>, JpaSpecificationExecutor<MasterTransactionHistory> {

    MasterTransactionHistory findByTransactionRef(String transactionRef);

    MasterTransactionHistory findByTransactionRefAndInvoiceNo(String transactionRef, String invoiceNo);
}
