package com.oneid.loyalty.accounting.ops.component.constant;

import lombok.Getter;

@Getter
public enum EIsDoB {
    IS_DOB("TRUE", "Is Dob"), NO_DOB("FALSE", "Not is Dob");

    private String value, display;

    EIsDoB(String value, String display) {
        this.value = value;
        this.display = display;
    }

    public static EIsDoB of(String value) {
        if (IS_DOB.value.equals(value)) {
            return IS_DOB;
        } else if (NO_DOB.value.equals(value)) {
            return NO_DOB;
        }
        throw new IllegalArgumentException();
    }
}
