package com.oneid.loyalty.accounting.ops.model.res;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardTransferReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Builder
public class GiftCardTransferInReviewDetailRes implements Serializable {
    private static final long serialVersionUID = -90892714946426245L; // how to gen????

    private Long requestId;

    private Long transferNo;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private Integer batchQuantity;

    private Integer cardQuantity;

    private String createdBy;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;

    private EApprovalStatus approvalStatus;

    private BasicRecipientInfo recipient;
    private String reason;

    private List<CreateGiftCardTransferReq.SelectedBatch> selectedBatchList;
}
