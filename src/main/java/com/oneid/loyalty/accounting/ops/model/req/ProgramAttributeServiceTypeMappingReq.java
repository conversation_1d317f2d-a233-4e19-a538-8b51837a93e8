package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotBlank;

import com.google.auto.value.AutoValue.Builder;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgramAttributeServiceTypeMappingReq {
    
    private Integer id;
    
    @NotBlank
    private String attributeCode;
    
    private boolean selected;
    
}
