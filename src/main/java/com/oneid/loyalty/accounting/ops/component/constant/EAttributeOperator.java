package com.oneid.loyalty.accounting.ops.component.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum EAttributeOperator {
    EQUAL("==", false),
    NOT_EQUAL("!=", false),
    IN("IN", true),
    NOT_IN("NOT_IN", true),
    LESS_THAN("<", false),
    GREATER_THAN(">", false),
    LESS_THAN_OR_EQUAL("<=", false),
    GREATER_THAN_OR_EQUAL(">=", false),
    MODULO("MOD", false),
    RANGE_MONTH("RANGE_MONTH", false),
    OUT_OF_RANGE_MONTH("OUT_OF_RANGE_MONTH", false);

    @JsonValue
    String expression;

    boolean multiple;

    public static EAttributeOperator lookup(String expression) {
        if (StringUtils.isEmpty(expression)) {
            return null;
        }

        return Stream.of(values())
                .filter(each -> each.getExpression().equals(expression))
                .findFirst()
                .orElse(null);
    }
}
