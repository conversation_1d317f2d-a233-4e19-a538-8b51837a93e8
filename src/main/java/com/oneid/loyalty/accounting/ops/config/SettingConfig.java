package com.oneid.loyalty.accounting.ops.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.oneid.loyalty.accounting.ops.setting.BatchAdjustTransactionSetting;
import com.oneid.loyalty.accounting.ops.setting.ElasticsearchSetting;
import com.oneid.loyalty.accounting.ops.setting.SFTPSetting;

@Configuration
public class SettingConfig {
    @Bean
    @ConfigurationProperties(prefix = "app.batch-adjust-transaction")
    BatchAdjustTransactionSetting batchAdjustTransactionSetting() {
        return new BatchAdjustTransactionSetting();
    }
    
    @Bean("cprsSftpSetting")
    @ConfigurationProperties(prefix = "app.sftp.cpr")
    SFTPSetting sftpSetting() {
        return new SFTPSetting();
    }
    
    @Bean
    @ConfigurationProperties(prefix = "app.elasticsearch")
    ElasticsearchSetting elasticsearchSetting() {
        return new ElasticsearchSetting();
    }
}
