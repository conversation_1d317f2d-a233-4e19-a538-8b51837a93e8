package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransactionSapSaleOrderCreateReq {
    @NotNull(message = "'batch_request_id' cannot be null")
    private Long batchRequestId;

    @NotBlank(message = "'sap_customer' cannot be blank")
    @Pattern(regexp="[0-9]+", message="Invalid 'sap_customer'")
    private String sapCustomer;

    @NotBlank(message = "'remark' cannot be blank")
    private String remark;

    @NotNull(message = "'posting_date' cannot be blank")
    @JsonDeserialize(using = DateDeserializer.class)
    private Date postingDate;
}
