package com.oneid.loyalty.accounting.ops.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

import javax.validation.constraints.NotNull;

@Getter
@Builder
@JsonDeserialize(builder = ActionOnCounterDto.ActionOnCounterDtoBuilder.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ActionOnCounterDto {
    @NotNull(message = "id counter must not be null")
    private Integer id;
    private Boolean archive;

    @JsonPOJOBuilder(withPrefix = "")
    public static class ActionOnCounterDtoBuilder {}
}
