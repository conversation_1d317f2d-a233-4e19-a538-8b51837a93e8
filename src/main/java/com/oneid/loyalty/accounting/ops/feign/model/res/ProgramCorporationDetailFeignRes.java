package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ProgramCorporationDetailFeignRes<T> {
    private Long id;

    private String requestName;

    private String requestCode;

    private Integer version;

    private String madeDate;

    private String checkedDate;

    private String status;

    private String madeByUserId;

    private String madeByUserName;

    private String madeByUserEmail;

    private String checkedByUserId;

    private String checkedByUserName;

    private String checkedByUserEmail;

    private T payload;
}
