package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AccountBalanceRes {
    
    private String programCode;
    
    private Integer poolId;
    
    private String poolCode;
    
    private String poolName;
    
    private String poolDescription;
    
    private BigDecimal balance;
    
    private BigDecimal redeemableBalance;
    
    private BigDecimal blockingBalance;
    
    private Long accountId;
    
    private Integer currencyId;
    
    private String currencyCode;
    
    private String currencyName;
    
    private Long expiredTime;
    
}
