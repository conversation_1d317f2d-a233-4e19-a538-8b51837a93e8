package com.oneid.loyalty.accounting.ops.validation;

import com.oneid.oneloyalty.common.validation.ValidMetaValidator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class OpsNameValidator implements ConstraintValidator<OpsName, String> {

    private String message;

    private static final String EN_CHARS = "a-zA-Z" + "\\s";
    private static final String NUMBERS = "0-9" + "\\s";
    private static final String VN_CHARS = "'" + ValidMetaValidator.VN_CHARS;

    private static final Pattern CHAR_PATTERN = Pattern.compile("^[" + EN_CHARS + NUMBERS + VN_CHARS + "]+$");
    private static final Pattern CHAR_REPEATED_PATTERN = Pattern.compile("^.*(\\S)\\1{2,}.*$");

    @Override
    public void initialize(OpsName constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true;
        }

        if (!CHAR_PATTERN.matcher(value).matches()) {
            return false;
        }

        return !CHAR_REPEATED_PATTERN.matcher(value).matches();
    }
}
