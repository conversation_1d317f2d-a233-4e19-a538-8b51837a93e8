package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.oneloyalty.common.constant.EIdType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class CpmTransactionReq {

    @JsonProperty("business_id")
    @NotNull(message = "Business must not be null")
    private Integer businessId;

    @JsonProperty("program_id")
    @NotNull(message = "Program must not be null")
    private Integer programId;

    @JsonProperty("corporation_id")
    private Integer corporationId;

    @JsonProperty("chain_id")
    private Integer chainId;

    @JsonProperty("store_id")
    private Integer storeId;

    @JsonProperty("terminal_id")
    private Integer terminalId;

    @JsonProperty("service_code")
    private String serviceCode;

    @JsonProperty("cpm_transaction_ref")
    private String cpmTransactionRef;

    @JsonProperty("invoice_no")
    private String invoiceNo;

    @JsonProperty("loc_transaction_ref")
    private String locTransactionRef;

    @JsonProperty("id_type")
    private EOpsIdType idType;

    @JsonProperty("id_no")
    private String idNo;

    @JsonProperty("transaction_time_start")
    private Long transactionTimeStart;

    @JsonProperty("transaction_time_end")
    private Long transactionTimeEnd;
}
