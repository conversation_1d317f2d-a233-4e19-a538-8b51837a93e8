package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.component.service.AttributeValueService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("v1/attribute-value")
public class AttributeValueController extends BaseController {

    @Autowired
    AttributeValueService metaDataService;

    @GetMapping("corporation/program/{program_id}")
    public ResponseEntity<?> getCorporations(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getCorporationsAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("member-status/{programId}")
    public ResponseEntity<?> getMemberStatus(@PathVariable Integer programId) {
        return success(metaDataService.getListMemberStatusAttributeValue(programId));
    }

    @GetMapping("transaction-type/{programId}")
    public ResponseEntity<?> getTransactionTypes(@PathVariable Integer programId) {
        return success(metaDataService.getListTxnAttributeValue(programId));
    }

    @GetMapping("chain/program/{program_id}")
    public ResponseEntity<?> getChains(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getChainsAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }


    @GetMapping("store/program/{program_id}")
    public ResponseEntity<?> getStores(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getStoresAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }


    @GetMapping("terminal/program/{program_id}")
    public ResponseEntity<?> getTerminals(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getTerminalsAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("tier/program/{program_id}")
    public ResponseEntity<?> getTiers(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getTierAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("card-type/program/{programId}")
    public ResponseEntity<?> getListCardType(@PathVariable(value = "programId") Integer programId) {
        return success(metaDataService.getListCardType(programId));
    }

    @GetMapping("function-code/program/{programId}")
    public ResponseEntity<?> getFunctionCode(@PathVariable(value = "programId") Integer programId) {
        return success(metaDataService.getFunctionCodes(programId));
    }

    @GetMapping("transaction-code/program/{program_id}")
    public ResponseEntity<?> getTransactionCodes(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "20") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getTransactionCodesAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("pool-code/program/{program_id}")
    public ResponseEntity<?> getPoolCodes(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "name_or_code", required = false) String nameOrCode,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (nameOrCode == null) {
            nameOrCode = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getPoolCodeAttributeValue(programId, nameOrCode, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("voucher-code/program/{program_id}")
    public ResponseEntity<?> getVoucherCodes(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "keyword", required = false) String keyword,
            @PathVariable(value = "program_id") Integer programId
    ) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getVoucherCodeAttributeValue(programId, keyword, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("service-code/{program_id}")
    public ResponseEntity<?> getServiceCodes(
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "limit", defaultValue = "1000") Integer limit,
            @RequestParam(value = "code", required = false) String code,
            @PathVariable(value = "program_id") Integer programId
    ) {
        if (code == null) {
            code = "";
        }
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        Page<AttributeCombobox> page = metaDataService.getListServiceCodeAttributeValue(programId, code, pageRequest);
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }
}