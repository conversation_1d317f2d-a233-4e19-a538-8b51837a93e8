package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.DestinationMemberStatus;
import com.oneid.oneloyalty.common.entity.Scheme;

import java.util.List;

public interface OpsRuleService {
    void verifyListRule(final Scheme scheme, VerifyCreateListSchemeRuleReq req);

    List<RuleRecordRes> findAllBySchemeId(Integer schemeId);

    List<RuleRes> getRule(String serviceCode, Integer programId, EServiceType serviceType);

    List<ResetRuleReq> createRule(Counter counter, List<RuleReq> ruleReqs, MakerCheckerInternalDataDetailRes data, String approveBy);

    List<ResetRuleReq> editRule(Counter counter, List<RuleReq> ruleReqs, MakerCheckerInternalDataDetailRes data, String approveBy);

    List<RuleRes> getRuleInReview(Integer programId, List<RuleReq> ruleReqs, String serviceCode);

    void validateRules(Integer programId, EServiceType serviceType, List<RuleReq> ruleReqs);

    List<ResetRuleReq> createRule(DestinationMemberStatus destinationMemberStatus, List<RuleReq> ruleReqs, String approvedBy, Integer programId, String madeDate);

    List<ResetRuleReq> editRule(DestinationMemberStatus destinationMemberStatus, List<RuleReq> ruleReqs, String approvedBy, Integer programId, String madeDate);
}