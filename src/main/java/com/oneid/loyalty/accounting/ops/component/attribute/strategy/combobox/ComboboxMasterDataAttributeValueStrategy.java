package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.entity.AttributeMasterData;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import joptsimple.internal.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ComboboxMasterDataAttributeValueStrategy extends com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy {

    @Autowired
    private AttributeMasterDataRepository attributeMasterDataRepository;

    public ComboboxMasterDataAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return attributeMasterDataRepository.existsByAttributeCodeAndAttributeType(type.getAttribute(), EAttributeType.MEMBER)
                || attributeMasterDataRepository.existsByAttributeCodeAndAttributeType(type.getAttribute(), EAttributeType.PROGRAM_TRANSACTION);
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {
        AttributeMasterData data = attributeMasterDataRepository.findByAttributeCodeAndValue(attribute, value);

        return AttributeCombobox.builder()
                .name(Strings.EMPTY)
                .value(value)
                .description(Objects.nonNull(data) ? data.getDescription() : null)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {
        if (operator.isMultiple()) {
            Set<String> combobox = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());
            return combobox.stream()
                    .map(c -> {
                                AttributeMasterData data = attributeMasterDataRepository.findByAttributeCodeAndValue(attribute, c);
                                return AttributeCombobox.builder()
                                        .name(Strings.EMPTY)
                                        .value(c)
                                        .description(Objects.nonNull(data) ? data.getDescription() : null)
                                        .checksumKeys(Arrays.asList(programIds))
                                        .build();
                            }
                    )
                    .collect(Collectors.toList());
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}