package com.oneid.loyalty.accounting.ops.component.attribute.strategy.combobox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.attribute.strategy.ComboboxAttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class StoreCodeAttributeValueStrategy extends ComboboxAttributeValueStrategy {

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ComboboxCodeConfig comboboxCodeConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(StoreCodeAttributeValueStrategy.class);

    public StoreCodeAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(objectMapper, attributeMasterDataRepository);
    }

    @Override
    public boolean isApplicable(ConditionAttributeDto type) {
        return comboboxCodeConfig.getStoreCode().equals(type.getAttribute());
    }

    @Override
    public AttributeCombobox deserialize(String attribute, String value, final Integer... programIds) {

        Store store = storeRepository.findByProgramIdAndStoreCode(programIds[0], value);

        if (store == null) {
            LOGGER.warn("Store {} not found", value);
            throw new IllegalArgumentException();
        }

        return AttributeCombobox.builder()
                .name(store.getName())
                .value(value)
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    public Object getReadValue(
            String attribute,
            EAttributeOperator operator,
            String value,
            final Integer... programIds) {

        if (operator.isMultiple()) {
            Set<String> storeCodes = Arrays.stream(String.valueOf(value).split("\\" + DELIMITER, -1))
                    .collect(Collectors.toSet());

            return storeRepository.findByProgramIdAndStoreCodes(programIds[0], storeCodes)
                    .stream()
                    .map(s -> AttributeCombobox.builder()
                            .name(s.getName())
                            .value(s.getCode())
                            .checksumKeys(Arrays.asList(programIds))
                            .build()
                    );
        } else {
            return this.deserialize(attribute, value, programIds);
        }
    }
}