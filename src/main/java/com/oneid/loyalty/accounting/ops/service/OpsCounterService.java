package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.EditCounterReq;
import com.oneid.loyalty.accounting.ops.model.res.CounterRes;
import com.oneid.loyalty.accounting.ops.model.res.CounterStatisticRes;
import com.oneid.loyalty.accounting.ops.model.res.VersionRes;
import com.oneid.loyalty.accounting.ops.model.res.LinkedServiceRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.entity.Counter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface OpsCounterService {
    Integer requestCreatingCounterRequest(CreateCounterReq req);

    MakerCheckerInternalMakerRes requestEditingCounterRequest(Integer requestId, EditCounterReq req);

    Page<CounterRes> getAvailableCounterRequests(
            Integer businessId,
            Integer programId,
            String code,
            ECommonStatus status,
            Pageable pageable);

    CounterRes getAvailableCounterRequestById(Integer requestId);

    CounterRes getInReviewCounterRequestById(Integer reviewId);

    CounterRes getEditCounterRequestSetting(Integer requestId);

    Page<CounterRes> getInReviewCounterRequests(EApprovalStatus approvalStatus,
                                                String fromCreatedAt, String toCreatedAt,
                                                String fromReviewedAt, String toReviewedAt,
                                                String createdBy, String checkedBy,
                                                Integer offset, Integer limit);

    Page<VersionRes> getVersionList(
            Integer CounterId,
            List<EApprovalStatus>  approvalStatus,
            String fromCreatedAt,
            String toCreatedAt,
            String fromReviewedAt,
            String toReviewedAt,
            String createdBy,
            String checkedBy,
            Integer offset,
            Integer limit
    );

    Page<LinkedServiceRes> getLinkedServiceTypes(Integer requestId, Pageable pageable);

    MakerCheckerInternalMakerRes createMaker(CreateCounterReq programReq);

    CounterStatisticRes counterStatistic(Integer counterId, Date startedAt, Date endedAt, ECounterLevel counterLevel, String code, ECommonStatus status, Pageable pageable);

    void approve(ApprovalReq req);

    Integer mapPeriodToNumber(ECounterPeriod period, Date startDate, Date endDate);

    int getCounterActiveStatus(Counter counter);
}
