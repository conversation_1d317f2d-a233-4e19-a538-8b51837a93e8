package com.oneid.loyalty.accounting.ops.support.data.databind;

import java.io.IOException;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.KeyDeserializer;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;

public class AccessRoleDeserializer extends KeyDeserializer {
    @Override
    public Object deserializeKey(String key, DeserializationContext ctxt) throws IOException {
        return AccessRole.lookup(key);
    }
}
