package com.oneid.loyalty.accounting.ops.feign.model.req;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Setter
@Getter
public class SendSmsInternalReq {
    private Meta meta;
    private List<Data> data;

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Meta {
        private String clientVersionCode;
        private String requestId;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Data {
        private String type;
        private String templateId;
        private List<Recipients> recipients;
        private Object data;


        @Builder
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class Recipients {
            private String type;
            private String identity;
        }

    }

}
