package com.oneid.loyalty.accounting.ops.model.req;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = SchemeSequenceReq.SchemeSequenceReqBuilder.class)
public class SchemeSequenceReq {
    @NotNull(message = "'id' must not be null")
    private Integer id;
    
    @NotNull(message = "'sequenceNo' must not be null")
    private Integer sequenceNo;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class SchemeSequenceReqBuilder {
    }
}
