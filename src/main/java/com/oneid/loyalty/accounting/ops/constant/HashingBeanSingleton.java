package com.oneid.loyalty.accounting.ops.constant;

import com.oneid.oneloyalty.common.support.Hashing;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HashingBean<PERSON>ingleton implements InitializingBean {
    @Autowired
    private Hashing hashing;

    private HashingBeanSingleton(){};

    private static HashingBeanSingleton instance;

    @Override
    public void afterPropertiesSet() throws Exception {
        instance = this;
    }

    public static Hashing getHashing() {
        return instance.hashing;
    }
}