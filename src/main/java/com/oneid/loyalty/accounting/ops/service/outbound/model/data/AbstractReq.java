package com.oneid.loyalty.accounting.ops.service.outbound.model.data;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 */
public abstract class AbstractReq {

    public static final String KEY_X_REQUEST_ID = "x-request-id";

    public abstract HttpEntity build(HttpHeaders headers);

    public HttpEntity toRequest(String bearerToken, String requestId) {
        return build(buildHeader(bearerToken, requestId));
    }

    public HttpEntity toRequestNoAuthentication(String requestId) {
        return build(buildHeaderNoAuthentication(requestId));
    }

    public HttpEntity toRequestBasicAuthentication(String requestId, String username, String password) {
        return build(buildHeaderBasicAuthentication(requestId, username, password));
    }

    public HttpHeaders buildHeaderNoAuthentication(String requestId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(KEY_X_REQUEST_ID, requestId);
        return headers;
    }

    public HttpHeaders buildHeader(String bearerToken, String requestId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(bearerToken);
        headers.set(KEY_X_REQUEST_ID, requestId);
        return headers;
    }

    public HttpHeaders buildHeaderBasicAuthentication(String requestId, String username, String password) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBasicAuth(username, password);
        headers.set(KEY_X_REQUEST_ID, requestId);
        return headers;
    }
}
