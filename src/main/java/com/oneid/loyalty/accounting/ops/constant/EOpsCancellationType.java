package com.oneid.loyalty.accounting.ops.constant;

import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum EOpsCancellationType {
    RVS("03", "RVS"),
    RFD("01", "RFD"),
    VOID("02", "VOID");

    private String value;
    private String displayName;

    public static EOpsCancellationType of(String value) {
        if (StringUtils.isEmpty(value)) return null;
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, EOpsCancellationType> {
        @Override
        public EOpsCancellationType convert(String source) {
            for (EOpsCancellationType e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Cancellation type mismatch ", source);
        }
    }
}
