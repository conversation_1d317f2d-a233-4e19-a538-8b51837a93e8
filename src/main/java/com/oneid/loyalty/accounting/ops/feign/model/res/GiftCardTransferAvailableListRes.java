package com.oneid.loyalty.accounting.ops.feign.model.res;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EProcessStatus;
import com.oneid.oneloyalty.common.constant.ESendVia;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GiftCardTransferAvailableListRes {
    private Integer id;

    private Long transferNo;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private Integer batchQuantity;

    private Integer cardQuantity;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private EProcessStatus processStatus;

    private EBoolean archiveFolder;

    private ShortEntityRes recipient;

    private ESendVia sendVia;

    public GiftCardTransferAvailableListRes(Object[] sgc) {
        Long id = ((BigDecimal) sgc[0]).longValue();
        Long businessID = ((BigDecimal) sgc[2]).longValue();
        Long programID = ((BigDecimal) sgc[3]).longValue();
        Long cardQuantity = ((BigDecimal) sgc[4]).longValue();
        Long batchQuantity = ((BigDecimal) sgc[5]).longValue();
        String processStatus = (String) sgc[8];

        this.id = id.intValue();
        this.transferNo = ((BigDecimal) sgc[1]).longValue();
        this.business = new ShortEntityRes(businessID.intValue(), (String) sgc[14], (String) sgc[15]);
        this.program = new ShortEntityRes(programID.intValue(), (String) sgc[16], (String) sgc[17]);
        String archiveFolder = (String) sgc[9];
        this.cardQuantity = cardQuantity.intValue();
        this.batchQuantity = batchQuantity.intValue();
        this.createdAt = (Date) sgc[6];
        this.updatedAt = (Date) sgc[7];
        this.processStatus = EProcessStatus.of(processStatus);
        this.archiveFolder = EBoolean.of(archiveFolder);
        this.setRecipient(new ShortEntityRes(((BigDecimal) sgc[10]).intValue(), (String) sgc[12], (String) sgc[11]));
        this.sendVia = ESendVia.of((String) sgc[13]);
    }
}
