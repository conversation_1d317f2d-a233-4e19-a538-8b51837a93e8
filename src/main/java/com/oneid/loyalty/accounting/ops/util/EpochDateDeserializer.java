package com.oneid.loyalty.accounting.ops.util;

import java.io.IOException;
import java.util.Date;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;

public class EpochDateDeserializer extends StdDeserializer<Date> {
    private static final long serialVersionUID = -4029188981071640936L;

    protected EpochDateDeserializer() {
        super(Date.class);
    }

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        String value = p.getText();
        
        if(value != null) {
            return new Date(Long.parseLong(value));  
        }
        
        return null;
    }

}
