package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonDeserialize(builder = SearchOperatorReq.SearchOperatorReqBuilder.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SearchOperatorReq {

    private Integer businessId;

    private Integer corporationId;

    private Integer chainId;

    private Integer storeId;

    private Integer terminalId;

    private String operatorId;

    private EApprovalStatus approvalStatus;

    private ECommonStatus status;

    @JsonPOJOBuilder(withPrefix = "")
    public static class SearchOperatorReqBuilder {
    }

}
