package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.validation.ValidMeta;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
public class TerminalUpdateReq {
    //    @NotNull(message = "'BusinessId' cannot be null")
//    @JsonProperty("business_id")
//    private Integer businessId;

//    @NotNull(message = "'CorporationId' cannot be null")
//    @JsonProperty("corporation_id")
//    private Integer corporationId;

//    @NotNull(message = "'ChainId' cannot be null")
//    @JsonProperty("chain_id")
//    private Integer chainId;

//    @NotNull(message = "'StoreId' cannot be null")
//    @JsonProperty("store_id")
//    private Integer storeId;

//    @Size(max = 15, message = "'Code' cannot exceed 15 characters")
//    @NotBlank(message = "'Code' cannot be empty")
//    @Pattern(regexp = "^[a-zA-Z0-9]{0,15}$", message = "'Code' is invalid")
//    private String code;

    @NotBlank(message = "'Name' cannot be blank")
    @Size(max = 255, message = "'Name' cannot exceed 255 characters")
    @ValidMeta(message = "'Name' is invalid")
    private String name;

    @Size(max = 255, message = "'Description' cannot exceed 255 characters")
    @ValidMeta(message = "'Description' is invalid")
    private String description;

//    @NotNull(message = "'Service start date' cannot be null")
//    @JsonProperty("service_start_date")
//    private Long serviceStartDate;

    @NotNull(message = "'Service end date' cannot be null")
    @JsonProperty("service_end_date")
    private Long serviceEndDate;

    @NotBlank(message = "'Status' cannot be empty")
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    private String status;
}
