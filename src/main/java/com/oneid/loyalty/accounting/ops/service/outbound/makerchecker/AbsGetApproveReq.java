package com.oneid.loyalty.accounting.ops.service.outbound.makerchecker;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.loyalty.accounting.ops.service.outbound.model.data.AbstractReq;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public abstract class AbsGetApproveReq extends AbstractReq {
    @JsonIgnore
    private String bearerToken;

    @JsonProperty("module")
    private String module;

    @JsonProperty("action_type")
    private String actionType;

    // Object maker-checker action, Example: Scheme id, business id.
    @JsonProperty("object_id")
    private String objectId;

    @JsonProperty("params")
    private Map<String, String> requestParamsCallback = new HashMap<>();
}