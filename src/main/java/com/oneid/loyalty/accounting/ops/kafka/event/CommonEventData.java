package com.oneid.loyalty.accounting.ops.kafka.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Builder
public class CommonEventData<T> implements Serializable {

    private static final long serialVersionUID = -5567702374625573L;

    @JsonProperty("event_id")
    private String id;

    @JsonProperty("event_code")
    private String event;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("service_code")
    private String serviceCode;

    @JsonProperty("timestamp")
    private Long timeStamp;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("member_code")
    private String memberCode;

    @JsonProperty("user_profile_code")
    private String userProfileCode;

    @JsonProperty("master_user_id")
    private String masterUserId;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("payload")
    private T payload;
}
