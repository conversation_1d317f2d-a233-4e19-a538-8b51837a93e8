package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EConditionType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class VerifyCreateListSchemeRuleReq {
    @NotNull(message = "Rule logic must not be null")
    @JsonProperty("rule_logic")
    private EConditionType ruleLogic;

    @Valid
    @JsonProperty("rule_list")
    private List<RuleRecordReq> ruleList;
}