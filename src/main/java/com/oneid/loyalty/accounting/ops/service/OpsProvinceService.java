package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.ProvinceDTO;
import com.oneid.oneloyalty.common.entity.Province;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.List;

public interface OpsProvinceService {

    /**
     * Get one province by id.
     *
     * @throws BusinessException if entity is not found
     */
    ProvinceDTO getOne(Integer id);

    /**
     * Get provinces by country ID.
     */
    List<Province> getProvinces(Integer countryId);

    /**
     * Get provinces by country code.
     */
    List<Province> getProvinces(String countryCode);
}
