package com.oneid.loyalty.accounting.ops.model.res;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProgramFunctionAvailableDetailRes extends OtherInfo {
    private static final long serialVersionUID = 2858939724386524543L;

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("program_id")
    private Integer programId;

    @JsonProperty("program_code")
    private String programCode;

    @JsonProperty("program_name")
    private String programName;

    @JsonProperty("program_status")
    private ECommonStatus programStatus;

    @JsonProperty("functions")
    private Map<String, List<ProgramFunctionInfo>> function;

    @JsonProperty("approval_status")
    private String approvalStatus;

}
