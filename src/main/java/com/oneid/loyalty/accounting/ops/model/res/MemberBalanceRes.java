package com.oneid.loyalty.accounting.ops.model.res;

import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class MemberBalanceRes {
    
    private Object memberProfile;
    
    private Object memberProductAccounts;
    
    private Object tierDetail;
    
    private Object memberAttributes;
    
    private List<AccountBalanceRes> accountPoolBalances;
    
    private BigDecimal debtBalance;
}
