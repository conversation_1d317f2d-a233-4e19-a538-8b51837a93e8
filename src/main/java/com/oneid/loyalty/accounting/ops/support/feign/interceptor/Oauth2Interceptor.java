package com.oneid.loyalty.accounting.ops.support.feign.interceptor;

import org.springframework.security.core.context.SecurityContextHolder;

import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.authentication.OPSAuthenticationToken;

import feign.RequestInterceptor;
import feign.RequestTemplate;

public class Oauth2Interceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        OPSAuthenticationToken authentication = (OPSAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        
        template.header("Authorization", "Bearer " + authentication.getToken().getTokenValue());
    }

}
