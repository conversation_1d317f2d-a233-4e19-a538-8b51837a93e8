package com.oneid.loyalty.accounting.ops.datasource.cpm.repository;

import com.oneid.loyalty.accounting.ops.datasource.cpm.entity.CPMTransactionHistoryAttribute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CPMTransactionHistoryAttributeRepository extends JpaRepository<CPMTransactionHistoryAttribute, Long> {
    List<CPMTransactionHistoryAttribute> findByTransactionRef(String transactionRef);
}
