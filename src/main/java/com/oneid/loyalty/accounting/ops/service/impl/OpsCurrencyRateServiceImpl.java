package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.CurrencyRateMapper;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CurrencyRateRes;
import com.oneid.loyalty.accounting.ops.service.OpsCurrencyRateService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.CurrencyRate;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CurrencyRateService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsCurrencyRateServiceImpl implements OpsCurrencyRateService {
    @Autowired
    CurrencyRateService currencyRateService;

    @Autowired
    BusinessService businessService;

    @Autowired
    CurrencyService currencyService;

    @Autowired
    AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @Override
    public Page<CurrencyRateRes> filterCurrencyRate(Integer baseCurrencyId, Integer currencyId, String status, Integer businessId, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<CurrencyRate> specificationBuilder = new SpecificationBuilder<>();

        if (baseCurrencyId != null)
            specificationBuilder.add(new SearchCriteria("baseCurrencyId", baseCurrencyId, SearchOperation.EQUAL));

        if (currencyId != null)
            specificationBuilder.add(new SearchCriteria("currencyId", currencyId, SearchOperation.EQUAL));

        if (status != null)
            specificationBuilder.add(new SearchCriteria("status", ECommonStatus.of(status), SearchOperation.EQUAL));

        if (businessId != null)
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        Page<CurrencyRate> currencyRates = currencyRateService.find(specificationBuilder, pageRequest);

        return new PageImpl<CurrencyRateRes>(currencyRates.getContent()
                .stream().map(it -> CurrencyRateRes.of(
                        it,
                        businessService.find(it.getBusinessId()).orElse(null),
                        currencyService.find(it.getBaseCurrencyId()).orElse(null),
                        currencyService.find(it.getCurrencyId()).orElse(null)
                )).collect(Collectors.toList())
                , pageRequest, currencyRates.getTotalElements());
    }

    @Override
    public CurrencyRateRes getCurrencyRate(Integer id) {
        Optional<CurrencyRate> currencyRate = currencyRateService.find(id);
        if (!currencyRate.isPresent())
            throw new BusinessException(ErrorCode.CURRENCY_RATE_NOT_FOUND, "Currency rate not found in business", LogData.createLogData().append("currency_rate_id", id));
        return CurrencyRateRes.of(
                currencyRate.get(),
                businessService.find(currencyRate.get().getBusinessId()).orElse(null),
                currencyService.find(currencyRate.get().getBaseCurrencyId()).orElse(null),
                currencyService.find(currencyRate.get().getCurrencyId()).orElse(null)
        );
    }

    @Override
    public void addCurrencyRate(CurrencyRateCreateReq currencyRateCreateReq) {
        currencyRateService.create(CurrencyRateMapper.toCurrencyRateOne(currencyRateCreateReq, auditorAware.getCurrentAuditor().get().getUserName()));
    }

    @Override
    public void updateCurrencyRate(Integer id, CurrencyRateUpdateReq currencyRateUpdateReq) {
        CurrencyRate currencyRate = currencyRateService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.CURRENCY_RATE_NOT_FOUND, "Currency rate not found in business", LogData.createLogData().append("currency_rate_id", id)));

        CurrencyRate currencyRateUpdate = CurrencyRateMapper.toCurrencyRateOne(currencyRate, currencyRateUpdateReq, auditorAware.getCurrentAuditor().get().getUserName());
        currencyRateService.update(currencyRateUpdate);
    }
}
