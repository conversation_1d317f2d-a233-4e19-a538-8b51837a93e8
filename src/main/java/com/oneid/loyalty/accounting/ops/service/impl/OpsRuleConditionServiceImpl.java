package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.service.OpsRuleConditionService;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.SystemAttribute;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.repository.ProgramTransactionAttributeRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpsRuleConditionServiceImpl implements OpsRuleConditionService {

    @Autowired
    private AttributeValueFactory attributeValueFactory;

    @Autowired
    private ProgramTransactionAttributeRepository programTransactionAttributeRepository;

    @Autowired
    private SystemAttributeRepository systemAttributeRepository;

    @Autowired
    private ProgramAttributeRepository programAttributeRepository;

    @Override
    public Collection<ConditionAttributeDto> getAttributes(Integer programId, EServiceType serviceType) {
        Collection<ConditionAttributeDto> result = new ArrayList<>();
        Collection<ProgramTransactionAttribute> programTransactionAttributes =
                programTransactionAttributeRepository.findByProgram(programId);

        Collection<ProgramAttribute> programAttributes = programAttributeRepository.findByProgramId(programId);

        Collection<SystemAttribute> systemAttributes = systemAttributeRepository.findAll();

        result.addAll(programTransactionAttributes.stream()
                .map(attr ->
                        ConditionAttributeDto
                                .builder()
                                .attribute(attr.getAttribute())
                                .name(attr.getName())
                                .description(attr.getDescription())
                                .operators(attr.getOperators())
                                .dataType(attr.getDataType())
                                .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                .valueValidationPattern(attr.getValueValidationPattern())
                                .build()
                ).collect(Collectors.toList()));

        result.addAll(systemAttributes.stream()
                .map(attr ->
                        ConditionAttributeDto
                                .builder()
                                .attribute(attr.getAttribute())
                                .name(attr.getName())
                                .description(attr.getDescription())
                                .operators(attr.getOperators())
                                .dataType(attr.getDataType())
                                .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                .resourcePath(attr.getResourcePath())
                                .supportFilter(attr.getSupportFilter() == EBoolean.YES ? Boolean.TRUE : Boolean.FALSE)
                                .valueValidationPattern(attr.getValueValidationPattern())
                                .build()
                ).collect(Collectors.toList()));

        result.addAll(programAttributes.stream()
                .map(attr ->
                        ConditionAttributeDto
                                .builder()
                                .attribute(attr.getCode())
                                .name(attr.getName())
                                .description(attr.getDescription())
                                .operators(attr.getOperators())
                                .dataType(attr.getDataType())
                                .dataTypeDisplay(EAttributeDataDisplayType.lookup(attr.getDataTypeDisplay()))
                                .valueValidationPattern(attr.getValueValidationPattern())
                                .build()
                ).collect(Collectors.toList()));

        return result;
    }

    @Override
    public Map<String, AttributeValueStrategy<?>> getMapAttributeValueStrategy(Integer programId, EServiceType serviceType) {
        return getAttributes(programId, serviceType).stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                c -> attributeValueFactory.lookup(c)
        ));
    }

    @Override
    public Map<String, ConditionAttributeDto> getMapAttributeValueDto(Integer programId, EServiceType serviceType) {
        return getAttributes(programId, serviceType).stream().collect(Collectors.toMap(
                ConditionAttributeDto::getAttribute,
                c -> c
        ));
    }
}