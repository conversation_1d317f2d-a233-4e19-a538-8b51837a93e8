package com.oneid.loyalty.accounting.ops.feign.model.res;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonDeserialize(builder = GTSkuSchemeFeignRes.GTSkuSchemeFeignResBuilder.class)
public class GTSkuSchemeFeignRes {
private Integer id;
    
    private String skuCode;
    
    private String skuTypeCode;
    
    private String merchantCode;
    
    private String merchantName;
    
    private String productBrandCode;
    
    private String productBrandName;
    
    private String productCategoryName;
    
    private String productCategoryCode;
    
    private String status;
    
    private Double awardRate;
    
    private Date createdAt;
    
    private Date updatedAt;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class GTSkuSchemeFeignResBuilder {
    }
}
