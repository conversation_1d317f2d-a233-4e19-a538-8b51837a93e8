package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.EFormulaAttribute;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class FormulaGroupReq {

    @NotNull(message = "Formula attribute must not be null")
    @JsonProperty("attribute")
    private EFormulaAttribute attribute;

    @Valid
    @NotNull(message = "Formula list must not be null")
    @JsonProperty("formula_list")
    @Size(min = 1, message = "Formula list must not empty")
    private List<FormulaRecordReq> formulaList;
}