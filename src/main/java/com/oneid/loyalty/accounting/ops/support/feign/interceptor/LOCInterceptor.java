package com.oneid.loyalty.accounting.ops.support.feign.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.nio.charset.Charset;
import java.util.Base64;

public class LOCInterceptor implements RequestInterceptor {

    private final String basicAuth;

    public LOCInterceptor(String basicAuth){
        this.basicAuth = Base64.getEncoder().encodeToString(basicAuth.getBytes(Charset.forName("US-ASCII")));
    }

    @Override
    public void apply(RequestTemplate template) {
        template.header("Authorization", "Basic " + basicAuth);
    }
}
