package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@SuperBuilder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LimitationDetailRes {
    private Integer id;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private CounterShortInformationRes counter;

    private String code;

    private String name;

    private String description;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date startDate;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date endDate;

    private BigDecimal threshold;

    private BigDecimal warningThreshold;

    private EBoolean allowResetCounter;

    private EBoolean allowWithRemainingValue;

    private List<RuleRes> rules;

    private ECommonStatus status;

    private String createdBy;

    private String updatedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;
}
