package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.RejectReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferFilterReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferReq;
import com.oneid.loyalty.accounting.ops.model.res.ListCardTransfer;
import com.oneid.loyalty.accounting.ops.model.res.RequestTransferDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.RequestTransferRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OpsCardTransferService {
    Integer createRequestCardTransfer(RequestTransferReq req);

    RequestTransferDetailRes getDetails(Integer id);

    Page<RequestTransferRes> filter(RequestTransferFilterReq req);

    Object approve(Integer id);

    Object reject(Integer id, RejectReq req);

    ListCardTransfer getListCardByTransferId(Integer cardTransferId, Pageable pageRequest);
}