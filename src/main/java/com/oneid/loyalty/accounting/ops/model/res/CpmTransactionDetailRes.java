package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECancellation;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CpmTransactionDetailRes implements Serializable {

    private static final long serialVersionUID = 195658562642249111L;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private String cpmTransactionRef;

    private Date transactionTime;

    private EBoolean cancellationStatus;

    private String accountLinkStatus;

    private EIdType productAccountType;

    private String productAccountCode;

    private String serviceCode;

    private String channelCode;

    private String description;

    private String invoiceNo;

    private Date cancellationTime;

    private ShortEntityRes corporation;

    private ShortEntityRes chain;

    private ShortEntityRes store;

    private ShortEntityRes pos;

    private LOCTransaction locTransaction;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private BigDecimal grossAmount;

    private BigDecimal redeemPoint;

    private String currency;

    private List<CPMTransactionHistory> transactionHistories;

    private List<TransactionAttribute> transactionAttributes;

    private ETransactionStatus status;

    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CPMTransactionHistory implements Serializable {

        private static final long serialVersionUID = 4810145274721920537L;

        @JsonProperty("transaction_ref_no")
        private String transactionRefNo;

        @JsonProperty("program")
        private String program;

        @JsonProperty("product_account_type")
        private EIdType productAccountType;

        @JsonProperty("product_account_code")
        private String productAccountCode;

        @JsonProperty("point_amount")
        private BigDecimal pointAmount;

        @JsonProperty("pool")
        private String pool;

        @JsonProperty("cancellation_status")
        private EBoolean cancellationStatus;

        @JsonProperty("cancellation_time")
        private Date cancellationTime;

        @JsonProperty("status")
        private ETransactionStatus status;
    }

    @Builder
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class LOCTransaction implements Serializable {

        private static final long serialVersionUID = -6980590364004922570L;

        @JsonProperty("loc_transaction_ref")
        private String locTransactionRef;

        @JsonProperty("voucher_code")
        private String voucherCode;

        @JsonProperty("serial")
        private String serial;

        @JsonProperty("cancellation_status")
        private ECancellation cancellationStatus;

        @JsonProperty("cancellation_time")
        private Date cancellationTime;
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TransactionAttribute implements Serializable {

        private static final long serialVersionUID = -6616443065133219578L;

        @JsonProperty("code")
        private String code;

        @JsonProperty("value")
        private String value;

        @JsonProperty("status")
        private ECommonStatus status;


    }


}
