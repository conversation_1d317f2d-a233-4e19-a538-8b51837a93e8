package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.ResetRuleReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberStatusTransitionReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionDetailAvaiRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionInReviewRes;
import com.oneid.loyalty.accounting.ops.model.res.MemberStatusTransitionRes;
import com.oneid.loyalty.accounting.ops.model.res.RuleRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsMemberStatusTransitionService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERequestType;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.DestinationMemberStatus;
import com.oneid.oneloyalty.common.entity.MemberStatus;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.DestinationMemberStatusService;
import com.oneid.oneloyalty.common.service.MemberStatusService;
import com.oneid.oneloyalty.common.service.ProgramService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.WeakHashMap;
import java.util.stream.Collectors;

@Service
public class OpsMemberStatusTransitionServiceImpl implements OpsMemberStatusTransitionService {

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private MemberStatusService memberStatusService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private DestinationMemberStatusService destinationMemberStatusService;

    @Autowired
    private OpsRuleService opsRuleService;

    @Override
    public MakerCheckerInternalMakerRes createRequest(MemberStatusTransitionReq req) {
        businessService.findActive(req.getBusinessId());
        programService.findActive(req.getProgramId());
        verifyCreateMemberStatusTransitionReq(req);
        validationCodeExistInOtherReqPending(req);

        req.setStatus(ECommonStatus.ACTIVE);
        req.setRequestType(ERequestType.CREATE);

        for (MemberStatusTransitionReq.StatusTransition result : req.getStatusTransitions()) {
            if (EBoolean.YES.equals(result.getIsAutomaticTransition())) {
                opsRuleService.validateRules(req.getProgramId(), EServiceType.MEMBER_STATUS, result.getRuleReqs());
            }

            if (Objects.isNull(result.getArchive())) {
                result.setArchive(false);
            }
        }

        MakerCheckerInternalMakerReq<MemberStatusTransitionReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<MemberStatusTransitionReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getName())
                .requestType(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType())
                .payload(req)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);

        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Member status transition call maker error: " + createMakerProgramCorporation.getMeta().getMessage(),
                    null);
        }

        return createMakerProgramCorporation.getData();
    }

    @Override
    @Transactional
    public void approve(ApprovalReq req) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), EMakerCheckerType.MEMBER_STATUS_TRANSITION);

        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (detailResData != null && detailResData.getPayload() != null) {
                processApprove(detailResData);
            }
        }
        makerCheckerInternalFeignClient.checkerDefault(req);
    }

    @Override
    public MemberStatusTransitionRes getChangeableByRequestId(Integer requestId) {
        List<DestinationMemberStatus> destinationMemberStatus = destinationMemberStatusService.findByBeginningStatusIdAndStatusIn(requestId, List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE));

        if (!CollectionUtils.isEmpty(destinationMemberStatus)) {
            opsReqPendingValidator.validationRequestPending(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType(), destinationMemberStatus.get(0).getRequestCode());
        }

        return getMemberStatusTransitionDetail(destinationMemberStatus);
    }

    @Override
    public MakerCheckerInternalMakerRes update(Integer beginningStatusId, MemberStatusTransitionReq req) {
        List<DestinationMemberStatus> destinationMemberStatusList = destinationMemberStatusService.findByBeginningStatusIdAndStatusIn(beginningStatusId, List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE));

        if (CollectionUtils.isEmpty(destinationMemberStatusList)) {
            throw new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_NOT_FOUND, null, null);
        }

        opsReqPendingValidator.verifyEditKey(req.getEditKey(), destinationMemberStatusList.get(0).getRequestCode(), destinationMemberStatusList.get(0).getVersion());
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType(), destinationMemberStatusList.get(0).getRequestCode());
        validationUpdate(req);

        for (MemberStatusTransitionReq.StatusTransition result : req.getStatusTransitions()) {
            if (Objects.isNull(result.getArchive())) {
                result.setArchive(false);
            }
        }

        MemberStatus beginningStatus = memberStatusService.findActive(beginningStatusId);

        Program program = programService.findActive(beginningStatus.getProgramId());

        businessService.findActive(program.getBusinessId());
        req.setRequestType(ERequestType.EDIT);

        MakerCheckerInternalMakerReq<MemberStatusTransitionReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<MemberStatusTransitionReq>builder()
                .requestCode(destinationMemberStatusList.get(0).getRequestCode())
                .requestName(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getName())
                .requestType(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType())
                .payload(req)
                .build();

        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerChecker = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerChecker.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "memberStatus error: " + createMakerChecker.getMeta().getMessage(),
                    null);
        }
        return createMakerChecker.getData();
    }

    @Override
    public Page<MemberStatusTransitionInReviewRes> getInReviewRequests(EApprovalStatus approvalStatus,
                                                                       String fromCreatedAt, String toCreatedAt,
                                                                       String fromReviewedAt, String toReviewedAt,
                                                                       String createdBy, String checkedBy,
                                                                       Integer offset, Integer limit) {
        MakerCheckerInternalPreviewReq.RangeDateReq makeDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromCreatedAt)
                .toDate(toCreatedAt)
                .build();
        MakerCheckerInternalPreviewReq.RangeDateReq checkedDate = MakerCheckerInternalPreviewReq
                .RangeDateReq
                .builder()
                .fromDate(fromReviewedAt)
                .toDate(toReviewedAt)
                .build();

        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList(),
                        createdBy,
                        makeDate,
                        checkedBy,
                        checkedDate
                );
        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<MemberStatusTransitionInReviewRes> content = previewRes.getData()
                .stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());
        return new PageImpl<>(content, new OffsetBasedPageRequest(offset, limit, null), previewRes.getMeta().getTotal());
    }

    @Override
    public MemberStatusTransitionInReviewRes getInReviewRequestById(Integer reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(reviewId));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Member status transition - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Member status transition - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        MakerCheckerInternalDataDetailRes data = previewDetailRes.getData();
        MemberStatusTransitionReq payload = this.objectMapper.convertValue(data.getPayload(), MemberStatusTransitionReq.class);
        Business business = businessService.find(payload.getBusinessId()).orElse(null);
        Program program = programService.find(payload.getProgramId()).orElse(null);

        MemberStatus beginningStatus = memberStatusService.findByProgramIdAndId(payload.getProgramId(), payload.getBeginningStatusId());

        List<MemberStatusTransitionInReviewRes.StatusTransition> statusTransitions = payload.getStatusTransitions().stream().map(ele -> {
            MemberStatusTransitionInReviewRes.StatusTransition statusTransition = new MemberStatusTransitionInReviewRes.StatusTransition();
            MemberStatus destinationStatus = memberStatusService.findByProgramIdAndId(payload.getProgramId(), ele.getDestinationStatusId());
            statusTransition.setDestinationStatus(Objects.nonNull(destinationStatus) ? new ShortEntityRes(destinationStatus.getId(), destinationStatus.getViName(), destinationStatus.getCode()) : null);
            if (!CollectionUtils.isEmpty(ele.getRuleReqs())) {
                List<RuleRes> ruleInReview = opsRuleService.getRuleInReview(payload.getProgramId(),
                        ele.getRuleReqs(),
                        null);
                statusTransition.setRuleResList(!ruleInReview.isEmpty() ? ruleInReview : null);
            }
            statusTransition.setIsAutomaticTransition(ele.getIsAutomaticTransition());
            statusTransition.setArchive(ele.getArchive());
            statusTransition.setRuleLogic(ele.getRuleLogic());
            return statusTransition;
        }).collect(Collectors.toList());

        return MemberStatusTransitionInReviewRes.builder()
                .business(Objects.nonNull(business) ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .status(payload.getStatus())
                .beginningStatus(Objects.nonNull(beginningStatus) ? new ShortEntityRes(beginningStatus.getId(), beginningStatus.getViName(), beginningStatus.getCode()) : null)
                .statusTransitions(statusTransitions)
                .createdAt(data.getMadeDate() != null ? Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant()) : null)
                .createdBy(data.getMadeByUserName())
                .approvedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .approvedBy(data.getCheckedByUserName())
                .reviewedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .reviewedBy(data.getCheckedByUserName())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .updatedBy(data.getCheckedByUserName())
                .updatedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .requestType(payload.getRequestType())
                .reason(previewDetailRes.getData().getComment())
                .build();
    }

    @Override
    public List<MemberStatusTransitionRes> getPageAvailable(Integer businessId, Integer programId, Integer memberStatusId, ECommonStatus status) {

        // Prepare data to mapping
        Map<Integer, Program> programInfoMap = new WeakHashMap<>();
        if (Objects.isNull(programId)) {
            Optional.ofNullable(programService.findByBusinessId(businessId))
                    .orElse(Collections.emptyList())
                    .stream()
                    .forEach(p -> {
                        programInfoMap.put(p.getId(), p);
                    });
        } else {
            Program program = programService.find(programId, businessId);
            programInfoMap.put(program.getId(), program);
        }
        Map<Integer, MemberStatus> memberStatusInfoMap = Optional.ofNullable(memberStatusService.findByBusinessIdAndProgramId(businessId, programId))
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(MemberStatus::getId, value -> value, (k1, k2) -> k2, WeakHashMap::new));
        // Group by BeginningStatusId
        Map<Integer, List<DestinationMemberStatus>> dataMap = destinationMemberStatusService.filter(businessId, programId, memberStatusId, status)
                .stream()
                .collect(Collectors.groupingBy(DestinationMemberStatus::getBeginningStatusId, LinkedHashMap::new, Collectors.toList()));

        List<MemberStatusTransitionRes> res = new ArrayList<>();
        MemberStatus beginningStatus;
        String destinationStatus;
        Program program;
        ECommonStatus transitionStatus;

        // Key is beginning status id and Value is list destination status
        for (Map.Entry<Integer, List<DestinationMemberStatus>> entry : dataMap.entrySet()) {
            program = programInfoMap.get(memberStatusInfoMap.get(entry.getKey()).getProgramId());
            beginningStatus = memberStatusInfoMap.get(entry.getKey());
            transitionStatus = Optional.ofNullable(entry.getValue().get(0).getStatus())
                    .orElse(ECommonStatus.INACTIVE);
            destinationStatus = entry.getValue()
                    .stream()
                    .filter(i -> Objects.nonNull(memberStatusInfoMap.get(i.getDestinationStatusId())))
                    .map(ds -> memberStatusInfoMap.get(ds.getDestinationStatusId()).getViName())
                    .collect(Collectors.joining(" - "));
            res.add(MemberStatusTransitionRes.builder()
                    .beginningStatus(new ShortEntityRes(beginningStatus.getId(), beginningStatus.getViName(), beginningStatus.getCode()))
                    .destinationStatus(destinationStatus)
                    .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                    .status(transitionStatus)
                    .build());
        }
        return res;
    }


    private MemberStatusTransitionInReviewRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        MemberStatusTransitionReq payload = objectMapper.convertValue(data.getPayload(), MemberStatusTransitionReq.class);
        Program program = programService.find(payload.getProgramId()).orElse(null);

        Business business = businessService.find(payload.getBusinessId()).orElse(null);
        List<MemberStatusTransitionInReviewRes.StatusTransition> statusTransitions = Optional.of(payload.getStatusTransitions())
                .orElse(new ArrayList<>())
                .stream()
                .map(ele -> {
                    MemberStatusTransitionInReviewRes.StatusTransition statusTransition = new MemberStatusTransitionInReviewRes.StatusTransition();
                    MemberStatus destinationStatus = memberStatusService.findByProgramIdAndId(payload.getProgramId(), ele.getDestinationStatusId());
                    ShortEntityRes entityRes = new ShortEntityRes(destinationStatus.getId(), destinationStatus.getViName(), destinationStatus.getCode());
                    statusTransition.setDestinationStatus(entityRes);
                    statusTransition.setArchive(ele.getArchive());
                    return statusTransition;
                })
                .collect(Collectors.toList());
        MemberStatus memberStatus = memberStatusService.findByProgramIdAndId(payload.getProgramId(), payload.getBeginningStatusId());

        return MemberStatusTransitionInReviewRes.builder()
                .business(Objects.nonNull(business) ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .id(data.getId())
                .memberStatusCode(memberStatus.getCode())
                .memberStatusName(memberStatus.getViName())
                .statusTransitions(statusTransitions)
                .createdAt(data.getMadeDate() != null ? Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant()) : null)
                .createdBy(data.getMadeByUserName())
                .approvedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .approvedBy(data.getCheckedByUserName())
                .reviewedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .reviewedBy(data.getCheckedByUserName())
                .updatedBy(data.getCheckedByUserName())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .updatedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .build();

    }

    private MemberStatusTransitionRes getMemberStatusTransitionDetail(List<DestinationMemberStatus> destinationMemberStatusList) {
        if (CollectionUtils.isEmpty(destinationMemberStatusList)) {
            throw new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_NOT_FOUND);
        }
        DestinationMemberStatus firstDestinationMemberStatus = destinationMemberStatusList.get(0);

        MemberStatus beginningStatus = memberStatusService.findActive(firstDestinationMemberStatus.getBeginningStatusId());

        Program program = programService.find(beginningStatus.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        Business business = businessService.findById(program.getBusinessId());

        String editKey = opsReqPendingValidator.generateEditKey(destinationMemberStatusList.get(0).getRequestCode(), destinationMemberStatusList.get(0).getVersion());

        MemberStatusTransitionRes memberStatusTransitionRes = new MemberStatusTransitionRes();

        memberStatusTransitionRes.setProgram(new ShortEntityRes(program.getId(), program.getName(), program.getCode()));
        memberStatusTransitionRes.setBusiness(new ShortEntityRes(business.getId(), business.getName(), business.getCode()));
        memberStatusTransitionRes.setBeginningStatus(new ShortEntityRes(beginningStatus.getId(), beginningStatus.getViName(), beginningStatus.getCode()));
        memberStatusTransitionRes.setEditKey(editKey);
        memberStatusTransitionRes.setStatus(firstDestinationMemberStatus.getStatus());
        memberStatusTransitionRes.setCreatedAt(firstDestinationMemberStatus.getCreatedAt());
        memberStatusTransitionRes.setCreatedBy(firstDestinationMemberStatus.getCreatedBy());
        memberStatusTransitionRes.setUpdatedAt(firstDestinationMemberStatus.getUpdatedAt());
        memberStatusTransitionRes.setUpdatedBy(firstDestinationMemberStatus.getUpdatedBy());
        memberStatusTransitionRes.setApprovedAt(firstDestinationMemberStatus.getApprovedAt());
        memberStatusTransitionRes.setApprovedBy(firstDestinationMemberStatus.getApprovedBy());
        memberStatusTransitionRes.setVersion(firstDestinationMemberStatus.getVersion());
        memberStatusTransitionRes.setRequestCode(firstDestinationMemberStatus.getRequestCode());

        List<MemberStatusTransitionRes.StatusTransition> statusTransitions = new ArrayList<>();

        for (DestinationMemberStatus destinationMemberStatus : destinationMemberStatusList) {
            MemberStatus destinationStatus = memberStatusService.findActive(destinationMemberStatus.getDestinationStatusId());
            MemberStatusTransitionRes.StatusTransition statusTransition = new MemberStatusTransitionRes.StatusTransition();

            statusTransition.setDestinationStatus(new ShortEntityRes(destinationStatus.getId(), destinationStatus.getViName(), destinationStatus.getCode()));
            statusTransition.setIsAutomaticTransition(destinationMemberStatus.getIsAutomaticTransition());
            if (EBoolean.YES.equals(statusTransition.getIsAutomaticTransition())) {
                statusTransition.setRuleRes(opsRuleService.getRule(destinationMemberStatus.getCode(), program.getId(), EServiceType.MEMBER_STATUS));
            }
            statusTransition.setId(destinationMemberStatus.getId());
            statusTransition.setRuleLogic(destinationMemberStatus.getRuleLogic());

            statusTransitions.add(statusTransition);
        }

        memberStatusTransitionRes.setStatusTransitions(statusTransitions);

        return memberStatusTransitionRes;
    }

    private void verifyCreateMemberStatusTransitionReq(MemberStatusTransitionReq req) {
        for (MemberStatusTransitionReq.StatusTransition result : req.getStatusTransitions()) {
            if (EBoolean.YES.equals(result.getIsAutomaticTransition()) && CollectionUtils.isEmpty(result.getRuleReqs())) {
                throw new BusinessException(ErrorCode.RULE_AND_CONDITION_OF_MEMBER_STATUS_TRANSITION_CANNOT_NULL);
            }

            memberStatusService.findByProgramIdAndIdAndStatus(req.getProgramId(), result.getDestinationStatusId(), ECommonStatus.ACTIVE).orElseThrow(() ->
                    new BusinessException(ErrorCode.DESTINATION_STATUS_OF_MEMBER_STATUS_TRANSITION_NOT_FOUND));
        }

        MemberStatus beginningStatus = memberStatusService.findByProgramIdAndIdAndStatus(req.getProgramId(), req.getBeginningStatusId(), ECommonStatus.ACTIVE).orElseThrow(() ->
                new BusinessException(ErrorCode.BEGINNING_STATUS_OF_MEMBER_STATUS_TRANSITION_NOT_FOUND));

        if (!CollectionUtils.isEmpty(destinationMemberStatusService.findByBeginningStatusIdAndStatusIn(beginningStatus.getId(), List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE)))) {
            throw new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_EXISTED);
        }
    }

    private void validationCodeExistInOtherReqPending(MemberStatusTransitionReq req) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.MEMBER_STATUS_TRANSITION.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        previewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), MemberStatusTransitionReq.class))
                .filter(ele -> ele.getBusinessId().equals(req.getBusinessId()) &&
                        ele.getProgramId().equals(req.getProgramId()) &&
                        req.getBeginningStatusId().equals(ele.getBeginningStatusId())
                )
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_EXISTED,
                            "[VALIDATION MEMBER STATUS TRANSITION] beginning status of member status transition already exist in other requests pending", null, null);
                });
    }

    private List<ResetRuleReq> processApprove(MakerCheckerInternalDataDetailRes detailResData) {
        MemberStatusTransitionReq payload = this.jsonMapper.convertValue(detailResData.getPayload(), MemberStatusTransitionReq.class);
        List<ResetRuleReq> resetRuleReqs = new ArrayList<>();
        String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
        Program program = programService.findActive(payload.getProgramId());

        if (ERequestType.CREATE.equals(payload.getRequestType())) {
            verifyCreateMemberStatusTransitionReq(payload);
            MemberStatus beginningStatus = memberStatusService.findActive(payload.getBeginningStatusId());
            for (MemberStatusTransitionReq.StatusTransition statusTransition : payload.getStatusTransitions()) {
                DestinationMemberStatus destinationMemberStatus = new DestinationMemberStatus();

                MemberStatus destinationStatus = memberStatusService.findActive(statusTransition.getDestinationStatusId());

                opsReqPendingValidator.updateInfoChecker(destinationMemberStatus, detailResData.getMadeDate(), detailResData.getMadeByUserName(), detailResData.getMadeByUserName(), approvedBy);
                destinationMemberStatus.setBeginningStatusId(beginningStatus.getId());
                destinationMemberStatus.setDestinationStatusId(destinationStatus.getId());
                destinationMemberStatus.setIsAutomaticTransition(statusTransition.getIsAutomaticTransition());
                destinationMemberStatus.setStatus(payload.getStatus());
                destinationMemberStatus.setVersion(detailResData.getVersion());
                destinationMemberStatus.setRequestCode(detailResData.getRequestCode());
                destinationMemberStatus.setRuleLogic(statusTransition.getRuleLogic());

                if (EBoolean.YES.equals(statusTransition.getIsAutomaticTransition())) {
                    destinationMemberStatus.setCode(String.format("%s_%s_%s", beginningStatus.getCode(), destinationStatus.getCode(), UUID.randomUUID()));
                    resetRuleReqs = opsRuleService.createRule(destinationMemberStatus, statusTransition.getRuleReqs(), approvedBy, program.getId(), detailResData.getMadeDate());
                }
                destinationMemberStatusService.save(destinationMemberStatus);
            }
        }

        if (ERequestType.EDIT.equals(payload.getRequestType())) {
            validationUpdate(payload);
            MemberStatus beginningStatus = memberStatusService.findActive(payload.getBeginningStatusId());
            for (MemberStatusTransitionReq.StatusTransition statusTransition : payload.getStatusTransitions()) {
                MemberStatus destinationStatus = memberStatusService.findActive(statusTransition.getDestinationStatusId());
                DestinationMemberStatus memberStatusTransition;
                if (Objects.nonNull(statusTransition.getId())) {
                    memberStatusTransition = destinationMemberStatusService.find(statusTransition.getId()).orElseThrow(() -> new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_NOT_FOUND));
                    opsReqPendingValidator.updateInfoChecker(memberStatusTransition, detailResData.getMadeDate(), detailResData.getMadeByUserName(), detailResData.getMadeByUserName(), approvedBy);
                    if (Boolean.TRUE.equals(statusTransition.getArchive())) {
                        memberStatusTransition.setStatus(ECommonStatus.DELETED);
                    } else {
                        memberStatusTransition.setStatus(payload.getStatus());
                        if (Objects.nonNull(memberStatusTransition.getCode())) {
                            resetRuleReqs = opsRuleService.editRule(memberStatusTransition, statusTransition.getRuleReqs(), approvedBy, program.getId(), detailResData.getMadeDate());
                        } else {
                            memberStatusTransition.setCode(String.format("%s_%s_%s", beginningStatus.getCode(), destinationStatus.getCode(), UUID.randomUUID()));
                            resetRuleReqs = opsRuleService.createRule(memberStatusTransition, statusTransition.getRuleReqs(), approvedBy, program.getId(), detailResData.getMadeDate());
                        }
                    }
                } else {
                    memberStatusTransition = new DestinationMemberStatus();

                    memberStatusTransition.setBeginningStatusId(beginningStatus.getId());

                    opsReqPendingValidator.updateInfoChecker(memberStatusTransition, detailResData.getMadeDate(), detailResData.getMadeByUserName(), detailResData.getMadeByUserName(), approvedBy);
                    if (EBoolean.YES.equals(statusTransition.getIsAutomaticTransition())) {
                        memberStatusTransition.setCode(String.format("%s_%s_%s", beginningStatus.getCode(), destinationStatus.getCode(), UUID.randomUUID()));
                        resetRuleReqs = opsRuleService.createRule(memberStatusTransition, statusTransition.getRuleReqs(), approvedBy, program.getId(), detailResData.getMadeDate());
                    }
                    memberStatusTransition.setStatus(payload.getStatus());
                }
                memberStatusTransition.setDestinationStatusId(destinationStatus.getId());
                memberStatusTransition.setIsAutomaticTransition(statusTransition.getIsAutomaticTransition());
                memberStatusTransition.setVersion(detailResData.getVersion());
                memberStatusTransition.setRequestCode(detailResData.getRequestCode());
                memberStatusTransition.setRuleLogic(statusTransition.getRuleLogic());

                destinationMemberStatusService.save(memberStatusTransition);
            }
        }

        return resetRuleReqs;
    }

    private void validationUpdate(MemberStatusTransitionReq req) {
        List<MemberStatusTransitionReq.StatusTransition> statusTransitionList = new ArrayList<>();
        for (MemberStatusTransitionReq.StatusTransition result : req.getStatusTransitions()) {
            if (EBoolean.YES.equals(result.getIsAutomaticTransition()) && CollectionUtils.isEmpty(result.getRuleReqs())) {
                throw new BusinessException(ErrorCode.RULE_AND_CONDITION_OF_MEMBER_STATUS_TRANSITION_CANNOT_NULL);
            }

            memberStatusService.findByProgramIdAndIdAndStatus(req.getProgramId(), result.getDestinationStatusId(), ECommonStatus.ACTIVE).orElseThrow(() ->
                    new BusinessException(ErrorCode.DESTINATION_STATUS_OF_MEMBER_STATUS_TRANSITION_NOT_FOUND));

            if (Boolean.FALSE.equals(result.getArchive())) {
                statusTransitionList.add(result);
            }
        }

        if (CollectionUtils.isEmpty(statusTransitionList)) {
            throw new BusinessException(ErrorCode.STATUS_TRANSITION_OF_MEMBER_STATUS_TRANSITION_CANNOT_NULL);
        }

        memberStatusService.findByProgramIdAndIdAndStatus(req.getProgramId(), req.getBeginningStatusId(), ECommonStatus.ACTIVE).orElseThrow(() ->
                new BusinessException(ErrorCode.BEGINNING_STATUS_OF_MEMBER_STATUS_TRANSITION_NOT_FOUND));
    }

    @Override
    public MemberStatusTransitionDetailAvaiRes getAvailableRequestById(Integer beginningStatusId) {
        List<DestinationMemberStatus> destinations = destinationMemberStatusService.findByBeginningStatusIdAndStatusIn(beginningStatusId, List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE));

        if (CollectionUtils.isEmpty(destinations)) {
            throw new BusinessException(ErrorCode.MEMBER_STATUS_TRANSITION_NOT_FOUND, "Member status transition not found", null);
        }

        DestinationMemberStatus firstDestination = destinations.get(0);

        MemberStatus beginningStatus = memberStatusService.find(firstDestination.getBeginningStatusId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Beginning member status not found", null));

        Business business = businessService.find(beginningStatus.getBusinessId())
                .orElseThrow(() -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND, "Business not found", null));

        Program program = programService.find(beginningStatus.getProgramId())
                .orElseThrow(() -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", null));

        List<MemberStatusTransitionDetailAvaiRes.StatusTransition> statusTransitions = destinations
                .stream().map(destination -> {
                    MemberStatusTransitionDetailAvaiRes.StatusTransition statusTransition = new MemberStatusTransitionDetailAvaiRes.StatusTransition();

                    MemberStatus memberStatus = memberStatusService.find(destination.getDestinationStatusId()).orElse(null);

                    statusTransition.setIsAutomaticTransition(destination.getIsAutomaticTransition());

                    statusTransition.setDestinationStatus(Objects.nonNull(memberStatus) ? new ShortEntityRes(memberStatus.getId(), memberStatus.getViName(), memberStatus.getCode()) : null);

                    statusTransition.setRuleRes(opsRuleService.getRule(destination.getCode(), program.getId(), EServiceType.MEMBER_STATUS));
                    statusTransition.setRuleLogic(destination.getRuleLogic());

                    return statusTransition;
                }).collect(Collectors.toList());

        return MemberStatusTransitionDetailAvaiRes.builder()
                .id(beginningStatusId)
                .business(Objects.nonNull(business) ? new ShortEntityRes(business.getId(), business.getName(), business.getCode()) : null)
                .program(Objects.nonNull(program) ? new ShortEntityRes(program.getId(), program.getName(), program.getCode()) : null)
                .beginningStatus(new ShortEntityRes(beginningStatus.getId(), beginningStatus.getViName(), beginningStatus.getCode()))
                .statusTransitions(statusTransitions)
                .createdBy(firstDestination.getCreatedBy())
                .createdAt(firstDestination.getCreatedAt())
                .updatedBy(firstDestination.getUpdatedBy())
                .updatedAt(firstDestination.getUpdatedAt())
                .approvedBy(firstDestination.getApprovedBy())
                .approvedAt(firstDestination.getApprovedAt())
                .status(firstDestination.getStatus())
                .build();
    }
}