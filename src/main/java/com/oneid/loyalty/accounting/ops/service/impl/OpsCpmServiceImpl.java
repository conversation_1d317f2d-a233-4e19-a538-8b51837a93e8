package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.ESchemeSortingField;
import com.oneid.loyalty.accounting.ops.datasource.checkout.entity.LOCTransactionHistory;
import com.oneid.loyalty.accounting.ops.datasource.checkout.entity.TransactionHistoryVoucher;
import com.oneid.loyalty.accounting.ops.datasource.checkout.repository.LOCTransactionHistoryRepository;
import com.oneid.loyalty.accounting.ops.datasource.checkout.repository.TransactionHistoryVoucherRepository;
import com.oneid.loyalty.accounting.ops.datasource.checkout.repository.TransactionRedeemInfoRepository;
import com.oneid.loyalty.accounting.ops.datasource.cpm.entity.MasterTransactionHistory;
import com.oneid.loyalty.accounting.ops.datasource.cpm.entity.CPMTransactionHistoryAttribute;
import com.oneid.loyalty.accounting.ops.datasource.cpm.repository.CPMTransactionHistoryRepository;
import com.oneid.loyalty.accounting.ops.datasource.cpm.repository.MasterTransactionHistoryRepository;
import com.oneid.loyalty.accounting.ops.datasource.cpm.repository.CPMTransactionHistoryAttributeRepository;
import com.oneid.loyalty.accounting.ops.feign.CPMServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.LOCServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.CpmTxnRefundReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.LocTxnRefundReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.LocTxnRefundRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CpmTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.CpmTxnReverseReq;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionDetailRes.*;
import com.oneid.loyalty.accounting.ops.model.res.CpmTransactionRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsCpmService;
import com.oneid.loyalty.accounting.ops.support.feign.BadRequestFeignException;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionHistory;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.TransactionHistoryRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class OpsCpmServiceImpl implements OpsCpmService {

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private ChainService chainService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private PosService posService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private TransactionHistoryRepository transactionHistoryRepository;

    @Autowired
    private MasterTransactionHistoryRepository masterTransactionHistoryRepository;

    @Autowired
    private CPMTransactionHistoryRepository cpmTransactionHistoryRepository;

    @Autowired
    private LOCTransactionHistoryRepository locTransactionHistoryRepository;

    @Autowired
    private TransactionHistoryVoucherRepository transactionHistoryVoucherRepository;

    @Autowired
    private CPMTransactionHistoryAttributeRepository CPMTransactionHistoryAttributeRepository;

    @Autowired
    private TransactionRedeemInfoRepository transactionRedeemInfoRepository;

    @Autowired
    private CPMServiceFeignClient cpmServiceFeignClient;

    @Autowired
    private LOCServiceFeignClient locServiceFeignClient;

    @Override
    public Page<CpmTransactionRes> getCpmTransactions(CpmTransactionReq req, Integer offset, Integer limit) {
        List<CpmTransactionRes> cpmTransactions = new ArrayList<>();
        boolean isValidReq = true;

        Sort sort = Sort.by(Sort.Direction.DESC, ESchemeSortingField.CREATED_AT.getMappingColumn());
        Pageable pageRequest = new OffsetBasedPageRequest(offset, limit, sort);

        // Get CPM Master transaction histories
        Page<LOCTransactionHistory> locTransactionHistories;
        Page<MasterTransactionHistory> cpmMasterTxnHistories = null;
        if (req.getLocTransactionRef() != null && req.getCpmTransactionRef() != null) {
            // Get transaction if both LOC transaction ref request ad CPM transaction ref request
            SpecificationBuilder locTxnSpecification = buildSpecification(req, req.getLocTransactionRef());
            SpecificationBuilder cpmTxnSpecification = buildSpecification(req, req.getCpmTransactionRef());
            if (locTxnSpecification == null || cpmTxnSpecification == null) {
                return new PageImpl<>(cpmTransactions, pageRequest,  0);
            }
            locTransactionHistories = getLocTransactions(locTxnSpecification, pageRequest);
            cpmMasterTxnHistories = getCpmTransactions(cpmTxnSpecification, pageRequest);

            // Validate CPM transaction ref from request match CPM transaction ref from LOC
            if (locTransactionHistories.getTotalElements() > 0 && cpmMasterTxnHistories.getTotalElements() > 0) {
                LOCTransactionHistory locTransactionHistory = locTransactionHistories.getContent().get(0);
                MasterTransactionHistory masterTransactionHistory = cpmMasterTxnHistories.getContent().get(0);
                isValidReq = validateCheckoutTransaction(locTransactionHistory, masterTransactionHistory);
            } else {
                isValidReq = false;
            }

        } else if (req.getLocTransactionRef() != null) {
            // Get transaction if only LOC transaction ref
            SpecificationBuilder locTxnSpecification = buildSpecification(req, req.getLocTransactionRef());
            if (locTxnSpecification == null) {
                return new PageImpl<>(cpmTransactions, pageRequest,  0);
            }
            locTransactionHistories = getLocTransactions(locTxnSpecification, pageRequest);
            if (locTransactionHistories.getTotalElements() > 0) {
                LOCTransactionHistory locTransactionHistory = locTransactionHistories.getContent().get(0);
                SpecificationBuilder cpmTxnSpecification = buildSpecification(req, locTransactionHistory.getCpmTransactionRef());
                if (locTransactionHistory.getCpmTransactionRef() == null || cpmTxnSpecification == null) {
                    return new PageImpl<>(cpmTransactions, pageRequest,  0);
                }
                cpmMasterTxnHistories = getCpmTransactions(cpmTxnSpecification, pageRequest);
            }
        } else if (req.getCpmTransactionRef() != null) {
            // Get transaction if only CPM transaction ref
            SpecificationBuilder cpmTxnSpecification = buildSpecification(req, req.getCpmTransactionRef());
            if (cpmTxnSpecification == null) {
                return new PageImpl<>(cpmTransactions, pageRequest,  0);
            }
            cpmMasterTxnHistories = getCpmTransactions(cpmTxnSpecification, pageRequest);
        } else {
            SpecificationBuilder cpmTxnSpecification = buildSpecification(req, null);
            if (cpmTxnSpecification == null) {
                return new PageImpl<>(cpmTransactions, pageRequest,  0);
            }
            cpmMasterTxnHistories = getCpmTransactions(cpmTxnSpecification, pageRequest);
        }

        if (isValidReq && cpmMasterTxnHistories != null) {
            cpmTransactions = buildCpmTxnRes(cpmMasterTxnHistories);
        }
        return new PageImpl<>(cpmTransactions, pageRequest, cpmMasterTxnHistories != null ? cpmMasterTxnHistories.getTotalElements() : 0);
    }


    private Page<LOCTransactionHistory> getLocTransactions(SpecificationBuilder specification, Pageable pageRequest){
        return locTransactionHistoryRepository.findAll(specification, pageRequest);
    }

    private Page<MasterTransactionHistory> getCpmTransactions(SpecificationBuilder specification, Pageable pageRequest){
        return masterTransactionHistoryRepository.findAll(specification, pageRequest);
    }

    private SpecificationBuilder buildSpecification(CpmTransactionReq req, String transactionRef){
        SpecificationBuilder specification = new SpecificationBuilder();

        Business business = businessService.findActive(req.getBusinessId());
        specification.add(new SearchCriteria("businessCode", business.getCode(), SearchOperation.EQUAL));

        Program program = programService.findActive(req.getProgramId());
        specification.add(new SearchCriteria("programCode", program.getCode(), SearchOperation.EQUAL));

        if (req.getInvoiceNo() != null) {
            specification.add(new SearchCriteria("invoiceNo", req.getInvoiceNo(), SearchOperation.EQUAL));
        }

        if (req.getServiceCode() != null) {
            specification.add(new SearchCriteria("serviceCode", req.getServiceCode(), SearchOperation.MATCH));
        }

        if (req.getCorporationId() != null) {
            Corporation corporation = corporationService.findByIdAndBusinessId(req.getCorporationId(), business.getId()).get();
            specification.add(new SearchCriteria("corpCode", corporation.getCode(), SearchOperation.EQUAL));
        }

        if (req.getTerminalId() != null) {
            Pos pos = posService.findActive(req.getTerminalId());
            specification.add(new SearchCriteria("posCode", pos.getCode(), SearchOperation.EQUAL));
        }

        if (req.getIdType() != null && req.getIdNo() != null) {
            // Get all transactions from a member
            Member member = getMember(req);
            if (member != null) {
                specification.add(new SearchCriteria("memberCode", member.getMemberCode(), SearchOperation.EQUAL));
            } else {
                return null;
            }
        } else if (req.getIdType() != null) {
            // Get transactions by id type
            specification.add(new SearchCriteria("cusIdType", req.getIdType(), SearchOperation.EQUAL));
        } else if (req.getIdNo() != null) {
            // Get transactions by specific id no
            specification.add(new SearchCriteria("cusId", req.getIdNo(), SearchOperation.EQUAL));
        }

        if (req.getTransactionTimeStart() != null) {
            specification.add(new SearchCriteria("transactionTime", DateTimes.toDate(req.getTransactionTimeStart()),
                    SearchOperation.GREATER_THAN_EQUAL_DATE));
        }
        if (req.getTransactionTimeEnd() != null) {
            specification.add(new SearchCriteria("transactionTime", DateTimes.toDate(req.getTransactionTimeEnd()),
                    SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        if (transactionRef != null) {
            specification.add(new SearchCriteria("transactionRef", transactionRef, SearchOperation.EQUAL));
        }

        return specification;
    }

    @Override
    public CpmTransactionDetailRes getCpmTransactionDetail(String transactionRef) {
        MasterTransactionHistory masterTransactionHistory = masterTransactionHistoryRepository.findByTransactionRef(transactionRef);
        if (masterTransactionHistory == null) {
            throw new BusinessException(ErrorCode.TRANSACTION_NOT_FOUND);
        }

        return buildCpmTransactionDetailRes(masterTransactionHistory);
    }

    private CpmTransactionDetailRes buildCpmTransactionDetailRes(MasterTransactionHistory masterTransactionHistory) {

        CpmTransactionDetailRes res = new CpmTransactionDetailRes();
        res.setCpmTransactionRef(masterTransactionHistory.getTransactionRef());
        res.setTransactionTime(masterTransactionHistory.getTransactionTime());
        res.setAccountLinkStatus(masterTransactionHistory.getLinkAccountStatus());
        res.setCancellationStatus(masterTransactionHistory.getCancellation());
        res.setCancellationTime(masterTransactionHistory.getCancellationTime());
        res.setProductAccountType(masterTransactionHistory.getCusIdType());
        res.setProductAccountCode(masterTransactionHistory.getCusId());
        res.setServiceCode(masterTransactionHistory.getServiceCode());
        res.setChannelCode(masterTransactionHistory.getChannelCode());
        res.setDescription(masterTransactionHistory.getDescription());
        res.setGrossAmount(masterTransactionHistory.getGrossAmount());
        res.setRedeemPoint(masterTransactionHistory.getTotalRedeemPoint());
        res.setCurrency(masterTransactionHistory.getCurrencyCode());
        res.setInvoiceNo(masterTransactionHistory.getInvoiceNo());
        res.setStatus(masterTransactionHistory.getStatus());

        // Set business
        Business business = businessService.findByCode(masterTransactionHistory.getBusinessCode());
        ShortEntityRes businessRes = new ShortEntityRes();
        if (business != null) {
            businessRes.setId(business.getId());
            businessRes.setCode(business.getCode());
            businessRes.setName(business.getName());

        } else {
            businessRes.setCode(masterTransactionHistory.getBusinessCode());
        }
        res.setBusiness(businessRes);

        // Set merchant
        Program program = null;
        if (business != null) {
            // Set program
            program = programService.find(business.getId(), masterTransactionHistory.getProgramCode()).orElse(null);
            ShortEntityRes programRes = new ShortEntityRes();
            if (program != null) {
                programRes.setId(program.getId());
                programRes.setCode(program.getCode());
                programRes.setName(program.getName());
            } else {
                programRes.setCode(masterTransactionHistory.getProgramCode());
            }
            res.setProgram(programRes);
            buildCpmTransactionMerchantRes(res, masterTransactionHistory, business, program);
        }

        // Set transaction histories
        buildCpmTransactionHistoriesRes(res, masterTransactionHistory);

        // Set LOC transaction
        buildLocTransactionRes(res, masterTransactionHistory);

        // Set transaction attribute
        buildCpmTransactionAttributeRes(res, masterTransactionHistory);

        // Set maker
        res.setCreatedBy(masterTransactionHistory.getCreatedBy());
        res.setCreatedAt(masterTransactionHistory.getCreatedAt());
        res.setUpdatedBy(masterTransactionHistory.getUpdatedBy());
        res.setUpdatedAt(masterTransactionHistory.getUpdatedAt());

        return res;
    }

    private void buildCpmTransactionMerchantRes(CpmTransactionDetailRes cpmTransactionDetailRes, MasterTransactionHistory masterTransactionHistory, Business business, Program program){
        // Set corporation
        Corporation corporation = corporationService.find(business.getId(), masterTransactionHistory.getCorpCode());
        ShortEntityRes corpRes = new ShortEntityRes();
        if (corporation != null) {
            corpRes.setId(corporation.getId());
            corpRes.setCode(corporation.getCode());
            corpRes.setName(corporation.getName());
        } else {
            corpRes.setCode(masterTransactionHistory.getCorpCode());
        }
        cpmTransactionDetailRes.setCorporation(corpRes);

        SpecificationBuilder specification = new SpecificationBuilder();
        specification.add(new SearchCriteria("businessId", business.getId(), SearchOperation.EQUAL));
        specification.add(new SearchCriteria("programId", program.getId(), SearchOperation.EQUAL));
        specification.add(new SearchCriteria("invoiceNo", masterTransactionHistory.getInvoiceNo(), SearchOperation.EQUAL));

        List<TransactionHistory> transactionHistories = transactionHistoryRepository.findAll(specification);
        if (!transactionHistories.isEmpty()) {
            TransactionHistory transactionHistory = transactionHistories.get(0);
            // Set chain
            Chain chain = chainService.findActive(transactionHistory.getChainId());
            ShortEntityRes chainRes = new ShortEntityRes();
            if (chain != null) {
                chainRes.setId(chain.getId());
                chainRes.setCode(chain.getCode());
                chainRes.setName(chain.getName());
            } else {
                chainRes.setCode(masterTransactionHistory.getChainCode());
            }
            cpmTransactionDetailRes.setChain(chainRes);

            // Set store
            Store store = storeService.findActive(transactionHistory.getStoreId());
            ShortEntityRes storeRes = new ShortEntityRes();
            if (store != null) {
                storeRes.setId(store.getId());
                storeRes.setCode(store.getCode());
                storeRes.setName(store.getName());
            } else {
                storeRes.setCode(masterTransactionHistory.getStoreCode());
            }
            cpmTransactionDetailRes.setStore(storeRes);
        }

        // Set pos/terminal
        Pos pos = posService.find(business.getId(), masterTransactionHistory.getPosCode());
        ShortEntityRes posRes = new ShortEntityRes();
        if (pos != null) {
            posRes.setId(pos.getId());
            posRes.setCode(pos.getCode());
            posRes.setName(pos.getName());
        } else {
            posRes.setCode(masterTransactionHistory.getPosCode());
        }
        cpmTransactionDetailRes.setPos(posRes);
    }

    private void buildCpmTransactionAttributeRes(CpmTransactionDetailRes cpmTransactionDetailRes, MasterTransactionHistory masterTransactionHistory) {
        List<CPMTransactionHistoryAttribute> CPMTransactionHistoryAttributes = CPMTransactionHistoryAttributeRepository.findByTransactionRef(masterTransactionHistory.getTransactionRef());
        List<CpmTransactionDetailRes.TransactionAttribute> transactionAttributes = CPMTransactionHistoryAttributes.stream()
                .map(CPMTransactionHistoryAttribute -> TransactionAttribute.builder()
                        .value(CPMTransactionHistoryAttribute.getValue())
                        .code(CPMTransactionHistoryAttribute.getCode())
                        .status(ECommonStatus.ACTIVE)
                        .build()).collect(Collectors.toList());
        cpmTransactionDetailRes.setTransactionAttributes(transactionAttributes);
    }

    private void buildLocTransactionRes(CpmTransactionDetailRes cpmTransactionDetailRes, MasterTransactionHistory masterTransactionHistory){
        LOCTransactionHistory locTransactionHistory = locTransactionHistoryRepository.findByCpmTransactionRefOrInvoiceNoAndProgramCode(
                masterTransactionHistory.getTransactionRef(), masterTransactionHistory.getInvoiceNo(), masterTransactionHistory.getProgramCode()
        );
        if (locTransactionHistory != null) {
            CpmTransactionDetailRes.LOCTransaction locTransaction = CpmTransactionDetailRes.LOCTransaction.builder()
                    .locTransactionRef(locTransactionHistory.getTransactionRef())
                    .voucherCode(locTransactionHistory.getVoucherCode())
                    .cancellationStatus(locTransactionHistory.getCancellation())
                    .cancellationTime(locTransactionHistory.getCancellationTime())
                    .build();
            Optional<TransactionHistoryVoucher> transactionHistoryVoucher = transactionHistoryVoucherRepository.findByTransactionRef(locTransactionHistory.getTransactionRef());
            transactionHistoryVoucher.ifPresent(voucher -> locTransaction.setSerial(voucher.getSerial()));
            cpmTransactionDetailRes.setLocTransaction(locTransaction);
        }
    }

    private void buildCpmTransactionHistoriesRes(CpmTransactionDetailRes cpmTransactionDetailRes, MasterTransactionHistory masterTransactionHistory){
        List<com.oneid.loyalty.accounting.ops.datasource.cpm.entity.CPMTransactionHistory> cpmTransactionHistories = cpmTransactionHistoryRepository.findByMasterId(masterTransactionHistory.getId());
        List<CPMTransactionHistory> transactionHistories = cpmTransactionHistories
                .stream()
                .map(transactionHistory -> CPMTransactionHistory.builder()
                        .transactionRefNo(transactionHistory.getLoyaltyTransactionRefNo())
                        .program(transactionHistory.getProgramCode())
                        .productAccountType(transactionHistory.getCusIdType())
                        .productAccountCode(transactionHistory.getCusId())
                        .pointAmount(transactionHistory.getRedeemPoint())
                        .pool(transactionHistory.getPoolCode())
                        .cancellationStatus(transactionHistory.getCancellation())
                        .cancellationTime(transactionHistory.getCancellationTime())
                        .status(transactionHistory.getStatus())
                        .build())
                .collect(Collectors.toList());
        cpmTransactionDetailRes.setTransactionHistories(transactionHistories);
    }

    private List<CpmTransactionRes> buildCpmTxnRes(Page<MasterTransactionHistory> cpmMasterTxnHistories) {
        List<CpmTransactionRes> res = new ArrayList<>();
        List<MasterTransactionHistory> cpmMasterTxnHistoryList = cpmMasterTxnHistories.getContent();

        for (MasterTransactionHistory masterTransactionHistory : cpmMasterTxnHistoryList) {
            CpmTransactionRes cpmTransactionRes = new CpmTransactionRes();
            cpmTransactionRes.setCpmTransactionRef(masterTransactionHistory.getTransactionRef());
            cpmTransactionRes.setInvoiceNo(masterTransactionHistory.getInvoiceNo());
            cpmTransactionRes.setServiceCode(masterTransactionHistory.getServiceCode());
            cpmTransactionRes.setChannelCode(masterTransactionHistory.getChannelCode());
            cpmTransactionRes.setProductAccountType(masterTransactionHistory.getCusIdType());
            cpmTransactionRes.setGrossAmount(masterTransactionHistory.getGrossAmount());
            cpmTransactionRes.setRedeemPoint(masterTransactionHistory.getTotalRedeemPoint());
            cpmTransactionRes.setCurrency(masterTransactionHistory.getCurrencyCode());
            cpmTransactionRes.setCancellationStatus(masterTransactionHistory.getCancellation());
            cpmTransactionRes.setCancellationTime(masterTransactionHistory.getCancellationTime());
            cpmTransactionRes.setTransactionTime(masterTransactionHistory.getTransactionTime());
            cpmTransactionRes.setStatus(masterTransactionHistory.getStatus());
            cpmTransactionRes.setProductAccountCode(masterTransactionHistory.getCusId());

            LOCTransactionHistory locTransactionHistory = locTransactionHistoryRepository.findByCpmTransactionRefOrInvoiceNoAndProgramCode(
                    masterTransactionHistory.getTransactionRef(), masterTransactionHistory.getInvoiceNo(), masterTransactionHistory.getProgramCode());
            if (locTransactionHistory != null) {
                cpmTransactionRes.setLocTransactionRef(locTransactionHistory.getTransactionRef());
            }

            Business business = businessService.findByCode(masterTransactionHistory.getBusinessCode());
            programService.find(business.getId(), masterTransactionHistory.getProgramCode()).flatMap(program -> memberService.find(masterTransactionHistory.getMemberCode(), program.getId())).ifPresent(member -> cpmTransactionRes.setMemberId(member.getId()));

            Corporation corporation = corporationService.find(business.getId(), masterTransactionHistory.getCorpCode());
            ShortEntityRes corporationRes = new ShortEntityRes();
            if (corporation != null) {
                corporationRes.setId(corporation.getId());
                corporationRes.setName(corporation.getName());
                corporationRes.setCode(corporation.getCode());
            } else {
                corporationRes.setCode(masterTransactionHistory.getCorpCode());
            }
            cpmTransactionRes.setCorporation(corporationRes);

            Pos pos = posService.find(business.getId(), masterTransactionHistory.getPosCode());
            ShortEntityRes posRes = new ShortEntityRes();
            if (pos != null) {
                posRes.setId(pos.getId());
                posRes.setName(pos.getName());
                posRes.setCode(pos.getCode());
            } else {
                posRes.setCode(masterTransactionHistory.getPosCode());
            }
            cpmTransactionRes.setTerminal(posRes);

            res.add(cpmTransactionRes);
        }

        return res;
    }

    private Boolean validateCheckoutTransaction(LOCTransactionHistory locTransactionHistory, MasterTransactionHistory masterTransactionHistory) {
        return masterTransactionHistory.getTransactionRef().equals(locTransactionHistory.getCpmTransactionRef());
    }

    private Member getMember(CpmTransactionReq req) {
        Member member = null;
        try {
            Business business = businessService.findActive(req.getBusinessId());
            Program program = programService.find(req.getProgramId(), business.getId());

            if (program != null) {
                CustomerIdentify customerIdentify = new CustomerIdentify();
                customerIdentify.setId(req.getIdNo());
                customerIdentify.setIdType(req.getIdType().getMapping());

                member = memberService.find(customerIdentify, program.getId());
            }
        } catch (Exception e) {
            return null;
        }
        return member;
    }

    @Override
    public void reverseCpmTransaction(CpmTxnReverseReq cpmTxnReverseReq) {
        MasterTransactionHistory masterTransactionHistory = masterTransactionHistoryRepository.findByTransactionRefAndInvoiceNo(
                cpmTxnReverseReq.getCpmTransactionRef(), cpmTxnReverseReq.getInvoiceNo());

        if (masterTransactionHistory == null) {
            throw new BusinessException(ErrorCode.TRANSACTION_NOT_FOUND);
        }

        LOCTransactionHistory locTransactionHistory = locTransactionHistoryRepository.findByCpmTransactionRefOrInvoiceNoAndProgramCode(
                cpmTxnReverseReq.getCpmTransactionRef(), cpmTxnReverseReq.getInvoiceNo(), masterTransactionHistory.getProgramCode());

        if (locTransactionHistory == null) {
            // Refund from CPM API
            refundCpmTransaction(masterTransactionHistory);
        } else {
            // Refund from LOC API
            refundLocTransaction(locTransactionHistory);
        }
    }

    private void refundCpmTransaction(MasterTransactionHistory masterTransactionHistory) {
        CustomerIdentify customerIdentify = new CustomerIdentify();
        customerIdentify.setIdType(masterTransactionHistory.getCusIdType());
        customerIdentify.setId(masterTransactionHistory.getCusId());

        CpmTxnRefundReq cpmTxnRefundReq = CpmTxnRefundReq.builder()
                .customerIdentifier(customerIdentify)
                .originalInvoiceNo(masterTransactionHistory.getInvoiceNo())
                .transactionTime(DateTimes.currentTimeSeconds())
                .txnRefNo(masterTransactionHistory.getTransactionRef())
                .updatedBy(opsReqPendingValidator.getCurrentUser())
                .build();
        try {
            APIResponse<?> cpmTxnRefundRes = cpmServiceFeignClient.refund(cpmTxnRefundReq);
            if (ErrorCode.SUCCESS.getValue() != cpmTxnRefundRes.getMeta().getCode()) {
                log.error(String.format("Error while refund: {\"transaction_ref\": \"%s\", \"error_code\": \"%s\", \"message\": \"%s\"}",
                        cpmTxnRefundReq.getTxnRefNo(), cpmTxnRefundRes.getMeta().getCode(), cpmTxnRefundRes.getMeta().getMessage()));
                throw new BusinessException(cpmTxnRefundRes.getMeta().getCode(), cpmTxnRefundRes.getMeta().getMessage(), null);
            }
        } catch (BadRequestFeignException e) {
            log.error(String.format("Error while refund: {\"transaction_ref\": \"%s\", \"error_code\": \"%s\", \"message\": \"%s\", \"request\": \"%s\"}",
                    cpmTxnRefundReq.getTxnRefNo(), e.getErrorCode(), e.getErrorMessage(), e.getRequest()));
            throw new BusinessException(e.getErrorCode(), e.getMessage(), null);
        }
    }

    private void refundLocTransaction(LOCTransactionHistory locTransactionHistory) {
        CustomerIdentify customerIdentify = new CustomerIdentify();
        customerIdentify.setIdType(locTransactionHistory.getCusIdType());
        customerIdentify.setId(locTransactionHistory.getCusId());

        LocTxnRefundReq locTxnRefundReq = LocTxnRefundReq.builder()
                .customerIdentifier(customerIdentify)
                .locRefNo(locTransactionHistory.getTransactionRef())
                .updatedBy(opsReqPendingValidator.getCurrentUser())
                .build();
        try {
            APIResponse<LocTxnRefundRes> locTxnRefundRes = locServiceFeignClient.refund(locTxnRefundReq);
            if (ErrorCode.SUCCESS.getValue() != locTxnRefundRes.getMeta().getCode()) {
                log.error(String.format("Error while refund: {\"transaction_ref\": \"%s\", \"error_code\": \"%s\", \"message\": \"%s\"}",
                        locTxnRefundReq.getLocRefNo(), locTxnRefundRes.getMeta().getCode(), locTxnRefundRes.getMeta().getMessage()));
                throw new BusinessException(locTxnRefundRes.getMeta().getCode(), locTxnRefundRes.getMeta().getMessage(), null);
            }
        } catch (BadRequestFeignException e) {
            log.error(String.format("Error while refund: {\"transaction_ref\": \"%s\", \"error_code\": \"%s\", \"message\": \"%s\", \"request\": \"%s\"}",
                    locTxnRefundReq.getLocRefNo(), e.getErrorCode(), e.getErrorMessage(), e.getRequest()));
            throw new BusinessException(e.getErrorCode(), e.getMessage(), null);
        }
    }

}
