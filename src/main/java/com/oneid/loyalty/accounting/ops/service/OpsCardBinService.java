package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardBinUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.AddCardRes;
import com.oneid.loyalty.accounting.ops.model.res.CardBinRes;
import org.springframework.data.domain.Page;

public interface OpsCardBinService {
    Page<CardBinRes> filter(Integer businessId, Integer programId, String binCode, Integer offset, Integer limit);

    CardBinRes get(Integer id);

    void add(CardBinCreateReq cardBinCreateReq);

    void update(Integer id, CardBinUpdateReq cardBinUpdateReq);

}
