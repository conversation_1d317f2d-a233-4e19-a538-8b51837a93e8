package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.converter.RFC3339Serialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MemberStatusTransitionDetailAvaiRes {

    private Integer id;

    private ShortEntityRes business;

    private ShortEntityRes program;

    private ShortEntityRes beginningStatus;

    private ECommonStatus status;

    private List<StatusTransition> statusTransitions;

    private String createdBy;

    private String updatedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date createdAt;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date updatedAt;

    private String approvedBy;

    @JsonSerialize(converter = RFC3339Serialize.class)
    private Date approvedAt;


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatusTransition {

        private ShortEntityRes destinationStatus;

        private EBoolean isAutomaticTransition;

        private List<RuleRes> ruleRes;

        private EConditionType ruleLogic;
    }
}