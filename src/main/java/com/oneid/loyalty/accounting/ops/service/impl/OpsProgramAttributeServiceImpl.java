package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.PoolRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramAttributeService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpsProgramAttributeServiceImpl implements OpsProgramAttributeService {
    @Autowired
    ProgramAttributeRepository programAttributeRepository;

    @Override
    public Page<ProgramAttributeRes> search(Integer programId, Integer offset, Integer limit) {
        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);

        SpecificationBuilder<ProgramAttribute> specificationBuilder = new SpecificationBuilder<>();

        if (programId != null)
            specificationBuilder.add(new SearchCriteria("programId", programId, SearchOperation.EQUAL));

        Page<ProgramAttribute> pools = programAttributeRepository.findAll(specificationBuilder, pageRequest);

        return new PageImpl<ProgramAttributeRes>(pools.getContent()
                .stream().map(it -> ProgramAttributeRes.builder()
                        .id(it.getId())
                        .programId(it.getProgramId())
                        .code(it.getCode())
                        .name(it.getName())
                        .valueValidationPattern(it.getValueValidationPattern())
                        .description(it.getDescription())
                        .status(it.getStatus().getValue())
                        .build()
                ).collect(Collectors.toList())
                , pageRequest, pools.getTotalElements());
    }
}
