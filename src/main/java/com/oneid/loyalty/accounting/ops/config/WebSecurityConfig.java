package com.oneid.loyalty.accounting.ops.config;

import com.oneid.loyalty.accounting.ops.support.web.security.authentication.OPSAuthenticationConverter;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.web.OPSAuthenticationEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

@Configuration
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
    @Autowired
    private OPSAuthenticationConverter authenticationConverter;

    @Autowired
    private OPSAuthenticationEntryPoint authenticationEntryPoint;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.
                csrf()
                .disable()
                .authorizeRequests()
                .antMatchers("/**/health")
                .permitAll()
                .anyRequest()
                .authenticated()
                .and()
                .oauth2ResourceServer()
                .jwt()
                .jwtAuthenticationConverter(authenticationConverter)
                .and()
                .authenticationEntryPoint(authenticationEntryPoint);
    }
}
