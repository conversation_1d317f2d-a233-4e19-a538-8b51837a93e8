package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateTierAdjustmentMemberReq extends CreateTierAdjustmentReq{

    @NotBlank(message = "'id_type' must not be blank")
    private String idType;

    @NotBlank(message = "'id_code' must not be blank")
    private String idCode;

    @NotNull(message = "'next_tier_id' must not be null")
    private Integer nextTierId;

    @OpsCode(message = "'reason code' in valid")
    @Length(max = 64, message = "max length of 'reason_code' is 64 byte")
    private String reasonCode;
}
