package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Getter
@Setter
public class CardBinUpdateReq {
//    @NotNull
//    @JsonProperty("business_id")
//    private Integer businessId;
//
//    @NotNull
//    @JsonProperty("program_id")
//    private Integer programId;
//
//    @NotBlank
//    @JsonProperty("bin_code")
//    private String binCode;

    @Length(max = 255, message = "'Description' max length is 255")
    @JsonProperty("description")
    private String description;

    @NotBlank
    @Pattern(regexp = "^(A|I)?$", message = "'Status' Only accept A/I values")
    @JsonProperty("status")
    private String status;
}
