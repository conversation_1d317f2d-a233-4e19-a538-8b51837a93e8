package com.oneid.loyalty.accounting.ops.feign.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GCTExistFeignRes {
    @JsonProperty("batch_nos")
    private List<String> batchNos = new ArrayList<>();
}
