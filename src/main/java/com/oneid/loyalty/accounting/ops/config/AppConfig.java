package com.oneid.loyalty.accounting.ops.config;

import java.util.List;

import com.oneid.loyalty.accounting.ops.util.Versioning;
import com.oneid.oneloyalty.common.encryption.AESSupport;
import org.apache.http.client.HttpClient;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.security.oauth2.jwt.JwtDecoder;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.oneid.loyalty.accounting.ops.feign.OpsAdminServiceFeignClient;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.service.impl.OpsTransactionServiceImpl;
import com.oneid.loyalty.accounting.ops.setting.SFTPSetting;
import com.oneid.loyalty.accounting.ops.support.data.databind.AccessRoleDeserializer;
import com.oneid.loyalty.accounting.ops.support.feign.decoder.ResponseErrorHandlingDecoder;
import com.oneid.loyalty.accounting.ops.support.feign.decoder.ResponseErrorHandlingVerifier;
import com.oneid.loyalty.accounting.ops.support.web.acl.ACLMethodSecurityAdvisor;
import com.oneid.loyalty.accounting.ops.support.web.acl.ACLMethodSecurityInterceptor;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.security.authentication.OPSAuthenticationConverter;
import com.oneid.loyalty.accounting.ops.support.web.security.authentication.OPSAuthenticationDecoder;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.server.resource.web.OPSAuthenticationEntryPoint;
import com.oneid.loyalty.accounting.ops.component.SFTPClient;

import feign.Client;
import feign.codec.Decoder;
import feign.httpclient.ApacheHttpClient;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

@Configuration
public class AppConfig {

    @Bean
    ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new Jackson2ObjectMapperBuilder()
                .createXmlMapper(false)
                .build();

        SimpleModule simpleModule = new SimpleModule();
        objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        simpleModule.addKeyDeserializer(AccessRole.class, new AccessRoleDeserializer());

        objectMapper.registerModule(simpleModule);

        return objectMapper;
    }

    @Bean
    OpsTransactionService opsTransactionService() {
        return new OpsTransactionServiceImpl();
    }

    @Bean("cprSFTPClient")
    SFTPClient cprSFTPClient(@Qualifier("cprsSftpSetting") SFTPSetting sftpSetting) {
        return SFTPClient.builder()
                .setting(sftpSetting)
                .build();
    }

    @Bean
    @Profile("!test")
    JwtDecoder jwtDecoderByPublicKeyValue(OpsAdminServiceFeignClient opsAdminServiceFeignClient) throws Exception {
        return new OPSAuthenticationDecoder(opsAdminServiceFeignClient);
    }

    @Bean
    OPSAuthenticationConverter authenticationConverter(ObjectMapper objectMapper) {
        return new OPSAuthenticationConverter(objectMapper);
    }

    @Bean
    OPSAuthenticationEntryPoint authenticationEntryPoint(ObjectMapper objectMapper) {
        return new OPSAuthenticationEntryPoint(objectMapper);
    }

    @Bean
    public ACLMethodSecurityInterceptor aclMethodSecurityInterceptor() {
        return new ACLMethodSecurityInterceptor();
    }

    @Bean
    public ACLMethodSecurityAdvisor ACLMethodSecurityAdvisor(ACLMethodSecurityInterceptor aclMethodSecurityInterceptor) {
        return new ACLMethodSecurityAdvisor(aclMethodSecurityInterceptor);
    }

    @Bean
    @SuppressWarnings("rawtypes")
    public Decoder feignDecoder(
            ObjectFactory<HttpMessageConverters> messageConverters,
            List<ResponseErrorHandlingVerifier> errorHandlingVerifiers) {
        return new ResponseErrorHandlingDecoder(new SpringDecoder(messageConverters), errorHandlingVerifiers);
    }

    @Bean
    public Client feignClient(HttpClient httpClient) {
        return new ApacheHttpClient(httpClient);
    }

    @Bean
    @ConfigurationProperties(prefix = "app.versioning")
    public Versioning versioning() {
        return new Versioning();
    }

}