package com.oneid.loyalty.accounting.ops.model.res;

import java.time.OffsetDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardBatchType;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(value = Include.NON_NULL)
public class GiftCardRes {

    private Integer reviewId;
    
    private Integer requestId;
    
    private Long id;
    
    private Long cprBatchNo;
    
    private EGiftCardBatchType batchType;
    
    private String serial;
    
    private Integer businessId;
    
    private String businessName;
    
    private Integer corporationId;
    
    private String corporationName;
    
    private Integer chainId;
    
    private String chainName;
    
    private Integer storeId;
    
    private String storeName;
    
    private Integer programId;
    
    private String programName;
    
    private OffsetDateTime expirationDate;
    
    private OffsetDateTime startDate;
    
    private OffsetDateTime topupAt;
    
    private EGiftCardStatus status;
    
    private EApprovalStatus approvalStatus;
    
    private Long memberId;
    
    private String memberCode;
    
    private GiftCardTypeRes giftCardType;
    
    private List<EGiftCardStatus> transitionStatuses;
    
    private String changedReason;
    
    private String rejectedReason;
    
    private OffsetDateTime createdAt;
    
    private String createdBy;
    
}
