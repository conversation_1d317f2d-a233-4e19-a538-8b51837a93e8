package com.oneid.loyalty.accounting.ops.validation;

import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.BaseMakerChecker;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.support.Hashing;
import com.oneid.oneloyalty.common.util.DateTimes;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class OpsReqPendingValidator {

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalClient;

    public void validationRequestPending(String requestType, String requestCode) {
        if (StringUtils.isNotBlank(requestCode)) {
            MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                    .requestType(requestType)
                    .requestCode(requestCode)
                    .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                    .build();

            APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> response =
                    makerCheckerInternalClient.preview(previewReq, 0, 10);

            if (ErrorCode.SUCCESS.getValue() != response.getMeta().getCode())
                throw new BusinessException(
                        ErrorCode.SERVER_ERROR,
                        "Request check pending error: " + response.getMeta().getMessage(),
                        null
                );

            // Throw exception if exist a request is pending for approval
            if (response.getMeta().getTotal() > 0) {
                throw new BusinessException(
                        ErrorCode.ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED,
                        null,
                        null
                );
            }
        }
    }

    public String generateEditKey(String requestCode, Integer version) {
        String originalInput = String.format("%s|%d", requestCode, version);
        return Hashing.getSHA256Secure(originalInput, originalInput.getBytes());
    }

    public void verifyEditKey(String editKey, String requestCode, Integer version) {
        String editKeySecure = generateEditKey(requestCode, version);
        if (Objects.isNull(editKey) || !editKeySecure.equals(editKey)) {
            throw new BusinessException(
                    ErrorCode.PROGRAM_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED,
                    null,
                    null
            );
        }
    }

    public <E extends BaseMakerChecker> void updateInfoChecker(E entity, String madeDate, String createdBy, String updatedBy, String approvedBy) {
        entity.setApprovedAt(DateTimes.currentTime());
        if (createdBy != null) entity.setCreatedBy(createdBy);
        entity.setUpdatedBy(updatedBy);
        entity.setApprovedBy(approvedBy);

        Date date = madeDate != null ? DateTimes.toDate(ZonedDateTime.parse(madeDate).toInstant().getEpochSecond()) : null;

        if (Objects.isNull(entity.getCreatedAt())) {
            entity.setCreatedAt(date);
        }

        entity.setUpdatedAt(date);
    }

    public String getCurrentAuditorAndSetValue(String userName) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        OPSAuthenticatedPrincipal opsAuthenticatedPrincipal = (OPSAuthenticatedPrincipal) authentication.getPrincipal();
        String currentAuditor = opsAuthenticatedPrincipal.getUserName();
        opsAuthenticatedPrincipal.setUserName(userName);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        return currentAuditor;
    }

    public String getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        OPSAuthenticatedPrincipal opsAuthenticatedPrincipal = (OPSAuthenticatedPrincipal) authentication.getPrincipal();
        String currentAuditor = opsAuthenticatedPrincipal.getUserName();
        return currentAuditor;
    }
}
