package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;

@Getter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateChangeRequestReq<T> {
    private final String module;
    private final String actionType;
    private final String objectId;
    private Map<String, String> params;
    private final T payload;
}