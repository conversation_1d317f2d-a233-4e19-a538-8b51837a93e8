package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.req.FormulaGroupReq;
import com.oneid.loyalty.accounting.ops.model.req.FormulaRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.FormulaGroupRes;
import com.oneid.loyalty.accounting.ops.model.res.FormulaRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EFormulaType;
import com.oneid.oneloyalty.common.constant.EFormulaUnitType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.constant.RoundingRule;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeFormula;
import com.oneid.oneloyalty.common.entity.SchemeFormulaRange;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.SchemeFormulaRangeRepository;
import com.oneid.oneloyalty.common.service.SchemeFormulaService;
import com.oneid.oneloyalty.common.util.LogData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OpsFormulaServiceImpl implements OpsFormulaService {

    private static final Logger logger = LoggerFactory.getLogger(OpsFormulaServiceImpl.class);

    @Autowired
    private SchemeFormulaService schemeFormulaService;

    @Autowired
    private SchemeFormulaRangeRepository schemeFormulaRangeRepository;

    @Override
    public void verify(FormulaGroupReq req) {
        // nothing;
    }

    @Override
    @Transactional
    public FormulaGroupRes create(final Scheme scheme, final FormulaGroupReq req) {
        if (req == null) {
            return null;
        }
        FormulaGroupRes result = new FormulaGroupRes();
        result.setAttribute(req.getAttribute());
        Optional.ofNullable(req.getFormulaList()).orElse(Collections.emptyList())
                .forEach(f -> f.setAttribute(req.getAttribute()));

        result.setFormulaList(createListFormulaInScheme(scheme, req.getFormulaList()));
        return result;
    }

    @Override
    public FormulaGroupRes getFormulaGroupByScheme(final Scheme scheme) {
        FormulaGroupRes result = new FormulaGroupRes();
        List<SchemeFormula> schemeFormulas = Optional.ofNullable(schemeFormulaService.getAllBySchemeId(scheme.getId()))
                .orElse(Collections.emptyList());

        result.setFormulaList(schemeFormulas.stream().map(
                f -> {
                    FormulaRecordRes record;
                    List<SchemeFormulaRange> schemeFormulaRanges = schemeFormulaRangeRepository.getByFormulaId(f.getId(), ECommonStatus.ACTIVE);
                    if (EFormulaType.F2.equals(f.getFormulaType())) {
                        if (schemeFormulaRanges.size() != 1) {
                            logger.error("[SCHEME_SERVICE] scheme award not find formula", scheme);
                            LogData.createLogData().append("[Scheme]", scheme);
                            throw new BusinessException(ErrorCode.CANNOT_FIND_FORMULA_IN_SCHEME,
                                    "Cannot find formula in scheme", scheme);
                        }
                        record = FormulaRecordRes.toF2(f, schemeFormulaRanges.get(0));
                    } else {
                        record = FormulaRecordRes.toF4(f, schemeFormulaRanges);
                    }
                    return record;
                }
        ).collect(Collectors.toList()));

        if (schemeFormulas.size() > 0) {
            result.setAttribute(schemeFormulas.get(0).getAttribute());
        }

        return result;
    }

    @Override
    public FormulaGroupRes updateFormula(Scheme scheme, FormulaGroupReq formulaGroup) {
        if (formulaGroup == null) {
            return null;
        }
        formulaGroup.getFormulaList().forEach(f -> f.setAttribute(formulaGroup.getAttribute()));
        FormulaGroupRes result = new FormulaGroupRes();
        result.setAttribute(formulaGroup.getAttribute());

        List<SchemeFormula> oldFormulas = schemeFormulaService.getAllBySchemeId(scheme.getId());
        Map<Integer, SchemeFormula> mapOldFormulas = oldFormulas.stream().collect(Collectors.toMap(
                SchemeFormula::getId,
                f -> f
        ));

        long checker = formulaGroup.getFormulaList().stream().filter(r -> r.getFormulaId() != null &&
                !mapOldFormulas.containsKey(r.getFormulaId())).count();

        if (checker > 0) {
            throw new BusinessException(ErrorCode.CANNOT_FIND_FORMULA_IN_SCHEME, "Cannot find formula in scheme",
                    formulaGroup);
        }

        /* create new */
        List<FormulaRecordReq> formulasWillCreate = new ArrayList<>();
        formulaGroup.getFormulaList().stream().filter(formulaRecordReq -> formulaRecordReq.getFormulaId() == null)
                .forEach(formulasWillCreate::add);

        List<FormulaRecordRes> formulasUpdated = new ArrayList<>(createListFormulaInScheme(scheme, formulasWillCreate));

        Map<Integer, FormulaRecordReq> mapIdToFormulaNeedUpdate = formulaGroup.getFormulaList()
                .stream().filter(f -> f.getFormulaId() != null)
                .collect(Collectors.toMap(
                        FormulaRecordReq::getFormulaId,
                        f -> f
                ));

        /* remove */
        oldFormulas.stream().filter(f -> !mapIdToFormulaNeedUpdate.containsKey(f.getId()))
                .forEach(schemeFormulaService::delete);

        /* update */
        for (FormulaRecordReq f : mapIdToFormulaNeedUpdate.values()) {
            if (mapOldFormulas.containsKey(f.getFormulaId())) {
                formulasUpdated.add(createOrUpdate(scheme, f, mapOldFormulas.get(f.getFormulaId())));
            }
        }

        result.setFormulaList(formulasUpdated);

        return result;
    }

    private List<FormulaRecordRes> createListFormulaInScheme(Scheme scheme, List<FormulaRecordReq> formulaRecordReqs) {
        List<FormulaRecordRes> result = new ArrayList<>();
        Optional.ofNullable(formulaRecordReqs).orElse(Collections.emptyList()).forEach(f -> {
            SchemeFormula schemeFormula = new SchemeFormula();
            schemeFormula.setSchemeId(scheme.getId());
            schemeFormula.setStatus(ECommonStatus.ACTIVE); // default
            schemeFormula.setFormulaType(f.getFormulaType());
            schemeFormula.setAttribute(f.getAttribute());
            schemeFormula.setUnitType(f.getFormulaUnitType());
            schemeFormula.setIsDistributeAll(EBoolean.NO);
            schemeFormula.setInputRoundingRule(RoundingRule.DOWN);
            final SchemeFormula finalSchemeFormula = schemeFormulaService.create(schemeFormula);
            result.add(createOrUpdate(scheme, f, finalSchemeFormula));
        });

        return result;
    }

    private FormulaRecordRes createOrUpdate(Scheme scheme, FormulaRecordReq req, SchemeFormula oldFormula) {
        SchemeFormula schemeFormula;

        if (oldFormula == null) {
            schemeFormula = new SchemeFormula();
        } else {
            schemeFormula = oldFormula;
        }

        schemeFormula.setSchemeId(scheme.getId());
        schemeFormula.setStatus(ECommonStatus.ACTIVE); // default
        schemeFormula.setFormulaType(req.getFormulaType());
        schemeFormula.setAttribute(req.getAttribute());
        schemeFormula.setUnitType(req.getFormulaUnitType());

        final SchemeFormula finalSchemeFormula;

        if (oldFormula == null) {
            finalSchemeFormula = schemeFormulaService.create(schemeFormula);
        } else {
            finalSchemeFormula = schemeFormulaService.update(schemeFormula);
        }

        // remove old scheme formula range old
        List<SchemeFormulaRange> schemeFormulaRanges = schemeFormulaRangeRepository
                .getByFormulaId(finalSchemeFormula.getId(), ECommonStatus.ACTIVE);

        schemeFormulaRanges.forEach( r-> {
                    r.setStatus(ECommonStatus.INACTIVE);
                    schemeFormulaRangeRepository.save(r);
                }
        );

        if (EFormulaType.F2.equals(schemeFormula.getFormulaType())) {
            SchemeFormulaRange range = new SchemeFormulaRange();
            range.setStatus(ECommonStatus.ACTIVE); // default
            range.setFormulaId(finalSchemeFormula.getId());
            range.setNValue(req.getN());
            range.setDValue(req.getD());
            range = schemeFormulaRangeRepository.save(range);
            return FormulaRecordRes.toF2(schemeFormula, range);
        } else {
            List<SchemeFormulaRange> ranges = new ArrayList<>();
            for (FormulaRecordReq.FormulaFactor factor : req.getFactorList()) {
                SchemeFormulaRange range = new SchemeFormulaRange();
                range.setStatus(ECommonStatus.ACTIVE); // default
                range.setFormulaId(finalSchemeFormula.getId());
                range.setAmountFrom(factor.getAmountFrom());
                range.setAmountTo(factor.getAmountTo());
                if (EFormulaUnitType.PERCENT.equals(finalSchemeFormula.getUnitType())) {
                    range.setPercentValue(factor.getValue());
                } else {
                    range.setPointValue(factor.getValue().longValue());
                }
                ranges.add(schemeFormulaRangeRepository.save(range));
            }
            return FormulaRecordRes.toF4(schemeFormula, ranges);
        }
    }
}