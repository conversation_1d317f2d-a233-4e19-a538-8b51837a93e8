package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Business;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BusinessDropDownRes {

    @JsonProperty("business_id")
    private Integer businessId;

    @JsonProperty("business_code")
    private String businessCode;

    @JsonProperty("business_name")
    private String businessName;

    private ECommonStatus status;

    public static BusinessDropDownRes valueOf(Business business) {
        BusinessDropDownRes res = new BusinessDropDownRes();
        res.setBusinessId(business.getId());
        res.setBusinessCode(business.getCode());
        res.setBusinessName(business.getName());
        res.setStatus(business.getStatus());
        return res;
    }
}