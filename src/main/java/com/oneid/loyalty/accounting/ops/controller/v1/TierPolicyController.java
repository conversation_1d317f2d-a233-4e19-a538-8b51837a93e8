package com.oneid.loyalty.accounting.ops.controller.v1;

import com.oneid.loyalty.accounting.ops.constant.PageConstant;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierOfPolicyRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPolicyRes;
import com.oneid.loyalty.accounting.ops.service.OpsTierPolicyService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.acl.Authorize;
import com.oneid.loyalty.accounting.ops.support.web.acl.AuthorizeGroup;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("v1/tier-policies")
public class TierPolicyController extends BaseController {

    @Autowired
    private OpsTierPolicyService opsTierPolicyService;

    @GetMapping("/requests/available")
    @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getAvailable(
            @RequestParam(value = "business_id") Integer businessId,
            @RequestParam(value = "program_id", required = false) Integer programId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "status", required = false) ECommonStatus status,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        FilterTierPolicyReq searchRequest = FilterTierPolicyReq.builder()
                .businessId(businessId)
                .programId(programId)
                .code(code)
                .status(status)
                .build();
        Page<TierPolicyRes> page = opsTierPolicyService.getAvailable(
                searchRequest,
                new OffsetBasedPageRequest(offset, limit, Sort.by(Sort.Direction.DESC, "id"))
        );
        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/available/{id}")
    @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getAvailableDetail(@PathVariable("id") Integer tierPolicyId) {
        TierPolicyRes availableDetail = opsTierPolicyService.getAvailableDetail(tierPolicyId);
        return success(availableDetail);
    }

    @PostMapping("/requests/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.CREATE}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    ResponseEntity<?> create(@Valid @RequestBody CreateTierPolicyReq req) {
        opsTierPolicyService.create(req);
        return success(null);
    }

    @PutMapping("/requests/{id}/request-approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.EDIT}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.MAKER_ROLE})
    })
    ResponseEntity<?> update(
            @PathVariable("id") Integer tierPolicyId,
            @Valid @RequestBody UpdateTierPolicyReq req
    ) {
        opsTierPolicyService.update(tierPolicyId, req);
        return success(null);
    }

    @PostMapping("/requests/approval")
    @AuthorizeGroup(authorize = {
            @Authorize(role = AccessRole.PROGRAM, permissions = {AccessPermission.VIEW}),
            @Authorize(role = AccessRole.MAKER_CHECKER, permissions = {AccessPermission.CHECKER_ROLE})
    })
    public ResponseEntity<?> approve(@Valid @RequestBody ApprovalReq req) {
        opsTierPolicyService.approve(req);
        return success(null);
    }

    @GetMapping("/requests/in-review")
    @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.VIEW})
    public ResponseEntity<?> getInReviews(
            @RequestParam(value = "approval_status", required = false) EApprovalStatus approvalStatus,
            @RequestParam(value = "created_start", required = false) String fromCreatedAt,
            @RequestParam(value = "created_end", required = false) String toCreatedAt,
            @RequestParam(value = "approved_start", required = false) String fromReviewedAt,
            @RequestParam(value = "approved_end", required = false) String toReviewedAt,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "approved_by", required = false) String reviewedBy,
            @RequestParam(value = PageConstant.OFFSET_PARAM, defaultValue = PageConstant.DEFAULT_OFFSET) Integer offset,
            @RequestParam(value = PageConstant.LIMIT_PARAM, defaultValue = PageConstant.DEFAULT_LIMIT) Integer limit
    ) {
        Page<TierPolicyRes> page = opsTierPolicyService.getInReviews(
                approvalStatus, fromCreatedAt, toCreatedAt,
                fromReviewedAt, toReviewedAt, createdBy, reviewedBy,
                offset, limit
        );

        return success(page.getContent(), offset, limit, (int) page.getTotalElements());
    }

    @GetMapping("/requests/in-review/{review_id}")
    @Authorize(role = AccessRole.TIER_POLICY, permissions = {AccessPermission.VIEW})
    ResponseEntity<?> getInReviewDetail(@PathVariable("review_id") Integer reviewId) {
        TierPolicyRes reviewDetail = opsTierPolicyService.getInReviewDetail(reviewId);
        return success(reviewDetail);
    }

    @GetMapping("/counter-available")
    ResponseEntity<?> getCounterAvailable(
            @RequestParam("business_id") Integer businessId,
            @RequestParam("period") ECounterPeriod period
    ) {
        List<ShortEntityRes> counterAvailable = opsTierPolicyService.getCounterAvailable(businessId, period);
        return success(counterAvailable);
    }

    @GetMapping("/{id}/tiers")
    ResponseEntity<?> getTierByRequestId(@PathVariable("id") Integer tierPolicyId) {
        List<TierOfPolicyRes> programTiers = opsTierPolicyService.getTiersByRequestId(tierPolicyId);
        return success(programTiers);
    }
}