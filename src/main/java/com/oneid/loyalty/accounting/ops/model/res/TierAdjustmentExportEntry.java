package com.oneid.loyalty.accounting.ops.model.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TierAdjustmentExportEntry implements Serializable {
    private static final long serialVersionUID = 5664879498837975241L;
    private Integer id;
    private String businessCode;
    private String programCode;
    private String memberCode;
    private String idType;
    private String idNo;
    private String initialTier;
    private String adjustmentTier;
    private String reason;
    private String description;
    private String updatedBy;
    private String status;
}



