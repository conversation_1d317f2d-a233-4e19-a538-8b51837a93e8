package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonDeserialize(builder = CardTransferringPayloadChangeRequestReq.CardTransferringPayloadChangeRequestReqBuilder.class)
public class CardTransferringPayloadChangeRequestReq {
    @JsonProperty("card_transfer_id")
    private Integer cardTransferId;

    @JsonPOJOBuilder(withPrefix = "")
    public static class CardTransferringPayloadChangeRequestReqBuilder {
    }
}