package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.entity.Store;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class StoreEnumAll {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    public static StoreEnumAll of(Store store) {
        return new StoreEnumAll(
                store.getId(),
                store.getCode(),
                store.getName(),
                store.getStatus().getValue()
        );
    }
}
