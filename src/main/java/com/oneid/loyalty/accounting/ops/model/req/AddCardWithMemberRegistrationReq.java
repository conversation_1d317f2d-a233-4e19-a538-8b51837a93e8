package com.oneid.loyalty.accounting.ops.model.req;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@EqualsAndHashCode
public class AddCardWithMemberRegistrationReq {
    @NotNull(message = "Card type not null")
    private Integer cardTypeId;

    @NotBlank(message = "Card no is not null")
    @Size(min = 1, max = 24)
    private String cardNo;
}