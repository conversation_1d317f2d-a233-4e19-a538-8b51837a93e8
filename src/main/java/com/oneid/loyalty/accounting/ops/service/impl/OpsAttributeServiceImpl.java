package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataTypeDisplay;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.dto.AttributeMasterDataExcelDTO;
import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.AttributeMasterDataVerifyReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.AttributeServiceRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramTransactionAttributeRes;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.service.OpsAttributeService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.util.excel.entry.EntryContext;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EAttributeType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.AttributeMasterData;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Counter;
import com.oneid.oneloyalty.common.entity.Limitation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramAttribute;
import com.oneid.oneloyalty.common.entity.ProgramTier;
import com.oneid.oneloyalty.common.entity.ProgramTransactionAttribute;
import com.oneid.oneloyalty.common.entity.Rule;
import com.oneid.oneloyalty.common.entity.RuleCondition;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRuleCondition;
import com.oneid.oneloyalty.common.entity.SystemAttribute;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.ProgramAttributeRepository;
import com.oneid.oneloyalty.common.repository.ProgramTransactionAttributeRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRepository;
import com.oneid.oneloyalty.common.repository.SystemAttributeRequestRepository;
import com.oneid.oneloyalty.common.service.AttributeMasterDataService;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CounterService;
import com.oneid.oneloyalty.common.service.LimitationService;
import com.oneid.oneloyalty.common.service.ProgramAttributeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.poiji.bind.Poiji;
import com.poiji.exception.PoijiExcelType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class OpsAttributeServiceImpl implements OpsAttributeService {

    private final List<ECommonStatus> VALID_STATUSES = List.of(ECommonStatus.ACTIVE, ECommonStatus.INACTIVE);

    @Autowired
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @Autowired
    private ProgramAttributeService programAttributeService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private OpsCommonExcelService commonExcelService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Autowired
    private AttributeMasterDataService attributeMasterDataService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SystemAttributeRequestRepository systemAttributeRequestRepository;

    @Autowired
    private ProgramTransactionAttributeRepository programTransactionAttributeRepository;

    @Autowired
    private ProgramAttributeRepository programAttributeRepository;

    @Autowired
    private ProgramTierService programTierService;

    @Autowired
    private LimitationService limitationService;

    @Autowired
    private CounterService counterService;

    @Autowired
    private SystemAttributeRepository systemAttributeRepository;

    @Override
    public List<ProgramTransactionAttributeRes> findByProgramId(Integer programId) {
        programService.findActive(programId);

        return programTransactionAttributeService
                .listAllByProgramId(programId)
                .stream()
                .map(item -> ProgramTransactionAttributeRes
                        .builder()
                        .id(item.getId())
                        .attribute(item.getAttribute())
                        .description(item.getDescription())
                        .dataType(item.getDataType())
                        .dataTypeDisplay(item.getDataTypeDisplay())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void createAttribute(AttributeCreateReq req, EMakerCheckerType type) {
        validateAttribute(req);

        if ((req.getEnableValidateMasterData() && req.getMasterDatas().isEmpty())
                || (EAttributeDataTypeDisplay.COMBOBOX.equals(req.getDataTypeDisplay()) && req.getMasterDatas().isEmpty())) {
            throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
        }

        validateMasterDatas(req);

        req.setHavingMasterData(!req.getMasterDatas().isEmpty());

        makerCheckerInternalFeignClient.makerDefault(
                type,
                UUID.randomUUID().toString(),
                req
        );
    }

    @Override
    public ResourceDTO createAttributeWithExcel(AttributeCreateReq req, MultipartFile file, EMakerCheckerType type) throws Exception {
        validateAttribute(req);

        List<AttributeMasterDataExcelDTO> dtos = mappingToExcelDto(file);

        boolean isValid = verifyExcelMasterData(dtos, req.getDataTypeDisplay(), req.getDataType(), req.getRegexValidation());

        if (!isValid) {
            return exportFileExcel(file.getOriginalFilename(), dtos, OPSConstant.VERIFY_ATTRIBUTE);
        }

        List<AttributeCreateReq.MasterData> masterDatas = dtos
                .stream().map(AttributeCreateReq.MasterData::valueOf)
                .collect(Collectors.toList());

        req.setMasterDatas(masterDatas);

        if ((req.getEnableValidateMasterData() && req.getMasterDatas().isEmpty())
                || (EAttributeDataTypeDisplay.COMBOBOX.equals(req.getDataTypeDisplay()) && req.getMasterDatas().isEmpty())) {
            throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
        }

        req.setHavingMasterData(!req.getMasterDatas().isEmpty());

        makerCheckerInternalFeignClient.makerDefault(
                type,
                UUID.randomUUID().toString(),
                req
        );

        return null;
    }

    @Override
    public ResourceDTO verifyAttributeMasterDataExcel(AttributeMasterDataVerifyReq req, MultipartFile file) throws Exception {
        List<AttributeMasterDataExcelDTO> dtos = mappingToExcelDto(file);
        boolean isValid = verifyExcelMasterData(dtos, req.getDataTypeDisplay(), req.getDataType(), req.getRegexValidation());
        return isValid ? null : exportFileExcel(file.getOriginalFilename(), dtos, OPSConstant.VERIFY_ATTRIBUTE);
    }

    @Override
    public AttributeRequestRes getInReviewDetail(Integer reviewId, EMakerCheckerType type) {
        MakerCheckerInternalDataDetailRes detailRes =
                makerCheckerInternalFeignClient.previewDetailDefault(Long.valueOf(reviewId), type);
        return convertPreview(detailRes);
    }

    @Override
    public Page<AttributeRequestRes> getInReviews(EApprovalStatus approvalStatus,
                                                  String fromCreatedAt,
                                                  String toCreatedAt,
                                                  String fromReviewedAt,
                                                  String toReviewedAt,
                                                  String createdBy,
                                                  String reviewedBy,
                                                  Integer offset,
                                                  Integer limit,
                                                  EMakerCheckerType type) {
        MakerCheckerInternalPreviewReq previewReq =
                new MakerCheckerInternalPreviewReq(type.getType(),
                        null,
                        approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.unmodifiableList(new ArrayList<>(Arrays.asList(EApprovalStatus.PENDING.getValue(), EApprovalStatus.REJECTED.getValue()))),
                        createdBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromCreatedAt, toCreatedAt),
                        reviewedBy,
                        MakerCheckerInternalPreviewReq.RangeDateReq.valueOf(fromReviewedAt, toReviewedAt)
                );

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, offset, limit);

        List<AttributeRequestRes> attributeRequestRes = previewRes.getData().stream()
                .map(this::convertPreview)
                .collect(Collectors.toList());

        return new PageImpl<>(attributeRequestRes, new OffsetBasedPageRequest(offset, limit), previewRes.getMeta().getTotal());
    }

    private AttributeRequestRes convertPreview(MakerCheckerInternalDataDetailRes data) {
        AttributeCreateReq req = this.jsonMapper.convertValue(data.getPayload(), AttributeCreateReq.class);
        Program program = programService.findById(req.getProgramId());
        Business business = businessService.findById(req.getBusinessId());

        return AttributeRequestRes.builder()
                .id(data.getId().intValue())
                .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                .code(req.getCode())
                .name(req.getName())
                .dataType(req.getDataType().name())
                .dataTypeDisplay(req.getDataTypeDisplay().name())
                .operators(CollectionUtils.isEmpty(req.getOperators()) ? null :
                        req.getOperators().stream().map(EAttributeOperator::getExpression).collect(Collectors.toList()))
                .regexValidation(req.getRegexValidation())
                .status(req.getStatus())
                .approvalStatus(EApprovalStatus.of(data.getStatus()))
                .createdAt(data.getMadeDate() != null ? Date.from(ZonedDateTime.parse(data.getMadeDate()).toInstant()) : null)
                .createdBy(data.getMadeByUserName())
                .lastUpdateAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .lastUpdateBy(data.getCheckedByUserName())
                .masterDatas(req.getMasterDatas())
                .reason(data.getComment())
                .enableValidateMasterData(req.getEnableValidateMasterData())
                .havingMasterData(req.getHavingMasterData())
                .reviewedAt(data.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(data.getCheckedDate()).toInstant()) : null)
                .reviewedBy(data.getCheckedByUserName())
                .build();
    }

    @Override
    @Transactional
    public void approveRequest(ApprovalReq req, EMakerCheckerType type) {
        MakerCheckerInternalDataDetailRes detailResData = makerCheckerInternalFeignClient.previewChecker(req.getId(), type);
        if (EApprovalStatus.APPROVED.equals(req.getStatus())) {
            if (Objects.nonNull(detailResData) && Objects.nonNull(detailResData.getPayload())) {
                processCreate(detailResData, type);
            }
        }
        makerCheckerInternalFeignClient.checkerDefault(req);
    }

    @Override
    public List<AttributeCombobox> getMasterData(Integer programId, String attributeCode, EAttributeType type) {
        return attributeMasterDataService.getMasterData(programId, attributeCode, type, ECommonStatus.ACTIVE)
                .stream()
                .map(
                        c -> AttributeCombobox.builder()
                                .name("")
                                .value(c.getValue())
                                .checksumKeys(Collections.singletonList(programId))
                                .build()
                ).collect(Collectors.toList());
    }

    @Override
    public AttributeRequestRes getAvailableDetail(Integer id, EAttributeType type) {
        List<AttributeServiceRes> serviceResList = getAttributeService(id, type);

        AttributeRequestRes res;

        switch (type) {
            case PROGRAM_TRANSACTION: {
                ProgramTransactionAttribute attribute = programTransactionAttributeService.findById(id);
                Program program = programService.findById(attribute.getProgramId());
                Business business = businessService.findById(program.getBusinessId());
                res = AttributeRequestRes.builder()
                        .id(attribute.getId())
                        .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                        .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                        .code(attribute.getAttribute())
                        .name(attribute.getName())
                        .dataType(attribute.getDataType())
                        .dataTypeDisplay(attribute.getDataTypeDisplay())
                        .operators(CollectionUtils.isEmpty(attribute.getOperators()) ? null :
                                attribute.getOperators())
                        .regexValidation(attribute.getValueValidationPattern())
                        .status(attribute.getStatus())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .createdAt(attribute.getCreatedAt())
                        .createdBy(attribute.getCreatedBy())
                        .lastUpdateAt(attribute.getUpdatedAt())
                        .lastUpdateBy(attribute.getUpdatedBy())
                        .reviewedAt(attribute.getApprovedAt())
                        .reviewedBy(attribute.getApprovedBy())
                        .enableValidateMasterData(attribute.getEnableValidateMasterData())
                        .masterDatas(this.getMasterData(attribute.getAttribute(), EAttributeType.PROGRAM_TRANSACTION))
                        .serviceApplied(!serviceResList.isEmpty())
                        .build();
                break;
            }
            default: {
                ProgramAttribute attribute = programAttributeService.findById(id);
                Program program = programService.findById(attribute.getProgramId());
                Business business = businessService.findById(program.getBusinessId());
                res = AttributeRequestRes.builder()
                        .id(attribute.getId())
                        .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                        .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                        .code(attribute.getCode())
                        .name(attribute.getName())
                        .dataType(attribute.getDataType())
                        .dataTypeDisplay(attribute.getDataTypeDisplay())
                        .operators(CollectionUtils.isEmpty(attribute.getOperators()) ? null :
                                attribute.getOperators())
                        .regexValidation(attribute.getValueValidationPattern())
                        .status(attribute.getStatus())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .createdAt(attribute.getCreatedAt())
                        .createdBy(attribute.getCreatedBy())
                        .lastUpdateAt(attribute.getUpdatedAt())
                        .lastUpdateBy(attribute.getUpdatedBy())
                        .reviewedAt(attribute.getApprovedAt())
                        .reviewedBy(attribute.getApprovedBy())
                        .masterDatas(this.getMasterData(attribute.getCode(), EAttributeType.MEMBER))
                        .enableValidateMasterData(attribute.isEnableValidateMasterData() ? Boolean.TRUE : Boolean.FALSE)
                        .serviceApplied(!serviceResList.isEmpty())
                        .build();
                break;
            }
        }
        return res;
    }

    private List<AttributeCreateReq.MasterData> getMasterData(String code, EAttributeType type) {
        return attributeMasterDataService
                .findByCodeAndType(code, type)
                .stream().map(value -> {
                    AttributeCreateReq.MasterData masterData = new AttributeCreateReq.MasterData();
                    masterData.setValue(value.getValue());
                    masterData.setDescription(value.getDescription());
                    masterData.setStatus(value.getStatus());
                    return masterData;
                }).collect(Collectors.toList());
    }

    @Override
    public ResourceDTO exportMasterData(Integer id, EAttributeType type) {
        String code;
        switch (type) {
            case PROGRAM_TRANSACTION: {
                ProgramTransactionAttribute attribute = programTransactionAttributeService.findById(id);
                code = attribute.getAttribute();
                break;
            }
            default: {
                ProgramAttribute attribute = programAttributeService.findById(id);
                code = attribute.getCode();
                break;
            }
        }

        List<AttributeMasterDataExcelDTO> dtos = attributeMasterDataService
                .findByCodeAndType(code, type)
                .parallelStream()
                .map((value) -> {
                    AttributeMasterDataExcelDTO dto = new AttributeMasterDataExcelDTO();
                    dto.setValue(value.getValue());
                    dto.setDescription(value.getDescription());
                    dto.setAttributeStatus(value.getStatus().getValue());
                    return dto;
                }).collect(Collectors.toList());
        String fileName = String.format(OPSConstant.FILE_NAME_EXPORT_ATTRIBUTE, type.name(), code);
        return this.exportFileExcel(fileName, dtos, OPSConstant.AVAILABlE_ATTRIBUTE);
    }

    @Override
    public ResourceDTO exportInReviewMasterData(Integer id, EMakerCheckerType type) {
        MakerCheckerInternalDataDetailRes detailRes =
                makerCheckerInternalFeignClient.previewDetailDefault(Long.valueOf(id), type);
        AttributeCreateReq req = this.jsonMapper.convertValue(detailRes.getPayload(), AttributeCreateReq.class);
        List<AttributeMasterDataExcelDTO> dtos = req.getMasterDatas().parallelStream()
                .map((value) -> {
                    AttributeMasterDataExcelDTO dto = new AttributeMasterDataExcelDTO();
                    dto.setValue(value.getValue());
                    dto.setDescription(value.getDescription());
                    dto.setAttributeStatus(value.getStatus().getValue());
                    return dto;
                }).collect(Collectors.toList());
        String fileName = String.format(OPSConstant.FILE_NAME_EXPORT_ATTRIBUTE, type.name(), req.getCode());
        return this.exportFileExcel(fileName, dtos, OPSConstant.AVAILABlE_ATTRIBUTE);
    }

    @Override
    public Page<AttributeRequestRes> getTransactionAvailable(Integer businessId,
                                                             Integer programId,
                                                             String code,
                                                             String name,
                                                             Boolean havingMasterData,
                                                             Date createdStart,
                                                             Date createdEnd,
                                                             ECommonStatus status,
                                                             Pageable pageable) {
        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        Program program = programService.findById(programId);
        if (!program.getBusinessId().equals(businessId)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }

        EBoolean havingMasterDataEBoolean = null;
        if (Objects.nonNull(havingMasterData)) {
            if (havingMasterData.equals(Boolean.TRUE)) {
                havingMasterDataEBoolean = EBoolean.YES;
            } else {
                havingMasterDataEBoolean = EBoolean.NO;
            }
        }

        Page<Object[]> res = programTransactionAttributeRepository.filter(
                programId,
                code,
                name,
                havingMasterDataEBoolean,
                createdStart,
                createdEnd,
                status,
                pageable
        );

        return new PageImpl<>(mapToDTO(res.getContent(), EAttributeType.PROGRAM_TRANSACTION, businessId), pageable, res.getTotalElements());
    }

    @Override
    public Page<AttributeRequestRes> getMemberAvailable(Integer businessId,
                                                        Integer programId,
                                                        String code,
                                                        String name,
                                                        Boolean havingMasterData,
                                                        Date createdStart,
                                                        Date createdEnd,
                                                        ECommonStatus status,
                                                        Pageable pageable) {

        createdEnd = createdEnd != null ? new Date(createdEnd.getTime() + OPSConstant.TIMES_OF_DAY) : null;
        Program program = programService.findById(programId);
        if (!program.getBusinessId().equals(businessId)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }

        EBoolean havingMasterDataEBoolean = null;
        if (Objects.nonNull(havingMasterData)) {
            if (havingMasterData.equals(Boolean.TRUE)) {
                havingMasterDataEBoolean = EBoolean.YES;
            } else {
                havingMasterDataEBoolean = EBoolean.NO;
            }
        }

        Page<Object[]> res = programAttributeRepository.filter(
                programId,
                code,
                name,
                havingMasterDataEBoolean,
                createdStart,
                createdEnd,
                status,
                pageable
        );

        return new PageImpl<>(mapToDTO(res.getContent(), EAttributeType.MEMBER, businessId), pageable, res.getTotalElements());
    }

    @Override
    public List<AttributeServiceRes> getAttributeService(Integer id, EAttributeType type) {
        String code;
        switch (type) {
            case PROGRAM_TRANSACTION: {
                ProgramTransactionAttribute attribute = programTransactionAttributeService.findById(id);
                code = attribute.getAttribute();
                break;
            }
            default: {
                ProgramAttribute attribute = programAttributeService.findById(id);
                code = attribute.getCode();
                break;
            }
        }

        // Add scheme service
        List<Object[]> test = attributeMasterDataService
                .filterSchemeServiceByAttributeCodeAndDateBetweenStartDateAndEndDate(code, new Date(),
                        ECommonStatus.ACTIVE, ECommonStatus.ACTIVE);

        List<AttributeServiceRes> serviceResList = attributeMasterDataService
                .filterSchemeServiceByAttributeCodeAndDateBetweenStartDateAndEndDate(code, new Date(),
                        ECommonStatus.ACTIVE, ECommonStatus.ACTIVE)
                .stream()
                .filter(objects -> Objects.nonNull(objects[0]) && Objects.nonNull(objects[1]))
                .map(objects -> {
                    Scheme scheme = (Scheme) objects[0];
                    SchemeRuleCondition schemeRuleCondition = (SchemeRuleCondition) objects[1];
                    return AttributeServiceRes.builder()
                            .serviceType(EServiceType.SCHEME)
                            .code(new ShortEntityRes(scheme.getId(), scheme.getName(), scheme.getCode()))
                            .operator(schemeRuleCondition.getOperator())
                            .values(Arrays.asList(schemeRuleCondition.getValue().split("\\|", -1)))
                            .startDate(scheme.getStartDate())
                            .endDate(scheme.getEndDate())
                            .build();
                }).collect(Collectors.toList());
        // Add Rule&Condition service
        List<AttributeServiceRes> serviceResListTem = attributeMasterDataService
                .filterRuleConditionServiceByAttributeCodeAndDateBetweenStartDateAndEndDate(code,
                        new Date(), ECommonStatus.ACTIVE, ECommonStatus.ACTIVE)
                .stream()
                .filter(objects -> Objects.nonNull(objects[0]) && Objects.nonNull(objects[1]))
                .map(objects -> {
                    Rule rule = (Rule) objects[0];
                    RuleCondition ruleCondition = (RuleCondition) objects[1];

                    ShortEntityRes shortEntityRes = this.createShortEntityResByServiceType(rule.getProgramId(),
                            rule.getServiceType(), rule.getServiceCode());
                    return Objects.isNull(shortEntityRes) ? null : AttributeServiceRes.builder()
                            .serviceType(rule.getServiceType())
                            .code(shortEntityRes)
                            .operator(ruleCondition.getOperator())
                            .values(Arrays.asList(ruleCondition.getValue().split("\\|", -1)))
                            .startDate(rule.getStartDate())
                            .endDate(rule.getEndDate())
                            .build();
                })
                .filter(value -> Objects.nonNull(value))
                .collect(Collectors.toList());
        serviceResList.addAll(serviceResListTem);
        return serviceResList;
    }

    private ShortEntityRes createShortEntityResByServiceType(Integer programId, EServiceType serviceType, String serviceCode) {
        ShortEntityRes res = new ShortEntityRes();
        try {
            switch (serviceType) {
                case TIER: {
                    ProgramTier programTier = programTierService.findActive(programId, serviceCode);
                    res.setId(programTier.getId());
                    res.setName(programTier.getName());
                    res.setCode(programTier.getTierCode());
                    break;
                }
                case COUNTER: {
                    Counter counter = counterService.findActive(programId, serviceCode);
                    res.setId(counter.getId());
                    res.setName(counter.getName());
                    res.setCode(counter.getCode());
                    break;
                }
                default: {
                    // Default is Limitation
                    Limitation limitation = limitationService.findActive(programId, serviceCode);
                    res.setId(limitation.getId());
                    res.setName(limitation.getName());
                    res.setCode(limitation.getCode());
                    break;
                }
            }
        } catch (BusinessException e) {
            Log.warn(LogData.createLogData()
                    .append("msg", "Get service type error")
                    .append("exception", e.getMessage())
            );
            return null;
        }
        return res;
    }

    private void processCreate(MakerCheckerInternalDataDetailRes request, EMakerCheckerType type) {
        AttributeCreateReq payload = this.jsonMapper.convertValue(request.getPayload(), AttributeCreateReq.class);

        List<String> operators = payload.getOperators().stream()
                .map(i -> i.getExpression()).collect(Collectors.toList());
        List<AttributeMasterData> masterDataList = new ArrayList<>();

        String createdBy;
        String updatedBy;

        if (EMakerCheckerType.MEMBER_ATTRIBUTE.equals(type)) {
            checkExistAttribute(payload.getProgramId(), payload.getCode());
            ProgramAttribute entity = new ProgramAttribute();
            entity.setProgramId(payload.getProgramId());
            entity.setCode(payload.getCode());
            entity.setName(payload.getName());
            entity.setValueValidationPattern(payload.getRegexValidation());
            entity.setDataType(payload.getDataType().name());
            entity.setDataTypeDisplay(payload.getDataTypeDisplay().name());
            entity.setOperators(operators);
            entity.setStatus(payload.getStatus());
            entity.setEnableValidateMasterData(Boolean.TRUE.equals(payload.getEnableValidateMasterData()));

            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(request.getMadeByUserName());
            updatedBy = createdBy = request.getMadeByUserName();
            opsReqPendingValidator.updateInfoChecker(entity, request.getMadeDate(), createdBy, updatedBy, approvedBy);
            createMasterDataList(masterDataList, payload, EAttributeType.MEMBER);

            entity.setHavingMasterData(masterDataList.isEmpty() ? EBoolean.NO : EBoolean.YES);

            programAttributeService.save(entity);
        } else {
            checkExistAttribute(payload.getProgramId(), payload.getCode());
            ProgramTransactionAttribute entity = new ProgramTransactionAttribute();
            entity.setProgramId(payload.getProgramId());
            entity.setAttribute(payload.getCode());
            entity.setName(payload.getName());
            entity.setValueValidationPattern(payload.getRegexValidation());
            entity.setDataType(payload.getDataType().name());
            entity.setDataTypeDisplay(payload.getDataTypeDisplay().name());
            entity.setOperators(operators);
            entity.setStatus(payload.getStatus());
            entity.setEnableValidateMasterData(payload.getEnableValidateMasterData());

            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(request.getMadeByUserName());
            updatedBy = createdBy = request.getMadeByUserName();
            opsReqPendingValidator.updateInfoChecker(entity, request.getMadeDate(), createdBy, updatedBy, approvedBy);
            createMasterDataList(masterDataList, payload, EAttributeType.PROGRAM_TRANSACTION);

            entity.setHavingMasterData(masterDataList.isEmpty() ? EBoolean.NO : EBoolean.YES);

            programTransactionAttributeService.save(entity);
        }
        if (CollectionUtils.isNotEmpty(masterDataList)) {
            attributeMasterDataService.saveAll(masterDataList);
        }
    }

    private void checkExistAttribute(Integer programId, String code) {

        //PROGRAM ATTRIBUTE
        ProgramAttribute entity = programAttributeService.findByProgramIdAndAttribute(programId, code);
        if (Objects.nonNull(entity)) {
            throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_CODE_EXISTED);
        }
        //PROGRAM TRANSACTION ATTRIBUTE
        programTransactionAttributeService.findByProgramIdAndAttribute(programId, code)
                .ifPresent((value) -> {
                    throw new BusinessException(ErrorCode.PROGRAM_TRANSACTION_ATTRIBUTE_CODE_EXISTED);
                });
        // SYSTEM
        List<SystemAttribute> systemAttributes = systemAttributeRepository.findByAttributeIn(List.of(code));
        if (CollectionUtils.isNotEmpty(systemAttributes)) {
            throw new BusinessException(ErrorCode.SYSTEM_ATTRIBUTE_EXISTED);
        }
    }

    private void createMasterDataList(List<AttributeMasterData> masterDataList,
                                      AttributeCreateReq payload,
                                      EAttributeType attributeType
    ) {
        for (AttributeCreateReq.MasterData masterData : payload.getMasterDatas()) {
            AttributeMasterData attributeMasterData = new AttributeMasterData();
            attributeMasterData.setProgramId(payload.getProgramId());
            attributeMasterData.setAttributeCode(payload.getCode());
            attributeMasterData.setAttributeType(attributeType);
            attributeMasterData.setValue(masterData.getValue());
            attributeMasterData.setStatus(masterData.getStatus());
            attributeMasterData.setDescription(masterData.getDescription());
            masterDataList.add(attributeMasterData);
        }
    }

    private List<AttributeMasterDataExcelDTO> mappingToExcelDto(MultipartFile file) throws Exception {
        return Poiji.fromExcel(file.getInputStream(), PoijiExcelType.XLSX, AttributeMasterDataExcelDTO.class);
    }

    private boolean verifyExcelMasterData(List<AttributeMasterDataExcelDTO> dtos,
                                          EAttributeDataTypeDisplay dataTypeDisplay,
                                          EAttributeDataType dataType,
                                          String regex) {
        if (dtos.isEmpty() || dtos.size() > 10000) {
            return false;
        }

        Map<String, Integer> indexMapByValue = new HashMap<>();

        for (AttributeMasterDataExcelDTO dto : dtos) {
            if (EAttributeDataTypeDisplay.DATE.equals(dataTypeDisplay)) {
                String format = "dd/MM/yyyy";
                DateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false);
                try {
                    indexMapByValue.computeIfAbsent(String.valueOf(sdf.parse(dto.getValue())), k -> dto.getRowIndex());
                } catch (ParseException e) {
                    indexMapByValue.computeIfAbsent(dto.getValue(), k -> dto.getRowIndex());
                }
                dto.setFormat(format);
            } else if (EAttributeDataTypeDisplay.DATE_TIME.equals(dataTypeDisplay)) {
                String format = "dd/MM/yyyy HH:mm:ss";
                DateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false);
                try {
                    indexMapByValue.computeIfAbsent(String.valueOf(sdf.parse(dto.getValue())), k -> dto.getRowIndex());
                } catch (ParseException e) {
                    indexMapByValue.computeIfAbsent(dto.getValue(), k -> dto.getRowIndex());
                }
                dto.setFormat(format);
            } else {
                indexMapByValue.computeIfAbsent(dto.getValue(), k -> dto.getRowIndex());
            }
        }

        Set<Boolean> result = dtos
                .parallelStream()
                .map(dto -> verifyRow(dto, dataTypeDisplay, dataType, indexMapByValue, regex))
                .collect(Collectors.toSet());

        return !result.contains(false);
    }

    private boolean verifyRow(AttributeMasterDataExcelDTO dto,
                              EAttributeDataTypeDisplay dataTypeDisplay,
                              EAttributeDataType dataType,
                              Map<String, Integer> indexMapByValue,
                              String regex) {
        try {
            dto.setIsValid(true);

            if (!dataTypeDisplay.hasDataType(dataType)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, String.format("The data type '%s' is not available for this data type display", dataType), null);
            }

            if (StringUtils.isBlank(dto.getValue())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Value must not be blank", null);
            }

            if (StringUtils.isBlank(dto.getAttributeStatus())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Status must not be blank", null);
            }

            ECommonStatus status = ECommonStatus.of(dto.getAttributeStatus());

            if (Objects.isNull(status)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Status mismatch", null);
            }

            if (!VALID_STATUSES.contains(status)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Status is not valid", null);
            }

            if (Objects.nonNull(dto.getValue()) && dto.getValue().length() > 64) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Value length must less than 65", null);
            }

            if (Objects.nonNull(dto.getDescription()) && dto.getDescription().length() > 255) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Description length must less than 256", null);
            }

            if (!isValidMasterDataValue(dto.getValue(), dataTypeDisplay, dataType)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "Value is not valid", null);
            }

            if (dto.getFormat() == null) {
                Integer index = indexMapByValue.get(dto.getValue());
                if (dto.getRowIndex() != index) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, String.format("Duplicated with row %s", index + 1), null);
                }
            } else {
                DateFormat sdf = new SimpleDateFormat(dto.getFormat());
                sdf.setLenient(false);
                try {
                    Integer index = indexMapByValue.get(String.valueOf(sdf.parse(dto.getValue())));
                    if (dto.getRowIndex() != index) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, String.format("Duplicated with row %s", index + 1), null);
                    }
                } catch (ParseException e) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "Value is not valid", null);
                }
            }

            if (Objects.nonNull(regex) && !regex.isEmpty()) {
                Pattern p = Pattern.compile(regex);
                Matcher m = p.matcher(dto.getValue());
                if (!m.matches()) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "Value mismatch with regex", null);
                }
            }
        } catch (Exception e) {
            dto.setErrorMessage(e.getMessage());
            dto.setIsValid(false);
            dto.setStatus(OPSConstant.INVALID);
        }
        return dto.getIsValid();
    }

    private ResourceDTO exportFileExcel(String fileName, List<AttributeMasterDataExcelDTO> dtos, String objectId) {
        EntryContext context = EntryContext.builder()
                .moduleId(OPSConstant.ATTRIBUTE)
                .objectId(objectId)
                .build();
        return commonExcelService.opsExport(context, fileName, dtos);
    }

    private void validateAttribute(AttributeCreateReq req) {
        businessService.findActive(req.getBusinessId());
        programService.findActive(req.getProgramId());

        Optional<ProgramTransactionAttribute> programTransactionAttributeOptional = programTransactionAttributeService
                .findByProgramIdAndAttribute(req.getProgramId(), req.getCode());

        if (programTransactionAttributeOptional.isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_TRANSACTION_ATTRIBUTE_CODE_EXISTED);
        }

        ProgramAttribute programAttribute = programAttributeService
                .findByProgramIdAndAttribute(req.getProgramId(), req.getCode());

        if (programAttribute != null) {
            throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_CODE_EXISTED);
        }

        systemAttributeRequestRepository.findExistedAttribute(req.getCode()).forEach(
                attributeRequest -> {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_REQUEST_EXISTED, null, null,
                            new Object[]{attributeRequest.getCode(), EAttributeType.SYSTEM});
                }
        );

        if (!req.getDataTypeDisplay().hasDataType(req.getDataType()))
            throw new BusinessException(
                    ErrorCode.ATTRIBUTE_DATA_TYPE_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY,
                    null,
                    null,
                    new Object[]{req.getDataType()}
            );

        for (EAttributeOperator operator : req.getOperators()) {
            if (!req.getDataTypeDisplay().hasOperator(operator)) {
                throw new BusinessException(
                        ErrorCode.ATTRIBUTE_OPERATOR_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY,
                        null,
                        null,
                        new Object[]{operator.getExpression()}
                );
            }
        }

        validationAttCodeExistInOtherReqPending(req.getBusinessId(), req.getProgramId(), req.getCode());
    }

    private void validateMasterDatas(AttributeCreateReq req) {
        Set<String> values = new HashSet<>();

        for (AttributeCreateReq.MasterData masterData : req.getMasterDatas()) {
            if (!VALID_STATUSES.contains(masterData.getStatus())) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_STATUS_NOT_VALID);
            }
            if (!isValidMasterDataValue(masterData.getValue(), req.getDataTypeDisplay(), req.getDataType())) {
                throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
            }
            if (Objects.nonNull(req.getRegexValidation()) && !req.getRegexValidation().isEmpty()) {
                Pattern p = Pattern.compile(req.getRegexValidation());
                Matcher m = p.matcher(masterData.getValue());
                if (!m.matches()) {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
                }
            }
            if (EAttributeDataTypeDisplay.DATE.equals(req.getDataTypeDisplay())) {
                String format = "dd/MM/yyyy";
                DateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false);
                try {
                    values.add(String.valueOf(sdf.parse(masterData.getValue())));
                } catch (ParseException e) {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
                }
            } else if (EAttributeDataTypeDisplay.DATE_TIME.equals(req.getDataTypeDisplay())) {
                String format = "dd/MM/yyyy HH:mm:ss";
                DateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false);
                try {
                    values.add(String.valueOf(sdf.parse(masterData.getValue())));
                } catch (ParseException e) {
                    throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID);
                }
            } else {
                values.add(masterData.getValue());
            }
        }

        if (values.size() < req.getMasterDatas().size()) {
            throw new BusinessException(ErrorCode.ATTRIBUTE_MASTER_DATA_VALUE_DUPLICATED);
        }
    }

    private boolean isValidMasterDataValue(String value,
                                           EAttributeDataTypeDisplay dataTypeDisplay,
                                           EAttributeDataType dataType) {
        boolean isValid = true;

        switch (dataTypeDisplay) {
            case NUMBER: //dataType=LONG
                isValid = isValidNumber(value);
                break;
            case DATE_TIME: //dataType=LONG
                isValid = isValidDateTime(value);
                break;
            case DATE: //dataType=STRING
                isValid = isValidDate(value);
                break;
            case COMBOBOX: //dataType=LONG,STRING
                isValid = !EAttributeDataType.LONG.equals(dataType)
                        || isValidNumber(value);
                break;
            default: //TEXT
        }
        return isValid;
    }

    private boolean isValidDate(String stringDate) {
        try {
            String format = "dd/MM/yyyy";
            if (stringDate.length() > format.length()) {
                return false;
            }
            DateFormat sdf = new SimpleDateFormat(format);
            sdf.setLenient(false);
            sdf.parse(stringDate);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private boolean isValidDateTime(String stringDate) {
        try {
            String format = "dd/MM/yyyy HH:mm:ss";
            if (stringDate.length() > format.length()) {
                return false;
            }
            DateFormat sdf = new SimpleDateFormat(format);
            sdf.setLenient(false);
            sdf.parse(stringDate);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    private boolean isValidNumber(String stringNumber) {
        try {
            long number = Long.parseLong(stringNumber);
            if (number < 0) {
                return false;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    private void validationAttCodeExistInOtherReqPending(Integer businessId, Integer programId, String code) {
        MakerCheckerInternalPreviewReq transactionPreviewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_TRANSACTION_ATTRIBUTE.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        MakerCheckerInternalPreviewReq memberPreviewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.MEMBER_ATTRIBUTE.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> transactionPreviewRes =
                makerCheckerInternalFeignClient.preview(transactionPreviewReq, null, null);

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> memberPreviewRes =
                makerCheckerInternalFeignClient.preview(memberPreviewReq, null, null);

        transactionPreviewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), AttributeCreateReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId) && ele.getCode().equals(code))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.PROGRAM_TRANSACTION_ATTRIBUTE_CODE_EXISTED,
                            "[VALIDATION TRANSACTION ATTRIBUTE CODE] transaction attribute code existed in other requests pending", code, new Object[]{code});
                });

        memberPreviewRes.getData().stream()
                .map(data -> this.objectMapper.convertValue(data.getPayload(), AttributeCreateReq.class))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId) && ele.getCode().equals(code))
                .findAny().ifPresent(ele -> {
                    throw new BusinessException(ErrorCode.PROGRAM_ATTRIBUTE_CODE_EXISTED,
                            "[VALIDATION MEMBER ATTRIBUTE CODE] member attribute code existed in other requests pending", code, new Object[]{code});
                });
    }

    private List<AttributeRequestRes> mapToDTO(List<Object[]> entities, EAttributeType attributeType, Integer businessId) {
        List<AttributeRequestRes> attributeRequestRes = new ArrayList<>();
        Program program = null;
        Business business = businessService.findById(businessId);
        if (EAttributeType.PROGRAM_TRANSACTION.equals(attributeType)) {
            ProgramTransactionAttribute programTransactionAttribute = null;
            for (Object[] a : entities) {
                programTransactionAttribute = (ProgramTransactionAttribute) a[0];
                program = (Program) a[1];

                attributeRequestRes.add(AttributeRequestRes.builder()
                        .id(programTransactionAttribute.getId())
                        .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                        .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                        .code(programTransactionAttribute.getAttribute())
                        .name(programTransactionAttribute.getName())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .dataType(programTransactionAttribute.getDataType())
                        .dataTypeDisplay(programTransactionAttribute.getDataTypeDisplay())
                        .operators(programTransactionAttribute.getOperators())
                        .regexValidation(programTransactionAttribute.getValueValidationPattern())
                        .status(programTransactionAttribute.getStatus())
                        .lastUpdateAt(programTransactionAttribute.getApprovedAt())
                        .lastUpdateBy(programTransactionAttribute.getApprovedBy())
                        .createdAt(programTransactionAttribute.getCreatedAt())
                        .createdBy(programTransactionAttribute.getCreatedBy())
                        .reviewedBy(programTransactionAttribute.getApprovedBy())
                        .reviewedAt(programTransactionAttribute.getApprovedAt())
                        .enableValidateMasterData(EBoolean.YES.equals(programTransactionAttribute.getEnableValidateMasterData()) ? Boolean.TRUE : Boolean.FALSE)
                        .havingMasterData(EBoolean.YES.equals(programTransactionAttribute.getHavingMasterData()) ? Boolean.TRUE : Boolean.FALSE)
                        .build());
            }
        } else {
            ProgramAttribute programAttribute = null;
            for (Object[] a : entities) {
                programAttribute = (ProgramAttribute) a[0];
                program = (Program) a[1];

                attributeRequestRes.add(AttributeRequestRes.builder()
                        .id(programAttribute.getId())
                        .program(new ShortEntityRes(program.getId(), program.getName(), program.getCode()))
                        .business(new ShortEntityRes(business.getId(), business.getName(), business.getCode()))
                        .code(programAttribute.getCode())
                        .name(programAttribute.getName())
                        .approvalStatus(EApprovalStatus.APPROVED)
                        .dataType(programAttribute.getDataType())
                        .dataTypeDisplay(programAttribute.getDataTypeDisplay())
                        .operators(programAttribute.getOperators())
                        .regexValidation(programAttribute.getValueValidationPattern())
                        .status(programAttribute.getStatus())
                        .lastUpdateAt(programAttribute.getApprovedAt())
                        .lastUpdateBy(programAttribute.getApprovedBy())
                        .createdAt(programAttribute.getCreatedAt())
                        .createdBy(programAttribute.getCreatedBy())
                        .reviewedBy(programAttribute.getApprovedBy())
                        .reviewedAt(programAttribute.getApprovedAt())
                        .enableValidateMasterData(programAttribute.isEnableValidateMasterData() ? Boolean.TRUE : Boolean.FALSE)
                        .havingMasterData(EBoolean.YES.equals(programAttribute.getHavingMasterData()) ? Boolean.TRUE : Boolean.FALSE)
                        .build());
            }
        }
        return attributeRequestRes;
    }
}