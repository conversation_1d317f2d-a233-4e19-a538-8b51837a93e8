package com.oneid.loyalty.accounting.ops.kafka.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.util.SimpleDateSerializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Date;

@Getter
@Builder
public class MemberData {
    @JsonProperty("member_profile")
    private Profile profile;

    @JsonProperty("member_product_accounts")
    private List<ProductAccount> productAccounts;

    @JsonProperty("tier_detail")
    private Tier tier;

    @JsonProperty("member_attributes")
    private List<Attribute> attributes;

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Profile {
        private String fullName;
        private String gender;
        private String memberStatus;
        private String registrationDate;
        private String phoneNo;
        private String status;
        private String address;
        private String dob;
        private String email;
        private String firstName;
        private String lastName;
        private String houseNumber;
        private String street;
        private String wardCode;
        private String districtCode;
        private String provinceCode;
        private String countryCode;
        private String homePhone;
        private String officePhone;
        private EIdentifyType identifyType;
        private String identifyNo;
        private String level;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ProductAccount {
        private String productCode;
        private String productAccountCode;
        @JsonSerialize(using = SimpleDateSerializer.class)
        private Date issueDate;
        private ECommonStatus productStatus;
        private Long expiredTime;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Tier {
        private String tierCode;
        private String tierName;
        private String description;
        private String cardBackgroundUrl;
        private String cardThumbnailUrl;
        private String badgeIconUrl;
    }

    @Getter
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Attribute {
        private Long id;
        private Integer programAttributeId;
        private String value;
    }
}
