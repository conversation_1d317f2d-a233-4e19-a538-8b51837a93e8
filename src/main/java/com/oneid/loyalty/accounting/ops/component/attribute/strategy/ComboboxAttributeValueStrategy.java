package com.oneid.loyalty.accounting.ops.component.attribute.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.model.AttributeCombobox;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AttributeMasterDataRepository;
import com.oneid.oneloyalty.common.util.LogData;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

@Component
public abstract class ComboboxAttributeValueStrategy
        extends AttributeValueStrategy<AttributeCombobox> {

    private final ObjectMapper objectMapper;

    public ComboboxAttributeValueStrategy(ObjectMapper objectMapper, AttributeMasterDataRepository attributeMasterDataRepository) {
        super(attributeMasterDataRepository);
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean isApplicable(EAttributeDataDisplayType type) {
        return EAttributeDataDisplayType.COMBOBOX.equals(type);
    }

    public abstract boolean isApplicable(ConditionAttributeDto type);

    @Override
    @SneakyThrows
    public AttributeCombobox serialize(Object value, final Integer... programIds) {
        Map<String, String> map = objectMapper.convertValue(value, Map.class);
        if (!map.containsKey("original_value")) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Original value is missing",
                    LogData.createLogData().append("value", value).append("strategy", "ComboboxAttributeValueStrategy"));
        }
        return AttributeCombobox.builder()
                .value(map.get("value"))
                .originalValue(map.get("original_value"))
                .checksumKeys(Arrays.asList(programIds))
                .build();
    }

    @Override
    protected String getWriteValue(String attribute, AttributeCombobox value, final Integer... programIds) {
        return String.valueOf(value.getValue());
    }
}