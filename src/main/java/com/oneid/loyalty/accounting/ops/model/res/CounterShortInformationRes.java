package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateSerializer;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ECounterAttribute;
import com.oneid.oneloyalty.common.constant.ECounterLevel;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import com.oneid.oneloyalty.common.constant.ECounterType;
import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Getter
@Builder
public class CounterShortInformationRes {
    private Integer id;
    private String name;
    private String code;
    private ECounterType type;
    @JsonSerialize(using = DateSerializer.class)
    private Date startDate;
    @JsonSerialize(using = DateSerializer.class)
    private Date endDate;
    private ECounterPeriod period;
    private ECounterLevel level;
    private ECounterAttribute attribute;
    private Integer numberDayCounterExpire;
    private ECommonStatus status;

    private List<RuleRes> rules;

    private BigDecimal resetValue;
    private String resetType;

}