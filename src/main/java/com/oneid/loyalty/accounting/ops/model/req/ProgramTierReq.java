package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.validation.ASCIICode;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ProgramTierReq implements Serializable {
    private static final long serialVersionUID = 476453653626626514L;

    private Integer id;

    private Integer businessId;

    private Integer programId;

    @Length(max = 100)
    @ASCIICode
    private String code;

    @NotBlank(message = "'name' must not be blank")
    @Length(max = 255)
    private String name;

    @NotBlank(message = "'enName' must not be blank")
    @Length(max = 255)
    private String enName;

    private String description;

    private String enDescription;

    private Integer tierPolicyId;

    @Length(max = 512, message = "'card_background_url' length must be between 1 and 512")
    private String cardBackgroundUrl;

    @Length(max = 512, message = "'badge_icon_url' length must be between 1 and 512")
    private String badgeIconUrl;

    @NotNull(message = "'qualify_point' must not be null")
    private Long qualifyPoint;

    @NotNull(message = "'rank_no' must not be null")
    @Positive(message = "'rank_no' must be greater than 0")
    @Max(value = 999, message = "'rank_no' must be less than <= 999")
    private Integer rankNo;

    @NotNull(message = "'tier_status' must not be null")
    private ECommonStatus tierStatus;

    @Valid
    @NotNull(message = "'rules' must not be null")
    @Size(min = 1, max = 100, message = "'rules' size between 1, 100")
    private List<RuleReq> rules;

    @Valid
    private List<TierPrivilegeReq> privileges;

    private Boolean nonePrivilege;

    private String editKey;

    @AssertTrue(message = "'business_id' must not be null")
    public boolean isValidBusinessId() {
        if (!this.isCreate()) {
            return this.getBusinessId() != null;
        }
        return true;
    }

    @AssertTrue(message = "'program_id' must not be null")
    public boolean isValidProgramId() {
        if (!this.isCreate()) {
            return this.getProgramId() != null;
        }
        return true;
    }

    @AssertTrue(message = "'code' must not be blank")
    public boolean isValidCode() {
        if (!this.isCreate()) {
            return this.getCode() != null;
        }
        return true;
    }

    @AssertTrue(message = "'tier_policy_id' must not be blank")
    public boolean isValidTierPolicyId() {
        if (!this.isCreate()) {
            return this.getTierPolicyId() != null;
        }
        return true;
    }

    @AssertTrue(message = "'edit_key' must not be blank")
    public boolean isValidEditKey() {
        if (!this.isCreate()) {
            return this.getEditKey() != null;
        }
        return true;
    }

    public boolean isCreate() {
        return this.getId() == null;
    }
}
