package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.res.ShortEntityRes;
import com.oneid.loyalty.accounting.ops.model.res.TierOfPolicyRes;
import com.oneid.loyalty.accounting.ops.model.res.TierPolicyRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECounterPeriod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OpsTierPolicyService {
    TierPolicyRes getAvailableDetail(Integer requestId);

    TierPolicyRes getInReviewDetail(Integer reviewId);

    void update(Integer requestId, UpdateTierPolicyReq req);

    void create(CreateTierPolicyReq req);

    void approve(ApprovalReq req);

    List<ShortEntityRes> getCounterAvailable(Integer businessId, ECounterPeriod period);

    Page<TierPolicyRes> getInReviews(EApprovalStatus approvalStatus,
                                     String fromCreatedAt, String toCreatedAt,
                                     String fromReviewedAt, String toReviewedAt,
                                     String createdBy, String reviewedBy,
                                     Integer offset, Integer limit);

    Page<TierPolicyRes> getAvailable(FilterTierPolicyReq req, Pageable offsetBasedPageRequest);

    List<TierOfPolicyRes> getTiersByRequestId(Integer requestId);
}
