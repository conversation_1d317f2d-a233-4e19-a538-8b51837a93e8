package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.res.TierMatchingRequestDetailRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.TierMatchingCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.TierMatchingUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.TierMatchingRequestRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;

public interface OpsTierMatchingRequestService {
    Page<TierMatchingRequestRes> getAvailableTierMatchingRequests(
            Integer businessId,
            Integer matchedProgramId,
            String code,
            String name,
            ECommonStatus status,
            EApprovalStatus approvalStatus,
            Pageable pageable);
    
    Page<TierMatchingRequestRes> getInReviewTierMatchingRequests(
            EApprovalStatus approvalStatus,
            Pageable pageable);
    
    TierMatchingRequestRes requestCreate(TierMatchingCreateReq req);

    TierMatchingRequestDetailRes getAvailableTierMatchingRequestById(Integer requestId);

    TierMatchingRequestDetailRes getInReviewTierMatchingRequestById(Integer reviewId);
    
    TierMatchingRequestDetailRes getChangeableRequest(Integer requestId);
    
    TierMatchingRequestRes requestUpdate(Integer requestId, TierMatchingUpdateReq req);
}
