package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramLevelReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramLevelInfo extends OtherInfo implements Serializable {

    private static final long serialVersionUID = -2511035834782247200L;

    private Integer id;

    private Integer businessId;
    private String businessCode;
    private String businessName;

    private Integer programId;
    private String programCode;
    private String programName;
    private String editKey;

    private Integer rank;
    private String level;
    private String description;
    private String descriptionEn;
    private ECommonStatus status;

    private String approvalStatus;
    private Long requestId;

    public static ProgramLevelInfo valueOf(ProgramLevel programLevel) {
        ProgramLevelInfo result = new ProgramLevelInfo();
        result.setProgramId(programLevel.getProgramId());
        result.setId(programLevel.getId());
        result.setRank(programLevel.getRank());
        result.setLevel(programLevel.getLevel());
        result.setDescription(programLevel.getDescription());
        result.setDescriptionEn(programLevel.getDescriptionEn());

        result.setCreatedAt(programLevel.getCreatedAt());
        result.setUpdatedAt(programLevel.getUpdatedAt());
        result.setApprovedAt(programLevel.getApprovedAt());
        result.setCreatedBy(programLevel.getCreatedBy());
        result.setUpdatedBy(programLevel.getUpdatedBy());
        result.setApprovedBy(programLevel.getApprovedBy());
        return result;
    }

    public static ProgramLevelInfo valueOf(MakerCheckerInternalDataDetailRes res, UpdateProgramLevelReq req) {
        ProgramLevelInfo result = new ProgramLevelInfo();
        result.setProgramId(req.getProgramId());
        result.setRequestId(res.getId());
        result.setId(req.getId());
        result.setRank(req.getRank());
        result.setLevel(req.getLevel());
        result.setDescription(req.getDescription());
        result.setDescriptionEn(req.getDescriptionEn());
        result.setCreatedAt(res.getMadeDate() != null ? Date.from(ZonedDateTime.parse(res.getMadeDate()).toInstant()) : null);
        result.setApprovedAt(res.getCheckedDate() != null ? Date.from(ZonedDateTime.parse(res.getCheckedDate()).toInstant()) : null);
        result.setCreatedBy(res.getMadeByUserName());
        result.setApprovedBy(res.getCheckedByUserName());
        result.setApprovalStatus(res.getStatus());

        return result;
    }

    public void withProgram(Program program) {
        this.programId = program.getId();
        this.programName = program.getName();
        this.programCode = program.getCode();
    }

    public void withBusiness(Business business) {
        this.businessId = business.getId();
        this.businessName = business.getName();
        this.businessCode = business.getCode();
    }
}
