package com.oneid.loyalty.accounting.ops.mapper;

import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.RetentionPolicyReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERetentionExpireType;
import com.oneid.oneloyalty.common.entity.RetentionPolicy;

public class RetentionPolicyMapper {
    public static RetentionPolicy toRetentionPolicyOneFromPoolCreateReq(PoolCreateReq request) {
        RetentionPolicyReq retentionPolicyReq = request.getRetentionPolicy();

        RetentionPolicy result = new RetentionPolicy();
        result.setBusinessId(request.getBusinessId());
        result.setName(request.getName());
        result.setType(ERetentionExpireType.of(retentionPolicyReq.getType()));
        result.setPeriodDay(retentionPolicyReq.getPeriodDay() != null ? retentionPolicyReq.getPeriodDay() : 1);
        result.setStatus(ECommonStatus.of(request.getStatus()));
        return result;
    }

    public static RetentionPolicy toRetentionPolicyOneFromPoolUpdateReq(RetentionPolicy retentionPolicy, PoolUpdateReq request) {
        RetentionPolicyReq retentionPolicyReq = request.getRetentionPolicy();

        retentionPolicy.setName(request.getName() != null ? request.getName() : retentionPolicy.getName());
        retentionPolicy.setType(retentionPolicyReq.getType() != null ? ERetentionExpireType.of(retentionPolicyReq.getType()) : retentionPolicy.getType());
        retentionPolicy.setPeriodDay(retentionPolicyReq.getPeriodDay() != null ? retentionPolicyReq.getPeriodDay() : retentionPolicy.getPeriodDay());
        retentionPolicy.setStatus(request.getStatus() != null ? ECommonStatus.of(request.getStatus()) : retentionPolicy.getStatus());
        return retentionPolicy;
    }
}
