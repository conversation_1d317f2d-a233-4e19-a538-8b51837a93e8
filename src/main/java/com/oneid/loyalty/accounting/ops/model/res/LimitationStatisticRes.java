package com.oneid.loyalty.accounting.ops.model.res;


import com.oneid.oneloyalty.common.constant.ECommonStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class LimitationStatisticRes {
    private Integer counterStatusHeader;

    private CounterStatisticRes.Statistic statistic;

    private List<History> histories;

    private Integer totalHistories;

    @Getter
    @Setter
    @Builder
    public static class History{
        private String code;
        private Date createdAt;
        private BigDecimal totalCounted;
        private BigDecimal threshold;
        private Long reachedThresholdAt;
        private BigDecimal warningThreshold;
        private Long reachedWarningThresholdAt;
        private String unit;
        private ECommonStatus status;
    }
}
