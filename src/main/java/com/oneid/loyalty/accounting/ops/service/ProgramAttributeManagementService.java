package com.oneid.loyalty.accounting.ops.service;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.ProgramAttributeServiceTypeUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.AttributeGroupRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramAttributeServiceTypeRequestRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;

public interface ProgramAttributeManagementService {
    
    boolean isAvailable(Integer programId, EServiceType serviceType);
    
    AttributeGroupRes getAllAttributes(Integer programId);
    
    ProgramAttributeServiceTypeRequestRes requestCreate(ProgramAttributeServiceTypeCreateReq req);
    
    Page<ProgramAttributeServiceTypeRequestRes> getPage(Integer businessId, Integer programId, List<EServiceType> serviceTypes, 
            EApprovalStatus approvalStatus, Pageable pageable);
    
    ProgramAttributeServiceTypeRequestRes getDetail(Integer id);
    
    ProgramAttributeServiceTypeRequestRes getChangeable(Integer id);
    
    ProgramAttributeServiceTypeRequestRes requestChange(ProgramAttributeServiceTypeUpdateReq req);
    
    Page<ProgramAttributeServiceTypeRequestRes> getInReviewPage(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable);
    
    ProgramAttributeServiceTypeRequestRes getInReviewDetail(int reviewId);
    
}
