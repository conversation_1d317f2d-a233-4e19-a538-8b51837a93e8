package com.oneid.loyalty.accounting.ops.feign.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Builder
public class MakerCheckerInternalCheckerReq implements Serializable {
    private static final long serialVersionUID = 571321337149433673L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("status")
    private String status;

    @JsonProperty("comment")
    private String comment;
}