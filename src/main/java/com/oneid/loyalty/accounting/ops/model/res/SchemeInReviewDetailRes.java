package com.oneid.loyalty.accounting.ops.model.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.oneid.loyalty.accounting.ops.model.req.FormulaGroupReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleRecordReq;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import com.oneid.oneloyalty.common.constant.RoundingRule;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SchemeInReviewDetailRes {

    private Integer schemeId;

    private String schemeCode;

    private String schemeName;

    private Integer businessId;

    private String businessName;

    private Integer programId;

    private String programName;

    private EConditionType ruleLogic;

    private String description;

    private ESchemeType schemeType;

    private Integer poolId;

    private String poolName;

    private ECommonStatus status;

    private Long startDate;

    private Long endDate;

    private RoundingRule roundingRule;

    private Long minAmount;

    private Long maxAmount;

    private List<RuleRecordReq> ruleList;

    private FormulaGroupReq formulaGroup;

    private EBoolean isVAT;

    private String editKey;

    private EApprovalStatus approvalStatus;

    private Long createdAt;

    private String createdBy;

    private Long updatedAt;

    private String updatedBy;

    private Long approvedAt;

    private String approvedBy;
}
