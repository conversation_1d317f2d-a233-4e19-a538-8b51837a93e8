package com.oneid.loyalty.accounting.ops.config;

import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

import java.util.Optional;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import com.oneid.loyalty.accounting.ops.support.AuditorAwareImpl;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "persistenceAuditorProvider")
public class PersistenceConfig {

    @Bean
    public AuditorAware<OPSAuthenticatedPrincipal> auditorProvider() {
        return new AuditorAwareImpl();
    }
    
    @Bean("persistenceAuditorProvider")
    public AuditorAware<String> primaryAuditorProvider() {
        return new AuditorAware<String>() {
            @Override
            public Optional<String> getCurrentAuditor() {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                
                OPSAuthenticatedPrincipal principal = (OPSAuthenticatedPrincipal) authentication.getPrincipal();
                
                if(principal != null) {
                    return Optional.of(principal.getUserName());
                }
                
                return Optional.of(null);
            }
        };
        
    }
}
