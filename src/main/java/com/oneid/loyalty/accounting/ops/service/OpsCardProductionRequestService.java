package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.req.CreateMemberCPRReq;
import com.oneid.loyalty.accounting.ops.model.res.CPRExportRes;
import com.oneid.loyalty.accounting.ops.model.res.CardProductionRequestRes;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;

public interface OpsCardProductionRequestService {

    /**
     * Get one card production request.
     */
    CardProductionRequestRes getById(Integer id);
    CPRExportRes exportDetailToSFTP(Integer id);
    
    Long createCPRRequest(OPSAuthenticatedPrincipal principal, CreateMemberCPRReq req);
}
