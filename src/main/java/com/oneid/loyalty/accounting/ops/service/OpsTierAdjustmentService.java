package com.oneid.loyalty.accounting.ops.service;

import com.oneid.loyalty.accounting.ops.model.dto.ResourceDTO;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierAdjustmentReq;
import com.oneid.loyalty.accounting.ops.model.req.MemberTierReq;
import com.oneid.loyalty.accounting.ops.model.res.MemberTierRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentRequestRes;
import com.oneid.loyalty.accounting.ops.model.res.TierAdjustmentStatisticRes;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ETierAdjustmentProcessStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;

public interface OpsTierAdjustmentService {
    MemberTierRes findMemberTier(MemberTierReq req);

    Integer createRequest(CreateTierAdjustmentMemberReq req);

    InputStream verifyFile(CreateTierAdjustmentReq req, MultipartFile multipartFile) throws Exception;

    InputStream createBatchRequest(CreateTierAdjustmentReq req, MultipartFile multipartFile) throws Exception;

    ResourceDTO exportFileAvailableRequest(Integer batchId) throws Exception;

    ResourceDTO exportFileInReviewRequest(Integer batchId) throws Exception;

    TierAdjustmentRequestRes inReviewDetail(Integer inReviewId);

    TierAdjustmentRequestRes availableDetail(Integer requestId);

    Page<TierAdjustmentRequestRes> getInReview(EApprovalStatus approvalStatus, LocalDate fromDate, LocalDate toDate, Pageable pageable);

    Page<TierAdjustmentRequestRes> getAvailableRequest(Integer businessId, Integer programId, String id, EIdType idType, ECommonStatus status, EApprovalStatus approvalStatus, Pageable offsetBasedPageRequest);

    Page<TierAdjustmentRequestRes> getInReviewDetails(Integer batchId, Pageable pageable);

    Page<TierAdjustmentRequestRes> getAvailableDetails(Integer batchId, ETierAdjustmentProcessStatus processStatus, Pageable pageable);

    TierAdjustmentStatisticRes getTierAdjustmentStatisticById(Integer requestId);
}
