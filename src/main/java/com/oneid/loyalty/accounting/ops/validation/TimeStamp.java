package com.oneid.loyalty.accounting.ops.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {TimeStampValidator.class})
public @interface TimeStamp {

    String message() default "Time stamp invalid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}