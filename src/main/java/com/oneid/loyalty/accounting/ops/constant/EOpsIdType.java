package com.oneid.loyalty.accounting.ops.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.constant.EIdType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum EOpsIdType {
    CARD("CARD", EIdType.CARD),
    VD_CARD("VD_CARD", EIdType.VD_CARD),
    MOBILE("MOBILE", EIdType.MOBILE),
    MEMBER_CODE("MEMBER_CODE", EIdType.MEMBER_ID),
    IDENTIFY("IDENTIFY", EIdType.IDENTIFY),
    USER_ID("USER_ID", EIdType.USER_ID),
    PARTNER_CUSTOMER_ID("PARTNER_CUSTOMER_ID", EIdType.PARTNER_CUSTOMER_ID);

    @JsonValue
    private String value;
    
    private EIdType mapping;


    public String getValue() {
        return this.value;
    }

    public EIdType getMapping() {
        return this.mapping;
    }

    public static EOpsIdType lookup(String value) {
        if (StringUtils.isEmpty(value))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    public static EOpsIdType lookup(EIdType eIdType) {
        if (eIdType == null)
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getMapping().equals(eIdType))
                .findFirst()
                .orElse(null);
    }
    
    public static class RequestQueryConverter implements Converter<String, EOpsIdType> {
        @Override
        public EOpsIdType convert(String source) {
            return EOpsIdType.lookup(source);
        }
    }
}