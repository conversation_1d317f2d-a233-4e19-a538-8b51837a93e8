package com.oneid.loyalty.accounting.ops.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.entity.ProgramLevel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Convert;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Getter
@Setter
public class CreateProgramLevelReq {
    @NotNull(message = "Program Id must not be blank")
    @JsonProperty("program_id")
    private Integer programId;

    @NotNull(message = "Rank must not be blank")
    @JsonProperty("rank")
    @Min(0)
    private Integer rank;

    @NotBlank(message = "Level must not be blank")
    @Size(max = 16, message = "Level cannot exceed 16 characters")
    @JsonProperty("level")
    private String level;

    @Size(max = 256, message = "Description cannot exceed 256 characters")
    @JsonProperty("description")
    private String description;

    @Size(max = 256, message = "English description cannot exceed 256 characters")
    @JsonProperty("description_en")
    private String descriptionEn;

//    @Convert(converter = ECommonStatus.Converter.class)
//    @JsonProperty("status")
//    private ECommonStatus status;

    public ProgramLevel toProgramLevel() {
        ProgramLevel level = new ProgramLevel();
        level.setDescription(this.getDescription());
        level.setDescriptionEn(this.getDescriptionEn());
        level.setLevel(this.getLevel());
        level.setRank(this.getRank());
        level.setProgramId(this.getProgramId());
        return level;
    }
}
