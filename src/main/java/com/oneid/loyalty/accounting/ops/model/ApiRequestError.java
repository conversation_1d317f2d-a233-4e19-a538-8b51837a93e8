package com.oneid.loyalty.accounting.ops.model;

import java.util.Collection;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonDeserialize(builder = ApiRequestError.ApiRequestErrorBuilder.class)
public class ApiRequestError {
    private int code;
    private Collection<?> arguments;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class ApiRequestErrorBuilder {
    }
}
