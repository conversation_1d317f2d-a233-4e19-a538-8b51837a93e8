package com.oneid.loyalty.accounting.ops.model.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionExportEntry implements Serializable{

    private static final long serialVersionUID = 3620887706792855056L;
    private String txnRefNo;
    private String invoiceNo;
    private String originalInvoiceNo;
    private String businessCode;
    private String programCode;
    private String corporationCode;
    private String chainCode;
    private String storeCode;
    private String terminalCode;
    private String memberCode;
    private String productAccountType;
    private String productAccountCode;
    private String transactionTime;
    private String transactionType;
    private Long gmv;
    private Long grossAmount;
    private Long nettAmount;
    private Long awardPoint;
    private Long redeemPoint;
    private String poolCode;
    private String currencyCode;
    private Long pointBalanceBefore;
    private Long pointBalanceAfter;
    private Long awardPointBeforeLimit;
    private String awardRetentionTime;
    private String description;
    private String cancellation;
    private String cancellationTime;
    private String cancellationType;
    private String reasonCode;
    private String schemeCode;
    private String channel;
    private String serviceCode;
    private String status;
    private String errorCode;
    private String errorMessage;
}
