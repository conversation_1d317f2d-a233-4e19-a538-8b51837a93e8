package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalCheckerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalMakerReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.MakerCheckerInternalPreviewReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalCheckerRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalDataDetailRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MakerCheckerInternalMakerRes;
import com.oneid.loyalty.accounting.ops.model.APIFeignInternalResponse;
import com.oneid.loyalty.accounting.ops.model.req.ApprovalReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramCorporationReq;
import com.oneid.loyalty.accounting.ops.model.res.CorporationInfo;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationPreviewRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramCorporationRes;
import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
import com.oneid.loyalty.accounting.ops.service.OpsProgramCorporationService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.ProgramCorporationService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class OpsProgramCorporationServiceImpl implements OpsProgramCorporationService {

    @Autowired
    private ProgramService programService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private ProgramCorporationService programCorporationService;

    @Autowired
    private CorporationRepository corporationRepository;

    @Autowired
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @Autowired
    private CorporationService corporationService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private OpsReqPendingValidator opsReqPendingValidator;

    @Override
    public Page<ProgramCorporationRes> searchProgramCorporation(Integer businessId, String programCode,
                                                                ECommonStatus status, Integer offset, Integer limit) {
        // validate program belong business

        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
        SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<>();

        if (ObjectUtils.isNotEmpty(businessId))
            specificationBuilder.add(new SearchCriteria("businessId", businessId, SearchOperation.EQUAL));

        if (ObjectUtils.isNotEmpty(programCode))
            specificationBuilder.add(new SearchCriteria("code", programCode, SearchOperation.EQUAL));

        if (ObjectUtils.isNotEmpty(status))
            specificationBuilder.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        Page<Program> pagePrograms = programService.searchPaging(specificationBuilder, pageRequest);

        List<ProgramCorporationRes> lprogramCorporationRes = new ArrayList<>();
        Set<Integer> lstBusinessId = pagePrograms.getContent().stream().map(p -> p.getBusinessId())
                .collect(Collectors.toSet());
        Map<Integer, Business> mapBusiness = new HashMap<>();
        Iterator<Integer> value = lstBusinessId.iterator();

        while (value.hasNext()) {
            Optional<Business> oBusiness = businessService.find(value.next());
            if (oBusiness.isPresent())
                mapBusiness.put(oBusiness.get().getId(), oBusiness.get());
        }
        Consumer<Program> consumer = program -> {
            Business business = mapBusiness.get(program.getBusinessId());
            ProgramCorporationRes pCorporationRes = ProgramCorporationRes.valueOf(program, business);
            List<ProgramCorporation> lProgramCorporation = programCorporationService.findByProgramId(program.getId());
            pCorporationRes.setCorporations(!lProgramCorporation.isEmpty());
            lprogramCorporationRes.add(pCorporationRes);
        };
        pagePrograms.getContent().stream().forEach(consumer);
        return new PageImpl<>(lprogramCorporationRes, pageRequest, pagePrograms.getTotalElements());
    }

    @Override
    public ProgramCorporationPreviewRes getAvailableProgramCorporationRequest(Integer programId) {
        Program program = programService.find(programId).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );
        List<ProgramCorporation> byProgramId = programCorporationService.findByProgramId(program.getId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .collect(Collectors.toList());
        return getProgramCorporationPreviewRes(program, byProgramId);
    }

    private ProgramCorporationPreviewRes getProgramCorporationPreviewRes(Program program, List<ProgramCorporation> byProgramId) {
        Business business = businessService.find(program.getBusinessId()).orElseThrow(
                () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
        );
        List<Integer> corporationIds = byProgramId
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .map(ProgramCorporation::getCorporationId)
                .collect(Collectors.toList());
        ProgramCorporationPreviewRes result = ProgramCorporationPreviewRes.builder()
                .programId(program.getId())
                .programCode(program.getCode())
                .programName(program.getName())
                .businessId(business.getId())
                .businessCode(business.getCode())
                .businessName(business.getName())
                .corporations(corporationRepository.findAllByIdIn(corporationIds)
                        .stream()
                        .map(CorporationInfo::valueOf)
                        .collect(Collectors.toList()))
                .programStatus(program.getStatus())
                .build();
        String editKey = opsReqPendingValidator.generateEditKey(byProgramId.get(0).getRequestCode(), byProgramId.get(0).getVersion());
        result.setEditKey(editKey);
        byProgramId = byProgramId
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(ele -> Objects.nonNull(ele.getCreatedAt()))
                .sorted(Comparator.comparing(ProgramCorporation::getCreatedAt))
                .collect(Collectors.toList());
        if (!byProgramId.isEmpty()) {
            result.setCreatedAt(byProgramId.get(0).getCreatedAt());
            result.setCreatedBy(byProgramId.get(0).getCreatedBy());
            result.setUpdatedAt(byProgramId.get(byProgramId.size() - 1).getUpdatedAt());
            result.setUpdatedBy(byProgramId.get(byProgramId.size() - 1).getUpdatedBy());
            result.setApprovedAt(byProgramId.get(byProgramId.size() - 1).getApprovedAt());
            result.setApprovedBy(byProgramId.get(byProgramId.size() - 1).getApprovedBy());
        }
        return result;
    }

    @Override
    public ProgramCorporationPreviewRes getInReviewProgramCorporationRequestById(Integer reviewId) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> previewDetailRes =
                makerCheckerInternalFeignClient.previewDetail(Long.valueOf(reviewId));
        if (ErrorCode.SUCCESS.getValue() != previewDetailRes.getMeta().getCode())
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_ID_NOT_FOUND,
                    "Program Corporation - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);

        if (!previewDetailRes.getData().getRequestType().equals(EMakerCheckerType.PROGRAM_CORPORATION.getType())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH,
                    "Program Corporation - request in-review  error: " + previewDetailRes.getMeta().getMessage(), null);
        }
        CreateProgramCorporationReq payload = this.jsonMapper.convertValue(previewDetailRes.getData().getPayload(), CreateProgramCorporationReq.class);
        Program program = programService.find(payload.getProgramId()).orElseThrow(
                () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
        );

        List<CorporationInfo> corporations = new ArrayList<>();
        if (Objects.nonNull(payload.getCorporations())) {
            corporations = corporationRepository.findAllByIdIn(payload.getCorporations())
                    .stream()
                    .map(CorporationInfo::valueOf)
                    .collect(Collectors.toList());
        }
        ProgramCorporationPreviewRes result = ProgramCorporationPreviewRes.builder()
                .programId(program.getId())
                .programCode(program.getCode())
                .businessId(program.getBusinessId())
                .approvalStatus(EApprovalStatus.of(previewDetailRes.getData().getStatus()))
                .corporations(corporations)
                .build();
        result.setCreatedAt(previewDetailRes.getData().getMadeDate() != null ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getMadeDate()).toInstant()) : null);
        result.setCreatedBy(previewDetailRes.getData().getMadeByUserName());
        result.setApprovedAt(previewDetailRes.getData().getCheckedDate() != null ? Date.from(ZonedDateTime.parse(previewDetailRes.getData().getCheckedDate()).toInstant()) : null);
        result.setApprovedBy(previewDetailRes.getData().getCheckedByUserName());
        return result;
    }

    @Override
    public ProgramCorporationPreviewRes getEditAttributeRequestSetting(Integer reviewId) {
        Program program = programService.find(reviewId).orElseThrow(() ->
                new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", LogData.createLogData().append("program_id", reviewId)));
        List<ProgramCorporation> byProgramId = programCorporationService.findByProgramId(program.getId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .collect(Collectors.toList());
        if (byProgramId.isEmpty()) {
            throw new BusinessException(ErrorCode.PROGRAM_IS_NOT_CONFIGURED, null, null);
        }
        opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_CORPORATION.getType(), byProgramId.get(0).getRequestCode());
        return getProgramCorporationPreviewRes(program, byProgramId);
    }

    @Override
    public MakerCheckerInternalMakerRes requestEditingAttributeRequest(Integer id, UpdateProgramCorporationReq req) {
        // validate program
        Program program = programService.find(id).orElseThrow(() ->
                new BusinessException(ErrorCode.PROGRAM_NOT_FOUND, "Program not found", LogData.createLogData().append("program_id", id)));
        CreateProgramCorporationReq payload = new CreateProgramCorporationReq();
        payload.setProgramId(id);
        payload.setBusinessId(program.getBusinessId());
        payload.setCorporations(req.getCorporations());
        // validate corporation
        Consumer<Integer> consumer = c -> {
            corporationService.find(c).orElseThrow(
                    () -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found", null));
        };
        req.getCorporations().forEach(consumer);

        Optional<ProgramCorporation> first = programCorporationService.findByProgramId(payload.getProgramId())
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .findFirst();

        if (first.isPresent()) {
            if (Objects.isNull(first.get().getRequestCode())) {
                throw new BusinessException(ErrorCode.REQUEST_CODE_NOT_FOUND, "request code not found",
                        LogData.createLogData()
                                .append("program_id", id));
            }
            opsReqPendingValidator.verifyEditKey(req.getEditKey(), first.get().getRequestCode(), first.get().getVersion());
            opsReqPendingValidator.validationRequestPending(EMakerCheckerType.PROGRAM_CORPORATION.getType(), first.get().getRequestCode());
        } else {
            throw new BusinessException(ErrorCode.PROGRAM_IS_NOT_CONFIGURED, null, null);
        }

        MakerCheckerInternalMakerReq<CreateProgramCorporationReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateProgramCorporationReq>builder()
                .requestCode(first.get().getRequestCode())
                .requestName(EMakerCheckerType.PROGRAM_CORPORATION.getName())
                .requestType(EMakerCheckerType.PROGRAM_CORPORATION.getType())
                .payload(payload)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> apiResponse = makerCheckerInternalFeignClient.maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != apiResponse.getMeta().getCode())
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program corporation - request editing attribute request call checker error: " + apiResponse.getMeta().getMessage(), null);
        return apiResponse.getData();
    }

    @Override
    public List<ProgramDropDownRes> getProgramByBusinessExcludeExist(Integer businessId) {
        List<Integer> existIds = programCorporationService.groupByProgram();
        Predicate<Program> predicate = program -> {
            if (!existIds.isEmpty() && existIds.contains(program.getId())) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        };
        return programService.findByBusinessId(businessId)
                .stream()
                .filter(ele -> ECommonStatus.ACTIVE.equals(ele.getStatus()))
                .filter(predicate)
                .map(ProgramDropDownRes::valueOf)
                .collect(Collectors.toList());
    }

    @Override
    public MakerCheckerInternalMakerRes create(CreateProgramCorporationReq request) {
        // validate program
        businessService.findActive(request.getBusinessId());
        // validate program
        programService.findActive(request.getProgramId());
        // validate whether exist request approved
        List<ProgramCorporation> byProgramId = programCorporationService.findByProgramId(request.getProgramId());
        if (!byProgramId.isEmpty()) {
            throw new BusinessException(ErrorCode.PROGRAM_IS_ALREADY_CONFIGURED, null, null);
        }
        // validate corporation
        Consumer<Integer> consumer = c -> {
            corporationService.find(c).orElseThrow(
                    () -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found", null));
        };
        request.getCorporations().forEach(consumer);
        validationProgramCorporationDoesNotExistInOtherReqPending(request.getBusinessId(), request.getProgramId());
        MakerCheckerInternalMakerReq<CreateProgramCorporationReq> createFeignInternalReq = MakerCheckerInternalMakerReq
                .<CreateProgramCorporationReq>builder()
                .requestCode(UUID.randomUUID().toString())
                .requestName(EMakerCheckerType.PROGRAM_CORPORATION.getName())
                .requestType(EMakerCheckerType.PROGRAM_CORPORATION.getType())
                .payload(request)
                .build();
        APIFeignInternalResponse<MakerCheckerInternalMakerRes> createMakerProgramCorporation = makerCheckerInternalFeignClient
                .maker(createFeignInternalReq);
        if (ErrorCode.SUCCESS.getValue() != createMakerProgramCorporation.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program corporation call maker error: " + createMakerProgramCorporation.getMeta().getMessage(),
                    null);
        }
        return createMakerProgramCorporation.getData();
    }

    private void validationProgramCorporationDoesNotExistInOtherReqPending(Integer businessId, Integer programId) {
        MakerCheckerInternalPreviewReq previewReq = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_CORPORATION.getType())
                .status(Collections.singletonList(EApprovalStatus.PENDING.getValue()))
                .build();

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes =
                makerCheckerInternalFeignClient.preview(previewReq, null, null);

        Optional<CreateProgramCorporationReq> any = previewRes.getData().stream()
                .map(data -> this.jsonMapper.convertValue(data.getPayload(), CreateProgramCorporationReq.class))
                .filter(ele -> Objects.nonNull(ele.getProgramId()))
                .filter(ele -> Objects.nonNull(ele.getBusinessId()))
                .filter(ele -> ele.getBusinessId().equals(businessId) && ele.getProgramId().equals(programId))
                .findAny();
        if (any.isPresent()) {
            throw new BusinessException(ErrorCode.PROGRAM_CODE_EXISTED,
                    "[VALIDATION PROGRAM] program existed in other requests pending", programId);
        }
    }

    @Override
    @Transactional
    public void update(ApprovalReq req) {
        APIFeignInternalResponse<MakerCheckerInternalDataDetailRes> detailRes = makerCheckerInternalFeignClient
                .previewDetail(req.getId());
        if (ObjectUtils.isEmpty(detailRes.getData())) {
            throw new BusinessException(ErrorCode.MAKER_CHECKER_CHECK_ID_NOT_FOUND, "Checker id not found",
                    LogData.createLogData()
                            .append("id", req.getId()));

        }

        MakerCheckerInternalDataDetailRes detailResData = detailRes.getData();
        CreateProgramCorporationReq payload = this.jsonMapper.convertValue(detailResData.getPayload(),
                CreateProgramCorporationReq.class);
        programService.findActive(payload.getProgramId());
        if (!EApprovalStatus.PENDING.getValue().equals(detailRes.getData().getStatus())) {
            throw new BusinessException(ErrorCode.REQUEST_STATUS_MUST_BE_PENDING,
                    "current status must be pending to reject or approve",
                    LogData.createLogData()
                            .append("id", req.getId())
                            .append("approve_status", req.getStatus()));
        }

        if (req.getStatus().equals(EApprovalStatus.APPROVED)) {
            String approvedBy = opsReqPendingValidator.getCurrentAuditorAndSetValue(detailResData.getMadeByUserName());
            Set<Integer> activeIds = new HashSet<>();
            Predicate<ProgramCorporation> predicate = ele -> {
                if (ECommonStatus.ACTIVE.equals(ele.getStatus())) {
                    activeIds.add(ele.getCorporationId());
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            };

            Map<Integer, ProgramCorporation> mapProgramCorporationInactive = programCorporationService
                    .findAllByProgramId(payload.getProgramId())
                    .stream()
                    .filter(predicate)
                    .collect(Collectors.toMap(ProgramCorporation::getCorporationId, Function.identity(), (first, last) -> last));

            // Handle create a program corporation
            List<ProgramCorporation> saveAll = new ArrayList<>();
            if (activeIds.isEmpty()) {
                payload.getCorporations().forEach(ele -> saveAll.add(prepareSaveProgramCorporation(detailResData, payload, ele, approvedBy)));
            } else {
                String updatedBy = detailResData.getMadeByUserName();
                // Handle edit a program corporation
                // Exp: | create: 1,2,3 | edit: 2,3,4 | we must create a corporation with id is
                // 4 and remove a corporation 1
                ArrayList<Integer> createIds = new ArrayList<>(
                        CollectionUtils.subtract(payload.getCorporations(), activeIds)); // 4
                ArrayList<Integer> deleteIds = new ArrayList<>(
                        CollectionUtils.subtract(activeIds, payload.getCorporations())); // 1
                ArrayList<Integer> holdIds = new ArrayList<>(payload.getCorporations()); // 2, 3
                holdIds.retainAll(activeIds);
                if (!createIds.isEmpty()) {
                    for (Integer corporationId : createIds) {
                        ProgramCorporation programCorporation;
                        // Set the active status for obsolete record
                        if (mapProgramCorporationInactive.containsKey(corporationId)) {
                            programCorporation = mapProgramCorporationInactive.get(corporationId);
                            programCorporation.setStatus(ECommonStatus.ACTIVE);
                            programCorporation.setRequestCode(detailResData.getRequestCode());
                            programCorporation.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programCorporation, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                        } else {
                            // New record
                            programCorporation = prepareSaveProgramCorporation(detailResData, payload,
                                    corporationId, approvedBy);
                        }
                        saveAll.add(programCorporation);
                    }
                }
                if (!deleteIds.isEmpty()) {
                    for (Integer deleteId : deleteIds) {
                        List<ProgramCorporation> programCorporations = programCorporationService
                                .find(payload.getProgramId(), deleteId, ECommonStatus.ACTIVE.getValue());
                        for (ProgramCorporation programCorporation : programCorporations) {
                            programCorporation.setStatus(ECommonStatus.INACTIVE);
                            programCorporation.setRequestCode(detailResData.getRequestCode());
                            programCorporation.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programCorporation, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAll.add(programCorporation);
                        }
                    }
                }
                if (!holdIds.isEmpty()) {
                    for (Integer holdId : holdIds) {
                        List<ProgramCorporation> programCorporations = programCorporationService
                                .find(payload.getProgramId(), holdId, ECommonStatus.ACTIVE.getValue());
                        for (ProgramCorporation programCorporation : programCorporations) {
                            programCorporation.setRequestCode(detailResData.getRequestCode());
                            programCorporation.setVersion(detailResData.getVersion());
                            opsReqPendingValidator.updateInfoChecker(programCorporation, detailResData.getMadeDate(), null, updatedBy, approvedBy);
                            saveAll.add(programCorporation);
                        }
                    }
                }
            }
            programCorporationService.repoSaveAll(saveAll);
        }

        MakerCheckerInternalCheckerReq checkerReq = MakerCheckerInternalCheckerReq.builder()
                .id(req.getId())
                .status(req.getStatus().getValue())
                .comment(req.getComment())
                .build();

        APIFeignInternalResponse<MakerCheckerInternalCheckerRes> checkerRes = makerCheckerInternalFeignClient
                .checker(checkerReq);

        if (ErrorCode.SUCCESS.getValue() != checkerRes.getMeta().getCode()) {
            throw new BusinessException(ErrorCode.SERVER_ERROR,
                    "Program corporation call checker error: " + checkerRes.getMeta().getMessage(),
                    LogData.createLogData().append("reviewId", req.getId()));
        }
    }

    private ProgramCorporation prepareSaveProgramCorporation(
            MakerCheckerInternalDataDetailRes detailResData,
            CreateProgramCorporationReq payload,
            Integer corporationId, String approvedBy) {
        String createdBy;
        String updatedBy;
        ProgramCorporation programCorporation = new ProgramCorporation();
        programCorporation.setProgramId(payload.getProgramId());
        programCorporation.setCorporationId(corporationId);
        programCorporation.setStatus(ECommonStatus.ACTIVE);
        programCorporation.setRequestCode(detailResData.getRequestCode());
        programCorporation.setVersion(detailResData.getVersion());
        updatedBy = createdBy = detailResData.getMadeByUserName();
        opsReqPendingValidator.updateInfoChecker(programCorporation, detailResData.getMadeDate(), createdBy, updatedBy, approvedBy);
        return programCorporation;
    }

    @Override
    public Page<ProgramCorporationPreviewRes> getProgramCorporationInReview(EApprovalStatus approvalStatus,
                                                                            Integer offset, Integer limit) {
        MakerCheckerInternalPreviewReq previewRequest = MakerCheckerInternalPreviewReq.builder()
                .requestType(EMakerCheckerType.PROGRAM_CORPORATION.getType())
                .build();
        previewRequest.setStatus(approvalStatus != null ? Collections.singletonList(approvalStatus.getValue()) : Collections.emptyList());

        APIFeignInternalResponse<List<MakerCheckerInternalDataDetailRes>> previewRes = makerCheckerInternalFeignClient
                .preview(previewRequest, offset, limit);

        makerCheckerInternalFeignClient.preview(previewRequest, offset, limit);
        if (ObjectUtils.isNotEmpty(previewRes)) {
            List<ProgramCorporationPreviewRes> result = new ArrayList<>();

            for (MakerCheckerInternalDataDetailRes previewDataRes : previewRes.getData()) {
                CreateProgramCorporationReq programCorporationReq = this.jsonMapper.convertValue(previewDataRes.getPayload(),
                        CreateProgramCorporationReq.class);

                ProgramCorporationPreviewRes pCorporationPreviewRes = new ProgramCorporationPreviewRes();
                Program program = programService.find(programCorporationReq.getProgramId()).orElseThrow(
                        () -> new BusinessException(ErrorCode.PROGRAM_NOT_FOUND)
                );

                Business business = businessService.find(programCorporationReq.getBusinessId()).orElseThrow(
                        () -> new BusinessException(ErrorCode.BUSINESS_NOT_FOUND)
                );

                pCorporationPreviewRes.setId(previewDataRes.getId());
                pCorporationPreviewRes.setBusinessId(business.getId());
                pCorporationPreviewRes.setBusinessCode(business.getCode());
                pCorporationPreviewRes.setBusinessName(business.getName());
                pCorporationPreviewRes.setProgramId(program.getId());
                pCorporationPreviewRes.setProgramCode(program.getCode());
                pCorporationPreviewRes.setProgramName(program.getName());
                pCorporationPreviewRes.setApprovalStatus(EApprovalStatus.of(previewDataRes.getStatus()));
                pCorporationPreviewRes.setCorporations(mapCorporationInfo(programCorporationReq.getCorporations()));
                pCorporationPreviewRes.setCreatedBy(previewDataRes.getMadeByUserName());
                pCorporationPreviewRes.setCreatedAt(convertStringToDate(previewDataRes.getMadeDate()));
                pCorporationPreviewRes.setUpdatedBy(previewDataRes.getCheckedByUserName());
                pCorporationPreviewRes.setUpdatedAt(convertStringToDate(previewDataRes.getCheckedDate()));
                result.add(pCorporationPreviewRes);
            }

            OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
            return new PageImpl<>(result, pageRequest, previewRes.getMeta().getTotal());
        }

        return null;
    }

    private List<CorporationInfo> mapCorporationInfo(Set<Integer> lCorporationId) {
        List<CorporationInfo> lCorporationRes = new ArrayList<>();

        for (int i : lCorporationId) {
            Corporation corporation = corporationService.find(i).orElseThrow(
                    () -> new BusinessException(ErrorCode.CORPORATION_NOT_FOUND, "Corporation not found", null));

            CorporationInfo corporationRes = new CorporationInfo();
            corporationRes.setId(corporation.getId());
            corporationRes.setCode(corporation.getCode());
            corporationRes.setName(corporation.getName());
            corporationRes.setStatus(corporation.getStatus());
            lCorporationRes.add(corporationRes);
        }

        return lCorporationRes;
    }

    private Date convertStringToDate(String sDate) {
        if (sDate == null)
            return null;
        try {
            Instant instant = Instant.parse(sDate);
            Date date = Date.from(instant);
            return date;
        } catch (Exception e) {
            return null;
        }
    }
}
