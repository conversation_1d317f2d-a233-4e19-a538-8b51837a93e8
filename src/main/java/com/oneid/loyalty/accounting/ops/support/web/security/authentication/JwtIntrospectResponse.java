package com.oneid.loyalty.accounting.ops.support.web.security.authentication;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@JsonDeserialize(builder = JwtIntrospectResponse.JwtIntrospectResponseBuilder.class)
public class JwtIntrospectResponse {
    @JsonProperty("is_valid_token")
    private boolean valid;
    
    @JsonProperty("user_id")
    private Long userId;
    
    @JsonProperty("user_name")
    private String userName;
    
    @JsonProperty("user_email")
    private String userEmail;
    
    @JsonProperty("permissions")
    Map<String, Integer[]> permissions;
    
    @JsonPOJOBuilder(withPrefix = "")
    public static class JwtIntrospectResponseBuilder {
    }
}

