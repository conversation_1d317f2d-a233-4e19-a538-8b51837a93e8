package com.oneid.loyalty.accounting.ops.constant;

import java.util.stream.Stream;

import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MemberSortingField {
    MEMBER_PHONE_NUMBER("phone_no", "phoneNo"),
    MEMBER_FULL_NAME("full_name", "fullName"),
    MEMBER_IDENTIFY_NO("identify_no", "identifyNo"),
    GENDER("gender", "gender"),
    MEMBER_STATUS("status", "status");
    
    @JsonValue
    String name;
    
    String mappingColumn;
    
    public static MemberSortingField lookup(String name) {
        if (StringUtils.isEmpty(name))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
    
    public static class UserSortingFieldConverter implements Converter<String, MemberSortingField> {
        @Override
        public MemberSortingField convert(String source) {
            return MemberSortingField.lookup(source);
        }
    }
}
