package com.oneid.loyalty.accounting.ops.support.data.databind;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.joda.time.DateTime;

public class DateDeserializer extends StdDeserializer<Date> {
    protected DateDeserializer() {
        super(Date.class);
    }

    private static final long serialVersionUID = 7105134305172677949L;

    private final static List<String> SUPPORTS_ENDED = List.of("Z", "+07:00", "+00:00");

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        for (String ended : SUPPORTS_ENDED) {
            if (p.getText().endsWith(ended)) {
                return Date.from(DateTime.parse(p.getText()).toDate().toInstant());
            }
        }
        if (StringUtils.isNoneBlank(p.getText())) {
            return Date.from(LocalDateTime.parse(p.getText()).atZone(ZoneId.systemDefault()).toInstant());
        }

        return null;
    }

}
