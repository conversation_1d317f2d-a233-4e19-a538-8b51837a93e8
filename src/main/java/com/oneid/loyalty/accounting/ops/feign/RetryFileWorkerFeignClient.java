package com.oneid.loyalty.accounting.ops.feign;

import com.oneid.loyalty.accounting.ops.feign.config.OpsCommonOneLoyaltyFeignConfig;
import com.oneid.loyalty.accounting.ops.feign.model.res.RetryFileRequestRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "ps2-retry-file-worker",
        url = "${retry-file.url}",
        configuration = OpsCommonOneLoyaltyFeignConfig.class
)
public interface RetryFileWorkerFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/retry/{control_file_id}")
    APIResponse<RetryFileRequestRes> requestRetry(
            @PathVariable("control_file_id") Integer controlFileId,
            @RequestParam("created_by") String createdBy
    );
}
