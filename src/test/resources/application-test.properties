JDBC_URL=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=Oracle
spring.datasource.driver-class-name=org.h2.Driver
JDBC_USERNAME=sa
JDBC_PASSWORD=
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.main.allow-bean-definition-overriding=true
MEMBER_SERVICE_URL=
CARD_SERVICE_URL=
CARD_SERVICE_BASE_URL=
ONELOYALTY_SERVICE_BASE_URL=

MAKER_CHECKER_URL_CHANGE=
ATTRIBUTE_VALUE_SECRET=

ELASTIC_APM_SERVER_URLS=
ELASTIC_APM_SERVICE_NAME=
ELASTIC_APM_APPLICATION_PACKAGES=
ELASTIC_APM_ENVIRONMENT=
ELASTIC_APM_VERIFY_SERVER_CERT=false
ES_TRANSACTION_HISTORY_ENABLE=false
OPS_AUTHEN_URL=
OPS_BASIC_AUTH=
internal_vinid_ops=
ES_ENDPOINTS=
ES_INDEX_ACTIVITY=
ES_USERNAME=
ES_PASSWORD=
ONELOYALTY_OPS_INTEGRATION_BASE_URL=
VD_CARD_PREFIX_CODES=
ONELOYALTY_INTEGRATION_OPS=
VD_CARD_TYPE_CODES=
MAKER_CHECKER_URL=
app.attribute.value.secret=test
GCP_STORAGE_OPS_BUCKET_NAME=vinid-loyalty-ops-internal-np
GCP_STORAGE_OPS_SERVICE_ACCOUNT_KEY=conf/ops-gcp-storage-upload-key
TCB_CORPORATION_CODE=
VGC_BUSINESS_CODE=