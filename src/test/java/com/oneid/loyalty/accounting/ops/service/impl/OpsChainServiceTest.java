package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.mapper.ChainMapper;
import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.ChainRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Country;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.*;
import com.oneid.oneloyalty.common.util.LogData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
@Ignore
public class OpsChainServiceTest {
    @Autowired
    OpsChainService opsChainService;

    @MockBean
    ChainRepository chainRepository;

    @MockBean
    ChainService chainService;

    @MockBean
    BusinessRepository businessRepository;

    @MockBean
    CorporationRepository corporationRepository;

    @MockBean
    CountryService countryService;

    @MockBean
    ProvinceService provinceService;

    @MockBean
    DistrictService districtService;

    @MockBean
    WardService wardService;

    @MockBean
    OpsBusinessService opsBusinessService;

    @MockBean
    OpsCorporationService opsCorporationService;

    Chain chain1 = new Chain();

    @Before
    public void before() {
        Business business = new Business();
        business.setId(1);
        business.setCode("bzncode1");
        business.setName("bznname1");
        business.setStatus(ECommonStatus.ACTIVE);
        Corporation corporation = new Corporation();
        corporation.setId(1);
        corporation.setCode("cprtcode1");
        corporation.setName("cprtcode1");
        corporation.setStatus(ECommonStatus.ACTIVE);
        corporation.setBusinessId(1);

        chain1.setId(1);
        chain1.setCode("chaincode1");
        chain1.setName("chainname1");
        chain1.setStatus(ECommonStatus.ACTIVE);
        chain1.setBusinessId(1);
        chain1.setCorporationId(1);

        Mockito.when(this.businessRepository.findAll()).thenReturn(Arrays.asList(business));
        Mockito.when(this.corporationRepository.findAll()).thenReturn(Arrays.asList(corporation));
    }

    @Test
    public void getEnumAll() {
        Mockito.when(chainRepository.findAll()).thenReturn(new ArrayList<>());
        Assert.assertEquals(opsChainService.getEnumAll(), new ArrayList<>());
    }

    @Test
    public void update() {
        ChainUpdateReq req = new ChainUpdateReq();
        req.setCountryId(1);
        req.setProvinceId(2);
        req.setDistrictId(3);
        req.setWardId(4);
        req.setServiceStartDate(132456789L);
        req.setServiceEndDate(132456989L);

        Chain chain = new Chain();
        chain.setId(11);
        chain.setCountryId(1);
        chain.setProvinceId(2);
        chain.setDistrictId(3);
        chain.setWardId(4);

        Mockito.when(countryService.getById(1)).thenReturn(Optional.of(new Country()));
        Mockito.when(provinceService.isValid(2,1)).thenReturn(true);
        Mockito.when(districtService.isValid(3,2)).thenReturn(true);
        Mockito.when(wardService.isValid(4,3)).thenReturn(true);
        Mockito.when(chainService.find(11)).thenReturn(Optional.ofNullable(chain));

        opsChainService.update(11, req);

        try {
            opsChainService.update(2, req);
            Assert.fail("Chain not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CHAIN_NOT_FOUND);
        }

        Mockito.when(countryService.getById(1)).thenReturn(Optional.ofNullable(null));
        try {
            opsChainService.update(11, req);
            Assert.fail("Chain not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.COUNTRY_NOT_FOUND);
        }
        Mockito.when(countryService.getById(1)).thenReturn(Optional.of(new Country()));

        Mockito.when(provinceService.isValid(2,1)).thenReturn(false);
        try {
            opsChainService.update(11, req);
            Assert.fail("Chain not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.PROVINCE_NOT_FOUND);
        }
        Mockito.when(provinceService.isValid(2,1)).thenReturn(true);


        Mockito.when(districtService.isValid(3,2)).thenReturn(false);
        try {
            opsChainService.update(11, req);
            Assert.fail("Chain not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.DISTRICT_NOT_FOUND);
        }
        Mockito.when(districtService.isValid(3,2)).thenReturn(true);

        Mockito.when(wardService.isValid(4,3)).thenReturn(false);
        try {
            opsChainService.update(11, req);
            Assert.fail("Chain not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.WARD_NOT_FOUND);
        }
    }

    @Test
    public void filter() {
        Pageable pageRequest = PageRequest.of(1,1);
        Mockito.when(chainService.find((SpecificationBuilder<Chain>) Mockito.any(), (Pageable) Mockito.any()))
                .thenReturn(new PageImpl<Chain>(new ArrayList<>(), pageRequest, 100));
        Page<ChainRes> exp = opsChainService.filter(1,1,""
                ,"","A", 1, 1);
        Assert.assertEquals(exp.getContent(), new ArrayList<>());
        Assert.assertEquals(exp.getTotalElements(), 100);
    }

    @Test
    public void get_success() {
        Mockito.when(this.chainService.find(1)).thenReturn(java.util.Optional.ofNullable(chain1));

        this.opsChainService.get(1);
    }

    @Test
    public void get_fail() {
        try {
            this.opsChainService.get(1);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.CHAIN_NOT_FOUND, e.getCode());
        }
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsChainService opsChainService() {
            return new OpsChainServiceImpl();
        }
    }
}
