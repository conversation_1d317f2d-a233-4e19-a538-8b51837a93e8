package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.exception.OpsBusinessException;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MemberServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.CardMemberFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.MemberProfileDetailFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateCardReq;
import com.oneid.loyalty.accounting.ops.model.res.CardRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardService;
import com.oneid.loyalty.accounting.ops.service.OpsMemberService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.repository.CardTypeRepository;
import com.oneid.oneloyalty.common.service.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Optional;

@RunWith(SpringRunner.class)
public class TestCardService {

    @Autowired
    private OpsCardService opsCardService;

    @MockBean
    CardService cardService;

    @MockBean
    BusinessService businessService;

    @MockBean
    ProgramService programService;

    @MockBean
    MemberService memberService;

    @MockBean
    ChainService chainService;

    @MockBean
    StoreService storeService;

    @MockBean
    CardTypeService cardTypeService;

    @MockBean
    CardTMPService cardTMPService;

    @MockBean
    CardPolicyService cardPolicyService;

    @MockBean
    CardTransferringHistoryService cardTransferringHistoryService;

    @MockBean
    OpsMemberService opsMemberService;

    @MockBean
    CardTypeRepository cardTypeRepository;

    @MockBean
    CorporationService corporationService;

    @MockBean
    private MemberServiceFeignClient memberServiceFeignClient;

    @MockBean
    private CardServiceFeignClient cardServiceFeignClient;

    @Test
    public void filter() {
        Integer cardType = 1, offSet = 1, limit = 20;
        String cardNo = "1111", businessId = "businessCode", programId = "programCode", storeId = "storeId", memberCode = "memberCode";

        Business business = new Business();
        business.setId(1);
        Program program = new Program();
        program.setId(2);
        Store store = new Store();
        Member member = new Member();
        Corporation corporation = new Corporation();

        Pageable pageable = new OffsetBasedPageRequest(offSet, limit);

        Mockito.when(businessService.findByCode(businessId)).thenReturn(business);
        Mockito.when(programService.find(business.getId(), programId)).thenReturn(Optional.of(program));
        Mockito.when(storeService.findByBusinessIdAndCode(business.getId(), storeId)).thenReturn(store);
        Mockito.when(memberService.find(memberCode, program.getId())).thenReturn(Optional.of(member));
        Mockito.when(cardService.findCardAndMember(business.getId(), program.getId(), store.getId(), cardType, cardNo, member.getId(), pageable))
                .thenReturn(new PageImpl<>(new ArrayList<>(), pageable, 0));
        Mockito.when(corporationService.find(store.getCorporationId())).thenReturn(Optional.of(corporation));

        opsCardService.filter(cardType, cardNo, businessId, programId, storeId, memberCode, offSet, limit);
    }

    @Test
    public void addCard() {
        OPSAuthenticatedPrincipal principal = new OPSAuthenticatedPrincipal();
        AddCardReq req = new AddCardReq();
        req.setCardTypeId(1);

        APIResponse<MemberProfileDetailFeignRes> response = new APIResponse<>();
        MemberProfileDetailFeignRes memberProfileDetailFeignRes = new MemberProfileDetailFeignRes();

        response.setData(memberProfileDetailFeignRes);
        response.setMeta(new Meta(200, "success"));

        Business business = new Business();
        Store store = new Store();
        Program program = new Program();
        CardTMP cardTMP = new CardTMP();
        cardTMP.setCardTypeId(1);

        Mockito.when(businessService.findByCode(req.getBusinessCode())).thenReturn(business);
        Mockito.when(storeService.findByBusinessIdAndCodeWithoutStatus(business.getId(), req.getStoreCode())).thenReturn(store);
        Mockito.when(programService.findActive(business.getId(), req.getProgramCode())).thenReturn(program);
        Mockito.when(cardTMPService.findPending(req.getCardNo(), business.getId(), program.getId(), store.getId())).thenReturn(cardTMP);

        Mockito.when(memberServiceFeignClient.getProfileByCode(req.getBusinessCode(), req.getProgramCode(), req.getMemberCode(), null))
                        .thenReturn(response);

        opsCardService.addCard(principal, req);
    }

    @Test
    public void addCard_cardTypeNotMatched() {
        OPSAuthenticatedPrincipal principal = new OPSAuthenticatedPrincipal();
        AddCardReq req = new AddCardReq();
        req.setCardTypeId(1);

        APIResponse<MemberProfileDetailFeignRes> response = new APIResponse<>();
        MemberProfileDetailFeignRes memberProfileDetailFeignRes = new MemberProfileDetailFeignRes();

        response.setData(memberProfileDetailFeignRes);
        response.setMeta(new Meta(200, "success"));

        Business business = new Business();
        Store store = new Store();
        Program program = new Program();
        CardTMP cardTMP = new CardTMP();
        cardTMP.setCardTypeId(2);

        Mockito.when(businessService.findByCode(req.getBusinessCode())).thenReturn(business);
        Mockito.when(storeService.findByBusinessIdAndCodeWithoutStatus(business.getId(), req.getStoreCode())).thenReturn(store);
        Mockito.when(programService.findActive(business.getId(), req.getProgramCode())).thenReturn(program);
        Mockito.when(cardTMPService.findPending(req.getCardNo(), business.getId(), program.getId(), store.getId())).thenReturn(cardTMP);

        Mockito.when(memberServiceFeignClient.getProfileByCode(req.getBusinessCode(), req.getProgramCode(), req.getMemberCode(), null))
                .thenReturn(response);

        try {
            opsCardService.addCard(principal, req);
            Assert.fail("card type not matched here");
        } catch(OpsBusinessException e) {
            Assert.assertEquals(e.getCode(), OpsErrorCode.CARD_TYPE_NOT_MATCHED);
        }
    }

    @Test
    public void getDetail() {
        Integer cardId = 1;
        String businessId = "BusinessCode", programId = "programCode";

        APIResponse<CardMemberFeignRes> response = new APIResponse<>();
        CardMemberFeignRes cardMemberFeignRes = CardMemberFeignRes.builder()
                .businessId(1)
                .programId(2)
                .build();

        response.setData(cardMemberFeignRes);
        response.setMeta(new Meta(200, "success"));

        APIResponse<MemberProfileDetailFeignRes> memberResponse = new APIResponse<>();
        MemberProfileDetailFeignRes memberProfileDetailFeignRes = new MemberProfileDetailFeignRes();
        memberResponse.setData(memberProfileDetailFeignRes);
        memberResponse.setMeta(new Meta(200, "success"));

        Business business = new Business();
        Store store = new Store();
        Chain chain = new Chain();
        Program program = new Program();
        CardTMP cardTMP = new CardTMP();
        cardTMP.setCardTypeId(2);

        CardType cardType = new CardType();

        Mockito.when(businessService.find(1)).thenReturn(Optional.of(business));
        Mockito.when(storeService.find(cardMemberFeignRes.getStoreId())).thenReturn(store);
        Mockito.when(programService.find(2)).thenReturn(Optional.of(program));
        Mockito.when(cardTMPService.findPending(cardMemberFeignRes.getCardNo(), business.getId(), program.getId(), store.getId())).thenReturn(cardTMP);
        Mockito.when(cardTypeService.find(cardMemberFeignRes.getCardType())).thenReturn(Optional.of(cardType));
        Mockito.when(chainService.find(store.getChainId())).thenReturn(Optional.of(chain));
        Mockito.when(corporationService.find(chain.getCorporationId())).thenReturn(Optional.of(new Corporation()));

        Mockito.when(cardServiceFeignClient.getMemberCardDetail(cardId)).thenReturn(response);
        Mockito.when(memberServiceFeignClient.getProfileById(cardMemberFeignRes.getMemberId())).thenReturn(memberResponse);

        opsCardService.getDetail(cardId, businessId, programId);
    }

    @Test
    public void update() {
    }


    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsCardService cardService() {
            return new OpsCardServiceImpl();
        }
    }
}