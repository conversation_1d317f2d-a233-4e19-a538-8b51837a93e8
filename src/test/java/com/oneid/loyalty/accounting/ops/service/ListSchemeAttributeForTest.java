package com.oneid.loyalty.accounting.ops.service;

import com.oneid.oneloyalty.common.entity.SchemeAttribute;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class ListSchemeAttributeForTest {
    public static List<SchemeAttribute> getListAttribute() {
        List<SchemeAttribute> res = new LinkedList<>();
        SchemeAttribute attributeAccumulatedEarnedPoint = new SchemeAttribute();
        attributeAccumulatedEarnedPoint.setAttribute("AccumulatedEarnedPoint");
        attributeAccumulatedEarnedPoint.setDataType("LONG");
        attributeAccumulatedEarnedPoint.setDataTypeDisplay("NUMBER");
        attributeAccumulatedEarnedPoint.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(attributeAccumulatedEarnedPoint);

        SchemeAttribute AccumulatedGMV = new SchemeAttribute();
        AccumulatedGMV.setAttribute("AccumulatedGMV");
        AccumulatedGMV.setDataType("LONG");
        AccumulatedGMV.setDataTypeDisplay("NUMBER");
        AccumulatedGMV.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(AccumulatedGMV);

        SchemeAttribute AccumulatedGrossAmount = new SchemeAttribute();
        AccumulatedGrossAmount.setAttribute("AccumulatedGrossAmount");
        AccumulatedGrossAmount.setDataType("LONG");
        AccumulatedGrossAmount.setDataTypeDisplay("NUMBER");
        AccumulatedGrossAmount.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(AccumulatedGrossAmount);


        SchemeAttribute AccumulatedNettAmount = new SchemeAttribute();
        AccumulatedNettAmount.setAttribute("AccumulatedNettAmount");
        AccumulatedNettAmount.setDataType("LONG");
        AccumulatedNettAmount.setDataTypeDisplay("NUMBER");
        AccumulatedNettAmount.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(AccumulatedNettAmount);

        SchemeAttribute ChainCode = new SchemeAttribute();
        ChainCode.setAttribute("ChainCode");
        ChainCode.setDataType("STRING");
        ChainCode.setDataTypeDisplay("CHAIN");
        ChainCode.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(ChainCode);

        SchemeAttribute CorporationCode = new SchemeAttribute();
        CorporationCode.setAttribute("CorporationCode");
        CorporationCode.setDataType("STRING");
        CorporationCode.setDataTypeDisplay("CORPORATION");
        CorporationCode.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(CorporationCode);

        SchemeAttribute DOB = new SchemeAttribute();
        DOB.setAttribute("DOB");
        DOB.setDataType("STRING");
        DOB.setDataTypeDisplay("DATE");
        DOB.setOperators(Arrays.asList("==|!=|>=|<=|>|<".split("\\|", -1)));
        res.add(DOB);

        SchemeAttribute GMV = new SchemeAttribute();
        GMV.setAttribute("GMV");
        GMV.setDataType("LONG");
        GMV.setDataTypeDisplay("NUMBER");
        GMV.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(GMV);

        SchemeAttribute GrossAmount = new SchemeAttribute();
        GrossAmount.setAttribute("GrossAmount");
        GrossAmount.setDataType("LONG");
        GrossAmount.setDataTypeDisplay("NUMBER");
        GrossAmount.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(GrossAmount);

        SchemeAttribute MemberStatus = new SchemeAttribute();
        MemberStatus.setAttribute("MemberStatus");
        MemberStatus.setDataType("STRING");
        MemberStatus.setDataTypeDisplay("MEMBER_STATUS");
        MemberStatus.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(MemberStatus);

        SchemeAttribute NettAmount = new SchemeAttribute();
        NettAmount.setAttribute("NettAmount");
        NettAmount.setDataType("LONG");
        NettAmount.setDataTypeDisplay("NUMBER");
        NettAmount.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(NettAmount);

        SchemeAttribute RedeemPoint = new SchemeAttribute();
        RedeemPoint.setAttribute("RedeemPoint");
        RedeemPoint.setDataType("LONG");
        RedeemPoint.setDataTypeDisplay("NUMBER");
        RedeemPoint.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(RedeemPoint);

        SchemeAttribute StoreCode = new SchemeAttribute();
        StoreCode.setAttribute("StoreCode");
        StoreCode.setDataType("STRING");
        StoreCode.setDataTypeDisplay("STORE");
        StoreCode.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(StoreCode);


        SchemeAttribute TerminalCode = new SchemeAttribute();
        TerminalCode.setAttribute("TerminalCode");
        TerminalCode.setDataType("STRING");
        TerminalCode.setDataTypeDisplay("TERMINAL");
        TerminalCode.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(TerminalCode);

        SchemeAttribute TierCode = new SchemeAttribute();
        TierCode.setAttribute("TierCode");
        TierCode.setDataType("STRING");
        TierCode.setDataTypeDisplay("TIER");
        TierCode.setOperators(Arrays.asList("==|!=|IN|NOT_IN".split("\\|", -1)));
        res.add(TierCode);

        SchemeAttribute TransactionDatetime = new SchemeAttribute();
        TransactionDatetime.setAttribute("TransactionDatetime");
        TransactionDatetime.setDataType("LONG");
        TransactionDatetime.setDataTypeDisplay("DATE_TIME");
        TransactionDatetime.setOperators(Arrays.asList(">=|<=|>|<|==|!=".split("\\|", -1)));
        res.add(TransactionDatetime);

        SchemeAttribute TransactionType = new SchemeAttribute();
        TransactionType.setAttribute("TransactionType");
        TransactionType.setDataType("STRING");
        TransactionType.setDataTypeDisplay("TRANSACTION_TYPE");
        TransactionType.setOperators(Arrays.asList("==|!=".split("\\|", -1)));
        res.add(TransactionType);

        return res;
    }
}
