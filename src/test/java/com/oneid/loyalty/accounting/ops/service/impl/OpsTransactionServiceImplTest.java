package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.config.CheckLogSourceConfigPram;
import com.oneid.loyalty.accounting.ops.constant.EOpsCancellationType;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.loyalty.accounting.ops.feign.MasterWorkerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.OneloyaltyServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.SapFeignClient;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsPoolService;
import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ETransactionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.CurrencyRate;
import com.oneid.oneloyalty.common.entity.Function;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.ProgramCorporation;
import com.oneid.oneloyalty.common.entity.ReasonCode;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.entity.TransactionAuditTrail;
import com.oneid.oneloyalty.common.entity.TransactionBatchRequest;
import com.oneid.oneloyalty.common.entity.TransactionHistory;
import com.oneid.oneloyalty.common.entity.UserProfile;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRateRepository;
import com.oneid.oneloyalty.common.repository.CurrencyRepository;
import com.oneid.oneloyalty.common.repository.FunctionRepository;
import com.oneid.oneloyalty.common.repository.MemberProductAccountRepository;
import com.oneid.oneloyalty.common.repository.PoolRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.ProgramCorporationRepository;
import com.oneid.oneloyalty.common.repository.ProgramProductRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.ReasonCodeRepository;
import com.oneid.oneloyalty.common.repository.SapSaleOrderCallRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.repository.TransactionBatchRequestRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryAttributeRepository;
import com.oneid.oneloyalty.common.repository.TransactionHistoryRepository;
import com.oneid.oneloyalty.common.repository.TransactionRepository;
import com.oneid.oneloyalty.common.repository.UserProfileRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.CurrencyRateService;
import com.oneid.oneloyalty.common.service.CurrencyService;
import com.oneid.oneloyalty.common.service.FunctionService;
import com.oneid.oneloyalty.common.service.LimitationService;
import com.oneid.oneloyalty.common.service.MemberService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.PosService;
import com.oneid.oneloyalty.common.service.ProgramCorporationService;
import com.oneid.oneloyalty.common.service.ProgramProductService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTransactionAttributeService;
import com.oneid.oneloyalty.common.service.ReasonCodeService;
import com.oneid.oneloyalty.common.service.SchemeService;
import com.oneid.oneloyalty.common.service.StoreService;
import com.oneid.oneloyalty.common.service.TransactionAuditTrailService;
import com.oneid.oneloyalty.common.service.TransactionBatchRequestService;
import com.oneid.oneloyalty.common.service.TransactionHistoryService;
import com.oneid.oneloyalty.common.service.TransactionRequestService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RunWith(SpringRunner.class)
@TestPropertySource(properties = {
        "checklog.source.api-wrapper-value:123",
        "checklog.source.mobile.value:123",
        "checklog.source.mobile.default-store-codes:A",
        "app.tmp.prefix.transaction.reverse:ss",
        "app.tmp.prefix.transaction.adjustment:ss",
        "ops.transaction-request.gen-invoice-no-random-character=#",
        "ops.transaction-request.min-invoice-no-pattern-length=10",
        "ops.transaction-request.max-invoice-no-pattern-length=50"
})
public class OpsTransactionServiceImplTest {
    @Autowired
    private OpsTransactionService opsTransactionService;

    @Autowired
    private CheckLogSourceConfigPram checkLogSourceConfigPram;

    @MockBean
    private TransactionHistoryRepository transactionHistoryRepository;

    @MockBean
    private TransactionBatchRequestService transactionBatchRequestService;

    @MockBean
    private TransactionRequestService transactionRequestService;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private ProgramService programService;

    @MockBean
    private TransactionRepository transactionRepository;

    @MockBean
    private ProgramCorporationService programCorporationService;

    @MockBean
    private FunctionService functionService;

    @MockBean
    private ReasonCodeService reasonCodeService;

    @MockBean
    private ProgramRepository programRepository;

    @MockBean
    private OpsCommonExcelService commonExcelService;

    @MockBean
    private ReasonCodeRepository reasonCodeRepository;

    @MockBean
    private StoreRepository storeRepository;

    @MockBean
    private ChainRepository chainRepository;

    @MockBean
    private ProgramCorporationRepository programCorporationRepository;

    @MockBean
    private PosRepository posRepository;

    @MockBean
    private PoolRepository poolRepository;

    @MockBean
    private CurrencyRepository currencyRepository;

    @MockBean
    private CurrencyRateRepository currencyRateRepository;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private CorporationService corporationService;

    @MockBean
    private ChainService chainService;

    @MockBean
    private StoreService storeService;

    @MockBean
    private ProgramTransactionAttributeService programTransactionAttributeService;

    @MockBean
    private PosService posService;

    @MockBean
    private PoolService poolService;

    @MockBean
    private OpsBusinessService opsBusinessService;

    @MockBean
    private OpsProgramService opsProgramService;

    @MockBean
    private OpsCorporationService opsCorporationService;

    @MockBean
    private OpsChainService opsChainService;

    @MockBean
    private OpsStoreService opsStoreService;

    @MockBean
    private OpsTerminalService opsTerminalService;

    @MockBean
    private OpsPoolService opsPoolService;

    @MockBean
    private MemberProductAccountRepository memberProductAccountRepository;

    @MockBean
    private ProgramProductRepository programProductRepository;

    @MockBean
    private MemberService memberService;

    @MockBean
    private TransactionHistoryService transactionHistoryService;

    @MockBean
    private CurrencyService currencyService;

    @MockBean
    private SchemeService schemeService;

    @MockBean
    private ProgramProductService programProductService;

    @MockBean
    private TransactionBatchRequestRepository transactionBatchRequestRepository;

    @MockBean
    private MasterWorkerFeignClient masterWorkerFeignClient;

    @Value("${app.tmp.prefix.transaction.adjustment}")
    private String adjustedTransactionInvoiceNoPrefix;

    @Value("${app.tmp.prefix.transaction.reverse}")
    private String reverseTransactionInvoiceNoPrefix;

    @Value("${ops.transaction-request.gen-invoice-no-random-character}")
    private char randomCharacter;

    @Value("${ops.transaction-request.min-invoice-no-pattern-length}")
    private int minPatternLength;

    @Value("${ops.transaction-request.max-invoice-no-pattern-length}")
    private int maxPatternLength;

    @MockBean
    private OneloyaltyServiceFeignClient oneloyaltyServiceFeignClient;

    @MockBean
    private TransactionAuditTrailService transactionAuditTrailService;

    @MockBean
    private FunctionRepository functionRepository;

    @MockBean
    private UserProfileRepository userProfileRepository;

    @MockBean
    private CurrencyRateService currencyRateService;

    @MockBean
    private LimitationService limitationService;

    @MockBean
    private OpsReqPendingValidator opsReqPendingValidator;

    @MockBean
    private SapFeignClient sapFeignClient;

    @MockBean
    private SapSaleOrderCallRepository sapSaleOrderCallRepository;

    @MockBean
    private TransactionHistoryAttributeRepository transactionHistoryAttributeRepository;

    private Business business;
    private Corporation corporation;
    private Chain chain;
    private Store store;
    private Pos pos;
    private Program program;
    private Member member;
    private UserProfile userProfile;
    private TransactionHistory transactionHistory;

    @Before
    public void before() {
        business = new Business();
        business.setId(1);

        corporation = new Corporation();
        corporation.setId(2);
        corporation.setBusinessId(business.getId());

        chain = new Chain();
        chain.setId(4);
        chain.setBusinessId(business.getId());
        chain.setCorporationId(corporation.getId());

        store = new Store();
        store.setId(5);
        store.setBusinessId(business.getId());
        store.setCorporationId(corporation.getId());
        store.setChainId(chain.getId());

        pos = new Pos();
        pos.setId(6);
        pos.setBusinessId(business.getId());
        pos.setCorporationId(corporation.getId());
        pos.setChainId(chain.getId());
        pos.setStoreId(store.getId());

        program = new Program();
        program.setId(8);
        program.setBusinessId(business.getId());

        member = new Member();
        member.setId(1132L);
        member.setProgramId(program.getId());
        member.setMemberCode("1123");

        userProfile = new UserProfile();
        userProfile.setId(1234L);
        userProfile.setBusinessId(business.getId());

        transactionHistory = new TransactionHistory();
        transactionHistory.setPointTransactionId("23");
        transactionHistory.setBusinessId(business.getId());
        transactionHistory.setType(ETransactionType.ADJUSTMENT);
        transactionHistory.setAwardPoint(BigDecimal.valueOf(0));
        transactionHistory.setRedeemPoint(BigDecimal.valueOf(0));
        transactionHistory.setInvoiceNo("dfdf");
    }

//    @Test
//    public void search() {
//        TransactionSearchReq req = TransactionSearchReq.builder()
//                .businessId(business.getId())
//                .corporationId(corporation.getId())
//                .chainId(chain.getId())
//                .storeId(store.getId())
//                .terminalId(pos.getId())
//                .programId(program.getId())
//                .cancellationType(EOpsCancellationType.RFD)
//                .invoiceNumber("iiii")
//                .tnxRefNo("refNo")
//                .memberId(member.getId())
//                .memberCode(member.getMemberCode())
//                .transactionFrom(123)
//                .transactionTo(123)
//                .userId("123")
//                .build();
//
//        Pageable pageRequest = Mockito.mock(Pageable.class);
//        Mockito.when(memberService.find(member.getMemberCode(), program.getId())).thenReturn(Optional.of(member));
//        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.of(program));
//        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(userProfile);
//        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.of(member));
//
//        Page pageTransactionMock = Mockito.mock(Page.class);
//        Mockito.when(transactionHistoryService.getPointTnxIds(
//                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
//                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
//                Mockito.any(), Mockito.any()
//        )).thenReturn(pageTransactionMock);
//        Mockito.when(pageTransactionMock.getContent()).thenReturn(List.of(transactionHistory.getPointTransactionId()));
//        Mockito.when(transactionHistoryService.getByTnxPointIds(Mockito.any())).thenReturn(List.of(transactionHistory));
//
//        Currency currencyMock = Mockito.mock(Currency.class);
//        Mockito.when(currencyMock.getCode()).thenReturn("gg");
//
//        Mockito.when(currencyService.findActive(Mockito.any(), Mockito.any())).thenReturn(new Currency());
//
//        opsTransactionService.search(req, pageRequest);
//    }

    @Test
    public void search_badRequest() {
        TransactionSearchReq req = TransactionSearchReq.builder()
                .businessId(business.getId())
                .corporationId(corporation.getId())
                .chainId(chain.getId())
                .storeId(store.getId())
                .terminalId(pos.getId())
                .cancellationType(EOpsCancellationType.RFD)
                .invoiceNumber("iiii")
                .tnxRefNo("refNo")
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .transactionFrom(123)
                .transactionTo(123)
                .userId("123")
                .build();

        Pageable pageRequest = Mockito.mock(Pageable.class);

        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.of(program));
        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(userProfile);
        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.of(member));
        try {
            opsTransactionService.search(req, pageRequest);
            Assert.fail("bad request here/ require program");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BAD_REQUEST);
        }
    }

    @Test
    public void search_programNotFound() {
        TransactionSearchReq req = TransactionSearchReq.builder()
                .businessId(business.getId())
                .corporationId(corporation.getId())
                .chainId(chain.getId())
                .storeId(store.getId())
                .terminalId(pos.getId())
                .programId(program.getId())
                .cancellationType(EOpsCancellationType.RFD)
                .invoiceNumber("iiii")
                .tnxRefNo("refNo")
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .transactionFrom(123)
                .transactionTo(123)
                .userId("123")
                .build();

        Pageable pageRequest = Mockito.mock(Pageable.class);

        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.ofNullable(null));
        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(userProfile);
        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.of(member));
        try {
            opsTransactionService.search(req, pageRequest);
            Assert.fail("Program not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.PROGRAM_NOT_FOUND);
        }
    }

    @Test
    public void search_programNotInBusiness() {
        TransactionSearchReq req = TransactionSearchReq.builder()
                .businessId(business.getId())
                .corporationId(corporation.getId())
                .chainId(chain.getId())
                .storeId(store.getId())
                .terminalId(pos.getId())
                .programId(program.getId())
                .cancellationType(EOpsCancellationType.RFD)
                .invoiceNumber("iii i")
                .tnxRefNo("refNo")
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .transactionFrom(123)
                .transactionTo(123)
                .userId("123")
                .build();

        Pageable pageRequest = Mockito.mock(Pageable.class);

        program.setBusinessId(-business.getId());
        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.ofNullable(program));
        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(userProfile);
        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.of(member));
        Page page = opsTransactionService.search(req, pageRequest);
        Assert.assertEquals(page.getContent().size(), 0);
        Assert.assertEquals(page.getTotalElements(), 0);
    }

    @Test
    public void search_userProfileNotFound() {
        TransactionSearchReq req = TransactionSearchReq.builder()
                .businessId(business.getId())
                .corporationId(corporation.getId())
                .chainId(chain.getId())
                .storeId(store.getId())
                .terminalId(pos.getId())
                .programId(program.getId())
                .cancellationType(EOpsCancellationType.RFD)
                .invoiceNumber("iii i")
                .tnxRefNo("refNo")
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .transactionFrom(123)
                .transactionTo(123)
                .userId("123")
                .build();

        Pageable pageRequest = Mockito.mock(Pageable.class);

        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.ofNullable(program));
        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(null);
        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.of(member));
        try {
            opsTransactionService.search(req, pageRequest);
            Assert.fail("User profile not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.USER_PROFILE_NOT_FOUND);
        }
    }

    @Test
    public void search_memberNotFound() {
        TransactionSearchReq req = TransactionSearchReq.builder()
                .businessId(business.getId())
                .corporationId(corporation.getId())
                .chainId(chain.getId())
                .storeId(store.getId())
                .terminalId(pos.getId())
                .programId(program.getId())
                .cancellationType(EOpsCancellationType.RFD)
                .invoiceNumber("iii i")
                .tnxRefNo("refNo")
                .memberId(member.getId())
                .memberCode(member.getMemberCode())
                .transactionFrom(123)
                .transactionTo(123)
                .userId("123")
                .build();

        Pageable pageRequest = Mockito.mock(Pageable.class);

        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.ofNullable(program));
        Mockito.when(userProfileRepository.findByMasterUserIdAndBusinessId(Mockito.anyString(), Mockito.any())).thenReturn(null);
        Mockito.when(memberService.findByUserProfileAndProgramId(userProfile.getId(), program.getId())).thenReturn(Optional.ofNullable(null));
        try {
            opsTransactionService.search(req, pageRequest);
            Assert.fail("User profile not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.USER_PROFILE_NOT_FOUND);
        }
    }

    @Test
    public void createTransaction_adjustment() {
        CreateTransactionReq req = new CreateTransactionReq();
        req.setBusinessId(business.getId());
        req.setProgramId(program.getId());
        req.setCorporationId(corporation.getId());
        req.setChainId(chain.getId());
        req.setStoreId(store.getId());
        req.setTerminalId(pos.getId());
        req.setIdType(EOpsIdType.CARD);
        req.setTransactionType(EOpsTransactionType.ADJUSTMENT);
        CreateTransactionReq.Transaction transaction = new CreateTransactionReq.Transaction();
        transaction.setGmv(BigDecimal.TEN);
        transaction.setInvoiceNo("111");
        transaction.setRedeemPoint(BigDecimal.ONE);
        transaction.setAdjustPoint(BigDecimal.ONE);
        req.setTransaction(transaction);

        business.setStatus(ECommonStatus.ACTIVE);
        program.setStatus(ECommonStatus.ACTIVE);
        store.setStatus(ECommonStatus.ACTIVE);
        pos.setStatus(ECommonStatus.ACTIVE);

        ProgramCorporation programCorporation =new ProgramCorporation();
        programCorporation.setCorporationId(corporation.getId());
        programCorporation.setProgramId(program.getId());

        ReasonCode reasonCode = Mockito.mock(ReasonCode.class);
        Function function = Mockito.mock(Function.class);
        Mockito.when(reasonCodeRepository.findByBusinessIdAndProgramIdAndId(Mockito.any(), Mockito.any(), Mockito.any()))
                        .thenReturn(Optional.of(reasonCode));
        Mockito.when(functionRepository.findByCode(Mockito.any()))
                        .thenReturn(function);
        Mockito.when(function.getId()).thenReturn(1);
        Mockito.when(reasonCode.getFunctionId()).thenReturn(1);
        Mockito.when(businessRepository.findById(business.getId())).thenReturn(Optional.of(business));
        Mockito.when(programRepository.findByIdAndBusinessId(program.getId(), business.getId())).thenReturn(Optional.of(program));
        Mockito.when(programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), req.getCorporationId()))
                .thenReturn(Optional.of(programCorporation));

        Mockito.when(chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.ofNullable(chain));

        Mockito.when(storeRepository.findByIdAndBusinessIdAndCorporationIdAndChainId(store.getId(), business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.of(store));

        Mockito.when(posRepository.findByIdAndStoreId(pos.getId(), store.getId()))
                .thenReturn(Optional.of(pos));

        OPSAuthenticatedPrincipal principal = Mockito.mock(OPSAuthenticatedPrincipal.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn(principal);
        Mockito.when(principal.getUserName()).thenReturn("ss");
        SecurityContextHolder.setContext(securityContext);

        Pool poolMock = Mockito.mock(Pool.class);
        Mockito.when(poolRepository.findByIdAndBusinessIdAndProgramId(Mockito.any(), Mockito.any(), Mockito.any()))
                        .thenReturn(Optional.of(poolMock));
        Mockito.when(poolMock.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        opsTransactionService.createTransaction(req);
    }

    @Test
    public void createTransaction_NotAdjustment() {
        CreateTransactionReq req = new CreateTransactionReq();
        req.setBusinessId(business.getId());
        req.setProgramId(program.getId());
        req.setCorporationId(corporation.getId());
        req.setChainId(chain.getId());
        req.setStoreId(store.getId());
        req.setTerminalId(pos.getId());
        req.setIdType(EOpsIdType.CARD);
        req.setTransactionType(EOpsTransactionType.SALE);
        CreateTransactionReq.Transaction transaction = new CreateTransactionReq.Transaction();
        transaction.setGmv(BigDecimal.TEN);
        transaction.setInvoiceNo("111");
        transaction.setRedeemPoint(BigDecimal.ONE);
        transaction.setAdjustPoint(BigDecimal.ONE);
        req.setTransaction(transaction);

        business.setStatus(ECommonStatus.ACTIVE);
        program.setStatus(ECommonStatus.ACTIVE);
        store.setStatus(ECommonStatus.ACTIVE);
        pos.setStatus(ECommonStatus.ACTIVE);

        ProgramCorporation programCorporation =new ProgramCorporation();
        programCorporation.setCorporationId(corporation.getId());
        programCorporation.setProgramId(program.getId());

        ReasonCode reasonCode = Mockito.mock(ReasonCode.class);
        Function function = Mockito.mock(Function.class);
        Mockito.when(reasonCodeRepository.findByBusinessIdAndProgramIdAndId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(reasonCode));
        Mockito.when(functionRepository.findByCode(Mockito.any()))
                .thenReturn(function);
        Mockito.when(function.getId()).thenReturn(1);
        Mockito.when(reasonCode.getFunctionId()).thenReturn(1);
        Currency currencyMock = Mockito.mock(Currency.class);
        Mockito.when(currencyMock.getCode()).thenReturn("aaa");
        Mockito.when(currencyRepository.findByCodeAndBusinessId(Mockito.any(), Mockito.any()))
                        .thenReturn(currencyMock);
        Mockito.when(businessRepository.findById(business.getId())).thenReturn(Optional.of(business));
        Mockito.when(programRepository.findByIdAndBusinessId(program.getId(), business.getId())).thenReturn(Optional.of(program));
        Mockito.when(programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), req.getCorporationId()))
                .thenReturn(Optional.of(programCorporation));
        Mockito.when(chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.ofNullable(chain));

        Mockito.when(storeRepository.findByIdAndBusinessIdAndCorporationIdAndChainId(store.getId(), business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.of(store));

        Mockito.when(posRepository.findByIdAndStoreId(pos.getId(), store.getId()))
                .thenReturn(Optional.of(pos));

        OPSAuthenticatedPrincipal principal = Mockito.mock(OPSAuthenticatedPrincipal.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn(principal);
        Mockito.when(principal.getUserName()).thenReturn("ss");
        SecurityContextHolder.setContext(securityContext);

        Pool poolMock = Mockito.mock(Pool.class);
        Mockito.when(poolRepository.findByIdAndBusinessIdAndProgramId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(poolMock));
        Mockito.when(poolMock.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        opsTransactionService.createTransaction(req);
    }

    @Test
    public void revertTransaction() {
        RevertTransactionReq req = new RevertTransactionReq();
        RevertTransactionReq.NewTxnInfo newTransactionInfo = new RevertTransactionReq.NewTxnInfo();
        newTransactionInfo.setTransactionTime(new Date().getTime());
        newTransactionInfo.setInvoiceNo("111134");
        newTransactionInfo.setTerminalId(pos.getId());
        newTransactionInfo.setRedeemPoint(BigDecimal.ONE);
        newTransactionInfo.setRefundAmount(BigDecimal.TEN);

        req.setTransactionId(transactionHistory.getPointTransactionId());
        req.setNewTxnInfo(newTransactionInfo);
        req.setOriginalInvoiceNo(transactionHistory.getInvoiceNo());
        req.setIsPartial(true);

        req.setNewTxnInfo(newTransactionInfo);

        transactionHistory.setProgramId(program.getId());
        transactionHistory.setStoreId(store.getId());
        transactionHistory.setPosId(pos.getId());
        transactionHistory.setPoolId(1);

        transactionHistory.setType(ETransactionType.SALE);
        Mockito.when(transactionHistoryService.getByTnxPointIds(Mockito.any()))
                        .thenReturn(Collections.singletonList(transactionHistory));
        business.setStatus(ECommonStatus.ACTIVE);
        program.setStatus(ECommonStatus.ACTIVE);
        store.setStatus(ECommonStatus.ACTIVE);
        pos.setStatus(ECommonStatus.ACTIVE);

        ProgramCorporation programCorporation =new ProgramCorporation();
        programCorporation.setCorporationId(corporation.getId());
        programCorporation.setProgramId(program.getId());

        ReasonCode reasonCode = Mockito.mock(ReasonCode.class);
        Function function = Mockito.mock(Function.class);
        Mockito.when(reasonCodeRepository.findByBusinessIdAndProgramIdAndId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(reasonCode));
        Mockito.when(functionRepository.findByCode(Mockito.any()))
                .thenReturn(function);
        Mockito.when(function.getId()).thenReturn(1);
        Mockito.when(reasonCode.getFunctionId()).thenReturn(1);
        Mockito.when(businessRepository.findById(business.getId())).thenReturn(Optional.of(business));
        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.of(program));
        Mockito.when(programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), corporation.getId()))
                .thenReturn(Optional.of(programCorporation));

        Mockito.when(chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.ofNullable(chain));

        Mockito.when(storeService.findActive(Mockito.any())).thenReturn(store);
        Mockito.when(posService.findActive(Mockito.any())).thenReturn(pos);

        OPSAuthenticatedPrincipal principal = Mockito.mock(OPSAuthenticatedPrincipal.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn(principal);
        Mockito.when(principal.getUserName()).thenReturn("ss");
        SecurityContextHolder.setContext(securityContext);
        Currency currencyMock = Mockito.mock(Currency.class);
        CurrencyRate currencyRateMock = Mockito.mock(CurrencyRate.class);

        Mockito.when(currencyMock.getCode()).thenReturn("gg");
        Mockito.when(currencyMock.getId()).thenReturn(1);

        Mockito.when(currencyRepository.findBaseCurrencyByCurrencyAndBusiness(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(List.of(currencyMock));
        Mockito.when(currencyRateMock.getSellRate()).thenReturn(1.0);
        Mockito.when(currencyService.findActive(Mockito.any()))
                .thenReturn(currencyMock);
        Pool poolMock = Mockito.mock(Pool.class);
        Mockito.when(poolRepository.findById(Mockito.any()))
                .thenReturn(Optional.of(poolMock));
        Mockito.when(poolMock.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(poolMock.getCurrencyId()).thenReturn(1);
        opsTransactionService.revertTransaction(req);
    }

    @Test
    public void revertTransaction_full() {
        RevertTransactionReq req = new RevertTransactionReq();
        RevertTransactionReq.NewTxnInfo newTransactionInfo = new RevertTransactionReq.NewTxnInfo();
        newTransactionInfo.setTransactionTime(new Date().getTime());
        newTransactionInfo.setInvoiceNo("111134");
        newTransactionInfo.setTerminalId(pos.getId());
        newTransactionInfo.setRedeemPoint(BigDecimal.ONE);
        newTransactionInfo.setRefundAmount(BigDecimal.TEN);

        req.setTransactionId(transactionHistory.getPointTransactionId());
        req.setNewTxnInfo(newTransactionInfo);
        req.setOriginalInvoiceNo(transactionHistory.getInvoiceNo());
        req.setIsPartial(false);

        req.setNewTxnInfo(newTransactionInfo);

        transactionHistory.setProgramId(program.getId());
        transactionHistory.setStoreId(store.getId());
        transactionHistory.setPosId(pos.getId());
        transactionHistory.setPoolId(1);

        transactionHistory.setType(ETransactionType.SALE);
        Mockito.when(transactionHistoryService.getByTnxPointIds(Mockito.any()))
                .thenReturn(Collections.singletonList(transactionHistory));
        business.setStatus(ECommonStatus.ACTIVE);
        program.setStatus(ECommonStatus.ACTIVE);
        store.setStatus(ECommonStatus.ACTIVE);
        pos.setStatus(ECommonStatus.ACTIVE);

        ProgramCorporation programCorporation =new ProgramCorporation();
        programCorporation.setCorporationId(corporation.getId());
        programCorporation.setProgramId(program.getId());

        ReasonCode reasonCode = Mockito.mock(ReasonCode.class);
        Function function = Mockito.mock(Function.class);
        Mockito.when(reasonCodeRepository.findByBusinessIdAndProgramIdAndId(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(reasonCode));
        Mockito.when(functionRepository.findByCode(Mockito.any()))
                .thenReturn(function);
        Mockito.when(function.getId()).thenReturn(1);
        Mockito.when(reasonCode.getFunctionId()).thenReturn(1);
        Mockito.when(businessRepository.findById(business.getId())).thenReturn(Optional.of(business));
        Mockito.when(programRepository.findById(program.getId())).thenReturn(Optional.of(program));
        Mockito.when(programCorporationRepository.findByProgramIdAndCorporationId(program.getId(), corporation.getId()))
                .thenReturn(Optional.of(programCorporation));

        Mockito.when(chainRepository.findByBusinessIdAndCorporationIdAndId(business.getId(), corporation.getId(), chain.getId()))
                .thenReturn(Optional.ofNullable(chain));

        Mockito.when(storeService.findActive(Mockito.any())).thenReturn(store);
        Mockito.when(posService.findActive(Mockito.any())).thenReturn(pos);

        OPSAuthenticatedPrincipal principal = Mockito.mock(OPSAuthenticatedPrincipal.class);
        Authentication authentication = Mockito.mock(Authentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        Mockito.when(authentication.getPrincipal()).thenReturn(principal);
        Mockito.when(principal.getUserName()).thenReturn("ss");
        SecurityContextHolder.setContext(securityContext);
        Currency currencyMock = Mockito.mock(Currency.class);
        CurrencyRate currencyRateMock = Mockito.mock(CurrencyRate.class);

        Mockito.when(currencyMock.getCode()).thenReturn("gg");
        Mockito.when(currencyMock.getId()).thenReturn(1);

        Mockito.when(currencyRepository.findBaseCurrencyByCurrencyAndBusiness(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(List.of(currencyMock));
        Mockito.when(currencyRateMock.getSellRate()).thenReturn(1.0);
        Mockito.when(currencyService.findActive(Mockito.any()))
                .thenReturn(currencyMock);
        Pool poolMock = Mockito.mock(Pool.class);
        Mockito.when(poolRepository.findById(Mockito.any()))
                .thenReturn(Optional.of(poolMock));
        Mockito.when(poolMock.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(poolMock.getCurrencyId()).thenReturn(1);
        opsTransactionService.revertTransaction(req);
    }

    @Test
    public void checkLog() {
        String txnRef = "d";
        TransactionAuditTrail transactionAuditTrail = Mockito.mock(TransactionAuditTrail.class);
        Mockito.when(transactionAuditTrail.getSource())
                .thenReturn("test");
        Map<String, Integer> transaction = Collections.singletonMap("store_id", 1);
        Map<String, Map<String, Integer>> payload = Collections.singletonMap("transaction", transaction);
        Mockito.when(transactionAuditTrail.getPayload())
                .thenReturn(payload);
        Mockito.when(transactionAuditTrailService.findAllByTransactionRef(txnRef))
                .thenReturn(List.of(transactionAuditTrail));
        opsTransactionService.checkLog(txnRef);
    }

//    @Test
//    public void getAvailableTransactionRequests() {
//        Pageable pageRequest = new OffsetBasedPageRequest(1, 1, null);
//        Mockito.when(transactionRequestService.find(Mockito.any(), Mockito.any())).
//                thenReturn(new PageImpl(new ArrayList(), pageRequest, 0));
//        opsTransactionService.getAvailableTransactionRequests(1L, ERequestProcessStatus.SUCCESS, Mockito.any());
//    }

    @Test
    public void getTransactionStatisticById() {
        Mockito.when(transactionBatchRequestRepository.findById(Mockito.any())).
                thenReturn(Optional.of(new TransactionBatchRequest()));
        opsTransactionService.getTransactionStatisticById(1L);
    }

    @Test
    public void retryBatchRequest() {
        TransactionBatchRequest transactionBatchRequest = new TransactionBatchRequest();
        transactionBatchRequest.setApprovalStatus(EApprovalStatus.APPROVED);
        transactionBatchRequest.setSuccessRequests(1);
        transactionBatchRequest.setTotalRequests(2);
        Mockito.when(transactionBatchRequestService.findActive(Mockito.any())).
                thenReturn(transactionBatchRequest);

        business = new Business();
        program = new Program();
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);

        Meta meta = new Meta();
        meta.setCode(ErrorCode.SUCCESS.getValue());

        APIResponse apiResponse = new APIResponse();
        apiResponse.setMeta(meta);

        Mockito.when(masterWorkerFeignClient.requestTransactions(Mockito.any())).
                thenReturn(apiResponse);
        opsTransactionService.retryBatchRequest(1L);
    }

    @TestConfiguration
    public static class TestConfig {
        @Bean
        public OpsTransactionService opsTransactionService() {
            return new OpsTransactionServiceImpl();
        }

        @Bean
        CheckLogSourceConfigPram checkLogSourceConfigPram() {
            return new CheckLogSourceConfigPram();
        }
    }
}
