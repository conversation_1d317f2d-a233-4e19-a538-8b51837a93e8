//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.entity.*;
//import com.oneid.loyalty.accounting.ops.exception.BusinessException;
//import com.oneid.loyalty.accounting.ops.model.req.SearchTransactionReq;
//import com.oneid.loyalty.accounting.ops.model.res.TransactionRes;
//import com.oneid.loyalty.accounting.ops.repository.WlTransactionRepository;
//import com.oneid.loyalty.accounting.ops.service.TransactionService;
//import com.oneid.loyalty.accounting.ops.service.core.*;
//import com.oneid.loyalty.accounting.ops.service.search.SpecificationBuilder;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.data.domain.Pageable;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@RunWith(SpringRunner.class)
//public class TestTransactionService {
//
//    private String memberCode = "Member1234Code";
//    private Long transactionId = 1l;
//    private String chainName = "chain Name";
//    private String partnerName = "partnerName";
//    private String memberProductAccountCode = "Product account code";
//    private String productCode = "productCode";
//    private WlMemberProductAccount wlMemberProductAccount = new WlMemberProductAccount();
//
//
//    private WlPartner partner = new WlPartner();
//    private List<WlPartner> partners = new ArrayList<>();
//
//    private WLTransaction transaction = new WLTransaction();
//    private List<WLTransaction> transactions = new ArrayList<>();
//    private Page<WLTransaction> transactionPage;
//
//    private String chainId = "chainId";
//    private WlChain wlChain = new WlChain();
//    private List<WlChain> wlChains = new ArrayList<>();
//
//    private String partnerCode = "partnerCode";
//
//    private TransactionRes transactionRes = new TransactionRes();
//    private TransactionRes transactionResDetails = new TransactionRes();
//
//    private List<TransactionRes> transactionResList = new ArrayList<>();
//    private Page<TransactionRes> transactionResPage;
//
//    private SearchTransactionReq txnReq = new SearchTransactionReq();
//
//
//    private int offset = 0, limit = 10, total = 100;
//    private OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset,limit,null);
//
//    private String storeId = "storeId";
//    private String storeName = "storeName";
//
//    private String posId = "posId";
//    private String posName = "pos Name";
//
//    private Map<String, String> storeMapCodeToName = new HashMap<>();
//    private Map<String, String> posMapCodeToName = new HashMap<>();
//
//    @Autowired
//    TransactionService transactionService;
//
//    @MockBean
//    WlTransactionRepository wlTransactionRepository;
//
//    @MockBean
//    CorePartnerService corePartnerService;
//
//    @MockBean
//    CoreChainService coreChainService;
//
//    @MockBean
//    CoreMemberService coreMemberService;
//
//    @MockBean
//    CoreStoreService coreStoreService;
//
//    @MockBean
//    CorePosService corePosService;
//
//    @MockBean
//    CoreProductAccountService coreProductAccountService;
//
//    @Before
//    public void before() {
//        partner.setName(partnerName);
//        partner.setPartnerCode(partnerCode);
//        partners.add(partner);
//
//        txnReq.setTransactionId(transactionId);
//        txnReq.setTransactionTo("********");
//        txnReq.setTransactionFrom("********");
//
//        transaction.setTransactionId(transactionId);
//        transaction.setMemberCode(memberCode);
//        transaction.setPartnerCode(partnerCode);
//        transaction.setChainId(chainId);
//        transaction.setStoreId(storeId);
//        transaction.setPosId(posId);
//        transaction.setMemberProductAccount(memberProductAccountCode);
//
//        wlChain.setChainId(chainId);
//        wlChain.setName(chainName);
//        wlChains.add(wlChain);
//
//        transactions.add(transaction);
//
//        transactionRes.setTransactionId(transactionId);
//        transactionRes.setPartnerCode(partnerCode);
//        transactionRes.setPartnerName(partnerName);
//        transactionRes.setChainCode(chainId);
//        transactionRes.setChainName(chainName);
//        transactionRes.setStoreId(storeId);
//        transactionRes.setStoreName(storeName);
//        transactionRes.setPosId(posId);
//        transactionRes.setPosName(posName);
//        transactionRes.setAccountCode(memberProductAccountCode);
//        transactionResList.add(transactionRes);
//
//        transactionResDetails.setTransactionId(transactionId);
//        transactionResDetails.setPartnerCode(partnerCode);
//        transactionResDetails.setPartnerName(partnerName);
//        transactionResDetails.setChainCode(chainId);
//        transactionResDetails.setChainName(chainName);
//        transactionResDetails.setStoreId(storeId);
//        transactionResDetails.setStoreName(storeName);
//        transactionResDetails.setPosId(posId);
//        transactionResDetails.setPosName(posName);
//        transactionResDetails.setAccountType(productCode);
//        transactionResDetails.setAccountCode(memberProductAccountCode);
//        transactionResDetails.setRefundedInvoices(new ArrayList<>());
//
//        transactionResPage = new PageImpl<>(transactionResList, pageRequest, total);
//        transactionPage = new PageImpl<>(transactions, pageRequest, total);
//
//        storeMapCodeToName.put(storeId, storeName);
//        posMapCodeToName.put(posId, posName);
//
//        wlMemberProductAccount.setProductCode(productCode);
//        wlMemberProductAccount.setProductAccountCode(memberProductAccountCode);
//
//        Mockito.when(corePartnerService.getAll()).
//                thenReturn(partners);
//        Mockito.when(corePartnerService.getMapCodeToNamePartner()).
//                thenReturn(partners.stream().collect(Collectors.toMap(t->t.getPartnerCode(),t->t.getName())));
//        Mockito.when(coreChainService.mapCodeToNameChain()).
//                thenReturn(wlChains.stream().collect(Collectors.toMap(t->t.getChainId(),t->t.getName())));
//        Mockito.when(coreChainService.getAll()).
//                thenReturn(wlChains);
//        Mockito.when(coreChainService.getAllChainByPartnerCode(partnerCode)).
//                thenReturn(wlChains);
//
//        Mockito.when(coreMemberService.find(memberCode)).
//                thenReturn(new WlMember());
//
//        Mockito.when(wlTransactionRepository.findAll((SpecificationBuilder)Mockito.any(), (Pageable) Mockito.any())).
//                thenReturn(transactionPage);
//        Mockito.when(wlTransactionRepository.findById(transactionId)).thenReturn(Optional.of(transaction));
//
//        Mockito.when(coreStoreService.mapCodeToNameOfPartner(partnerCode)).thenReturn(storeMapCodeToName);
//        Mockito.when(corePosService.mapCodeToNameOfStore(storeId)).thenReturn(posMapCodeToName);
//        Mockito.when(coreStoreService.mapCodeToName()).thenReturn(storeMapCodeToName);
//        Mockito.when(corePosService.mapCodeToName()).thenReturn(posMapCodeToName);
//        Mockito.when(coreProductAccountService.findOne(memberProductAccountCode)).thenReturn(wlMemberProductAccount);
//    }
//
//    @Test
//    public void getTransactionsByMemberCode() {
//        Page<TransactionRes> exp = transactionService.getSearchHistoryForMember(memberCode,
//                txnReq,pageRequest);
//        Assert.assertEquals(exp, transactionResPage);
//    }
//
//    @Test
//    public void getTransactionDetails() {
//        TransactionRes exp = transactionService.getTransactionDetails(txnReq);
//        Assert.assertEquals(exp, transactionResDetails);
//    }
//
//    @Test(expected = BusinessException.class)
//    public void getTransactionDetailsException() {
//        TransactionRes exp = transactionService.getTransactionDetails(new SearchTransactionReq());
//        Assert.assertEquals(exp, transactionResDetails);
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public TransactionService transactionService() {
//            return new TransactionServiceImpl();
//        }
//    }
//}
