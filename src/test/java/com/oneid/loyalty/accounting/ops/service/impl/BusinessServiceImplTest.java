package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.req.CreateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.res.BusinessDropDownRes;
import com.oneid.loyalty.accounting.ops.model.res.BusinessRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.CreateBusinessMCReq;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.GetApproveBusinessMCService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.business.UpdateBusinessMCReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.District;
import com.oneid.oneloyalty.common.entity.Ward;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.DistrictRepository;
import com.oneid.oneloyalty.common.repository.WardRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
public class BusinessServiceImplTest {

    @Autowired
    private OpsBusinessService opsBusinessService;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private CountryService countryService;

    @MockBean
    private ProvinceService provinceService;

    @MockBean
    private DistrictService districtService;

    @MockBean
    private DistrictRepository districtRepository;

    @MockBean
    private WardService wardService;
    
    @MockBean
    private WardRepository wardRepository;

    @MockBean
    GetApproveBusinessMCService getApproveBusinessMCService;

    /* =========================== INIT PARAM =================== */
    private Business business = new Business();
    private final Integer businessId = 1234;
    private final String businessCode = "businessCode";
    private String businessName = "businessName";
    private String businessPhone = "***********";
    private final Integer countryId = 1;
    private final Integer provinceId = 2;
    private final Integer districtId = 3;
    private final Integer wardId = 3;
    /* ========================= END INT PARAM =================== */


    @Test
    public void getApproveUpdate() {
        UpdateBusinessMCReq req = new UpdateBusinessMCReq();
        UpdateBusinessReq updateReq = new UpdateBusinessReq();
        req.setPayload(updateReq);
        try {
            opsBusinessService.getApproveUpdate(req);
            Assert.fail("Business not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BUSINESS_NOT_FOUND);
        }

        Business business = new Business();
        business.setId(1);
        updateReq.setBusinessId(1);
        updateReq.setCountryId(countryId);
        updateReq.setProvinceId(provinceId);
        updateReq.setDistrictId(districtId);
        updateReq.setWardId(12);
        Mockito.when(businessRepository.findById(1)).thenReturn(Optional.ofNullable(business));
        District district = new District();
        district.setCode("22");
        Ward ward = new Ward();
        ward.setId(12);
        ward.setCode("SS");
        Mockito.when(this.districtRepository.findById(districtId)).thenReturn(Optional.of(district));
        Mockito.when(this.wardService.getWard(districtId, "SS")).thenReturn(ward);

        opsBusinessService.getApproveUpdate(req);
    }

    @Test
    public void create_MissCountry_ExceptionThrown() {
        // Prepare
        CreateBusinessMCReq businessReq = new CreateBusinessMCReq();
        businessReq.setBearerToken("I");
        businessReq.setPayload(new CreateBusinessReq());

        try {
            this.opsBusinessService.getApproveCreate(businessReq);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.BAD_REQUEST, e.getCode());
        }
    }

    @Test
    public void create_MissProvince_ExceptionThrown() {
        // Prepare
        CreateBusinessReq businessReq = new CreateBusinessReq();
        businessReq.setStatus("I");
        businessReq.setCountryId(countryId);
        CreateBusinessMCReq req = new CreateBusinessMCReq();
        req.setBearerToken("I");
        req.setPayload(businessReq);

        try {
            this.opsBusinessService.getApproveCreate(req);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.BAD_REQUEST, e.getCode());
        }
    }

    @Test
    public void create_MissDistrict_ExceptionThrown() {
        // Prepare
        CreateBusinessMCReq req = new CreateBusinessMCReq();
        req.setBearerToken("I");

        CreateBusinessReq businessReq = new CreateBusinessReq();
        businessReq.setStatus("I");
        businessReq.setCountryId(countryId);
        businessReq.setProvinceId(provinceId);

        req.setPayload(businessReq);


        try {
            this.opsBusinessService.getApproveCreate(req);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.BAD_REQUEST, e.getCode());
        }
    }

    @Test
    public void create_MissWard_ExceptionThrown() {
        // Prepare
        CreateBusinessReq businessReq = new CreateBusinessReq();
        businessReq.setStatus("I");
        businessReq.setCountryId(countryId);
        businessReq.setProvinceId(provinceId);
        businessReq.setDistrictId(districtId);


        CreateBusinessMCReq req = new CreateBusinessMCReq();
        req.setBearerToken("I");
        req.setPayload(businessReq);

        District district = new District();
        district.setCode("22");

        // Mock
        Mockito.when(this.districtRepository.findById(districtId)).thenReturn(Optional.of(district));

        try {
            this.opsBusinessService.getApproveCreate(req);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.BAD_REQUEST, e.getCode());
        }
    }

    @Test
    public void getOne_NotFound_ExceptionThrown() {
        // Mock
        Mockito.when(this.businessRepository.findById(businessId)).thenReturn(Optional.empty());

        try {
            this.opsBusinessService.getOne(businessId);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.BUSINESS_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void getOne_Success() {
        // Prepare
        ECommonStatus status = ECommonStatus.ACTIVE;

        business.setId(businessId);
        business.setStatus(status);

        // Mock
        Mockito.when(this.businessRepository.findById(businessId)).thenReturn(Optional.of(business));

        // Test
        BusinessRes response = this.opsBusinessService.getOne(businessId);
        Assert.assertEquals(businessId, response.getId());
        Assert.assertEquals(status.getValue(), response.getStatus());
    }

    @Test
    public void getAll() {
        businessService.searchAll(new SpecificationBuilder<>())
                .stream().map(BusinessDropDownRes::valueOf).collect(Collectors.toList());
    }

    @Test
    public void search() {
        Pageable pageable = PageRequest.of(0, 10);
        Mockito.when(businessRepository.findAll((Specification<Business>) Mockito.any(), (Pageable) Mockito.any()))
                .thenReturn(new PageImpl<>(new ArrayList<>(), pageable, 199));

        Assert.assertEquals(opsBusinessService.search(new SpecificationBuilder<>(), pageable),
                new PageImpl<>(new ArrayList<>(), pageable, 199));
    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsBusinessService opsBusinessService() {
            return new OpsBusinessServiceImpl();
        }
    }
}