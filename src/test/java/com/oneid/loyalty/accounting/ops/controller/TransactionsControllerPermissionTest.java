package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.OPSConstant;
import com.oneid.loyalty.accounting.ops.constant.EOpsIdType;
import com.oneid.loyalty.accounting.ops.constant.EOpsTransactionType;
import com.oneid.loyalty.accounting.ops.constant.ETransactionTemplateType;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.TransactionsController;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.RevertTransactionReq;
import com.oneid.loyalty.accounting.ops.model.req.TransactionSearchReq;
import com.oneid.loyalty.accounting.ops.model.res.TransactionBatchRequestDetailRes;
import com.oneid.loyalty.accounting.ops.model.res.TransactionStatisticRes;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.excel.OpsCommonExcelService;
import com.oneid.oneloyalty.common.constant.ECharacterSet;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(TransactionsController.class)
public class TransactionsControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsCommonExcelService opsCommonExcelService;


    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getTransactions() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        Integer mockTransactionForm = 1;
        Integer mockTransactionTo = 1;

        Mockito.when(opsTransactionService
                        .search(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        Mockito.when(opsTransactionService
                        .search(Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        TransactionSearchReq transactionSearchReq = TransactionSearchReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .transactionFrom(mockTransactionForm)
                .transactionTo(mockTransactionTo).build();

        String reqString = objectMapper.writeValueAsString(transactionSearchReq);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions/list")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("offset", "1")
                .queryParam("limit", "1")
                .content(reqString);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getTransactionsNoPermission() throws Exception {
        mockOPSAuthenticatedPrincipal(Map.of());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/list", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("tnx_date_to", "1")
                .queryParam("tnx_date_from", "1");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getTransactionsDetail() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/{id}", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getTransactionsDetail_NoPermission() throws Exception {

        mockOPSAuthenticatedPrincipal(Map.of());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/{id}", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void createTransaction() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.CREATE))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        APIResponse<Object> serviceReturn = new APIResponse<>();
        serviceReturn.setMeta(new Meta(200, "SUCCESS"));
        serviceReturn.setData(null);

        Mockito.doReturn(serviceReturn).when(opsTransactionService).createTransaction(Mockito.any());

        CreateTransactionReq req = new CreateTransactionReq();
        CreateTransactionReq.Transaction transaction = new CreateTransactionReq.Transaction();
        transaction.setInvoiceNo("4567899");
        transaction.setGmv(BigDecimal.ZERO);
        transaction.setGrossAmount(BigDecimal.ZERO);
        transaction.setRedeemPoint(BigDecimal.ZERO);

        req.setIdType(EOpsIdType.IDENTIFY);
        req.setTransactionType(EOpsTransactionType.BURN);
        req.setTransaction(transaction);

        String reqString = objectMapper.writeValueAsString(req);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(reqString);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void createTransaction_noPermission() throws Exception {
        mockOPSAuthenticatedPrincipal(Map.of());

        APIResponse<Object> serviceReturn = new APIResponse<>();
        serviceReturn.setMeta(new Meta(200, "SUCCESS"));
        serviceReturn.setData(null);

        Mockito.doReturn(serviceReturn).when(opsTransactionService).createTransaction(Mockito.any());

        CreateTransactionReq req = new CreateTransactionReq();
        CreateTransactionReq.Transaction transaction = new CreateTransactionReq.Transaction();
        transaction.setInvoiceNo("4567899");
        transaction.setGmv(BigDecimal.ZERO);
        transaction.setGrossAmount(BigDecimal.ZERO);
        transaction.setRedeemPoint(BigDecimal.ZERO);

        req.setIdType(EOpsIdType.IDENTIFY);
        req.setTransactionType(EOpsTransactionType.BURN);
        req.setTransaction(transaction);

        String reqString = objectMapper.writeValueAsString(req);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(reqString);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void reverse() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION)
                        || role.equals(AccessRole.REFUNDING_CRATE_NEW_TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.EDIT))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        APIResponse<Object> serviceReturn = new APIResponse<>();
        serviceReturn.setMeta(new Meta(200, "SUCCESS"));
        serviceReturn.setData(null);

        Mockito.doReturn(serviceReturn).when(opsTransactionService).revertTransaction(Mockito.any());

        RevertTransactionReq req = new RevertTransactionReq();
        req.setTransactionId("123");
        req.setOriginalInvoiceNo("ssss");
        req.setIsPartial(false);

        String reqString = objectMapper.writeValueAsString(req);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions/reverse")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(reqString);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void reverse_noPermission() throws Exception {
        mockOPSAuthenticatedPrincipal(Map.of());

        APIResponse<Object> serviceReturn = new APIResponse<>();
        serviceReturn.setMeta(new Meta(200, "SUCCESS"));
        serviceReturn.setData(null);

        Mockito.doReturn(serviceReturn).when(opsTransactionService).createTransaction(Mockito.any());

        Mockito.doReturn(serviceReturn).when(opsTransactionService).revertTransaction(Mockito.any());

        RevertTransactionReq req = new RevertTransactionReq();
        req.setTransactionId("123");
        req.setOriginalInvoiceNo("ssss");
        req.setIsPartial(false);

        String reqString = objectMapper.writeValueAsString(req);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions/reverse")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(reqString);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getInReviewTransactionBatchRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTransactionService, times(0)).filterTransaction(any(), any(), any(), any(), any(), any(), any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getInReviewTransactionBatchRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Mockito.when(opsTransactionService.filterTransaction(any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void randomInvoiceNo_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        String pattern = anyString();

        ECharacterSet type = ECharacterSet.ALPHANUMERIC;

        Mockito.when(opsTransactionService.randomInvoiceNumber(pattern, eq(type))).thenReturn("");

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/random-invoice-no")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("pattern", pattern)
                .queryParam("type", type.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void randomInvoiceNo_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.CREATE))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        String pattern = anyString();

        ECharacterSet type = ECharacterSet.ALPHANUMERIC;

        Mockito.when(opsTransactionService.randomInvoiceNumber(pattern, eq(type))).thenReturn("");

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/random-invoice-no")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("pattern", pattern)
                .queryParam("type", type.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void downloadTransactionBatchRequestTemplate_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/template")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .queryParam("type", ETransactionTemplateType.EARN_BURN_SALE.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        MockHttpServletResponse response = result.getResponse();

        BaseResponseData baseResponseData = objectMapper.readValue(response.getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
        assertEquals("application/json", response.getContentType());
    }

    @Test
    public void downloadTransactionBatchRequestTemplate_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.CREATE)).mapToLong(AccessPermission::getCode).sum();
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        ETransactionTemplateType template = ETransactionTemplateType.EARN_BURN_SALE;
        String pathFileExcel = "template/txn/Template_TransactionRequest_Earn_Burn_Sale.xlsx";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        Mockito.when(opsCommonExcelService.getTemplate(OPSConstant.TXN, template.getFileName()))
                .thenReturn(new ResponseEntity<>(new InputStreamResource(new ClassPathResource(pathFileExcel)
                        .getInputStream()), headers, HttpStatus.OK));

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/template")
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .queryParam("type", String.valueOf(template));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        MockHttpServletResponse response = result.getResponse();
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8", response.getContentType());
    }

    @Test
    public void getAvailableFileReview_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Long requestId = 1L;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("batch_request_id", String.valueOf(requestId));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTransactionService, times(0)).getAvailableTransactionRequests(eq(requestId), any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableFileReview_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Long requestId = 1L;

        Mockito.when(opsTransactionService.getAvailableTransactionRequests(eq(requestId), any(), any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("batch_request_id", requestId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getStatistics_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Long requestId = 1L;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/available/" + requestId + "/statistic")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTransactionService, times(0)).getTransactionStatisticById(eq(requestId));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getStatistics_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        Long requestId = 1L;

        Mockito.when(opsTransactionService.getTransactionStatisticById(eq(requestId))).thenReturn(new TransactionStatisticRes());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/transactions/available/" + requestId + "/statistic")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void retry_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Long requestId = 1L;
        Mockito.when(opsTransactionService.retryBatchRequest(requestId)).thenReturn(TransactionBatchRequestDetailRes.builder().build());

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions/requests/retry/" + requestId)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void retry_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();
        permissions.put(AccessRole.TRANSACTION, AccessPermission.VIEW.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        Long requestId = 1L;
        Mockito.when(opsTransactionService.retryBatchRequest(requestId)).thenReturn(TransactionBatchRequestDetailRes.builder().build());

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/transactions/requests/retry/" + requestId)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
}
