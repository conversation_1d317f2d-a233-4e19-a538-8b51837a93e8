package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.constant.ConditionAttributeDto;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeDataDisplayType;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.*;
import com.oneid.loyalty.accounting.ops.repository.elasticsearch.SchemeESRepository;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.OpsSchemeService;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Pool;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.*;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.PoolService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.SchemeService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
public class TestOpsSchemeServiceImpl {

    @Autowired
    private OpsSchemeService opsSchemeService;

    @MockBean
    private SchemeService schemeService;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private ProgramService programService;

    @MockBean
    private PoolService poolService;

    @MockBean
    private OpsRuleService opsRuleService;

    @MockBean
    private OpsFormulaService opsFormulaService;

    @MockBean
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @MockBean
    private MakerCheckerConfigParam makerCheckerConfigParam;

    @MockBean
    private OpsConditionService opsConditionService;

    @MockBean
    private AuditorAware<OPSAuthenticatedPrincipal> auditorAware;

    @MockBean
    private ProgramPoolRepository programPoolRepository;

    @MockBean
    private SchemeESRepository schemeESRepository;

    @MockBean
    private CorporationRepository corporationRepository;

    @MockBean
    private ChainRepository chainRepository;

    @MockBean
    private StoreRepository storeRepository;

    @MockBean
    private PosRepository posRepository;


    /* Data */
    private VerifySchemeInfoReq verifySchemeInfoReq;
    private Pool pool;
    private Program program;
    private CreateSchemeReq createSchemeReq;
    private Business business;


    @Before
    public void before() {
        Long currentTime = DateTimes.toEpochSecond(new Date());
        verifySchemeInfoReq = new VerifySchemeInfoReq();
        verifySchemeInfoReq.setSchemeCode("SCHEME_CODE");
        verifySchemeInfoReq.setSchemeName("SCHEME_NAME");
        verifySchemeInfoReq.setSchemeType(ESchemeType.TOPUP);
        verifySchemeInfoReq.setDescription("Description");

        verifySchemeInfoReq.setStartDate(currentTime);
        verifySchemeInfoReq.setEndDate(DateTimes.addDays(currentTime, 1));
        verifySchemeInfoReq.setPoolId(1);
        verifySchemeInfoReq.setProgramId(1);
        verifySchemeInfoReq.setRuleLogic(EConditionType.ANY);

        pool = new Pool();
        pool.setBusinessId(1);
        pool.setProgramId(1);
        pool.setStatus(ECommonStatus.ACTIVE);
        pool.setId(1);
        pool.setCode("POOL_CODE");

        program = new Program();
        program.setBusinessId(1);
        program.setStatus(ECommonStatus.ACTIVE);
        program.setId(1);
        program.setCode("PROGRAM_CODE");
        program.setName("PROGRAM_NAME");

        createSchemeReq = new CreateSchemeReq();
        createSchemeReq.setSchemeCode("SCHEME_CODE");
        createSchemeReq.setSchemeName("SCHEME_NAME");
        createSchemeReq.setSchemeType(ESchemeType.TOPUP);
        createSchemeReq.setDescription("Description");

        createSchemeReq.setStartDate(currentTime);
        createSchemeReq.setEndDate(DateTimes.addDays(currentTime, 1));
        createSchemeReq.setPoolId(1);
        createSchemeReq.setProgramId(1);
        createSchemeReq.setRuleLogic(EConditionType.ANY);
        createSchemeReq.setRuleList(Collections.emptyList());

        business = new Business();
        business.setId(1);
        business.setCode("BUSINESS_CODE");
        business.setName("Business name");
        business.setStatus(ECommonStatus.ACTIVE);
    }

    @Test
    public void verifySchemeInfo_poolNotInProgram() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);

        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[][]{});
        pool.setProgramId(-1);
        try {
            opsSchemeService.verifySchemeInfo(verifySchemeInfoReq);
            Assert.fail("Pool not in program here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.POOL_NOT_IN_PROGRAM);
        }
    }

    @Test
    public void verifySchemeInfo_success() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        opsSchemeService.verifySchemeInfo(verifySchemeInfoReq);
        Mockito.verify(programService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getProgramId());
        Mockito.verify(poolService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getPoolId());
    }

    @Test
    public void verifySchemeInfo_success2() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);

        Pool pool2 = new Pool();
        pool2.setProgramId(verifySchemeInfoReq.getProgramId());
        pool2.setStatus(ECommonStatus.ACTIVE);
        pool2.setId(verifySchemeInfoReq.getPoolId());
        Object[] programPool = new Object[]{pool2, program};

        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[][]{programPool});
        pool.setProgramId(-1);
        opsSchemeService.verifySchemeInfo(verifySchemeInfoReq);
        Mockito.verify(programService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getProgramId());
        Mockito.verify(poolService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getPoolId());
    }

    @Test
    public void verifySchemeInfo_invalidStartDateAndEndDate() {
        verifySchemeInfoReq.setStartDate(DateTimes.addDays(verifySchemeInfoReq.getEndDate(), 1));
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        try {
            opsSchemeService.verifySchemeInfo(verifySchemeInfoReq);
            Assert.fail("throw exception START_DATE_IS_LESS_END_DATE here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.START_DATE_IS_LESS_END_DATE);
        }
    }

    @Test
    public void verifySchemeInfo_invalidSchemeCode() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);

        Scheme schemeExisted = new Scheme();
        schemeExisted.setId(1);
        schemeExisted.setCode(verifySchemeInfoReq.getSchemeCode());
        Mockito.when(schemeService.find(verifySchemeInfoReq.getProgramId(), verifySchemeInfoReq.getSchemeCode()))
                .thenReturn(Optional.of(schemeExisted));

        try {
            opsSchemeService.verifySchemeInfo(verifySchemeInfoReq);
            Assert.fail("Scheme code existed in program here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_CODE_EXISTED);
        }
    }

    @Test
    public void verifySchemeCombine() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        opsSchemeService.verifySchemeCombine(createSchemeReq);
        Mockito.verify(programService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getProgramId());
        Mockito.verify(poolService, Mockito.times(1))
                .findActive(verifySchemeInfoReq.getPoolId());
    }

    @Test
    public void getApproveCreate() {
        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        opsSchemeService.getApproveCreate(createSchemeReq);

        Mockito.verify(programService, Mockito.atLeast(1))
                .findActive(createSchemeReq.getProgramId());
        Mockito.verify(poolService, Mockito.atLeast(1))
                .findActive(createSchemeReq.getPoolId());
    }

    @Test
    public void getApproveUpdate_success() {
        UpdateSchemeReq updateSchemeReq = new UpdateSchemeReq();
        updateSchemeReq.setSchemeCode("SCHEME_CODE");
        updateSchemeReq.setSchemeName("SCHEME_NAME");
        updateSchemeReq.setSchemeType(ESchemeType.TOPUP);
        updateSchemeReq.setDescription("Description");
        Long currentTime = DateTimes.toEpochSecond(new Date());
        updateSchemeReq.setStartDate(currentTime);
        updateSchemeReq.setEndDate(DateTimes.addDays(currentTime, 1));
        updateSchemeReq.setPoolId(1);
        updateSchemeReq.setProgramId(1);
        updateSchemeReq.setRuleLogic(EConditionType.ANY);
        updateSchemeReq.setRuleList(Collections.EMPTY_LIST);

        Scheme scheme = new Scheme();
        scheme.setCode(updateSchemeReq.getSchemeCode());
        scheme.setProgramId(updateSchemeReq.getProgramId());
        scheme.setId(1);

        ConditionRecordReq conditionReq = new ConditionRecordReq();
        conditionReq.setAttribute("ATTRIBUTE");
        conditionReq.setOperator(EAttributeOperator.EQUAL);
        conditionReq.setValue("1");
        RuleRecordReq ruleReq = new RuleRecordReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setListCondition(Collections.singletonList(conditionReq));
        updateSchemeReq.setRuleList(Collections.singletonList(ruleReq));

        Mockito.when(opsConditionService.conditionAttributeDtos(program.getId()))
                .thenReturn(Collections.singletonList(
                        ConditionAttributeDto.builder()
                                .attribute("ATTRIBUTE")
                                .dataType("STRING")
                                .dataTypeDisplay(EAttributeDataDisplayType.TEXT)
                                .operators(List.of("=="))
                                .supportFilter(Boolean.FALSE)
                                .resourcePath(null)
                                .build()
                ));

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(schemeService.find(updateSchemeReq.getSchemeId()))
                .thenReturn(Optional.of(scheme));
        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        opsSchemeService.getApproveUpdate(updateSchemeReq);

        Mockito.verify(programService, Mockito.atLeast(1))
                .findActive(createSchemeReq.getProgramId());
        Mockito.verify(poolService, Mockito.atLeast(1))
                .findActive(createSchemeReq.getPoolId());
    }

    @Test
    public void getApproveUpdate_schemeNotFound() {
        UpdateSchemeReq updateSchemeReq = new UpdateSchemeReq();
        updateSchemeReq.setSchemeCode("SCHEME_CODE");
        updateSchemeReq.setSchemeName("SCHEME_NAME");
        updateSchemeReq.setSchemeType(ESchemeType.TOPUP);
        updateSchemeReq.setDescription("Description");
        Long currentTime = DateTimes.toEpochSecond(new Date());
        updateSchemeReq.setStartDate(currentTime);
        updateSchemeReq.setEndDate(DateTimes.addDays(currentTime, 1));
        updateSchemeReq.setPoolId(1);
        updateSchemeReq.setProgramId(1);
        updateSchemeReq.setRuleLogic(EConditionType.ANY);

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        try {
            opsSchemeService.getApproveUpdate(updateSchemeReq);
            Assert.fail("Scheme not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_NOT_FOUND);
        }
    }

    @Test
    public void getApproveUpdate_schemeCannotUpdate() {
        UpdateSchemeReq updateSchemeReq = new UpdateSchemeReq();
        updateSchemeReq.setSchemeCode("SCHEME_CODE____");
        updateSchemeReq.setSchemeName("SCHEME_NAME");
        updateSchemeReq.setSchemeType(ESchemeType.TOPUP);
        updateSchemeReq.setDescription("Description");
        Long currentTime = DateTimes.toEpochSecond(new Date());
        updateSchemeReq.setStartDate(currentTime);
        updateSchemeReq.setEndDate(DateTimes.addDays(currentTime, 1));
        updateSchemeReq.setPoolId(1);
        updateSchemeReq.setProgramId(1);
        updateSchemeReq.setRuleLogic(EConditionType.ANY);
        updateSchemeReq.setStatus(ECommonStatus.INACTIVE);
        updateSchemeReq.setRuleList(Collections.EMPTY_LIST);

        Scheme scheme = new Scheme();
        scheme.setCode(updateSchemeReq.getSchemeCode());
        scheme.setProgramId(updateSchemeReq.getProgramId());
        scheme.setId(1);
        scheme.setStatus(ECommonStatus.ACTIVE);

        Mockito.when(schemeService.find(updateSchemeReq.getSchemeId()))
                .thenReturn(Optional.of(scheme));

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        try {
            opsSchemeService.getApproveUpdate(updateSchemeReq);
            Assert.fail("Scheme cannot update here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_CANNOT_UPDATE);
        }
    }


    @Test
    public void getApproveUpdate_schemeCannotUpdate2() {
        UpdateSchemeReq updateSchemeReq = new UpdateSchemeReq();
        updateSchemeReq.setSchemeCode("SCHEME_CODE____");
        updateSchemeReq.setSchemeName("SCHEME_NAME");
        updateSchemeReq.setSchemeType(ESchemeType.TOPUP);
        updateSchemeReq.setDescription("Description");
        Long currentTime = DateTimes.toEpochSecond(new Date());
        updateSchemeReq.setStartDate(currentTime);
        updateSchemeReq.setEndDate(DateTimes.addDays(currentTime, 1));
        updateSchemeReq.setPoolId(1);
        updateSchemeReq.setProgramId(1);
        updateSchemeReq.setRuleLogic(EConditionType.ANY);
        updateSchemeReq.setRuleList(Collections.EMPTY_LIST);

        Scheme scheme = new Scheme();
        scheme.setCode(updateSchemeReq.getSchemeCode());
        scheme.setProgramId(updateSchemeReq.getProgramId());
        scheme.setId(1);
        scheme.setStatus(ECommonStatus.INACTIVE);

        Mockito.when(schemeService.find(updateSchemeReq.getSchemeId()))
                .thenReturn(Optional.of(scheme));

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.findActive(1)).thenReturn(program);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        try {
            opsSchemeService.getApproveUpdate(updateSchemeReq);
            Assert.fail("Scheme cannot update here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_CANNOT_UPDATE);
        }
    }

    @Test
    public void getDetails() {
        Scheme scheme = new Scheme();
        scheme.setProgramId(1);
        scheme.setId(1);
        scheme.setStatus(ECommonStatus.INACTIVE);

        Mockito.when(schemeService.find(1))
                .thenReturn(Optional.of(scheme));

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programService.find(1)).thenReturn(Optional.of(program));
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        opsSchemeService.getDetails(1);
    }

    @Test
    public void getDetails_notFound() {
        try {
            opsSchemeService.getDetails(1);
            Assert.fail("Scheme not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_NOT_FOUND);
        }
    }

    @Test
    public void getDetails_programNotFound() {
        Scheme scheme = new Scheme();
        scheme.setProgramId(1);
        scheme.setId(1);
        scheme.setStatus(ECommonStatus.INACTIVE);

        Mockito.when(schemeService.find(1))
                .thenReturn(Optional.of(scheme));

        Mockito.when(poolService.findActive(1)).thenReturn(pool);
        Mockito.when(programPoolRepository.findPoolByProgramId(1)).thenReturn(new Object[0][0]);
        Mockito.when(auditorAware.getCurrentAuditor()).thenReturn(Optional.of(new OPSAuthenticatedPrincipal()));
        Mockito.when(businessService.findActive(program.getBusinessId()))
                .thenReturn(business);

        Mockito.when(makerCheckerFeignClient.createRequestScheme(Mockito.any()))
                .thenReturn(new APIResponse<>());

        try {
            opsSchemeService.getDetails(1);
            Assert.fail("Program not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.PROGRAM_NOT_FOUND);
        }
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsSchemeService opsSchemeService() {
            return new OpsSchemeServiceImpl();
        }
    }
}
