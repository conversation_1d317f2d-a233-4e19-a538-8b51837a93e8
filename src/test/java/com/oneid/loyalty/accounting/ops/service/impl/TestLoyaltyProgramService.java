//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.constant.ECommonStatus;
//import com.oneid.loyalty.accounting.ops.entity.WlLoyaltyProgram;
//import com.oneid.loyalty.accounting.ops.mapper.LoyaltyProgramMapper;
//import com.oneid.loyalty.accounting.ops.model.req.CreateProgramReq;
//import com.oneid.loyalty.accounting.ops.model.req.ProgramGetAllReq;
//import com.oneid.loyalty.accounting.ops.model.req.SearchProgramReq;
//import com.oneid.loyalty.accounting.ops.model.req.UpdateProgramReq;
//import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
//import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
//import com.oneid.loyalty.accounting.ops.repository.WlProductRepository;
//import com.oneid.loyalty.accounting.ops.service.LoyaltyProgramService;
//import com.oneid.loyalty.accounting.ops.service.core.CoreProgramService;
//import com.oneid.loyalty.accounting.ops.service.search.SpecificationBuilder;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.data.domain.Pageable;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@RunWith(SpringRunner.class)
//public class TestLoyaltyProgramService {
//
//    @Autowired
//    private LoyaltyProgramService loyaltyProgramService;
//
//    @MockBean
//    CoreProgramService coreProgramService;
//
//    private String businessCode = "VINPEARL";
//    private String programCode = "programCode";
//    private String programCodeUpdate = "programCodeUpdate";
//    private String programName = "programName";
//    private String programDescription = "programDescription";
//
//    private UpdateProgramReq updateReq = new UpdateProgramReq();
//
//    private ProgramRes programRes = new ProgramRes();
//    private CreateProgramReq reqCreate = new CreateProgramReq();
//
//    private WlLoyaltyProgram program;
//    private WlLoyaltyProgram programUpdate = new WlLoyaltyProgram();
//    private ProgramRes programUpdateRes = new ProgramRes();
//    private SearchProgramReq searchReq = new SearchProgramReq();
//
//    SpecificationBuilder<WlLoyaltyProgram> specificationBuilder = new SpecificationBuilder<>();
//    Pageable pageRequest = new OffsetBasedPageRequest(0,1);
//    Page<WlLoyaltyProgram> wlLoyaltyProgramPage;
//
//
//    @MockBean
//    WlProductRepository productRepository;
//
//
//    @Before
//    public void before() {
//        program = LoyaltyProgramMapper.toLoyaltyProgram(new CreateProgramReq());
//        wlLoyaltyProgramPage = new PageImpl<WlLoyaltyProgram>(new ArrayList<WlLoyaltyProgram>(), pageRequest,1);
//
//        programRes.setProgramCode(programCode);
//        program.setLoyaltyProgramCode(programCode);
//
//        programUpdate.setLoyaltyProgramCode(programCodeUpdate);
//        programUpdate.setName(programName);
//        programUpdate.setDescription(programDescription);
//        programUpdate.setStatus(ECommonStatus.ACTIVE);
//
//        updateReq.setProgramCode(programCodeUpdate);
//        updateReq.setProgramName(programName);
//        updateReq.setDescription(programDescription);
//        updateReq.setStatus(ECommonStatus.ACTIVE);
//
//        programUpdateRes.setProgramCode(programCodeUpdate);
//        programUpdateRes.setProgramName(programName);
//        programUpdateRes.setDescription(programDescription);
//        programUpdateRes.setStatus(ECommonStatus.ACTIVE);
//
//        searchReq.setStatus(ECommonStatus.ACTIVE);
//        searchReq.setProgramCode(programCode);
//        searchReq.setBusinessCode("");
//
//        reqCreate.setProgramCode(programCode);
//        Mockito.when(coreProgramService.create(program)).thenReturn(program);
//        Mockito.when(coreProgramService.getOne(programCode)).thenReturn(program);
//        Mockito.when(coreProgramService.getOne(programCodeUpdate)).thenReturn(programUpdate);
//        Mockito.when(coreProgramService.update(programUpdate)).thenReturn(programUpdate);
//        Mockito.when(coreProgramService.search(Mockito.any(), Mockito.any())).thenReturn(wlLoyaltyProgramPage);
//    }
//
//    @Test
//    public void create() {
//        ProgramRes exp = loyaltyProgramService.create(reqCreate);
//        Assert.assertEquals(exp , programRes);
//    }
//
//    @Test
//    public void getOne() {
//        ProgramRes exp = loyaltyProgramService.getOne(programCode);
//        Assert.assertEquals(exp , programRes);
//    }
//
//    @Test
//    public void updateDate() {
//        ProgramRes exp = loyaltyProgramService.update(updateReq);
//        Assert.assertEquals(exp , programUpdateRes);
//    }
//
//    @Test
//    public void search() {
//        Page<ProgramRes> exp = loyaltyProgramService.search(searchReq, pageRequest);
//        Assert.assertEquals(exp , new PageImpl<>(new ArrayList<>(), pageRequest, 1));
//    }
//
//    @Test
//    public void filter() {
//        ProgramGetAllReq req = new ProgramGetAllReq();
//        req.setStatus(ECommonStatus.ACTIVE);
//        List<ProgramDropDownRes> exp = loyaltyProgramService.filter(req);
//        Assert.assertEquals(exp , new ArrayList<>());
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public LoyaltyProgramService loyaltyProgramService() {
//            return new LoyaltyProgramServiceImpl();
//        }
//    }
//}