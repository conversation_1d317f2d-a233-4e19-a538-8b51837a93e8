//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.entity.WlProduct;
//import com.oneid.loyalty.accounting.ops.repository.WlProductRepository;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@RunWith(SpringRunner.class)
//public class TestProductOpsService {
//
//    @Autowired
//    private ProductOpsService productService;
//
//    private String partnerCode = "VINPEARL";
//    private String productCode = "CARD_STATIC";
//    private String productName = "Hard card";
//
//    private WlProduct wlProduct = new WlProduct();
//    private List<WlProduct> wlProductList = new ArrayList<>();
//
//    private Map<String, String> productCodeToName = new HashMap<>();
//
//    @MockBean
//    WlProductRepository productRepository;
//
//
//    @Before
//    public void before() {
//        wlProduct.setName(productName);
//        wlProduct.setProductCode(productCode);
//
//        wlProductList.add(wlProduct);
//        productCodeToName.put(productCode, productName);
//
//        Mockito.when(productRepository.findAll()).thenReturn(wlProductList);
//    }
//
//    @Test
//    public void getCardsByMemberCode() {
//        List<WlProduct> exp = productService.getAll();
//        Assert.assertEquals(exp, wlProductList);
//    }
//
//    @Test
//    public void codeToName() {
//        Map<String,String> exp = productService.codeToName();
//        Assert.assertEquals(exp, productCodeToName);
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public ProductOpsService cardService() {
//            return new ProductServiceImpl();
//        }
//    }
//}