package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.LimitationController;
import com.oneid.loyalty.accounting.ops.model.req.*;
import com.oneid.loyalty.accounting.ops.service.OpsLimitationService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.repository.CounterHistoryRepository;
import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(LimitationController.class)
public class LimitationControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsLimitationService opsLimitationService;

    @MockBean
    private CounterHistoryRepository counterHistoryRepository;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void requestCreatingLimitationRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setAttribute("A");
        ruleConditionReq.setValue("a");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);

        RuleReq ruleReq = new RuleReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setConditions(List.of(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());
        ruleReq.setName("name");

        CreateLimitationReq req = CreateLimitationReq.builder()
                .status(ECommonStatus.ACTIVE.getValue())
                .name("Test Limitation A")
                .code("TEST")
                .programId(1)
                .businessId(1)
                .startDate(new Date())
                .endDate(new Date())
                .counterId(1)
                .allowWithRemainingValue("N")
                .allowResetCounter("N")
                .threshold(BigDecimal.valueOf(100))
                .rules(List.of(ruleReq))
                .build();

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/limitations/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(0)).createRequest(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestCreatingLimitationRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.LIMITATION, AccessPermission.CREATE.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setAttribute("A");
        ruleConditionReq.setValue("a");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);

        RuleReq ruleReq = new RuleReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setConditions(Arrays.asList(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());
        ruleReq.setName("name");

        CreateLimitationReq req = CreateLimitationReq.builder()
                .status(ECommonStatus.ACTIVE.getValue())
                .name("Test Limitation A")
                .code("TEST")
                .programId(1)
                .businessId(1)
                .startDate(new Date())
                .endDate(new Date())
                .counterId(1)
                .allowWithRemainingValue("N")
                .allowResetCounter("N")
                .threshold(BigDecimal.valueOf(100))
                .rules(Arrays.asList(ruleReq))
                .build();

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/limitations/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        ArgumentCaptor<CreateLimitationReq> reqCaptor = ArgumentCaptor.forClass(CreateLimitationReq.class);

        Mockito.verify(opsLimitationService, times(1)).createRequest(reqCaptor.capture());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(reqCaptor.getValue());

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingLimitationRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setAttribute("A");
        ruleConditionReq.setValue("a");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);

        RuleReq ruleReq = new RuleReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setConditions(List.of(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());
        ruleReq.setName("name");

        CreateLimitationReq req = CreateLimitationReq.builder()
                .status(ECommonStatus.ACTIVE.getValue())
                .name("Test Limitation A")
                .code("TEST")
                .programId(1)
                .businessId(1)
                .startDate(new Date())
                .endDate(new Date())
                .counterId(1)
                .allowWithRemainingValue("N")
                .allowResetCounter("N")
                .threshold(BigDecimal.valueOf(100))
                .rules(List.of(ruleReq))
                .build();


        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/limitations/requests/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(0)).update(any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingLimitationRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.LIMITATION, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setAttribute("A");
        ruleConditionReq.setValue("a");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);

        RuleReq ruleReq = new RuleReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setConditions(List.of(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());
        ruleReq.setName("name");

        CreateLimitationReq req = CreateLimitationReq.builder()
                .status(ECommonStatus.ACTIVE.getValue())
                .name("Test Limitation A")
                .code("TEST")
                .programId(1)
                .businessId(1)
                .startDate(new Date())
                .endDate(new Date())
                .counterId(1)
                .allowWithRemainingValue("N")
                .allowResetCounter("N")
                .threshold(BigDecimal.valueOf(100))
                .rules(List.of(ruleReq))
                .build();

        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/limitations/requests/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getEditLimitationRequestSetting_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(0)).getInReviewLimitationRequestById(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getEditLimitationRequestSetting_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.LIMITATION, AccessPermission.VIEW.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(1)).getInReviewLimitationRequestById(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableLimitationRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(0)).getAvailableLimitationByRequestId(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableLimitationRequestById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.LIMITATION))
                .collect(Collectors.toMap(key -> key, key -> {
                    return (Long) Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .mapToLong(AccessPermission::getCode).sum();
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

//        Mockito.verify(opsLimitationService, times(1)).getAvailableLimitationByRequestId(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }


    @Test
    public void getInReviewLimitationRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        //Mockito.verify(opsLimitationService, times(0)).getInReviewLimitations(null, null, null, new OffsetBasedPageRequest(0, 20, null));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getInReviewLimitationRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.LIMITATION))
                .collect(Collectors.toMap(key -> key, key -> {
                    return (Long) Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .mapToLong(AccessPermission::getCode).sum();
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        int mockBusinessId = 1;

        Mockito.when(opsLimitationService.getInReview(any(), any(), any(), any(),any(), any(), any(), any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", Integer.toString(mockBusinessId));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableLimitationRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        FilterLimitationAvailableReq req = FilterLimitationAvailableReq.builder()
                .businessId(1)
                .approvalStatus(EApprovalStatus.PENDING)
                .build();
        Mockito.verify(opsLimitationService, times(0)).getAvailableLimitations(req, new OffsetBasedPageRequest(0, 20, null));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableLimitationRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.LIMITATION))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Mockito.when(opsLimitationService.getAvailableLimitations(Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        Integer mockBusinessId = 1;

        FilterLimitationAvailableReq req = FilterLimitationAvailableReq.builder()
                .businessId(1)
                .approvalStatus(EApprovalStatus.PENDING)
                .programId(1)
                .code("CC")
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.verify(opsLimitationService, times(0)).getAvailableLimitations(req, new OffsetBasedPageRequest(0, 20, null));

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableCounter() throws Exception {
        Map<AccessRole, Long> permissions = Collections.emptyMap();

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Mockito.when(opsLimitationService.getAvailableLimitations(Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        Integer mockBusinessId = 1;

        FilterLimitationAvailableReq req = FilterLimitationAvailableReq.builder()
                .businessId(1)
                .approvalStatus(EApprovalStatus.PENDING)
                .programId(1)
                .code("CC")
                .status(ECommonStatus.ACTIVE)
                .build();

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/counters-available/business/{business_id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        Mockito.verify(opsLimitationService, times(1)).getAvailableCounters(1);

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableCounterRule() throws Exception {
        Map<AccessRole, Long> permissions = Collections.emptyMap();

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/limitations/counters-available/{id}/rules", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsLimitationService, times(1)).getAvailableCounterRuleById(1);

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
}
