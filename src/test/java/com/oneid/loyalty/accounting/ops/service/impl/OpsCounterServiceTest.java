package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.EMakerCheckerActionType;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerInternalFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeRequestFeignReq;
import com.oneid.loyalty.accounting.ops.feign.model.req.CounterFeignReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateCounterReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleConditionReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.service.VersioningService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.*;
import com.oneid.oneloyalty.common.service.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.PlatformTransactionManager;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;

@RunWith(SpringRunner.class)
@Import(value = {OpsCounterServiceTest.ConfigurationTest.class})
@TestPropertySource(properties = {"maker-checker.module.counter_seq=counter"})
public class OpsCounterServiceTest {
    @Autowired
    private OpsCounterService opsCounterService;

    @MockBean
    private PlatformTransactionManager platformTransactionManager;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @MockBean
    private ProgramRepository programRepository;

    @MockBean
    private CounterRequestRepository counterRequestRepository;

    @MockBean
    private OpsRuleRequestService opsRuleRequestService;

    @MockBean
    private ServiceCounterRequestRepository serviceCounterRequestRepository;

    @MockBean
    private CounterRepository counterRepository;

    @MockBean
    private LimitationRequestRepository limitationRequestRepository;

    @MockBean
    private TierPolicyRequestRepository tierPolicyRequestRepository;

    @MockBean
    private OpsReqPendingValidator opsReqPendingValidator;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private ProgramService programService;

    @MockBean
    private CounterService counterService;

    @MockBean
    private MakerCheckerInternalFeignClient makerCheckerInternalFeignClient;

    @MockBean
    private ObjectMapper objectMapper;

    @MockBean
    private LimitationService limitationService;

    @MockBean
    private OpsRuleService opsRuleService;

    @MockBean
    private ProgramTierPolicyService programTierPolicyService;

    @MockBean
    private CounterHistoryService counterHistoryService;

    @Mock
    private Business mockBusiness;

    @Mock
    private Program mockProgram;

    @Mock
    private CounterRequest mockAvailableCounterRequest;

    @Mock
    private CounterRequest mockCreatedCounterRequest;

    @Mock
    private Counter mockAvailableCounter;

    @Mock
    private Pageable pageable;

    @Mock
    private Page<Object[]> mockPageResult;

    @Mock
    private Page<LimitationRequest> mockPageResultLimitation;

    @MockBean
    private VersioningService versioningService;

    @Value("${maker-checker.module.counter_seq}")
    private String mockModuleId;

    @Test
    public void getEditCounterRequestSetting_pending_version_invalid() {
        int mockRequestId = 1;
        int mockCounterRequestId = 1;

        Mockito.when(mockAvailableCounterRequest.getId()).thenReturn(mockCounterRequestId);
        Mockito.when(mockAvailableCounterRequest.getApprovalStatus()).thenReturn(EApprovalStatus.APPROVED);
        Mockito.when(mockAvailableCounterRequest.getRequestStatus()).thenReturn(ECommonStatus.ACTIVE);

        Mockito.when(counterRequestRepository.findById(mockRequestId)).thenReturn(Optional.of(mockAvailableCounterRequest));
        Mockito.when(counterRequestRepository.findPendingVersion(any(), any(), any())).thenReturn(Optional.of(mockAvailableCounterRequest));

        try {
            opsCounterService.getEditCounterRequestSetting(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
//            assertEquals(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, e.getCode());
        }
    }

    @Test
    public void getEditCounterRequestSetting_counter_request_status_invalid() {
        int mockRequestId = 1;

        Mockito.when(counterRequestRepository.findById(mockRequestId)).thenReturn(Optional.ofNullable(mockAvailableCounterRequest));
        Mockito.when(mockAvailableCounterRequest.getApprovalStatus()).thenReturn(EApprovalStatus.APPROVED);
        Mockito.when(mockAvailableCounterRequest.getRequestStatus()).thenReturn(ECommonStatus.INACTIVE);

        try {
            opsCounterService.getEditCounterRequestSetting(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
//            assertEquals(ErrorCode.COUNTER_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED, e.getCode());
        }
    }

    @Test
    public void getEditCounterRequestSetting_counter_rejected_approval_status_invalid() {
        int mockRequestId = 1;

        Mockito.when(counterRequestRepository.findById(mockRequestId)).thenReturn(Optional.ofNullable(mockAvailableCounterRequest));
        Mockito.when(mockAvailableCounterRequest.getApprovalStatus()).thenReturn(EApprovalStatus.REJECTED);

        try {
            opsCounterService.getEditCounterRequestSetting(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
//            assertEquals(ErrorCode.COUNTER_REQUEST_CAN_NOT_BE_EDITED, e.getCode());
        }
    }

    @Test
    public void getEditCounterRequestSetting_counter_pending_approval_status_invalid() {
        int mockRequestId = 1;

        Mockito.when(counterRequestRepository.findById(mockRequestId)).thenReturn(Optional.ofNullable(mockAvailableCounterRequest));
        Mockito.when(mockAvailableCounterRequest.getApprovalStatus()).thenReturn(EApprovalStatus.PENDING);

        try {
            opsCounterService.getEditCounterRequestSetting(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
//            assertEquals(ErrorCode.COUNTER_REQUEST_IS_ALREADY_REQUESTED, e.getCode());
        }
    }

    @Test
    public void getEditCounterRequestSetting_counter_not_found() {
        int mockRequestId = 1;

        Mockito.when(counterRequestRepository.findById(mockRequestId)).thenReturn(Optional.empty());

        try {
            opsCounterService.getEditCounterRequestSetting(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_REQUEST_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void getAvailableCounterRequestById_business_not_found() {
        int mockRequestId = 1;

        Mockito.when(counterService.findById(mockRequestId)).thenReturn(Optional.of(mockAvailableCounter));
        Mockito.when(businessRepository.findById(any())).thenReturn(Optional.empty());
        Mockito.when(opsRuleService.getRule(any(), any(), any())).thenReturn(new ArrayList<>());

        try {
            opsCounterService.getAvailableCounterRequestById(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.BUSINESS_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void getAvailableCounterRequestById_program_not_found() {
        int mockRequestId = 1;
        int mockProgramId = 1;

        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);
        Mockito.when(counterService.findById(mockRequestId)).thenReturn(Optional.of(mockAvailableCounter));

        Mockito.when(businessRepository.findById(any())).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(any(), any())).thenReturn(Optional.empty());
        Mockito.when(opsRuleService.getRule(any(), any(), any())).thenReturn(new ArrayList<>());

        try {
            opsCounterService.getAvailableCounterRequestById(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.PROGRAM_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void getAvailableCounterRequestById_request_not_found() {
        int mockRequestId = 1;
        Mockito.when(counterService.findById(mockRequestId)).thenReturn(Optional.empty());

        try {
            opsCounterService.getAvailableCounterRequestById(mockRequestId);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_service_type_invalid_2nd() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        LocalDateTime startlocalDateTime = LocalDate.now()
                .plusDays(2)
                .atTime(LocalTime.MAX);

        LocalDateTime endlocalDateTime = LocalDate.now()
                .plusDays(3)
                .atTime(LocalTime.MAX);

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .endDate(Date.from(endlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .serviceType(EServiceType.TIER)
                .counterLevel(ECounterLevel.PROGRAM)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_LEVEL_IS_NOT_AVAILABLE_FOR_SEVICE_TYPE, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_service_type_invalid_1st() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        LocalDateTime startlocalDateTime = LocalDate.now()
                .plusDays(2)
                .atTime(LocalTime.MAX);

        LocalDateTime endlocalDateTime = LocalDate.now()
                .plusDays(3)
                .atTime(LocalTime.MAX);

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .endDate(Date.from(endlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .serviceType(EServiceType.SCHEME)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_SERVICE_TYPE_IS_INVALID, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_end_date_is_invalid_2nd() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        LocalDateTime startlocalDateTime = LocalDate.now()
                .plusDays(2)
                .atTime(LocalTime.MAX);

        LocalDateTime endlocalDateTime = LocalDate.now()
                .plusDays(1)
                .atTime(LocalTime.MAX);

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .endDate(Date.from(endlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_END_DATE_INVALID, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_end_date_is_invalid_1st() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        LocalDateTime startlocalDateTime = LocalDate.now()
                .plusDays(1)
                .atTime(LocalTime.MAX);


        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .endDate(new Date())
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_END_DATE_INVALID, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_start_date_is_invalid() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        LocalDateTime startlocalDateTime = LocalDate.now()
                .minusDays(1)
                .atTime(LocalTime.MAX);

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_START_DATE_INVALID, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_code_is_being_used() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        String mockCode = "TEST_A";

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .code(mockCode)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(1);
        Mockito.when(counterRequestRepository.findPendingVersion(mockBusinessId, mockProgramId, mockCode)).thenReturn(Optional.of(new CounterRequest()));
        Mockito.when(counterRequestRepository.findEffectedVersion(mockBusinessId, mockProgramId, mockCode)).thenReturn(Optional.empty());

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTER_CODE_IS_BEING_USED, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_program_not_active() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.INACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.PROGRAM_NOT_ACTIVE, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_program_not_found() {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .programId(mockProgramId)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.empty());

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.PROGRAM_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_business_not_active() {
        Integer mockBusinessId = 1;

        CreateCounterReq req = CreateCounterReq.builder()
                .businessId(mockBusinessId)
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.INACTIVE);
        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.BUSINESS_NOT_ACTIVE, e.getCode());
        }
    }

    @Test
    public void requestCreatingCounterRequest_business_not_found() {
        CreateCounterReq req = CreateCounterReq.builder()
                .build();

        Mockito.when(businessRepository.findById(any())).thenReturn(Optional.empty());

        try {
            opsCounterService.requestCreatingCounterRequest(req);

            fail("Expected an exception here but got nothing");
        } catch (BusinessException e) {
            assertEquals(ErrorCode.BUSINESS_NOT_FOUND, e.getCode());
        }
    }

    private void requestCreatingCounterRequest_success(ECounterType counterType, ECounterAttribute counterAttribute) {
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        Integer mockCreatedCounterRequestId = 1;
        String mockCode = "TEST_A";

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setAttribute("A");
        ruleConditionReq.setValue("a");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);

        RuleReq ruleReq = new RuleReq();
        ruleReq.setConditionLogic(EConditionType.ALL);
        ruleReq.setConditions(Arrays.asList(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());
        ruleReq.setName("name");

        LocalDateTime startlocalDateTime = LocalDate.now()
                .plusDays(1)
                .atTime(LocalTime.MAX);

        LocalDateTime endlocalDateTime = LocalDate.now()
                .plusDays(2)
                .atTime(LocalTime.MAX);

        CreateCounterReq req = CreateCounterReq.builder()
                .counterStatus(ECommonStatus.ACTIVE.getValue())
                .name("Test counter A")
                .code(mockCode)
                .programId(mockProgramId)
                .businessId(mockBusinessId)
                .period(ECounterPeriod.DAILY)
                .counterLevel(ECounterLevel.MEMBER)
                .counterType(counterType)
                .counterAttribute(counterAttribute)
                .counterAttribute(ECounterAttribute.AwardPoint)
                .startDate(Date.from(startlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .endDate(Date.from(endlocalDateTime.atZone(ZoneId.systemDefault()).toInstant()))
                .rules(Arrays.asList(ruleReq))
                .build();

        Mockito.when(mockBusiness.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        Mockito.when(mockBusiness.getId()).thenReturn(mockBusinessId);
        Mockito.when(mockProgram.getId()).thenReturn(mockProgramId);

        Mockito.when(mockCreatedCounterRequest.getId()).thenReturn(mockCreatedCounterRequestId);
        Mockito.when(mockCreatedCounterRequest.getProgramId()).thenReturn(mockProgramId);

        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        Mockito.when(programRepository.findByIdAndBusinessId(mockProgramId, mockBusinessId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(counterRequestRepository.countByBusinessIdAndProgramIdAndCode(mockBusinessId, mockProgramId, mockCode)).thenReturn(0);

        Mockito.when(counterRequestRepository.save(any())).thenReturn(mockCreatedCounterRequest);

        opsCounterService.requestCreatingCounterRequest(req);

        Mockito.verify(counterRequestRepository, times(1)).save(any());
        Mockito.verify(opsRuleRequestService, times(1)).createRuleRequest(mockCreatedCounterRequestId, mockProgramId, EServiceType.COUNTER, req.getRules());

        Mockito.verify(platformTransactionManager, times(1)).commit(any());

        ChangeRequestFeignReq expectedFeignReq = ChangeRequestFeignReq.builder()
                .actionType(EMakerCheckerActionType.CREATE)
                .module(mockModuleId)
                .objectId(mockCreatedCounterRequestId.toString())
                .payload(CounterFeignReq.builder()
                        .counterRequestId(mockCreatedCounterRequestId)
                        .build())
                .build();

        ArgumentCaptor<ChangeRequestFeignReq> feignReqCaptor = ArgumentCaptor.forClass(ChangeRequestFeignReq.class);

        Mockito.verify(makerCheckerServiceClient, times(1)).changes(feignReqCaptor.capture());

        ChangeRequestFeignReq actualFeignReq = feignReqCaptor.getValue();

        assertNotNull(actualFeignReq);

        assertEquals(expectedFeignReq, actualFeignReq);
    }

    public static class ConfigurationTest {
        @Bean
        public OpsCounterService opsCounterService() {
            return new OpsCounterServiceImpl();
        }
    }
}
