package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes.ChangeRecordFeginRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.GiftCard;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Member;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.SingleGiftCardRequest;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.GiftCardRepository;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.repository.GiftCardTypeRepository;
import com.oneid.oneloyalty.common.repository.MemberRepository;
import com.oneid.oneloyalty.common.repository.ProgramRepository;
import com.oneid.oneloyalty.common.repository.SingleGiftCardRequestRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.service.GiftCardService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.PlatformTransactionManager;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;

@RunWith(SpringRunner.class)
@TestPropertySource(properties = { "maker-checker.module.gift-card=gift_card" }) 
public class OpsGiftCardServiceTest {
    
    @TestConfiguration
    static class Configuration {
        @Bean
        OpsGiftCardService opsGiftCardService() {
            return new OpsGiftCardServiceImpl();
        }
    }
    
    @Value("${maker-checker.module.gift-card}")
    private String mockModuleId;
    
    @Autowired
    private OpsGiftCardService opsGiftCardService;
    
    @MockBean
    private PlatformTransactionManager platformTransactionManager;
    
    @MockBean
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @MockBean
    private ProgramRepository programRepository;
    
    @MockBean
    private GiftCardRepository giftCardRepository;
    
    @MockBean
    private SingleGiftCardRequestRepository singleGiftCardRequestRepository;
    
    @MockBean
    private BusinessRepository businessRepository;
    
    @MockBean
    private GiftCardTypeRepository giftCardTypeRepository;
    
    @MockBean
    private CorporationRepository corporationRepository;
    
    @MockBean
    private ChainRepository chainRepository;
    
    @MockBean
    private StoreRepository storeRepository;
    
    @MockBean
    private MemberRepository memberRepository;
    
    @MockBean
    private GiftCardRequestRepository giftCardRequestRepository;

    @MockBean
    private GiftCardService giftCardService;

    @Mock
    private Page<Object[]> mockPageResult;
    
    @Mock
    private Pageable pageable;
    
    @Mock
    private Business mockBusiness;
    
    @Mock
    private Program mockProgram;
    
    @Mock
    private Corporation mockCorporation;
    
    @Mock
    private Chain mockChain;
    
    @Mock
    private Store mockStore;
    
    @Mock
    private GiftCard mockGiftCard;
    
    @Mock
    private GiftCardType mockGiftCardType;
    
    @Mock
    private SingleGiftCardRequest mockSgcRequest;
    
    @Mock
    private GiftCardRequest mockGiftCardRequest;
    
    @Mock
    private Member mockMember;
    
    @Test
    public void getPage_approved_pending_status_SUCCESS() {
        int mockBusinessId = 1;
        
        List<Object[]> pageContent = new ArrayList<Object[]>();
        pageContent.add(new Object[] { mockGiftCard, mockGiftCardType, mockStore, mockChain, mockCorporation, mockProgram, mockSgcRequest });
        
        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        
        Mockito.when(mockGiftCard.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(mockPageResult.getContent()).thenReturn(pageContent);
        
        Mockito.when(singleGiftCardRequestRepository.findPage(mockBusinessId,
                null, null, null, null, null, null, null, null, 
                EApprovalStatus.PENDING, null, null, pageable))
                .thenReturn(mockPageResult);
        
        Mockito.when(mockSgcRequest.getStatus()).thenReturn(EGiftCardStatus.ACTIVE);
        Mockito.when(mockSgcRequest.getExpirationDate()).thenReturn(new Date());
        
        opsGiftCardService.getPage(mockBusinessId, null, null, null, null, null, null, null, null, 
                EApprovalStatus.PENDING, null, null, pageable);
    }
    
    @Test
    public void getPage_approved_reject_status_SUCCESS() {
        int mockBusinessId = 1;
        
        List<Object[]> pageContent = new ArrayList<Object[]>();
        pageContent.add(new Object[] { mockGiftCard, mockGiftCardType, mockStore, mockChain, mockCorporation, mockProgram, mockSgcRequest });
        
        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        
        Mockito.when(mockGiftCard.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(mockPageResult.getContent()).thenReturn(pageContent);
        
        Mockito.when(singleGiftCardRequestRepository.findPage(mockBusinessId,
                null, null, null, null, null, null, null, null, 
                EApprovalStatus.REJECTED, null, null, pageable))
                .thenReturn(mockPageResult);
        
        opsGiftCardService.getPage(mockBusinessId, null, null, null, null, null, null, null, null, 
                EApprovalStatus.REJECTED, null, null, pageable);
    }
    
    @Test
    public void getPage_approved_approval_status_SUCCESS() {
        int mockBusinessId = 1;
        
        List<Object[]> pageContent = new ArrayList<Object[]>();
        pageContent.add(new Object[] { mockGiftCard, mockGiftCardType, mockStore, mockChain, mockCorporation, mockProgram });
        
        Mockito.when(businessRepository.findById(mockBusinessId)).thenReturn(Optional.of(mockBusiness));
        
        Mockito.when(mockGiftCard.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(mockPageResult.getContent()).thenReturn(pageContent);
        
        Mockito.when(giftCardRepository.findPage(mockBusinessId,
                null, null, null, null, null, null, null, null, null, null,
                pageable))
                .thenReturn(mockPageResult);
        
        opsGiftCardService.getPage(mockBusinessId, null, null, null, null, null, null, null, null, 
                EApprovalStatus.APPROVED, null, null, pageable);
    }
    
    
    
    @Test
    public void getApprovedDetail_not_found_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.empty());
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.getApprovedDetail(mockProgramId, mockSerial));
        assertEquals(ErrorCode.GIFT_CARD_NOT_FOUND, expectedException.getCode());
    }
    
    @Test
    public void getApprovedDetail_SUCCESS() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        this.mockGiftCardDetail();
        
        opsGiftCardService.getApprovedDetail(mockProgramId, mockSerial);
    }
    
    
    
    @Test
    public void getRequestDetail_request_not_found_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(singleGiftCardRequestRepository.findById(mockRequestId)).thenReturn(Optional.empty());
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.getRequestDetail(mockProgramId, mockSerial, mockRequestId));
        assertEquals(ErrorCode.SINGLE_GIFT_CARD_REQUEST_NOT_FOUND, expectedException.getCode());
    }
    
    @Test
    public void getRequestDetail_SUCCESS() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(singleGiftCardRequestRepository.findById(mockRequestId)).thenReturn(Optional.of(mockSgcRequest));
        
        this.mockGiftCardDetail();
        
        Mockito.when(mockSgcRequest.getStatus()).thenReturn(EGiftCardStatus.ACTIVE);
        Mockito.when(mockSgcRequest.getExpirationDate()).thenReturn(new Date());
        
        opsGiftCardService.getRequestDetail(mockProgramId, mockSerial, mockRequestId);
    }
    
    
    
    @Test
    public void getChangeable_cannot_update_used_card_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(EGiftCardStatus.USED);
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.getChangeable(mockProgramId, mockSerial));
        assertEquals(ErrorCode.GIFT_CARD_STATUS_CAN_NOT_UPDATE, expectedException.getCode());
    }
    
    @Test
    public void getChangeable_has_pending_request_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(EGiftCardStatus.ACTIVE);
        
        Mockito.when(singleGiftCardRequestRepository.findPendingVersion(mockProgramId, mockSerial)).thenReturn(Optional.of(mockSgcRequest));
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.getChangeable(mockProgramId, mockSerial));
        assertEquals(ErrorCode.SINGLE_GIFT_CARD_REQUEST_IS_ALREADY_REQUESTED, expectedException.getCode());
    }
    
    @Test
    public void getChangeable_SUCCESS() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        EGiftCardStatus mockCurrentStatus = EGiftCardStatus.ACTIVE;
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(mockCurrentStatus);
        
        Mockito.when(singleGiftCardRequestRepository.findPendingVersion(mockProgramId, mockSerial)).thenReturn(Optional.empty());
        
        this.mockGiftCardDetail();
        
        opsGiftCardService.getChangeable(mockProgramId, mockSerial);
    }
    
    
    
    @Test
    public void requestUpdate_inactive_program_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        EGiftCardStatus mockNewStatus = EGiftCardStatus.ACTIVE;
        
        GiftCardUpdateReq req = new GiftCardUpdateReq();
        req.setProgramId(mockProgramId);
        req.setSerial(mockSerial);
        req.setStatus(mockNewStatus);
        
        Mockito.when(programRepository.findById(mockProgramId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.INACTIVE);
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.requestUpdate(req));
        assertEquals(ErrorCode.PROGRAM_NOT_ACTIVE, expectedException.getCode());
    }
    
    @Test
    public void requestUpdate_invalid_transition_status_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        EGiftCardStatus mockCurrentStatus = EGiftCardStatus.INACTIVE;
        EGiftCardStatus mockNewStatus = EGiftCardStatus.USED;
        
        GiftCardUpdateReq req = new GiftCardUpdateReq();
        req.setProgramId(mockProgramId);
        req.setSerial(mockSerial);
        req.setStatus(mockNewStatus);
        
        Mockito.when(programRepository.findById(mockProgramId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(mockCurrentStatus);
        
        Mockito.when(singleGiftCardRequestRepository.findPendingVersion(mockProgramId, mockSerial)).thenReturn(Optional.empty());
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.requestUpdate(req));
        assertEquals(ErrorCode.GIFT_CARD_STATUS_CAN_NOT_UPDATE, expectedException.getCode());
    }
    
    @Test
    public void requestUpdate_cannot_update_ExpirationDate_at_inactive_INVALID() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        EGiftCardStatus mockCurrentStatus = EGiftCardStatus.INACTIVE;
        OffsetDateTime mockExpirationDate = OffsetDateTime.now();
        
        GiftCardUpdateReq req = new GiftCardUpdateReq();
        req.setProgramId(mockProgramId);
        req.setSerial(mockSerial);
        req.setExpirationDate(mockExpirationDate);
        
        Mockito.when(programRepository.findById(mockProgramId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(mockCurrentStatus);
        
        Mockito.when(singleGiftCardRequestRepository.findPendingVersion(mockProgramId, mockSerial)).thenReturn(Optional.empty());
        
        BusinessException expectedException = assertThrows(BusinessException.class, () -> opsGiftCardService.requestUpdate(req));
        assertEquals(ErrorCode.GIFT_CARD_NOT_ACTIVE, expectedException.getCode());
    }
    
    @Test
    public void requestUpdate_SUCCESS() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        EGiftCardStatus mockCurrentStatus = EGiftCardStatus.ACTIVE;
        EGiftCardStatus mockNewStatus = EGiftCardStatus.INACTIVE;
        OffsetDateTime mockExpirationDate = OffsetDateTime.now();
        
        GiftCardUpdateReq req = new GiftCardUpdateReq();
        req.setProgramId(mockProgramId);
        req.setSerial(mockSerial);
        req.setStatus(mockNewStatus);
        req.setExpirationDate(mockExpirationDate);
        
        Mockito.when(programRepository.findById(mockProgramId)).thenReturn(Optional.of(mockProgram));
        Mockito.when(mockProgram.getStatus()).thenReturn(ECommonStatus.ACTIVE);
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        Mockito.when(mockGiftCard.getStatus()).thenReturn(mockCurrentStatus);
        
        Mockito.when(singleGiftCardRequestRepository.findPendingVersion(mockProgramId, mockSerial)).thenReturn(Optional.empty());
        
        Mockito.when(singleGiftCardRequestRepository.save(any())).thenAnswer(entity -> {
            SingleGiftCardRequest request = SingleGiftCardRequest.class.cast(entity.getArgument(0));
            request.setId(mockRequestId);
            return request;
        });
        
        opsGiftCardService.requestUpdate(req);
        
        Mockito.verify(singleGiftCardRequestRepository, times(1)).save(any());
    }
    
    
    
    @Test
    public void getInReviewPage_empty_record_SUCCESS() {
        int mockPageNumber = 1;
        int mockPageSize = 20;
        int mockTotalRecordCount = 0;
        EApprovalStatus approvalStatus = EApprovalStatus.PENDING;
        
        LocalDate fromDate = LocalDate.now();
        LocalDate toDate = LocalDate.now();
        Integer fromEpochSecond = (int) fromDate.atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond();
        Integer toEpochSecond = (int) toDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toEpochSecond();
        
        APIResponse<ChangeRequestPageFeignRes> response = new APIResponse<ChangeRequestPageFeignRes>();
        ChangeRequestPageFeignRes changeRequestPageFeignRes = new ChangeRequestPageFeignRes();
        changeRequestPageFeignRes.setTotalRecordCount(mockTotalRecordCount);
        changeRequestPageFeignRes.setRecords(Collections.emptyList());
        
        response.setData(changeRequestPageFeignRes);
        
        Mockito.when(pageable.getPageNumber()).thenReturn(mockPageNumber);
        Mockito.when(pageable.getPageSize()).thenReturn(mockPageSize);
        
        Mockito.when(makerCheckerServiceClient.getChangeRequests(mockModuleId, null, null, 
                approvalStatus.getDisplayName().toUpperCase(), 
                mockPageNumber, mockPageSize, fromEpochSecond , toEpochSecond))
                .thenReturn(response);
        
        opsGiftCardService.getInReviewPage(approvalStatus, fromDate, toDate, pageable);
        
        Mockito.verify(singleGiftCardRequestRepository, times(0)).findDataByIds(any());
    }
    
    @Test
    public void getInReviewPage_without_status_SUCCESS() {
        getInReviewPage_SUCCESS(null);
    }
    
    @Test
    public void getInReviewPage_with_status_SUCCESS() {
        getInReviewPage_SUCCESS(EApprovalStatus.APPROVED);
    }
    
    private void getInReviewPage_SUCCESS(EApprovalStatus approvalStatus) {
        int mockPageNumber = 1;
        int mockPageSize = 20;
        int mockTotalRecordCount = 1;
        int mockRequestId = 10;
        int mockChangeRequestId = 1280;
        
        approvalStatus = approvalStatus != null ? approvalStatus : EApprovalStatus.PENDING;
        
        ChangeRecordFeginRes changeRecordFeginRes = new ChangeRecordFeginRes();
        changeRecordFeginRes.setObjectId(String.valueOf(mockRequestId));
        changeRecordFeginRes.setChangeRequestId(mockChangeRequestId);
        
        ChangeRequestPageFeignRes changeRequestPageFeignRes = new ChangeRequestPageFeignRes();
        changeRequestPageFeignRes.setTotalRecordCount(mockTotalRecordCount);
        changeRequestPageFeignRes.setRecords(Arrays.asList(changeRecordFeginRes));
        
        APIResponse<ChangeRequestPageFeignRes> response = new APIResponse<ChangeRequestPageFeignRes>();
        response.setData(changeRequestPageFeignRes);
        
        List<Object[]> pageContent = new ArrayList<Object[]>();
        pageContent.add(new Object[] { mockGiftCard, mockGiftCardType, mockStore, mockChain, mockCorporation, mockProgram, mockSgcRequest, mockBusiness });
        
        Mockito.when(pageable.getPageNumber()).thenReturn(mockPageNumber);
        Mockito.when(pageable.getPageSize()).thenReturn(mockPageSize);
        
        Mockito.when(makerCheckerServiceClient.getChangeRequests(mockModuleId, null, null,
                approvalStatus.getDisplayName().toUpperCase(),
                mockPageNumber, mockPageSize, null, null))
                .thenReturn(response);
        
        Mockito.when(singleGiftCardRequestRepository.findDataByIds(Arrays.asList(mockRequestId))).thenReturn(pageContent);
        
        Mockito.when(mockGiftCard.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(mockSgcRequest.getId()).thenReturn(mockRequestId);
        Mockito.when(mockSgcRequest.getStatus()).thenReturn(EGiftCardStatus.ACTIVE);
        Mockito.when(mockSgcRequest.getExpirationDate()).thenReturn(new Date());
        
        opsGiftCardService.getInReviewPage(approvalStatus, null, null, pageable);
        
        Mockito.verify(singleGiftCardRequestRepository, times(1)).findDataByIds(Arrays.asList(mockRequestId));
    }
    
    
    
    @Test
    public void getInReviewDetail_SUCCESS() {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        Integer mockReviewId = 1280;
        
        APIResponse<ChangeRecordFeginRes> response = new APIResponse<ChangeRecordFeginRes>();
        
        ChangeRecordFeginRes changeRecordFeginRes = new ChangeRecordFeginRes();
        changeRecordFeginRes.setObjectId(String.valueOf(mockRequestId));
        changeRecordFeginRes.setChangeRequestId(mockReviewId);
        
        response.setData(changeRecordFeginRes);
        
        Mockito.when(makerCheckerServiceClient.getChangeRequestById(String.valueOf(mockReviewId))).thenReturn(response);
        
        Mockito.when(singleGiftCardRequestRepository.findById(mockRequestId)).thenReturn(Optional.of(mockSgcRequest));
        
        Mockito.when(mockSgcRequest.getProgramId()).thenReturn(mockProgramId);
        Mockito.when(mockSgcRequest.getSerial()).thenReturn(mockSerial);
        Mockito.when(mockSgcRequest.getStatus()).thenReturn(EGiftCardStatus.ACTIVE);
        Mockito.when(mockSgcRequest.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(giftCardRepository.findByProgramIdAndSerial(mockProgramId, mockSerial)).thenReturn(Optional.of(mockGiftCard));
        
        this.mockGiftCardDetail();
        
        opsGiftCardService.getInReviewDetail(mockReviewId);
    }
    
    
    
    private void mockGiftCardDetail() {
        Mockito.when(programRepository.findById(mockGiftCard.getProgramId())).thenReturn(Optional.of(mockProgram));
        
        Mockito.when(giftCardTypeRepository.findById(mockGiftCard.getGcTypeId())).thenReturn(Optional.of(mockGiftCardType));
        
        Mockito.when(storeRepository.findById(mockGiftCard.getStoreId())).thenReturn(Optional.of(mockStore));
        
        Mockito.when(chainRepository.findById(mockStore.getChainId())).thenReturn(Optional.of(mockChain));
        
        Mockito.when(corporationRepository.findById(mockStore.getCorporationId())).thenReturn(Optional.of(mockCorporation));
        
        Mockito.when(businessRepository.findById(mockGiftCard.getBusinessId())).thenReturn(Optional.of(mockBusiness));
        
        Mockito.when(mockGiftCard.getExpirationDate()).thenReturn(new Date());
        
        Mockito.when(giftCardRequestRepository.findByCprBatchNoAndBusinessIdAndProgramId(
                mockGiftCard.getCprBatchNo(), mockGiftCard.getBusinessId(), mockGiftCard.getProgramId()))
                .thenReturn(mockGiftCardRequest);
        
        Mockito.when(memberRepository.findById(mockGiftCard.getMemberId())).thenReturn(Optional.of(mockMember));
    }
    
}
