package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.req.FormulaGroupReq;
import com.oneid.loyalty.accounting.ops.model.req.FormulaRecordReq;
import com.oneid.loyalty.accounting.ops.model.res.FormulaGroupRes;
import com.oneid.loyalty.accounting.ops.model.res.FormulaRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsFormulaService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EFormulaAttribute;
import com.oneid.oneloyalty.common.constant.EFormulaType;
import com.oneid.oneloyalty.common.constant.EFormulaUnitType;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeFormula;
import com.oneid.oneloyalty.common.entity.SchemeFormulaRange;
import com.oneid.oneloyalty.common.repository.SchemeFormulaRangeRepository;
import com.oneid.oneloyalty.common.service.SchemeFormulaService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
public class OpsFormulaServiceImplTest {

    @Autowired
    private OpsFormulaService opsFormulaService;

    @MockBean
    private SchemeFormulaService schemeFormulaService;

    @MockBean
    private SchemeFormulaRangeRepository schemeFormulaRangeRepository;

    @Test
    public void verify() {
        opsFormulaService.verify(new FormulaGroupReq());
    }

    @Test
    public void create() {
        Assert.assertNull(opsFormulaService.create(new Scheme(), null));
        Scheme scheme = new Scheme();
        scheme.setId(1234);
        FormulaGroupReq req = new FormulaGroupReq();
        req.setAttribute(EFormulaAttribute.GMV);
        FormulaRecordReq record1 = new FormulaRecordReq();
        record1.setFormulaType(EFormulaType.F2); // N, D
        record1.setN(232L);
        record1.setD(348L);
        FormulaRecordReq record2 = new FormulaRecordReq();
        record2.setFormulaUnitType(EFormulaUnitType.PERCENT);
        record2.setFormulaType(EFormulaType.F4);
        FormulaRecordReq.FormulaFactor factor1 = new FormulaRecordReq.FormulaFactor();
        factor1.setAmountFrom(0L);
        factor1.setAmountTo(199L);
        factor1.setValue(100.0d);
        FormulaRecordReq.FormulaFactor factor2 = new FormulaRecordReq.FormulaFactor();
        factor2.setAmountFrom(199L);
        factor2.setAmountTo(2000L);
        factor2.setValue(200.0d);
        List<FormulaRecordReq.FormulaFactor> factors = Arrays.asList(factor1, factor2);
        record2.setFactorList(factors);

        FormulaRecordReq record3 = new FormulaRecordReq();
        record3.setFormulaUnitType(EFormulaUnitType.POINT);
        record3.setFormulaType(EFormulaType.F4);
        record3.setFactorList(factors);

        req.setFormulaList(Arrays.asList(record1, record2, record3));

        SchemeFormula schemeFormula1 = new SchemeFormula();
        schemeFormula1.setSchemeId(1234);
        schemeFormula1.setStatus(ECommonStatus.ACTIVE); // default
        schemeFormula1.setFormulaType(EFormulaType.F2);
        schemeFormula1.setAttribute(EFormulaAttribute.GMV);

        SchemeFormula schemeFormula2 = new SchemeFormula();
        schemeFormula2.setSchemeId(1234);
        schemeFormula2.setStatus(ECommonStatus.ACTIVE); // default
        schemeFormula2.setFormulaType(EFormulaType.F4);
        schemeFormula2.setAttribute(EFormulaAttribute.GMV);
        schemeFormula2.setUnitType(EFormulaUnitType.PERCENT);

        SchemeFormula schemeFormula3 = new SchemeFormula();
        schemeFormula3.setSchemeId(1234);
        schemeFormula3.setStatus(ECommonStatus.ACTIVE); // default
        schemeFormula3.setFormulaType(EFormulaType.F4);
        schemeFormula3.setAttribute(EFormulaAttribute.GMV);
        schemeFormula3.setUnitType(EFormulaUnitType.POINT);

        Mockito.when(schemeFormulaService.create(schemeFormula1)).thenReturn(schemeFormula1);
        Mockito.when(schemeFormulaService.create(schemeFormula2)).thenReturn(schemeFormula2);
        Mockito.when(schemeFormulaService.create(schemeFormula3)).thenReturn(schemeFormula3);

        Mockito.when(schemeFormulaService.update(schemeFormula1)).thenReturn(schemeFormula1);
        Mockito.when(schemeFormulaService.update(schemeFormula2)).thenReturn(schemeFormula2);
        Mockito.when(schemeFormulaService.update(schemeFormula3)).thenReturn(schemeFormula3);

        SchemeFormulaRange range1 = new SchemeFormulaRange();
        range1.setStatus(ECommonStatus.ACTIVE);
        range1.setDValue(348L);
        range1.setNValue(232L);
        Mockito.when(schemeFormulaRangeRepository.save(range1)).thenReturn(range1);

        SchemeFormulaRange range2 = new SchemeFormulaRange();
        range2.setStatus(ECommonStatus.ACTIVE);
        range2.setAmountFrom(0L);
        range2.setAmountTo(199L);
        range2.setPercentValue(100.0);
        Mockito.when(schemeFormulaRangeRepository.save(range2)).thenReturn(range2);

        SchemeFormulaRange range3 = new SchemeFormulaRange();
        range3.setStatus(ECommonStatus.ACTIVE);
        range3.setAmountFrom(199L);
        range3.setAmountTo(2000L);
        range3.setPercentValue(200.0);
        Mockito.when(schemeFormulaRangeRepository.save(range3)).thenReturn(range3);


        SchemeFormulaRange range4 = new SchemeFormulaRange();
        range4.setStatus(ECommonStatus.ACTIVE);
        range4.setAmountFrom(0L);
        range4.setAmountTo(199L);
        range4.setPointValue(100L);
        Mockito.when(schemeFormulaRangeRepository.save(range4)).thenReturn(range4);

        SchemeFormulaRange range5 = new SchemeFormulaRange();
        range5.setStatus(ECommonStatus.ACTIVE);
        range5.setAmountFrom(199L);
        range5.setAmountTo(2000L);
        range5.setPointValue(200L);
        Mockito.when(schemeFormulaRangeRepository.save(range5)).thenReturn(range5);


        FormulaGroupRes exp = opsFormulaService.create(scheme, req);
        FormulaGroupRes act = new FormulaGroupRes();

        act.setAttribute(EFormulaAttribute.GMV);
        FormulaRecordRes recordRes1 = new FormulaRecordRes();
        recordRes1.setSchemeId(1234);
        recordRes1.setN(232L);
        recordRes1.setD(348L);
        recordRes1.setFormulaType(EFormulaType.F2);
        record1.setFactorList(new ArrayList<>());

        FormulaRecordRes recordRes2 = new FormulaRecordRes();
        recordRes2.setFormulaUnitType(EFormulaUnitType.PERCENT);
        recordRes2.setSchemeId(1234);
        recordRes2.setFormulaType(EFormulaType.F4);

        FormulaRecordReq.FormulaFactor recordRes2Factor1 = new FormulaRecordReq.FormulaFactor();
        recordRes2Factor1.setValue(100.0);
        recordRes2Factor1.setAmountFrom(0L);
        recordRes2Factor1.setAmountTo(199L);

        FormulaRecordReq.FormulaFactor recordRes2Factor2 = new FormulaRecordReq.FormulaFactor();
        recordRes2Factor2.setValue(200.0);
        recordRes2Factor2.setAmountFrom(199L);
        recordRes2Factor2.setAmountTo(2000L);


        List<FormulaRecordReq.FormulaFactor> recordRes2Factors = Arrays.asList(recordRes2Factor1,recordRes2Factor2);
        recordRes2.setFactorList(recordRes2Factors);


        FormulaRecordRes recordRes3 = new FormulaRecordRes();
        recordRes3.setSchemeId(1234);
        recordRes3.setFormulaType(EFormulaType.F4);
        recordRes3.setFormulaUnitType(EFormulaUnitType.POINT);
        recordRes3.setFactorList(recordRes2Factors);

        act.setFormulaList(Arrays.asList(recordRes1, recordRes2, recordRes3));

        Assert.assertEquals(exp.getAttribute(), act.getAttribute());
        Assert.assertEquals(act.getFormulaList().size(), exp.getFormulaList().size());

        for (int j = 0;j < exp.getFormulaList().size();++j) {
            Assert.assertEquals(act.getFormulaList().get(j), exp.getFormulaList().get(j));
            Assert.assertEquals(act.getFormulaList().get(j).getFactorList().size(),
                    exp.getFormulaList().get(j).getFactorList().size());

            for (int i = 0; i < act.getFormulaList().get(j).getFactorList().size();++i) {
                Assert.assertEquals(act.getFormulaList().get(j).getFactorList().get(i),
                        exp.getFormulaList().get(j).getFactorList().get(i));
            }
        }
    }

    @Test
    public void getFormulaGroupByScheme() {
        Scheme scheme = new Scheme();
        scheme.setId(78);
        SchemeFormula schemeFormula = new SchemeFormula();
        schemeFormula.setStatus(ECommonStatus.ACTIVE);
        schemeFormula.setFormulaType(EFormulaType.F2);
        schemeFormula.setAttribute(EFormulaAttribute.GMV);
        schemeFormula.setId(18);

        SchemeFormula schemeFormula2 = new SchemeFormula();
        schemeFormula2.setStatus(ECommonStatus.ACTIVE);
        schemeFormula2.setFormulaType(EFormulaType.F4);
        schemeFormula2.setAttribute(EFormulaAttribute.GMV);
        schemeFormula2.setId(10);
        List<SchemeFormula> schemeFormulas = Arrays.asList(schemeFormula, schemeFormula2);
        Mockito.when(schemeFormulaService.getAllBySchemeId(78)).thenReturn(schemeFormulas);

        FormulaGroupRes act = new FormulaGroupRes();

        FormulaRecordRes record1 = new FormulaRecordRes();
        record1.setFormulaId(18);
        record1.setN(10L);
        record1.setD(199L);
        record1.setFormulaType(EFormulaType.F2);

        FormulaRecordRes record2 = new FormulaRecordRes();
        record2.setFormulaId(10);
        record2.setFormulaUnitType(EFormulaUnitType.PERCENT);
        record2.setFormulaType(EFormulaType.F4);
        FormulaRecordReq.FormulaFactor factor1 = new FormulaRecordReq.FormulaFactor();
        factor1.setAmountFrom(0L);
        factor1.setAmountTo(199L);
        factor1.setValue(100.0d);
        FormulaRecordReq.FormulaFactor factor2 = new FormulaRecordReq.FormulaFactor();
        factor2.setAmountFrom(199L);
        factor2.setAmountTo(2000L);
        factor2.setValue(200.0d);
        List<FormulaRecordReq.FormulaFactor> factors = Arrays.asList(factor1, factor2);
        record2.setFactorList(factors);

        List<FormulaRecordRes> formulaRecordRes = Arrays.asList(record1, record2);
        act.setFormulaList(formulaRecordRes);
        act.setAttribute(EFormulaAttribute.GMV);
        SchemeFormulaRange range = new SchemeFormulaRange();
        range.setFormulaId(18);
        range.setNValue(10L);
        range.setDValue(199L);
        range.setStatus(ECommonStatus.ACTIVE);


        SchemeFormulaRange range1 = new SchemeFormulaRange();
        range1.setFormulaId(10);
        range1.setPercentValue(100.0);
        range1.setAmountTo(199L);
        range1.setAmountFrom(0L);
        range1.setStatus(ECommonStatus.ACTIVE);

        SchemeFormulaRange range2 = new SchemeFormulaRange();
        range2.setFormulaId(10);
        range2.setPercentValue(200.0);
        range2.setAmountTo(2000L);
        range2.setAmountFrom(199L);
        range2.setStatus(ECommonStatus.ACTIVE);

        Mockito.when(schemeFormulaRangeRepository.getByFormulaId(18, ECommonStatus.ACTIVE))
                .thenReturn(Arrays.asList(range));
        Mockito.when(schemeFormulaRangeRepository.getByFormulaId(10, ECommonStatus.ACTIVE))
                .thenReturn(Arrays.asList(range1, range2));

        FormulaGroupRes exp = opsFormulaService.getFormulaGroupByScheme(scheme);
        Assert.assertEquals(act.getAttribute(), exp.getAttribute());
        Assert.assertEquals(act.getFormulaList().size(), exp.getFormulaList().size());

        for(int i  = 0;i < act.getFormulaList().size();++i) {
            Assert.assertEquals(act.getFormulaList().get(i).getAttribute(), exp.getFormulaList().get(i).getAttribute());
            Assert.assertEquals(act.getFormulaList().get(i).getFactorList().size(),
                                exp.getFormulaList().get(i).getFactorList().size());
            for (int j = 0;j < act.getFormulaList().get(i).getFactorList().size();++j) {
                Assert.assertEquals(act.getFormulaList().get(i).getFactorList().get(j),
                        exp.getFormulaList().get(i).getFactorList().get(j));
            }
        }
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsFormulaService opsFormulaService() {
            return new OpsFormulaServiceImpl();
        }
    }
}