//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
//import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
//import com.oneid.loyalty.accounting.ops.component.attribute.*;
//import com.oneid.loyalty.accounting.ops.component.attribute.strategy.*;
//import com.oneid.loyalty.accounting.ops.component.constant.AttributeOperator;
//import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
//import com.oneid.loyalty.accounting.ops.model.res.ConditionRecordRes;
//import com.oneid.loyalty.accounting.ops.service.ListSchemeAttributeForTest;
//import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
//import com.oneid.oneloyalty.common.constant.ECommonStatus;
//import com.oneid.oneloyalty.common.constant.ErrorCode;
//import com.oneid.oneloyalty.common.entity.*;
//import com.oneid.oneloyalty.common.exception.BusinessException;
//import com.oneid.oneloyalty.common.service.*;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Import;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//
//@RunWith(SpringRunner.class)
//@Import(value = {AttributeValueTest.ConfigurationTest.class })
//@Ignore
//public class TestOpsConditionServiceImpl {
//
//    @Autowired
//    private OpsConditionService opsConditionService;
//
//    @Autowired
//    private AttributeValueFactory attributeValueFactory;
//
//    @MockBean
//    private SchemeAttributeService schemeAttributeService;
//
//    @MockBean
//    private SchemeRuleConditionService  schemeRuleConditionService;
//
//    @MockBean
//    private SchemeService schemeService;
//
//    @MockBean
//    private CorporationService corporationService;
//
//    @MockBean
//    private ChainService chainService;
//
//    @MockBean
//    private StoreService storeService;
//
//    @MockBean
//    private PosService posService;
//
//    @MockBean
//    private ProgramService programService;
//
//    @MockBean
//    private ProgramTierService programTierService;
//
//
//    private final String operatorString =  "==";
//    private final AttributeOperator operator =  AttributeOperator.lookup(operatorString);
//    private final List<String> operatorStrings = new ArrayList<>();
//    private final List<AttributeOperator> operators = new ArrayList<>();
//
//    private final Integer ruleId = 12;
//    private final SchemeRuleCondition schemeRuleCondition = new SchemeRuleCondition();
//    private final List<SchemeRuleCondition> conditions = new ArrayList<>();
//    private final String conditionValue = "4584599";
//    private final String attribute = "AccumulatedEarnedPoint";
//
//    private final Integer conditionId = 3485430;
//    private final List<ConditionRecordRes> conditionRes = new ArrayList<>();
//    private final ConditionRecordRes conditionRecordRes =  new ConditionRecordRes();
//    private List<SchemeAttribute> attributes = ListSchemeAttributeForTest.getListAttribute();
//
//    @Before
//    public void before() {
//        Mockito.when(schemeAttributeService.getAll()).thenReturn(attributes);
//        operatorStrings.add(operatorString);
//        operators.add(operator);
//
//        SchemeAttribute schemeAttribute = attributes.get(0);
//
//        schemeRuleCondition.setId(conditionId);
//        schemeRuleCondition.setSchemeRuleId(ruleId);
//        schemeRuleCondition.setValue(conditionValue);
//        schemeRuleCondition.setAttribute(attribute);
//        assert operator != null;
//        schemeRuleCondition.setOperator(operator.getExpression());
//        schemeRuleCondition.setStatus(ECommonStatus.ACTIVE);
//        schemeRuleCondition.setId(conditionId);
//        conditions.add(schemeRuleCondition);
//
//        conditionRecordRes.setAttribute(schemeAttribute.getAttribute());
//        conditionRecordRes.setOperator(operatorString);
//        conditionRecordRes.setId(conditionId);
//        conditionRecordRes.setStatus(ECommonStatus.ACTIVE);
//        AttributeValueStrategy<Integer> attributeValueStrategy = new NumberAttributeValueStrategy();
//        conditionRecordRes.setValue(attributeValueStrategy.getReadValue(AttributeOperator.EQUAL, conditionValue));
//        conditionRes.add(conditionRecordRes);
//
//        Mockito.when(schemeRuleConditionService.getAllByRuleId(ruleId)).thenReturn(conditions);
//    }
//
//
//    @Test
//    public void verifyConditions_success() {
//        List<ConditionRecordReq> recordReqs = new ArrayList<>();
//
//        ConditionRecordReq req1 = new ConditionRecordReq();
//        req1.setAttribute("AccumulatedEarnedPoint");
//        req1.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req1);
//
//        ConditionRecordReq req2 = new ConditionRecordReq();
//        req2.setAttribute("AccumulatedGMV");
//        req2.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req2);
//
//        ConditionRecordReq req3 = new ConditionRecordReq();
//        req3.setAttribute("AccumulatedGrossAmount");
//        req3.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req3);
//
//        ConditionRecordReq req4 = new ConditionRecordReq();
//        req4.setAttribute("AccumulatedNettAmount");
//        req4.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req4);
//
//        ConditionRecordReq req5 = new ConditionRecordReq();
//        req5.setAttribute("ChainCode");
//        req5.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req5);
//
//        ConditionRecordReq req6 = new ConditionRecordReq();
//        req6.setAttribute("CorporationCode");
//        req6.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req5);
//
//        ConditionRecordReq req7 = new ConditionRecordReq();
//        req7.setAttribute("DOB");
//        req7.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req7);
//
//        ConditionRecordReq req8 = new ConditionRecordReq();
//        req8.setAttribute("GMV");
//        req8.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req8);
//
//        ConditionRecordReq req9 = new ConditionRecordReq();
//        req9.setAttribute("GrossAmount");
//        req9.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req9);
//
//        ConditionRecordReq req10 = new ConditionRecordReq();
//        req10.setAttribute("MemberStatus");
//        req10.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req10);
//
//        ConditionRecordReq req11 = new ConditionRecordReq();
//        req11.setAttribute("NettAmount");
//        req11.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req11);
//
//        ConditionRecordReq req12 = new ConditionRecordReq();
//        req12.setAttribute("RedeemPoint");
//        req12.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req12);
//
//        ConditionRecordReq req13 = new ConditionRecordReq();
//        req13.setAttribute("TerminalCode");
//        req13.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req13);
//
//        ConditionRecordReq req14 = new ConditionRecordReq();
//        req14.setAttribute("StoreCode");
//        req14.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req14);
//
//        ConditionRecordReq req15 = new ConditionRecordReq();
//        req15.setAttribute("TierCode");
//        req15.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req15);
//
//        ConditionRecordReq req16 = new ConditionRecordReq();
//        req16.setAttribute("TransactionDatetime");
//        req16.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req16);
//
//        ConditionRecordReq req17 = new ConditionRecordReq();
//        req17.setAttribute("TransactionType");
//        req17.setOperator(AttributeOperator.EQUAL);
//        recordReqs.add(req17);
//
//        opsConditionService.verifyConditions(new Scheme(), recordReqs);
//    }
//
//    @Test
//    public void verifyConditions_fail_attribute_not_found() {
//
//        ConditionRecordReq req = new ConditionRecordReq();
//        req.setAttribute("");
//        List<ConditionRecordReq> recordReqs = new ArrayList<>();
//        recordReqs.add(req);
//        try {
//            opsConditionService.verifyConditions(new Scheme(), recordReqs);
//            Assert.fail("Attribute not found here");
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.ATTRIBUTE_NOT_FOUND);
//        }
//    }
//
//    @Test
//    public void verifyConditions_fail_operator_not_found() {
//        Mockito.when(schemeAttributeService.getAll()).thenReturn(attributes);
//        ConditionRecordReq req = new ConditionRecordReq();
//        req.setAttribute("TransactionType");
//        req.setOperator(AttributeOperator.IN);
//
//        List<ConditionRecordReq> recordReqs = new ArrayList<>();
//        recordReqs.add(req);
//        try {
//            opsConditionService.verifyConditions(new Scheme(),recordReqs);
//            Assert.fail("Operator not found here");
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.OPERATOR_NOT_FOUND);
//        }
//    }
//
//    @Test
//    public void verifyConditions_fail_bad_request() {
//        ConditionRecordReq req = new ConditionRecordReq();
//        req.setAttribute("TransactionType");
//        req.setValue("[]");
//        req.setOperator(AttributeOperator.EQUAL);
//
//        List<ConditionRecordReq> recordReqs = new ArrayList<>();
//        recordReqs.add(req);
//        try {
//            opsConditionService.verifyConditions(new Scheme(),recordReqs);
//            Assert.fail("Bad request fail here, object value not right format");
//        } catch (IllegalArgumentException e){
//        // Exception handler to business
//        } catch(BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.BAD_REQUEST);
//        }
//    }
//
//
//    @Test
//    public void getAllByRule() {
//        Integer schemeId = 24;
//        Scheme scheme = new Scheme();
//        scheme.setId(schemeId);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setSchemeId(schemeId);
//        schemeRule.setId(ruleId);
//        Mockito.when(schemeService.find(schemeRule.getSchemeId())).thenReturn(Optional.ofNullable(scheme));
//        List<ConditionRecordRes> exp = opsConditionService.getAllByRule(schemeRule);
//        Assert.assertEquals(exp, conditionRes);
//    }
//
//
//    @Test
//    public void schemeNotFound() {
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setSchemeId(1234);
//        schemeRule.setId(ruleId);
//        Mockito.when(schemeService.find(schemeRule.getSchemeId())).thenReturn(Optional.ofNullable(null));
//        try {
//            opsConditionService.getAllByRule(schemeRule);
//            Assert.fail("Scheme not found here");
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.SCHEME_NOT_FOUND);
//        }
//    }
//
//    @Test
//    public void updateConditionNotInRUle() {
//        Integer conditionRemoveId = 9834;
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//
//        SchemeRuleCondition conditionForRemove = new SchemeRuleCondition();
//        conditionForRemove.setId(conditionRemoveId);
//        conditionForRemove.setSchemeRuleId(ruleId);
//        conditionForRemove.setStatus(ECommonStatus.INACTIVE);
//        conditions.add(conditionForRemove);
//        Mockito.when(schemeRuleConditionService.getAllByRuleId(ruleId)).thenReturn(conditions);
//
//        AttributeValueStrategy<Integer> attributeValueStrategy = new NumberAttributeValueStrategy();
//        ConditionRecordReq reqUpdate = new ConditionRecordReq();
//        reqUpdate.setRuleId(38);
//        reqUpdate.setConditionId(conditionId);
//        reqUpdate.setValue(conditionValue);
//        reqUpdate.setAttribute(attribute);
//        reqUpdate.setOperator(operator);
//
//        ConditionRecordReq reqCreate = new ConditionRecordReq();
//        reqCreate.setRuleId(ruleId);
//        reqCreate.setValue(conditionValue);
//        reqCreate.setAttribute(attribute);
//        reqCreate.setOperator(operator);
//
//        List<SchemeRuleCondition> schemeRuleConditionsCreate = new ArrayList<>();
//        SchemeRuleCondition schemeRuleConditionForCreate = new SchemeRuleCondition();
//        schemeRuleConditionForCreate.setSchemeRuleId(ruleId);
//        schemeRuleConditionForCreate.setValue(conditionValue);
//        schemeRuleConditionForCreate.setAttribute(attribute);
//        schemeRuleConditionForCreate.setOperator(operator.getExpression());
//        schemeRuleConditionsCreate.add(schemeRuleConditionForCreate);
//
//        ConditionRecordRes conditionRecordResCreated = new ConditionRecordRes();
//
//        conditionRecordResCreated.setValue(attributeValueStrategy.getReadValue(operator, conditionValue));
//        conditionRecordResCreated.setAttribute(attribute);
//        conditionRecordResCreated.setOperator(operator.getExpression());
//
//        conditionRes.add(conditionRecordResCreated);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        reqs.add(reqCreate);
//        reqs.add(reqUpdate);
//
//        Mockito.when(schemeRuleConditionService.save(conditionForRemove))
//                .thenReturn(conditionForRemove);
//
//        Mockito.when(schemeRuleConditionService.save(schemeRuleCondition))
//                .thenReturn(schemeRuleCondition);
//
//        Mockito.when(schemeRuleConditionService.createAllInOneSchemeRule(schemeRuleConditionsCreate))
//                .thenReturn(schemeRuleConditionsCreate);
//
//        try {
//            opsConditionService.updateAllInOneSchemeRule(schemeRule, reqs);
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.CONDITION_NOT_IN_RULE);
//        }
//    }
//
//
//
//    @Test
//    public void updateConditionNotInRule2() {
//        Integer conditionRemoveId = 9834;
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//
//        SchemeRuleCondition conditionForRemove = new SchemeRuleCondition();
//        conditionForRemove.setId(conditionRemoveId);
//        conditionForRemove.setSchemeRuleId(ruleId);
//        conditionForRemove.setStatus(ECommonStatus.INACTIVE);
//        conditions.add(conditionForRemove);
//        Mockito.when(schemeRuleConditionService.getAllByRuleId(ruleId)).thenReturn(conditions);
//
//        AttributeValueStrategy<Integer> attributeValueStrategy = new NumberAttributeValueStrategy();
//        ConditionRecordReq reqUpdate = new ConditionRecordReq();
//        reqUpdate.setRuleId(ruleId);
//        reqUpdate.setConditionId(conditionId);
//        reqUpdate.setValue(conditionValue);
//        reqUpdate.setAttribute(attribute);
//        reqUpdate.setOperator(operator);
//
//        ConditionRecordReq reqUpdate2 = new ConditionRecordReq();
//        reqUpdate2.setRuleId(ruleId);
//        reqUpdate2.setConditionId(233254654);
//        reqUpdate2.setValue(conditionValue);
//        reqUpdate2.setAttribute(attribute);
//        reqUpdate2.setOperator(operator);
//
//        ConditionRecordReq reqCreate = new ConditionRecordReq();
//        reqCreate.setRuleId(ruleId);
//        reqCreate.setValue(conditionValue);
//        reqCreate.setAttribute(attribute);
//        reqCreate.setOperator(operator);
//
//        List<SchemeRuleCondition> schemeRuleConditionsCreate = new ArrayList<>();
//        SchemeRuleCondition schemeRuleConditionForCreate = new SchemeRuleCondition();
//        schemeRuleConditionForCreate.setSchemeRuleId(ruleId);
//        schemeRuleConditionForCreate.setValue(conditionValue);
//        schemeRuleConditionForCreate.setAttribute(attribute);
//        schemeRuleConditionForCreate.setOperator(operator.getExpression());
//        schemeRuleConditionsCreate.add(schemeRuleConditionForCreate);
//
//        ConditionRecordRes conditionRecordResCreated = new ConditionRecordRes();
//
//        conditionRecordResCreated.setValue(attributeValueStrategy.getReadValue(operator, conditionValue));
//        conditionRecordResCreated.setAttribute(attribute);
//        conditionRecordResCreated.setOperator(operator.getExpression());
//
//        conditionRes.add(conditionRecordResCreated);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        reqs.add(reqCreate);
//        reqs.add(reqUpdate);
//        reqs.add(reqUpdate2);
//
//        Mockito.when(schemeRuleConditionService.save(conditionForRemove))
//                .thenReturn(conditionForRemove);
//
//        Mockito.when(schemeRuleConditionService.save(schemeRuleCondition))
//                .thenReturn(schemeRuleCondition);
//
//        Mockito.when(schemeRuleConditionService.createAllInOneSchemeRule(schemeRuleConditionsCreate))
//                .thenReturn(schemeRuleConditionsCreate);
//
//        try {
//            opsConditionService.updateAllInOneSchemeRule(schemeRule, reqs);
//            Assert.fail("Condition not in rule here");
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.CONDITION_NOT_IN_RULE);
//        }
//    }
//
//    @Test
//    public void createAllInOneSchemeRule() {
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//        ConditionRecordReq req = new ConditionRecordReq();
//        req.setRuleId(ruleId);
//        req.setValue(conditionValue);
//        req.setAttribute(attribute);
//        req.setOperator(operator);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        reqs.add(req);
//
//        Mockito.when(schemeRuleConditionService.createAllInOneSchemeRule(conditions)).thenReturn(conditions);
//
//        List<ConditionRecordRes> exp = opsConditionService.createAllInOneSchemeRule(schemeRule, reqs);
//        Assert.assertEquals(exp, conditionRes);
//    }
//
//    @Test
//    public void updateAllInOneSchemeRule() {
//        Integer conditionRemoveId = 9834;
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//
//        SchemeRuleCondition conditionForRemove = new SchemeRuleCondition();
//        conditionForRemove.setId(conditionRemoveId);
//        conditionForRemove.setSchemeRuleId(ruleId);
//        conditionForRemove.setStatus(ECommonStatus.INACTIVE);
//        conditions.add(conditionForRemove);
//        Mockito.when(schemeRuleConditionService.getAllByRuleId(ruleId)).thenReturn(conditions);
//
//        AttributeValueStrategy<Integer> attributeValueStrategy = new NumberAttributeValueStrategy();
//        ConditionRecordReq reqUpdate = new ConditionRecordReq();
//        reqUpdate.setRuleId(ruleId);
//        reqUpdate.setConditionId(conditionId);
//        reqUpdate.setValue(conditionValue);
//        reqUpdate.setAttribute(attribute);
//        reqUpdate.setOperator(operator);
//
//        ConditionRecordReq reqCreate = new ConditionRecordReq();
//        reqCreate.setRuleId(ruleId);
//        reqCreate.setValue(conditionValue);
//        reqCreate.setAttribute(attribute);
//        reqCreate.setOperator(operator);
//
//        List<SchemeRuleCondition> schemeRuleConditionsCreate = new ArrayList<>();
//        SchemeRuleCondition schemeRuleConditionForCreate = new SchemeRuleCondition();
//        schemeRuleConditionForCreate.setSchemeRuleId(ruleId);
//        schemeRuleConditionForCreate.setValue(conditionValue);
//        schemeRuleConditionForCreate.setAttribute(attribute);
//        schemeRuleConditionForCreate.setOperator(operator.getExpression());
//        schemeRuleConditionsCreate.add(schemeRuleConditionForCreate);
//
//        ConditionRecordRes conditionRecordResCreated = new ConditionRecordRes();
//
//        conditionRecordResCreated.setValue(attributeValueStrategy.getReadValue(operator, conditionValue));
//        conditionRecordResCreated.setAttribute(attribute);
//        conditionRecordResCreated.setOperator(operator.getExpression());
//
//        conditionRes.add(conditionRecordResCreated);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        reqs.add(reqCreate);
//        reqs.add(reqUpdate);
//
//        Mockito.when(schemeRuleConditionService.save(conditionForRemove))
//                .thenReturn(conditionForRemove);
//
//        Mockito.when(schemeRuleConditionService.save(schemeRuleCondition))
//                .thenReturn(schemeRuleCondition);
//
//        Mockito.when(schemeRuleConditionService.createAllInOneSchemeRule(schemeRuleConditionsCreate))
//                .thenReturn(schemeRuleConditionsCreate);
//
//        List<ConditionRecordRes> exp = opsConditionService.updateAllInOneSchemeRule(schemeRule, reqs);
//
//        Assert.assertEquals(exp.size(), conditionRes.size());
//
//        conditionRes.forEach(
//                f1-> {
//                    Assert.assertEquals(exp.stream().filter(f1::equals).count(), 1);
//                }
//        );
//    }
//
//    @Test
//    public void updateAllInOneSchemeRule_fail_conditionNotInRule() {
//        Integer conditionRemoveId = 9834;
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//
//        ConditionRecordReq conditionForRemove = new ConditionRecordReq();
//        conditionForRemove.setConditionId(conditionRemoveId);
//        conditionForRemove.setRuleId(ruleId);
//
//        reqs.add(conditionForRemove);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        try {
//            opsConditionService.updateAllInOneSchemeRule(schemeRule, reqs);
//            Assert.fail("Condition not in rule here");
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.CONDITION_NOT_IN_RULE);
//        }
//    }
//
//    @Test
//    public void updateAllInOneSchemeRule_fail_conditionNotInRule2() {
//        Integer conditionRemoveId = 9834;
//        List<ConditionRecordReq> reqs = new ArrayList<>();
//
//        SchemeRuleCondition conditionForRemove = new SchemeRuleCondition();
//        conditionForRemove.setId(conditionRemoveId);
//        conditionForRemove.setSchemeRuleId(242);
//        conditionForRemove.setStatus(ECommonStatus.INACTIVE);
//        conditions.add(conditionForRemove);
//
//        SchemeRule schemeRule = new SchemeRule();
//        schemeRule.setId(ruleId);
//
//        try {
//            opsConditionService.updateAllInOneSchemeRule(schemeRule, reqs);
//            Assert.fail();
//        } catch (BusinessException e) {
//            Assert.assertEquals(e.getCode(), ErrorCode.CONDITION_NOT_IN_RULE);
//        }
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public OpsConditionService opsConditionService() {
//            return new OpsConditionServiceImpl();
//        }
//        @Bean
//        public AttributeValueFactory attributeValueFactory() {
//            return new AttributeValueFactory();
//        }
//
//    }
//}
