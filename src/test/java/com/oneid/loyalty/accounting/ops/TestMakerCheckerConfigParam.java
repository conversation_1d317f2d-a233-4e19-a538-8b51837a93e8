package com.oneid.loyalty.accounting.ops;

import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.service.impl.OpsGiftCardProductionRequestServiceImpl;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestPropertySource;

@TestPropertySource(properties = {"maker-checker.module.limitation:tier_policy", "maker-checker.module.gift-card-production-request:xxx",
"maker-checker.module.card-transfer", "maker-checker.module.scheme", "maker-checker.module.operator:operator", "maker-checker.action.create", "maker-checker.action.update"})
@TestConfiguration
public class TestMakerCheckerConfigParam {
    @Bean
    public MakerCheckerConfigParam makerCheckerConfigParam() {
        return new MakerCheckerConfigParam();
    }
}
