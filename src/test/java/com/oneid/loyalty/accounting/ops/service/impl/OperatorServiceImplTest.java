package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.req.ChangeOperatorReq;
import com.oneid.loyalty.accounting.ops.feign.model.res.SearchChangeRequestsRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.PayloadOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.SearchOperatorReq;
import com.oneid.loyalty.accounting.ops.service.OpsOperatorService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.repository.*;
import com.oneid.oneloyalty.common.service.BusinessService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
public class OperatorServiceImplTest {

    @Autowired
    private OpsOperatorService opsOperatorService;

    @Value("${maker-checker.module.operator:operator}")
    public String moduleId;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private CorporationRepository corporationRepository;

    @MockBean
    private ChainRepository chainRepository;

    @MockBean
    private StoreRepository storeRepository;

    @MockBean
    private PosRepository posRepository;

    @MockBean
    private OperatorRequestRepository operatorRequestRepository;

    @MockBean
    private OperatorProfileRepository operatorProfileRepository;

    @MockBean
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @MockBean
    private MakerCheckerConfigParam makerCheckerConfigParam;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void getApprove_operatorIdExisted() {
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.of(new OperatorProfile()));
        try {
            opsOperatorService.getApprove(CreateOperatorReq.builder().status(ECommonStatus.ACTIVE).build());
            Assert.fail("Operator id existed exception here");
        } catch (BusinessException e) {
            Assert.assertSame(e.getCode(), ErrorCode.OPERATOR_ID_EXISTED);
        }
    }

    @Test
    public void getApprove_pingCodeNotEcceptedEmpty() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode(null)
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setId(1);
        corporation.setStatus(ECommonStatus.ACTIVE);
        Chain chain = new Chain();
        chain.setId(2);
        chain.setStatus(ECommonStatus.ACTIVE);
        Store store = new Store();
        store.setId(3);
        store.setStatus(ECommonStatus.ACTIVE);
        Pos pos = new Pos();
        pos.setId(4);
        pos.setStatus(ECommonStatus.ACTIVE);

        Business business = new Business();
        business.setId(12800);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));

        try {
            opsOperatorService.getApprove(req);
            Assert.fail("BadRequest exception here");
        } catch (BusinessException e) {
            Assert.assertSame(e.getCode(), ErrorCode.BAD_REQUEST);
        }
    }

    @Test
    public void getApprove_success() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setStatus(ECommonStatus.ACTIVE);
        corporation.setId(1);
        Chain chain = new Chain();
        chain.setId(2);
        chain.setStatus(ECommonStatus.ACTIVE);
        Store store = new Store();
        store.setId(3);
        store.setStatus(ECommonStatus.ACTIVE);
        Pos pos = new Pos();
        pos.setId(4);
        pos.setStatus(ECommonStatus.ACTIVE);

        Business business = new Business();
        business.setId(12800);

        OperatorRequest OperatorRequest = new OperatorRequest();
        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(req.getBusinessId());
        operatorRequest.setOperatorId(req.getOperatorId());
        operatorRequest.setRequestPayload(req.getPayload());
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(req.getStatus());
        operatorRequest.setPinCode(req.getPinCode());
        operatorRequest.setVersion(1);
        operatorRequest.setId(233);

        Mockito.when(operatorRequestRepository.save(Mockito.any())).thenReturn(operatorRequest);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));
        opsOperatorService.getApprove(req);
    }

    @Test
    public void getApproveForUpdated_success() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setId(1);
        corporation.setStatus(ECommonStatus.ACTIVE);
        Chain chain = new Chain();
        chain.setId(2);
        chain.setStatus(ECommonStatus.ACTIVE);
        Store store = new Store();
        store.setId(3);
        store.setStatus(ECommonStatus.ACTIVE);
        Pos pos = new Pos();
        pos.setId(4);
        pos.setStatus(ECommonStatus.ACTIVE);

        Business business = new Business();
        business.setId(12800);

        OperatorRequest OperatorRequest = new OperatorRequest();
        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(req.getBusinessId());
        operatorRequest.setOperatorId(req.getOperatorId());
        operatorRequest.setRequestPayload(req.getPayload());
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(req.getStatus());
        operatorRequest.setPinCode(req.getPinCode());
        operatorRequest.setVersion(1);
        operatorRequest.setId(233);

        OperatorRequest effectedReq = new OperatorRequest();
        effectedReq.setBusinessId(req.getBusinessId());
        effectedReq.setVersion(1);

        Mockito.when(operatorRequestRepository.save(Mockito.any())).thenReturn(operatorRequest);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));
        Mockito.when(operatorRequestRepository.findByOperatorIdAndBusinessIdAndApprovalStatus(req.getOperatorId(), req.getBusinessId(), EApprovalStatus.PENDING))
                .thenReturn(Collections.emptyList());
        Mockito.when(operatorRequestRepository.findEffectedVersion(req.getBusinessId(), req.getOperatorId()))
                .thenReturn(Optional.of(effectedReq));
        opsOperatorService.getApproveForUpdate(req);
    }

    @Test
    public void getApproveForUpdate_operatorNotfound() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setId(1);
        Chain chain = new Chain();
        chain.setId(2);
        Store store = new Store();
        store.setId(3);
        Pos pos = new Pos();
        pos.setId(4);

        Business business = new Business();
        business.setId(12800);

        OperatorRequest OperatorRequest = new OperatorRequest();
        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(req.getBusinessId());
        operatorRequest.setOperatorId(req.getOperatorId());
        operatorRequest.setRequestPayload(req.getPayload());
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(req.getStatus());
        operatorRequest.setPinCode(req.getPinCode());
        operatorRequest.setVersion(1);
        operatorRequest.setId(233);

        OperatorRequest effectedReq = new OperatorRequest();
        effectedReq.setBusinessId(req.getBusinessId());
        effectedReq.setVersion(1);

        Mockito.when(operatorRequestRepository.save(Mockito.any())).thenReturn(operatorRequest);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));
        Mockito.when(operatorRequestRepository.findByOperatorIdAndBusinessIdAndApprovalStatus(req.getOperatorId(), req.getBusinessId(), EApprovalStatus.PENDING))
                .thenReturn(Collections.emptyList());
        Mockito.when(operatorRequestRepository.findEffectedVersion(req.getBusinessId(), req.getOperatorId()))
                .thenReturn(Optional.ofNullable(null));

        try {
            opsOperatorService.getApproveForUpdate(req);
            Assert.fail("Operator not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.OPERATOR_NOT_FOUND);
        }
    }

    @Test
    public void getApproveForUpdate_operatorWaitingForUpdate() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setId(1);
        Chain chain = new Chain();
        chain.setId(2);
        Store store = new Store();
        store.setId(3);
        Pos pos = new Pos();
        pos.setId(4);

        Business business = new Business();
        business.setId(12800);

        OperatorRequest OperatorRequest = new OperatorRequest();
        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(req.getBusinessId());
        operatorRequest.setOperatorId(req.getOperatorId());
        operatorRequest.setRequestPayload(req.getPayload());
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(req.getStatus());
        operatorRequest.setPinCode(req.getPinCode());
        operatorRequest.setVersion(1);
        operatorRequest.setId(233);

        OperatorRequest effectedReq = new OperatorRequest();
        effectedReq.setBusinessId(req.getBusinessId());
        effectedReq.setVersion(1);

        Mockito.when(operatorRequestRepository.save(Mockito.any())).thenReturn(operatorRequest);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));
        Mockito.when(operatorRequestRepository.findByOperatorIdAndBusinessIdAndApprovalStatus(req.getOperatorId(), req.getBusinessId(), EApprovalStatus.PENDING))
                .thenReturn(List.of(operatorRequest));
        Mockito.when(operatorRequestRepository.findEffectedVersion(req.getBusinessId(), req.getOperatorId()))
                .thenReturn(Optional.of(operatorRequest));

        try {
            opsOperatorService.getApproveForUpdate(req);
            Assert.fail("Operator waiting for approve here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.OPERATOR_IS_WAITING_FOR_APPROVE);
        }
    }

    @Test
    public void getApproveForUpdate_cannot_update() {
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();
        Mockito.when(operatorProfileRepository.findByBusinessIdAndOperatorId(Mockito.any(), Mockito.any()))
                .thenReturn(Optional.ofNullable(null));

        Corporation corporation = new Corporation();
        corporation.setId(1);
        Chain chain = new Chain();
        chain.setId(2);
        Store store = new Store();
        store.setId(3);
        Pos pos = new Pos();
        pos.setId(4);

        Business business = new Business();
        business.setId(12800);

        OperatorRequest OperatorRequest = new OperatorRequest();
        OperatorRequest operatorRequest = new OperatorRequest();
        operatorRequest.setBusinessId(req.getBusinessId());
        operatorRequest.setOperatorId(req.getOperatorId());
        operatorRequest.setRequestPayload(req.getPayload());
        operatorRequest.setRejectedReason(null);
        operatorRequest.setApprovalStatus(EApprovalStatus.PENDING);
        operatorRequest.setStatus(ECommonStatus.PENDING);
        operatorRequest.setInitialStatusOperatorProfile(req.getStatus());
        operatorRequest.setPinCode(req.getPinCode());
        operatorRequest.setVersion(1);
        operatorRequest.setId(233);

        OperatorRequest effectedReq = new OperatorRequest();
        effectedReq.setBusinessId(req.getBusinessId());

        Mockito.when(operatorRequestRepository.save(Mockito.any())).thenReturn(operatorRequest);
        Mockito.when(businessService.findActive(Mockito.any())).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessIdAndIdIn(req.getBusinessId(), List.of(1)))
                .thenReturn(List.of(corporation));
        Mockito.when(chainRepository.findByCorporationIdAndIdIn(1, List.of(2)))
                .thenReturn(List.of(chain));
        Mockito.when(storeRepository.findByChainIdAndIdIn(2, List.of(3)))
                .thenReturn(List.of(store));
        Mockito.when(posRepository.findByStoreIdAndIdIn(3, List.of(4)))
                .thenReturn(List.of(pos));
        Mockito.when(operatorRequestRepository.findByOperatorIdAndBusinessIdAndApprovalStatus(req.getOperatorId(), req.getBusinessId(), EApprovalStatus.PENDING))
                .thenReturn(Collections.singletonList(effectedReq));
        Mockito.when(operatorRequestRepository.findEffectedVersion(req.getBusinessId(), req.getOperatorId()))
                .thenReturn(Optional.of(effectedReq));
        try {
            opsOperatorService.getApproveForUpdate(req);
            Assert.fail("Expected throw business exception");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.OPERATOR_IS_WAITING_FOR_APPROVE);
        }
    }

    @Test
    public void approve_success() {
        APIResponse<SearchChangeRequestsRes<ChangeOperatorReq>> response = new APIResponse<>();
        response.setMeta(Meta.success());
        SearchChangeRequestsRes<ChangeOperatorReq> data = new SearchChangeRequestsRes();
        SearchChangeRequestsRes.RecordChangeRequestRes recordChangeRequestRes = new SearchChangeRequestsRes.RecordChangeRequestRes();
        recordChangeRequestRes.setId(1L);
        recordChangeRequestRes.setObjectId("1");
        data.setRecords(List.of(recordChangeRequestRes));
        response.setData(data);
        Mockito.when(makerCheckerFeignClient
                        .getChangeRequestOperators(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(response);
        opsOperatorService.approve(1);
    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsOperatorService opsOperatorService() {
            return new OpsOperatorServiceImpl();
        }

        @Bean
        public ObjectMapper objectMapper() {
            return new ObjectMapper();
        }
    }
}
