package com.oneid.loyalty.accounting.ops.controller;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.GiftCardController;
import com.oneid.loyalty.accounting.ops.model.req.GiftCardUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;

@Import(GiftCardController.class)
public class GiftCardControllerPermissionTest extends BasedWebMvcTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @MockBean
    private OpsGiftCardService opsGiftCardService;
    
    @After
    public void teardown() {
        defaultTeardown();
    }
    
    @Test
    public void getPage_without_role_access_DENIED() throws Exception {
        Integer mockBusinessId = 1;
        
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).getPage(mockBusinessId, null, null, null, null, null, null, null, null, null, null, null,
                 new OffsetBasedPageRequest(0, 20, null));
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void getPage_SUCCESS() throws Exception {
        Integer mockBusinessId = 1;
        
        Map<AccessRole, Long> permissions = new HashMap<>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.VIEW.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Page mockPage = Mockito.mock(Page.class);
        
        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);
        
        Mockito.when(opsGiftCardService.getPage(mockBusinessId, null, null, null, null, null, null, null, null, 
                EApprovalStatus.APPROVED, null, null, 
                new OffsetBasedPageRequest(0, 20, null)))
                .thenReturn(mockPage);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    
    
    @Test
    public void getDetail_without_role_access_DENIED() throws Exception {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available/program/{program_id}/serial/{serial}", mockProgramId, mockSerial)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("request_id", mockRequestId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).getApprovedDetail(mockProgramId, mockSerial);
        Mockito.verify(opsGiftCardService, times(0)).getRequestDetail(mockProgramId, mockSerial, mockRequestId);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getDetail_approved_detail_SUCCESS() throws Exception {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Map<AccessRole, Long> permissions = new HashMap<>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.VIEW.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available/program/{program_id}/serial/{serial}", mockProgramId, mockSerial)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(1)).getApprovedDetail(mockProgramId, mockSerial);
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void getDetail_request_detail_SUCCESS() throws Exception {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        Integer mockRequestId = 10;
        
        Map<AccessRole, Long> permissions = new HashMap<>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.VIEW.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available/program/{program_id}/serial/{serial}", mockProgramId, mockSerial)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("request_id", mockRequestId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(1)).getRequestDetail(mockProgramId, mockSerial, mockRequestId);
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    
    
    @Test
    public void getChangeable_without_role_access_DENIED() throws Exception {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available/program/{program_id}/serial/{serial}/changeable", mockProgramId, mockSerial)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).getChangeable(mockProgramId, mockSerial);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getChangeable_SUCCESS() throws Exception {
        Integer mockProgramId = 1;
        String mockSerial = "serial";
        
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/available/program/{program_id}/serial/{serial}/changeable", mockProgramId, mockSerial)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(1)).getChangeable(mockProgramId, mockSerial);
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    
    
    @Test
    public void requestUpdate_without_role_access_DENIED() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/gift-card/request/change")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(this.createValidUpdateReq()));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).requestUpdate(any());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestUpdate_SUCCESS() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/gift-card/request/change")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(this.createValidUpdateReq()));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        ArgumentCaptor<GiftCardUpdateReq> reqCaptor = ArgumentCaptor.forClass(GiftCardUpdateReq.class);
        
        Mockito.verify(opsGiftCardService, times(1)).requestUpdate(reqCaptor.capture());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        assertNotNull(reqCaptor.getValue());
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    private GiftCardUpdateReq createValidUpdateReq() {
        GiftCardUpdateReq req = new GiftCardUpdateReq();
        req.setProgramId(1);
        req.setSerial("serial");
        req.setStatus(EGiftCardStatus.ACTIVE);
        return req;
    }
    
    
    
    @Test
    public void getInReviewPage_without_role_access_DENIED() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).getInReviewPage(any(), any(), any(), any());
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void getInReviewPage_SUCCESS() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.VIEW.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Page mockPage = Mockito.mock(Page.class);
        
        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);
        
        Mockito.when(opsGiftCardService.getInReviewPage(any(), any(), any(), any())).thenReturn(mockPage);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    
    
    @Test
    public void getInReviewDetail_without_role_access_DENIED() throws Exception {
        Integer mockReviewId = 1;
        
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/in-review/{id}", mockReviewId)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(0)).getInReviewDetail(anyInt());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getInReviewDetail_SUCCESS() throws Exception {
        Integer mockReviewId = 1;
        
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();
        permissions.put(AccessRole.GIFT_CARD, AccessPermission.VIEW.getCode().longValue());
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/gift-card/request/in-review/{id}", mockReviewId)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsGiftCardService, times(1)).getInReviewDetail(mockReviewId);
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
}
