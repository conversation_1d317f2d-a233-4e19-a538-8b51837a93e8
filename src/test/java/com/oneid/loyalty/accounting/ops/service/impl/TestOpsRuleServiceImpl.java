package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
import com.oneid.loyalty.accounting.ops.model.req.ConditionRecordReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleRecordReq;
import com.oneid.loyalty.accounting.ops.model.req.VerifyCreateListSchemeRuleReq;
import com.oneid.loyalty.accounting.ops.model.res.RuleRecordRes;
import com.oneid.loyalty.accounting.ops.service.OpsConditionService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleService;
import com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.entity.Scheme;
import com.oneid.oneloyalty.common.entity.SchemeRule;
import com.oneid.oneloyalty.common.repository.RuleConditionRepository;
import com.oneid.oneloyalty.common.repository.RuleRepository;
import com.oneid.oneloyalty.common.service.RuleService;
import com.oneid.oneloyalty.common.service.SchemeRuleService;
import com.oneid.oneloyalty.common.service.SchemeService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestOpsRuleServiceImpl {

    @Autowired
    OpsRuleService opsRuleService;

    @MockBean
    SchemeRuleService schemeRuleService;

    @MockBean
    SchemeService schemeService;

    @MockBean
    OpsConditionService opsConditionService;

    @MockBean
    RuleService ruleService;

    @MockBean
    RuleRepository ruleRepository;

    @MockBean
    RuleConditionRepository ruleConditionRepository;

    @MockBean
    AttributeValueFactory attributeValueFactory;

    @MockBean
    com.oneid.loyalty.accounting.ops.validation.OpsReqPendingValidator OpsReqPendingValidator;

    private EConditionType conditionType = EConditionType.ALL;
    private Integer schemeId = 935;
    private String description = "sdfkdjsfksdjl";
    private Integer seqNo = 1;
    private String name = "sdjf";
    private Integer ruleId = 435;
    private Scheme scheme = new Scheme();


    private SchemeRule schemeRule = new SchemeRule();

    @Before
    public void before() {
        schemeRule.setId(ruleId);
        schemeRule.setSeqNo(seqNo);
        schemeRule.setSchemeId(schemeId);
        schemeRule.setStatus(ECommonStatus.ACTIVE);
        schemeRule.setConditionLogic(conditionType);
        schemeRule.setDescription(description);
        schemeRule.setName(name);

        scheme.setRuleLogic(conditionType);
        scheme.setId(schemeId);
    }

    @Test
    public void verifyListRule() {
        VerifyCreateListSchemeRuleReq req = new VerifyCreateListSchemeRuleReq();
        req.setRuleLogic(conditionType);

        List<RuleRecordReq> recordReqs = new ArrayList<>();
        RuleRecordReq ruleRecordReq = new RuleRecordReq();

        ConditionRecordReq conditionRecordReq = new ConditionRecordReq();
        conditionRecordReq.setRuleId(ruleId);
        List<ConditionRecordReq> conditionRecordReqs = new ArrayList<>();
        conditionRecordReqs.add(conditionRecordReq);
        ruleRecordReq.setListCondition(conditionRecordReqs);
        recordReqs.add(ruleRecordReq);
        req.setRuleList(recordReqs);

        opsRuleService.verifyListRule(new Scheme(), req);
    }

    @Test
    public void findAllBySchemeId() {
        List<SchemeRule> schemeRules = new ArrayList<>();
        schemeRules.add(schemeRule);

        RuleRecordRes ruleRecordRes = new RuleRecordRes();
        ruleRecordRes.setRuleId(ruleId);
        ruleRecordRes.setSchemeId(schemeId);
        ruleRecordRes.setSeqNo(seqNo);
        ruleRecordRes.setConditionLogic(conditionType);
        ruleRecordRes.setStatus(ECommonStatus.ACTIVE);
        ruleRecordRes.setListCondition(new LinkedList<>());
        ruleRecordRes.setRuleName(name);
        ruleRecordRes.setRuleDescription(description);

        List<RuleRecordRes> actual = new ArrayList<>();
        actual.add(ruleRecordRes);

        Mockito.when(schemeRuleService.getAllBySchemeId(schemeId)).thenReturn(schemeRules);

        List<RuleRecordRes> exp = opsRuleService.findAllBySchemeId(schemeId);

        Assert.assertEquals(exp, actual);
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsRuleService opsRuleService() {
            return new OpsRuleServiceImpl();
        }
    }
}