package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardTransferService;
import com.oneid.oneloyalty.common.constant.ECardTransferHistoryStatus;
import com.oneid.oneloyalty.common.constant.ECardTransferStatus;
import com.oneid.oneloyalty.common.constant.EConfSeq;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.service.*;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;
import java.util.Set;
import java.util.HashSet;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;

@RunWith(SpringRunner.class)
public class OpsCardTransferServiceImplTest {

    @Autowired
    OpsCardTransferService cardTransferService;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private ProgramService programService;

    @MockBean
    private StoreService storeService;

    @MockBean
    SequenceService sequenceService;

    @MockBean
    CorporationService corporationService;

    @MockBean
    ChainService chainService;

    @MockBean
    CardTransferringService cardTransferringService;

    @MockBean
    CardTMPService cardTMPService;

    @MockBean
    CardTransferringHistoryService cardTransferringHistoryService;

    @MockBean
    ObjectMapper objectMapper;

    @MockBean
    MakerCheckerConfigParam makerCheckerConfigParam;

    @MockBean
    MakerCheckerFeignClient makerCheckerServiceClient;
    
    @MockBean
    CardTypeService cardTypeService;

    @Test
    public void createRequestCardTransfer() {
        RequestTransferReq req = new RequestTransferReq();
        req.setBusinessId(1);
        req.setProgramId(2);
        req.setFromStoreId(3);
        req.setToStoreId(4);
        req.setBatchNo(5L);
        req.setDescription("test");

        Set<String> cardNoList = new HashSet<>();
        cardNoList.add("12345");
        req.setCardNoList(cardNoList);

        Business business = new Business();
        business.setId(1);

        Program program = new Program();
        program.setId(2);
        program.setBusinessId(1);

        Store storeFrom = new Store(), storeTo = new Store();
        storeFrom.setId(3);
        storeFrom.setBusinessId(1);
        storeTo.setId(4);
        storeTo.setBusinessId(1);

        storeFrom.setChainId(6);
        storeTo.setChainId(6);

        Mockito.when(businessService.findActive(1)).thenReturn(business);
        Mockito.when(programService.findActive(2)).thenReturn(program);
        Mockito.when(storeService.find(3)).thenReturn(storeFrom);
        Mockito.when(storeService.find(4)).thenReturn(storeTo);
        Mockito.when(sequenceService.getNextSeq(
                EConfSeq.CARD_TRANSFER_NO.getSeqId(1, 2))
        ).thenReturn(53475L);

        CardTMP card5 = new CardTMP();
        card5.setCprBatchNo(5L);
        card5.setIsTransferring(Boolean.FALSE);

        Mockito.when(cardTMPService.findPending("12345", 1, 2, 3)).thenReturn(card5);

        cardTransferService.createRequestCardTransfer(req);

        CardTransferring cardTransferring = new CardTransferring();
        cardTransferring.setId(100);
        cardTransferring.setCprBatchNo(5L);
        cardTransferring.setCardTransferNo(53475L);

        Mockito.verify(cardTransferringService, times(1)).save(argThat(transferring -> {
            Assert.assertSame(transferring.getStatus(), ECardTransferStatus.PENDING);
            return true;
        }));

        Mockito.when(cardTransferringService.save(Mockito.any())).thenReturn(cardTransferring);

        Mockito.verify(cardTMPService, times(cardNoList.size()))
                .findPending(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());

        Mockito.verify(cardTransferringHistoryService, times(cardNoList.size()))
                .saveAll(argThat((listCard) -> {
                    for (CardTransferringHistory cardHistory : listCard) {
                        Assert.assertEquals(53475L, (long) cardHistory.getCardTransferNo());
                        Assert.assertSame(cardHistory.getBatchNo(), 5L);
                        Assert.assertSame(cardHistory.getStatus(), ECardTransferHistoryStatus.PENDING);
                    }
                    return true;
                }));
    }

    @Test
    public void getDetailCardTransferNotFound() {
        try {
            cardTransferService.getDetails(1);
            Assert.fail("Card transfer not found here");
        } catch (BusinessException e) {
            Assert.assertSame(e.getCode(), ErrorCode.CARD_TRANSFER_NOT_FOUND);
        }
    }

    @Test
    public void getDetail() {
        CardTransferring cardTransferring = new CardTransferring();

        cardTransferring.setToStore(2);
        cardTransferring.setFromStore(3);

        Store store = new Store();
        store.setChainId(4);
        store.setCorporationId(5);
        store.setBusinessId(6);

        Mockito.when(cardTransferringService.findById(1)).thenReturn(Optional.of(cardTransferring));
        Mockito.when(businessService.find(Mockito.any())).thenReturn(Optional.of(new Business()));
        Mockito.when(programService.find(Mockito.any())).thenReturn(Optional.of(new Program()));
        Mockito.when(storeService.find(Mockito.anyInt())).thenReturn(store);
        Mockito.when(chainService.find(Mockito.anyInt())).thenReturn(Optional.of(new Chain()));
        Mockito.when(corporationService.find(Mockito.anyInt())).thenReturn(Optional.of(new Corporation()));
        cardTransferService.getDetails(1);
    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsCardTransferService cardTransferService() {
            return new OpsCardTransferServiceImpl();
        }
    }
}