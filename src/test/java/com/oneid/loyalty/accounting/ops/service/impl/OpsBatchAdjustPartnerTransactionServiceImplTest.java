package com.oneid.loyalty.accounting.ops.service.impl;

import com.google.cloud.storage.Storage;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.BatchAdjustPartnerTransactionReq;
import com.oneid.loyalty.accounting.ops.service.OpsBatchAdjustPartnerTransactionService;
import com.oneid.loyalty.accounting.ops.setting.BatchAdjustPartnerTransactionSetting;
import com.oneid.loyalty.accounting.ops.setting.BatchAdjustTransactionSetting;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBatchProcessStatusType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.BatchAdjustPartnerTransaction;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@TestPropertySource(properties = {"maker-checker.module.tcb-batch-adj-txn", "gcp.storagebucket-name:11",
        "app.batch-adjust-transaction.vgc_code", "app.batch-adjust-transaction.tcb_corporation_code", "app.format.tcb-export-date-pattern",
        "app.format.tcb-export-date-pattern", "app.format.date.pattern"})
public class OpsBatchAdjustPartnerTransactionServiceImplTest {

    @Autowired
    private OpsBatchAdjustPartnerTransactionService opsBatchAdjustPartnerTransactionService;

    @Autowired
    private BatchAdjustTransactionSetting batchAdjustTransactionSetting;

    @MockBean
    private PlatformTransactionManager platformTransactionManager;

    @MockBean
    private MakerCheckerFeignClient makerCheckerServiceClient;

    @MockBean
    private BatchAdjustPartnerTransactionRepository batchAdjustPartnerTransactionRepository;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private ProgramRepository programRepository;

    @MockBean
    private CorporationRepository corporationRepository;

    @MockBean
    private TcbFileTransactionRepository tcbFileTransactionRepository;

    @MockBean
    private ProgramCorporationRepository programCorporationRepository;

    @MockBean
    private TransactionTemplate transactionTemplate;

    @MockBean
    private Storage storage;

    @Value("${maker-checker.module.tcb-batch-adj-txn}")
    private String moduleId;

    @Value("${gcp.storagebucket-name}")
    private String gcpStorageBucketName;

    @Value("${app.batch-adjust-transaction.vgc_code}")
    private String vgcBusinessCode;

    @Value("${app.batch-adjust-transaction.tcb_corporation_code}")
    private String tcbCorporationCode;

    @Value("${app.format.tcb-export-date-pattern}")
    private String tcbExportDatePattern;

    @Value("${app.format.date.pattern}")
    private String ymdFormatPattern;

    private Business business;

    private Corporation corporation;

    @Before
    public void before() {
        business = new Business();
        corporation = new Corporation();

        business.setId(1);
        business.setCode("Business code");

        corporation.setId(2);
        corporation.setBusinessId(1);
        corporation.setCode("corporationCode");
        corporation.setStatus(ECommonStatus.ACTIVE);
    }

    @Test
    public void getTcbPartnerSetting() {
        Mockito.when(businessRepository.findByCode(vgcBusinessCode)).thenReturn(business);
        Mockito.when(corporationRepository.findByBusinessId(business.getId(), tcbCorporationCode)).thenReturn(corporation);
        Mockito.when(programCorporationRepository.findByCorporationIdAndStatus(corporation.getId(), ECommonStatus.ACTIVE))
                .thenReturn(new ArrayList<>());

        opsBatchAdjustPartnerTransactionService.getTcbPartnerSetting();
    }

    @Test
    public void exportBatchFileTemplate() {
        Mockito.when(corporationRepository.findById(corporation.getId())).thenReturn(Optional.of(corporation));
        opsBatchAdjustPartnerTransactionService.exportBatchFileTemplate(corporation.getId());
    }

    @Test
    public void requestCreatingBatchRequest() throws IOException {
        BatchAdjustPartnerTransactionReq req = new BatchAdjustPartnerTransactionReq();
        req.setCorporationId(corporation.getId());
        req.setBatchFileName("batchFileName");
        req.setProgramId(89);
        MultipartFile file = Mockito.mock(MockMultipartFile.class);
        Workbook mockWorkbook = Mockito.mock(Workbook.class);
        Sheet mockSheet = Mockito.mock(Sheet.class);
        Row mockRow = Mockito.mock(Row.class);
        Cell mockCell = Mockito.mock(Cell.class);

        Mockito.when(file.getOriginalFilename()).thenReturn("tcb_adjust_transaction.xlsx");
        Mockito.when(corporationRepository.findById(corporation.getId())).thenReturn(Optional.of(corporation));
        try {
            opsBatchAdjustPartnerTransactionService.requestCreatingBatchRequest(req, file);
        } catch (Exception e) {
            // todo
        }
    }

    @Test
    public void getDownloadedBatchFileLink() throws MalformedURLException {
        BatchAdjustPartnerTransaction transaction = new BatchAdjustPartnerTransaction();
        transaction.setResourcePath("ss");
        URL url = new URL("http", "", 8080, "");
        Mockito.when(batchAdjustPartnerTransactionRepository.findById(1)).thenReturn(Optional.of(transaction));
        Mockito.when(storage.signUrl(Mockito.any(), Mockito.anyInt(), Mockito.any(TimeUnit.class))).thenReturn(url);
        opsBatchAdjustPartnerTransactionService.getDownloadedBatchFileLink(1);
    }

    @Test
    public void getDownloadedBatchFileLink_batchAdjustTransactionNotFound() throws MalformedURLException {
        URL url = new URL("http", "", 8080, "");
        Mockito.when(storage.signUrl(Mockito.any(), Mockito.anyInt(), Mockito.any(TimeUnit.class))).thenReturn(url);
        try {
            opsBatchAdjustPartnerTransactionService.getDownloadedBatchFileLink(1);
            Assert.fail("Batch adjust partner transaction not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND);
        }
    }

    @Test
    public void getAvailableRequests() {
        Pageable pageable = new OffsetBasedPageRequest(1,1);

        Mockito.when(batchAdjustPartnerTransactionRepository.filter(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(new PageImpl(new ArrayList(), pageable, 0));

        opsBatchAdjustPartnerTransactionService.getAvailableRequests(
                "String batchName",
                1,
                2,
                3, 4,
                EBatchProcessStatusType.PENDING,
                EApprovalStatus.APPROVED,
                LocalDate.now(),
                LocalDate.now(),
                pageable);
    }

    @Test
    public void getAvailableRequestById() {
        BatchAdjustPartnerTransaction transaction = new BatchAdjustPartnerTransaction();
        transaction.setBusinessId(1);
        transaction.setProgramId(1);
        transaction.setCorporationId(1);
        Mockito.when(batchAdjustPartnerTransactionRepository.findById(1)).thenReturn(Optional.of(transaction));
        Mockito.when(businessRepository.findById(1)).thenReturn(Optional.of(new Business()));
        Mockito.when(programRepository.findByIdAndBusinessId(Mockito.any(),Mockito.any())).thenReturn(Optional.of(new Program()));
        Mockito.when(corporationRepository.findById(1)).thenReturn(Optional.of(new Corporation()));
        opsBatchAdjustPartnerTransactionService.getAvailableRequestById(1);
    }

    @Test
    public void getInReviewRequests() {

    }

    @Test
    public void getInReviewRequestById() {
        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> apiResponse = new APIResponse<>();
        ChangeRequestPageFeignRes.ChangeRecordFeginRes changeRecordFeginRes = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
        changeRecordFeginRes.setChangeRequestId(1);
        changeRecordFeginRes.setObjectId("1");
        apiResponse.setData(changeRecordFeginRes);
        Mockito.when(makerCheckerServiceClient.getChangeRequestById("1")).thenReturn(apiResponse);

        BatchAdjustPartnerTransaction transaction = new BatchAdjustPartnerTransaction();
        transaction.setBusinessId(1);
        transaction.setProgramId(1);
        transaction.setCorporationId(1);

        Mockito.when(batchAdjustPartnerTransactionRepository.findById(1)).thenReturn(Optional.of(transaction));
        Mockito.when(businessRepository.findById(1)).thenReturn(Optional.of(new Business()));
        Mockito.when(programRepository.findByIdAndBusinessId(Mockito.any(),Mockito.any())).thenReturn(Optional.of(new Program()));
        Mockito.when(corporationRepository.findById(1)).thenReturn(Optional.of(new Corporation()));

        opsBatchAdjustPartnerTransactionService.getInReviewRequestById(1);
    }

    @Test
    public void getTransactionsById() {

    }

    @Test
    public void exportAllTransactionsByRequestId() {

    }

    @Test
    public void getTransactionStatisticById(){

    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsBatchAdjustPartnerTransactionService opsBatchAdjustPartnerTransactionService() {
            return new OpsBatchAdjustPartnerTransactionServiceImpl();
        }

        @Bean
        public BatchAdjustTransactionSetting batchAdjustTransactionSetting() {

            BatchAdjustTransactionSetting result = new BatchAdjustTransactionSetting();
            BatchAdjustPartnerTransactionSetting setting = new BatchAdjustPartnerTransactionSetting();

            setting.setCorporationCode("corporationCode");
            setting.setBatchFileDir("batchFileDir");
            setting.setBatchFileName("batchFileDir");
            setting.setMaxRowSize(1);
            setting.setBucketDir("C");
            setting.setCsvExportHeaders(List.of("csvExportHeaders"));

            result.setPartners(List.of(setting));
            return result;
        }
    }
}
