package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.CounterController;
import com.oneid.loyalty.accounting.ops.service.OpsCounterService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.service.CounterService;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(CounterController.class)
public class CounterControllerPermissionTest extends BasedWebMvcTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsCounterService opsCounterService;

    @MockBean
    private CounterService counterService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void getLinkedServiceTypes_without_permission() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/request/{id}/linked-service-types", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(0)).getLinkedServiceTypes(any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getEditCounterRequestSetting_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/request/{id}/request-approval", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(0)).getEditCounterRequestSetting(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getEditCounterRequestSetting_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.COUNTER, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/request/{id}/request-approval", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(1)).getEditCounterRequestSetting(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableCounterRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(0)).getAvailableCounterRequestById(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getInReviewCounterRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(0)).getInReviewCounterRequestById(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableCounterRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsCounterService, times(0)).getAvailableCounterRequests(mockBusinessId, null, null, null, new OffsetBasedPageRequest(0, 20, null));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableCounterRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.COUNTER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return (Long) Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW)).mapToLong(AccessPermission::getCode).sum();
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;

        Mockito.when(opsCounterService.getAvailableCounterRequests(
                        Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/counters/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
}
