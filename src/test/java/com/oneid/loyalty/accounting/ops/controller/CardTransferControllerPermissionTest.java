package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.CardTransferController;
import com.oneid.loyalty.accounting.ops.model.req.RejectReq;
import com.oneid.loyalty.accounting.ops.model.req.RequestTransferReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardTransferService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.oneloyalty.common.constant.ECardTransferIndicatorStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardIndicator;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(CardTransferController.class)
public class CardTransferControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsCardTransferService opsCardTransferService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void create_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD_TRANSFER) || role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CREATE) || permission.equals(AccessPermission.MAKER_ROLE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        RequestTransferReq req = new RequestTransferReq();
        req.setBusinessId(12800);
        req.setProgramId(89);
        req.setFromStoreId(1);
        req.setToStoreId(2);
        req.setBatchNo(3L);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void create_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        RequestTransferReq req = new RequestTransferReq();
        req.setBusinessId(12800);
        req.setProgramId(89);
        req.setFromStoreId(1);
        req.setToStoreId(2);
        req.setBatchNo(3L);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void view_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD_TRANSFER))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-transfers/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void view_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-transfers/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void filter_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD_TRANSFER))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        Mockito.when(opsCardTransferService.filter(Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-transfers")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("indicator", ECardTransferIndicatorStatus.UN_SUCCESS.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void filter_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(opsCardTransferService.filter(Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-transfers")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("indicator", ECardTransferIndicatorStatus.UN_SUCCESS.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void approve_success() throws Exception {

        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CHECKER_ROLE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers/1/approve")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void approve_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers/1/approve")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void reject_succes() throws Exception {

        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CHECKER_ROLE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        RejectReq req = RejectReq.builder()
                .reason("reason")
                .build();
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers/1/reject")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void reject_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());
        RejectReq req = RejectReq.builder()
                .reason("reason")
                .build();
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-transfers/1/reject")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));


        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

}
