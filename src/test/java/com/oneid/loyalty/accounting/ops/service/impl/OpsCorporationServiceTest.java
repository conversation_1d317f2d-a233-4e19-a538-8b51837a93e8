package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.req.CorporationCreateReq;
import com.oneid.loyalty.accounting.ops.model.res.AccumulationDetailDTO;
import com.oneid.loyalty.accounting.ops.model.res.CorporationRes;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.*;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
@Ignore
public class OpsCorporationServiceTest {

    @Autowired
    private OpsCorporationService opsCorporationService;

    @MockBean
    private CountryService countryService;

    @MockBean
    private ProvinceService provinceService;

    @MockBean
    private DistrictService districtService;

    @MockBean
    private WardService wardService;

    @MockBean
    private CorporationRepository corporationRepository;

    @MockBean
    private CorporationService corporationService;

    @MockBean
    private BusinessRepository businessRepository;

    Corporation corporation1 = new Corporation();

    Corporation corporation2 = new Corporation();

    @Before
    public void before() {
        Business business = new Business();
        business.setId(1);
        business.setCode("bzncode1");
        business.setName("bznname1");
        business.setStatus(ECommonStatus.ACTIVE);

        corporation1.setId(1);
        corporation1.setCode("cprtcode1");
        corporation1.setName("cprtname1");
        corporation1.setBusinessId(1);
        corporation1.setStatus(ECommonStatus.ACTIVE);

        corporation2.setId(2);
        corporation2.setCode("cprtcode2");
        corporation2.setName("cprtname2");
        corporation2.setBusinessId(0);
        corporation2.setStatus(ECommonStatus.ACTIVE);

        Mockito.when(this.businessRepository.findAll()).thenReturn(Arrays.asList(business));
        Mockito.when(this.businessRepository.findById(business.getId())).thenReturn(Optional.of(business));
    }

    @Test
    public void search() {
//        Mockito.when(this.corporationService.find(Mockito.any(), Mockito.any())).thenReturn(new PageImpl<>(Arrays.asList(corporation1, corporation2)));
//
//        Page<CorporationRes> res = this.opsCorporationService.filter(1, null, null, "A", 0, 10);
//        assertEquals(2, res.getTotalElements());
    }

    @Test
    public void get_success() {
        Mockito.when(this.corporationService.find(1)).thenReturn(java.util.Optional.ofNullable(corporation1));

        this.opsCorporationService.get(1);
    }

    @Test
    public void get_fail() {
        try {
            this.opsCorporationService.get(1);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.CORPORATION_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void add_success() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());

        this.opsCorporationService.add(corporationCreateReq);
    }

    @Test
    public void add_fail_not_found_corporation() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());
        try {
            this.opsCorporationService.add(corporationCreateReq);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.CORPORATION_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void add_fail_not_found_country() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setCountryId(1);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());

        try {
            this.opsCorporationService.add(corporationCreateReq);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.COUNTRY_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void add_fail_not_found_province() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setProvinceId(1);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());

        try {
            this.opsCorporationService.add(corporationCreateReq);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.PROVINCE_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void add_fail_not_found_district() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setDistrictId(1);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());

        try {
            this.opsCorporationService.add(corporationCreateReq);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.DISTRICT_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void add_fail_not_found_ward() {
        Mockito.when(this.corporationRepository.findByBusinessId(0, "cprtcode2")).thenReturn(corporation2);

        CorporationCreateReq corporationCreateReq = new CorporationCreateReq();
        corporationCreateReq.setBusinessId(1);
        corporationCreateReq.setCode("cprtcode2");
        corporationCreateReq.setName("cprtname2");
        corporationCreateReq.setServiceStartDate(0L);
        corporationCreateReq.setServiceEndDate(0L);
        corporationCreateReq.setWardId(1);
        corporationCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());

        try {
            this.opsCorporationService.add(corporationCreateReq);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.WARD_NOT_FOUND, e.getCode());
        }
    }


    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsCorporationService opsCorporationService() {
            return new OpsCorporationServiceImpl();
        }
    }
}
