//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateCreateReq;
//import com.oneid.loyalty.accounting.ops.model.req.CurrencyRateUpdateReq;
//import com.oneid.loyalty.accounting.ops.model.res.CurrencyRateRes;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import com.oneid.oneloyalty.common.constant.ECommonStatus;
//import com.oneid.oneloyalty.common.entity.Business;
//import com.oneid.oneloyalty.common.entity.Currency;
//import com.oneid.oneloyalty.common.repository.BusinessRepository;
//import com.oneid.oneloyalty.common.repository.CurrencyRateRepository;
//import com.oneid.oneloyalty.common.repository.CurrencyRepository;
//import com.oneid.oneloyalty.common.service.CurrencyRateService;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//import java.util.Optional;
//
//import static org.junit.Assert.*;
//
//@RunWith(SpringRunner.class)
//public class CurrencyRateServiceTest {
//
//    private static final double DELTA = 0.01;
//    String codeCurrency1 = "VND";
//    ECommonStatus statusCurrency1 = ECommonStatus.ACTIVE;
//    String codeCurrency2 = "USD";
//    ECommonStatus statusCurrency2 = ECommonStatus.ACTIVE;
//    String codeCurrency3 = "YEN";
//    ECommonStatus statusCurrency3 = ECommonStatus.ACTIVE;
//    String businessCode = "partnerCode";
//
//    @MockBean
//    CurrencyRateRepository currencyRateRepository;
//
//    @MockBean
//    CurrencyRepository currencyRepository;
//
//    @MockBean
//    BusinessRepository businessRepository;
//
//    @Autowired
//    CurrencyRateService currencyRateService;
//
//    @Before
//    public void before() {
//        // Prepare data
//        Currency currency1 = new Currency();
//        currency1.setCode(codeCurrency1);
//        currency1.setStatus(statusCurrency1);
//
//        Currency currency2 = new Currency();
//        currency2.setCode(codeCurrency2);
//        currency2.setStatus(statusCurrency2);
//
//        Currency currency3 = new Currency();
//        currency3.setCode(codeCurrency3);
//        currency3.setStatus(statusCurrency3);
//
//        Business business = new Business();
//        business.setCode(businessCode);
//
//        // Mock
//        Mockito.when(currencyRepository.findOne(codeCurrency1)).thenReturn(currency1);
//        Mockito.when(currencyRepository.findOne(codeCurrency2)).thenReturn(currency2);
//        Mockito.when(currencyRepository.findOne(codeCurrency3)).thenReturn(currency3);
//        Mockito.when(partnerRepository.findById(businessCode)).thenReturn(Optional.of(wlPartner));
//    }
//
//    @Test
//    public void getCurrencyRate_Successful() {
//        // Prepare Data
//        Long id = 1L;
//        String baseCurrency = "VND";
//        String currency = "DLHK";
//        Long buyRateValue = 1000L;
//        Long sellRateValue = 2000L;
//        ERoundRule roundRule = ERoundRule.of("ROUND");
//        double buyRate = 0.25;
//        double sellRate = 0.5;
//        ECommonStatus status = ECommonStatus.of("A");
//        CurrencyRate currencyRate = new CurrencyRate();
//        currencyRate.setId(id);
//        currencyRate.setBaseCurrency(baseCurrency);
//        currencyRate.setCurrency(currency);
//        currencyRate.setBuyRateValue(buyRateValue);
//        currencyRate.setSellRateValue(sellRateValue);
//        currencyRate.setRoundRule(roundRule);
//        currencyRate.setBuyRate(buyRate);
//        currencyRate.setSellRate(sellRate);
//        currencyRate.setStatus(status);
//
//        // Mock
//        Mockito.when(currencyRateRepository.findById(id)).thenReturn(Optional.of(currencyRate));
//
//        // Test
//        CurrencyRateRes result = currencyRateService.getCurrencyRate(id);
//
//        assertEquals(id, result.getId());
//        assertEquals(baseCurrency, result.getBaseCurrency());
//        assertEquals(currency, result.getCurrency());
//        assertEquals(buyRateValue, result.getBuyRateValue());
//        assertEquals(sellRateValue, result.getSellRateValue());
//        assertEquals(roundRule.getValue(), result.getRoundRule());
//        assertEquals(buyRate, result.getBuyRate(), 3);
//        assertEquals(sellRate, result.getSellRate(), 3);
//        assertEquals(status.getValue(), result.getStatus());
//    }
//
//    @Test
//    public void getCurrencyRate_NotFound() {
//        // Prepare data
//        Long id = 1L;
//        // Mock
//        Mockito.when(currencyRateRepository.findById(id)).thenReturn(Optional.empty());
//        // Test
//        CurrencyRateRes result = this.currencyRateService.getCurrencyRate(id);
//
//        assertNull(result);
//    }
//
//    @Test
//    public void filterCurrencyRate_WrongStatus_ExceptionThrown() {
//        String baseCurrency = "VND";
//        String currency = "USD";
//        String partnerCode = "partnerCode";
//        String status = "active";
//        Integer offset = 0;
//        Integer limit = 10;
//
//        try {
//            this.currencyRateService.filterCurrencyRate(baseCurrency, currency, status, partnerCode, offset, limit);
//            fail("should have thrown");
//        } catch (BusinessException e) {
//            assertEquals(ErrorCode.BAD_REQUEST, e.getCode());
//            assertEquals("Enum status wrong", e.getMessage());
//        }
//    }
//
//    @Test
//    public void filterCurrencyRate_Successful() {
//        // Prepare Data
//        String baseCurrency = null;
//        String currency = null;
//        String partnerCode = null;
//        String roundRule = "ROUND";
//        String status = "A";
//        int offset = 0;
//        int limit = 10;
//        int total = 1;
//        // Mock
//        CurrencyRate currencyRate = new CurrencyRate();
//        currencyRate.setRoundRule(ERoundRule.of(roundRule));
//        currencyRate.setStatus(ECommonStatus.of(status));
//        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit);
//        Page<CurrencyRate> page = new PageImpl<>(Arrays.asList(currencyRate), pageRequest, total);
//        Mockito.when(currencyRateRepository.filter(baseCurrency, currency, ECommonStatus.of(status), partnerCode, pageRequest)).thenReturn(page);
//
//        // Test
//        Page<CurrencyRateRes> result = this.currencyRateService.filterCurrencyRate(baseCurrency, currency, status, partnerCode, offset, limit);
//        assertEquals(total, result.getTotalElements());
//    }
//
//    @Test
//    public void addCurrency_BaseCurrencyNotFound_ExceptionThrown() {
//        // Prepare data
//        CurrencyRateCreateReq data = new CurrencyRateCreateReq();
//
//        // Test
//        try {
//            this.currencyRateService.addCurrencyRate(data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.BASE_CURRENCY_NOT_FOUND, ex.getCode());
//            assertEquals("Base currency not found", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void addCurrency_CurrencyNotFound_ExceptionThrown() {
//        // Prepare data
//        CurrencyRateCreateReq data = new CurrencyRateCreateReq();
//        data.setBaseCurrency(codeCurrency1);
//
//        // Test
//        try {
//            this.currencyRateService.addCurrencyRate(data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.CURRENCY_NOT_FOUND, ex.getCode());
//            assertEquals("Currency not found", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void addCurrency_PartnerNotFound_ExceptionThrown() {
//        // Prepare data
//        CurrencyRateCreateReq data = new CurrencyRateCreateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency2);
//
//        // Test
//        try {
//            this.currencyRateService.addCurrencyRate(data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.PARTNER_CODE_NOT_FOUND, ex.getCode());
//            assertEquals("Partner not found", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void addCurrency_CurrencyRateCreated_ExceptionThrown() {
//        // Prepare data
//        CurrencyRateCreateReq data = new CurrencyRateCreateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency2);
//        data.setPartnerCode(businessCode);
//
//        CurrencyRate currencyRateRes = new CurrencyRate();
//        currencyRateRes.setBaseCurrency(codeCurrency1);
//        currencyRateRes.setCurrency(codeCurrency2);
//
//        // Mock
//        Mockito.when(currencyRateRepository.findByBaseCurrencyAndCurrency(codeCurrency1, codeCurrency2)).thenReturn(Arrays.asList(currencyRateRes));
//
//        // Test
//        try {
//            this.currencyRateService.addCurrencyRate(data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.CURRENCY_RATE_EXIST, ex.getCode());
//            assertEquals("Currency rate created", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void addCurrency_Successful() {
//        // Prepare data
//        CurrencyRateCreateReq data = new CurrencyRateCreateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency2);
//        data.setPartnerCode(businessCode);
//        data.setBuyRate(0.01f);
//        data.setSellRate(0.01f);
//
//        this.currencyRateService.addCurrencyRate(data);
//    }
//
//    @Test
//    public void updateCurrency_CurrencyRateNotFound_ExceptionThrown() {
//        // Prepare data
//        Long currencyRateId = 1L;
//        CurrencyRateUpdateReq data = new CurrencyRateUpdateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency2);
//        data.setPartnerCode(businessCode);
//
//        // Test
//        try {
//            this.currencyRateService.updateCurrencyRate(currencyRateId, data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.CURRENCY_RATE_NOT_FOUND, ex.getCode());
//            assertEquals("Currency rate not found", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void updateCurrency_CurrencyRateCreated_ExceptionThrown() {
//        // Prepare data
//        Long currencyRateId1 = 1L;
//        Long currencyRateId2 = 2L;
//
//        CurrencyRateUpdateReq data = new CurrencyRateUpdateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency3);
//        data.setPartnerCode(businessCode);
//
//        CurrencyRate currencyRate1 = new CurrencyRate();
//        currencyRate1.setId(currencyRateId1);
//        currencyRate1.setBaseCurrency(codeCurrency1);
//        currencyRate1.setCurrency(codeCurrency2);
//        currencyRate1.setPartnerCode(businessCode);
//        CurrencyRate currencyRate2 = new CurrencyRate();
//        currencyRate2.setId(currencyRateId2);
//        currencyRate2.setBaseCurrency(codeCurrency1);
//        currencyRate2.setCurrency(codeCurrency3);
//        currencyRate2.setPartnerCode(businessCode);
//
//        // Mock
//        Mockito.when(currencyRateRepository.findById(1L)).thenReturn(Optional.of(currencyRate1));
//        Mockito.when(currencyRateRepository.findByBaseCurrencyAndCurrency(codeCurrency1, codeCurrency3)).thenReturn(Arrays.asList(currencyRate2));
//
//        // Test
//        try {
//            this.currencyRateService.updateCurrencyRate(currencyRateId1, data);
//            fail("should have thrown");
//        } catch (BusinessException ex) {
//            assertEquals(ErrorCode.CURRENCY_RATE_EXIST, ex.getCode());
//            assertEquals("Currency rate created", ex.getMessage());
//        }
//    }
//
//    @Test
//    public void updateCurrency_Successful() {
//        // Prepare data
//        Long currencyRateId1 = 1L;
//
//        CurrencyRateUpdateReq data = new CurrencyRateUpdateReq();
//        data.setBaseCurrency(codeCurrency1);
//        data.setCurrency(codeCurrency3);
//        data.setPartnerCode(businessCode);
//
//        CurrencyRate currencyRate1 = new CurrencyRate();
//        currencyRate1.setId(currencyRateId1);
//        currencyRate1.setBaseCurrency(codeCurrency1);
//        currencyRate1.setCurrency(codeCurrency2);
//        currencyRate1.setPartnerCode(businessCode);
//
//        // Mock
//        Mockito.when(currencyRateRepository.findById(1L)).thenReturn(Optional.of(currencyRate1));
//
//        // Test
//        this.currencyRateService.updateCurrencyRate(currencyRateId1, data);
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public CurrencyRateService currencyRateService() {
//            return new CurrencyRateServiceImpl();
//        }
//    }
//}
