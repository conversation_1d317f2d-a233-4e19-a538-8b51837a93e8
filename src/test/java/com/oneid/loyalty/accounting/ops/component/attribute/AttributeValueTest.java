//package com.oneid.loyalty.accounting.ops.component.attribute;
//
//import static org.junit.Assert.assertNotNull;
//import static org.junit.Assert.assertTrue;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.stream.Collectors;
//
//import com.oneid.loyalty.accounting.ops.component.AttributeValueFactory;
//import com.oneid.loyalty.accounting.ops.component.AttributeValueStrategy;
//import com.oneid.loyalty.accounting.ops.component.attribute.strategy.*;
//import com.oneid.loyalty.accounting.ops.component.constant.AttributeOperator;
//import com.oneid.loyalty.accounting.ops.component.constant.AttributeType;
//import com.oneid.loyalty.accounting.ops.model.component.AttributeValueComponent;
//import com.oneid.oneloyalty.common.entity.Corporation;
//import com.oneid.oneloyalty.common.entity.Program;
//import com.oneid.oneloyalty.common.entity.Scheme;
//import com.oneid.oneloyalty.common.service.*;
//import com.oneid.oneloyalty.common.support.Hashing;
//import lombok.SneakyThrows;
//import org.assertj.core.util.Arrays;
//import org.junit.Before;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//@RunWith(SpringRunner.class)
//public class AttributeValueTest {
//    @Autowired
//    private AttributeValueFactory attributeValueFactory;
//
//    @MockBean
//    private CorporationService corporationService;
//
//    @MockBean
//    private ChainService chainService;
//
//    @MockBean
//    private StoreService storeService;
//
//    @MockBean
//    private SchemeService schemeService;
//
//    @MockBean
//    private PosService posService;
//
//    @MockBean
//    private ProgramService programService;
//
//    @MockBean
//    private ProgramTierService programTierService;
//
//    @Autowired
//    private Hashing hashing;
//
//    @Autowired
//    CorporationAttributeValueStrategy corporationAttributeValueStrategy;
//
//    String checksumValue = "checksumValue";
//
//    Program program = new Program();
//    Scheme scheme = new Scheme();
//    Corporation corporation = new Corporation();
//
//    @SneakyThrows
//    @Before
//    public void before() {
//        program.setBusinessId(1);
//        program.setId(1);
//
//        scheme.setId(1);
//        scheme.setProgramId(program.getId());
//
//        corporation.setBusinessId(1);
//        corporation.setCode("CORPORATION_CODE");
//        corporation.setName("corporationName");
//        Mockito.when(corporationService.findActive(1, corporation.getCode())).thenReturn(corporation);
//        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
//    }
//
//    @Test
//    public void testGetWriteValue_dateAttribute_single_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.DATE);
//
//        assertNotNull(attributeValueStrategy);
//
//        String value = attributeValueStrategy.getWriteValue(AttributeOperator.EQUAL, "20201120");
//
//        assertEquals(String.valueOf("20201120"), value);
//    }
//
//    @Test
//    public void testGetReadValue_dateAttribute_single_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.DATE);
//
//        assertNotNull(attributeValueStrategy);
//
//        String dummyId = "20201120";
//
//        Object value = attributeValueStrategy.getReadValue(AttributeOperator.EQUAL, dummyId);
//
//        assertTrue(value.getClass().isAssignableFrom(String.class));
//
//        String actual = (String) value;
//        assertEquals(dummyId, actual);
//    }
//
//    @Test
//    public void testGetWriteValue_dateAttribute_multiple_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.DATE);
//
//        assertNotNull(attributeValueStrategy);
//
//        List<Object> dummyValues = Arrays.asList(new String[] {"20200228", "20201231"});
//
//        String value = attributeValueStrategy.getWriteValue(AttributeOperator.IN, dummyValues);
//
//        String expected = dummyValues.stream()
//                .map(String::valueOf)
//                .collect(Collectors.joining(AttributeValueStrategy.DELIMITER));
//
//        assertEquals(expected, value.toString());
//    }
//
//    @Test
//    public void testGetReadValue_dateAttribute_multiple_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.DATE);
//
//        assertNotNull(attributeValueStrategy);
//
//
//        List<String> expected = java.util.Arrays.asList(
//                "20200228|20201231".split("\\" + AttributeValueStrategy.DELIMITER));
//
//        Object value = attributeValueStrategy.getReadValue(AttributeOperator.IN, "20200228|20201231");
//
//        assertEquals(expected.toString(), value.toString());
//    }
//
//    @Test
//    public void testGetWriteValue_numberAttribute_single_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.NUMBER);
//        
//        assertNotNull(attributeValueStrategy);
//        
//        String value = attributeValueStrategy.getWriteValue(AttributeOperator.EQUAL, 1);
//        
//        assertEquals(String.valueOf(1), value);
//    }
//    
//    @Test
//    public void testGetReadValue_numberAttribute_single_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.NUMBER);
//        
//        assertNotNull(attributeValueStrategy);
//        
//        Integer dummyId = 1;
//        
//        Object value = attributeValueStrategy.getReadValue(AttributeOperator.EQUAL, String.valueOf(dummyId));
//        
//        assertTrue(value.getClass().isAssignableFrom(Integer.class));
//        
//        Integer actual = (Integer) value;
//        assertEquals(dummyId, actual);
//    }
//    
//    @Test
//    public void testGetWriteValue_numberAttribute_multiple_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.NUMBER);
//        
//        assertNotNull(attributeValueStrategy);
//        
//        List<Object> dummyValues = Arrays.asList(new Integer[] {1, 2});
//        
//        String value = attributeValueStrategy.getWriteValue(AttributeOperator.IN, dummyValues);
//        
//        String expected = dummyValues.stream()
//        .map(String::valueOf)
//        .collect(Collectors.joining(AttributeValueStrategy.DELIMITER));
//        
//        assertEquals(expected, value.toString());
//    }
//    
//    @Test
//    public void testGetWriteValue_corporationrAttribute_single_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.CORPORATION);
//
//        assertNotNull(attributeValueStrategy);
//
//        AttributeValueComponent component = corporationAttributeValueStrategy
//                .toComponentResponse(corporation, scheme.getProgramId());
//
//        Object value = attributeValueStrategy.getWriteValue(AttributeOperator.EQUAL, component, scheme);
//        assertEquals(component.getOriginalValue(), value.toString());
//    }
//    
//    @Test
//    public void testGetReadValue_corporationrAttribute_single_valid() {
//
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.CORPORATION);
//        
//        assertNotNull(attributeValueStrategy);
//
//        String dummy = corporation.getCode();
//
//        Object value = attributeValueStrategy.getReadValue(AttributeOperator.EQUAL, dummy, scheme);
//        
//        assertTrue(value.getClass().isAssignableFrom(AttributeValueComponent.class));
//
//        AttributeValueComponent corporationReq = (AttributeValueComponent) value;
//        assertEquals(dummy, corporationReq.getOriginalValue());
//    }
//    
//    @Test
//    @Ignore //TODO will be back later
//    public void testGetWriteValue_corporationrAttribute_multiple_valid() {
//        AttributeValueStrategy<?> attributeValueStrategy = attributeValueFactory.lookup(AttributeType.CORPORATION);
//        
//        assertNotNull(attributeValueStrategy);
//        
//        LinkedHashMap<String, Object> firstDummyParams = new LinkedHashMap<String, Object>();
//        
//        Integer firstDummyId = 1;
//        Integer secondDummyId = 2;
//        
//        List<Object> dummyValues = Arrays.asList(new Integer[] {firstDummyId, secondDummyId});
//        
//        firstDummyParams.put("business_id", firstDummyId);
//        firstDummyParams.put("corporation_id", firstDummyId);
//        
//        LinkedHashMap<String, Object> secondDummyParams = new LinkedHashMap<String, Object>();
//        
//        secondDummyParams.put("business_id", secondDummyId);
//        secondDummyParams.put("corporation_id", secondDummyId);
//        
//        Object value = attributeValueStrategy.getWriteValue(AttributeOperator.NOT_IN, Arrays.asList(new Object[] {firstDummyParams, secondDummyParams}));
//        
//        String expected = dummyValues.stream()
//                .map(String::valueOf)
//                .collect(Collectors.joining(AttributeValueStrategy.DELIMITER));
//                
//                assertEquals(expected, value.toString());
//    }
//    
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        ObjectMapper objectMapper() {
//            return new ObjectMapper();
//        }
//        
//        @Bean
//        AttributeValueFactory attributeValueFactory() {
//            return new AttributeValueFactory();
//        }
//
//        @Bean
//        NumberAttributeValueStrategy numberAttributeValueStrategy() {
//            return new NumberAttributeValueStrategy();
//        }
//        
//        @Bean
//        DateAttributeValueStrategy dateAttributeValueStrategy() {
//            return new DateAttributeValueStrategy();
//        }
//        
//        @Bean
//        CorporationAttributeValueStrategy corporationAttributeValueStrategy() {
//            return new CorporationAttributeValueStrategy();
//        }
//
//        @Bean
//        ChainAttributeValueStrategy chainAttributeValueStrategy() {
//            return new ChainAttributeValueStrategy();
//        }
//
//        @Bean
//        StoreAttributeValueStrategy storeAttributeValueStrategy() {
//            return new StoreAttributeValueStrategy();
//        }
//
//        @Bean
//        ComboboxAttributeValueStrategy terminalAttributeValueStrategy() {
//            return new ComboboxAttributeValueStrategy();
//        }
//
//        @Bean
//        TierAttributeValueStrategy tierAttributeValueStrategy() {
//            return new TierAttributeValueStrategy();
//        }
//
//        @Bean
//        DateTimeAttributeValueStrategy dateTimeAttributeValueStrategy() {
//            return new DateTimeAttributeValueStrategy();
//        }
//
//        @Bean
//        TransactionTypeAttributeValueStrategy transactionTypeAttributeValueStrategy() {
//            return new TransactionTypeAttributeValueStrategy();
//        }
//
//        @Bean
//        MemberStatusAttributeValueStrategy memberStatusAttributeValueStrategy() {
//            return new MemberStatusAttributeValueStrategy();
//        }
//
//        @Bean
//        public Hashing hashing() {
//            return new Hashing("oneloyalty","HMACMD5");
//        }
//    }
//}
