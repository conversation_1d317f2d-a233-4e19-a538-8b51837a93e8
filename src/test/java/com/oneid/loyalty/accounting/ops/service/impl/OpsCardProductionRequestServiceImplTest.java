package com.oneid.loyalty.accounting.ops.service.impl;

import java.util.ArrayList;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;

import com.oneid.loyalty.accounting.ops.component.SFTPClient;
import com.oneid.loyalty.accounting.ops.feign.CardServiceFeignClient;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.service.OpsCardProductionRequestService;
import com.oneid.loyalty.accounting.ops.setting.SFTPSetting;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.CardProductionRequest;
import com.oneid.oneloyalty.common.entity.CardTMP;
import com.oneid.oneloyalty.common.entity.CardType;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.CardProductionRequestRepository;
import com.oneid.oneloyalty.common.repository.CardTMPRepository;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.StoreService;

@RunWith(SpringRunner.class)
public class OpsCardProductionRequestServiceImplTest {

    @Autowired
    private OpsCardProductionRequestService opsCardProductionRequestService;

    @MockBean
    private CardProductionRequestRepository cardProductionRequestRepository;

    @MockBean
    private CardTMPRepository cardTMPRepository;

    @MockBean
    private CorporationService corporationService;

    @MockBean
    private StoreService storeService;

    @MockBean
    private CardTypeService cardTypeService;
    
    @MockBean
    private BusinessRepository businessRepository;
    
    @MockBean
    private MakerCheckerFeignClient makerCheckerServiceClient;
    
    @MockBean
    private CardServiceFeignClient cardServiceFeignClient;
    
    @MockBean(name = "cprSFTPClient")
    private SFTPClient sftpClient;
    
    @MockBean(name = "cprsSftpSetting")
    private SFTPSetting SFTPSetting;

    @Test
    public void getById() {
        CardProductionRequest newCardProductionRequest = new CardProductionRequest();
        newCardProductionRequest.setStatus(ECommonStatus.ACTIVE);
        Mockito.when(cardProductionRequestRepository.findById(Mockito.any()))
                .thenReturn(Optional.of(newCardProductionRequest));
        opsCardProductionRequestService.getById(1);
    }

    @Test
    public void getById_notFound() {
        try {
            opsCardProductionRequestService.getById(1);
            Assert.fail("Card production request not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_PRODUCTION_REQUEST_NOT_FOUND);
        }
    }

    @Test // todo
    @Ignore
    public void exportDetailToSFTP() {
        Integer storeId = 1, corporationId = 2, cardTypeId = 3;
        Store store = new Store();
        store.setCorporationId(corporationId);
        CardProductionRequest newCardProductionRequest = new CardProductionRequest();
        newCardProductionRequest.setStoreId(storeId);
        newCardProductionRequest.setStatus(ECommonStatus.ACTIVE);
        newCardProductionRequest.setCardTypeId(cardTypeId);

        Mockito.when(cardProductionRequestRepository.findById(1)).thenReturn(Optional.of(newCardProductionRequest));
        Mockito.when(cardTMPRepository.findAll((Specification<CardTMP>) Mockito.any()))
                .thenReturn(new ArrayList<>());
        Mockito.when(storeService.findActive(storeId)).thenReturn(store);
        Mockito.when(corporationService.findActive(corporationId)).thenReturn(new Corporation());
        Mockito.when(cardTypeService.findActive(cardTypeId)).thenReturn(new CardType());

        opsCardProductionRequestService.exportDetailToSFTP(1);
    }

    @TestConfiguration
    public static class classConfiguration {
        @Bean
        public OpsCardProductionRequestService opsCardProductionRequestService() {
            return new OpsCardProductionRequestServiceImpl();
        }
    }
}
