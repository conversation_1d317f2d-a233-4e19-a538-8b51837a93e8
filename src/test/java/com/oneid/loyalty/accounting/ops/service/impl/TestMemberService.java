//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.constant.EGender;
//import com.oneid.loyalty.accounting.ops.constant.EMemberStatus;
//import com.oneid.loyalty.accounting.ops.entity.WlMember;
//import com.oneid.loyalty.accounting.ops.entity.WlMemberProductAccount;
//import com.oneid.loyalty.accounting.ops.exception.BusinessException;
//import com.oneid.loyalty.accounting.ops.model.req.SearchMemberReq;
//import com.oneid.loyalty.accounting.ops.model.res.MemberRes;
//import com.oneid.loyalty.accounting.ops.repository.WlMemberRepository;
//import com.oneid.loyalty.accounting.ops.service.core.CorePartnerService;
//import com.oneid.loyalty.accounting.ops.service.core.CoreProductAccountService;
//import com.oneid.loyalty.accounting.ops.service.search.SpecificationBuilder;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.data.domain.Pageable;
//import org.springframework.data.domain.Sort;
//import org.springframework.data.jpa.domain.Specification;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.*;
//
//@RunWith(SpringRunner.class)
//public class TestMemberService {
//
//    @Autowired
//    private MemberService memberService;
//
//    @MockBean
//    private WlMemberRepository wlMemberRepository;
//
//    @MockBean
//    private CorePartnerService partnerService;
//
//    @MockBean
//    private CoreProductAccountService productAccountService;
//
//    private String accountNo = "****************";
//    private String csn = "1023488";
//    private String address = "123 Nguyen Thi Minh Khai";
//    private String partnerCode = "VINPEARL";
//    private String partnerName = "Vinpearl Nha Trang";
//    private String memberCode = "VP0000000220";
//    private String phoneNo = "**********";
//    private String fullName = "Cuong dola";
//    private String identifyType = "CMND";
//    private String identifyNo = "**********";
//    private String cardNo = "CARD12345";
//    private String dob = "********";
//    private String email = "<EMAIL>";
//    private Integer offset = 0;
//    private Integer limit = 20;
//    private Integer total = 100;
//    private Sort sort = Sort.by("memberCode");
//    private EMemberStatus status = EMemberStatus.ACTIVE;
//    private EGender gender = EGender.MALE;
//
//    private WlMember wlMember = new WlMember();
//    private List<WlMember> wlMembers = new ArrayList<>();
//    private Page<WlMember> wlMemberPage;
//
//    private WlMemberProductAccount wlMemberProductAccount = new WlMemberProductAccount();
//
//    private SearchMemberReq searchMemberReq = new SearchMemberReq();
//
//    OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, sort);
//
//    private MemberRes memberRes = new MemberRes();
//    private List<MemberRes> memberResList = new ArrayList<>();
//    private Page<MemberRes> memberResPage;
//
//    @Before
//    public void before() {
//        wlMember.setAccountNo(accountNo);
//        wlMember.setAddress(address);
//        wlMember.setCsn(csn);
//        wlMember.setDob(dob);
//        wlMember.setEmail(email);
//        wlMember.setFullName(fullName);
//        wlMember.setPartnerCode(partnerCode);
//        wlMember.setGender(gender);
//        wlMember.setIdentifyNo(identifyNo);
//        wlMember.setIdentifyType(identifyType);
//        wlMember.setPhoneNo(phoneNo);
//        wlMember.setStatus(status);
//        wlMember.setMemberCode(memberCode);
//
//        searchMemberReq.setCardNo(cardNo);
//        searchMemberReq.setFullName(fullName);
//        searchMemberReq.setIdentifyNo(identifyNo);
//        searchMemberReq.setIdentifyType(identifyType);
//        searchMemberReq.setMemberCode(memberCode);
//        searchMemberReq.setPartnerCode(partnerCode);
//        searchMemberReq.setPhoneNo(phoneNo);
//
//        wlMemberProductAccount.setMemberCode(memberCode);
//
//        wlMembers.add(wlMember);
//        wlMemberPage = new PageImpl<>(wlMembers, pageRequest, total);
//
//        SpecificationBuilder<WlMember> specification = new SpecificationBuilder<>();
//
//        Mockito.when(wlMemberRepository.findAll((Specification<WlMember>) Mockito.any(Object.class),
//                    (Pageable) Mockito.any(Object.class))).thenReturn(wlMemberPage);
//
//        Mockito.when(wlMemberRepository.findOneByMemberCode(memberCode)).
//                        thenReturn(Optional.of(wlMember));
//
//        Mockito.when(wlMemberRepository.findOneByMemberCode("")).
//                thenReturn(Optional.ofNullable(null));
//
//        Map<String, String> mapCodeToNamePartner = new HashMap<>();
//        mapCodeToNamePartner.put(partnerCode, partnerName);
//
//        Mockito.when(partnerService.getMapCodeToNamePartner()).thenReturn(mapCodeToNamePartner);
//        Mockito.when(productAccountService.findOne(Mockito.anyString())).thenReturn(wlMemberProductAccount);
//
//        memberRes = MemberRes.valueOf(wlMember);
//        memberRes.setPartnerName(partnerName);
//
//        memberResList.add(memberRes);
//
//
//        wlMembers.add(wlMember);
//        memberResPage = new PageImpl<MemberRes>(memberResList, pageRequest, 100);
//    }
//
//    @Test
//    public void getListUser() {
//        Page<MemberRes> exp = memberService.searchMembers(searchMemberReq, pageRequest);
//        Assert.assertEquals(exp, memberResPage);
//    }
//
//    @Test
//    public void getDetails() {
//        MemberRes exp = memberService.getDetails(memberCode);
//        Assert.assertEquals(exp, memberRes);
//    }
//
//    @Test(expected = BusinessException.class)
//    public void getDetailsNotFound() {
//        memberService.getDetails("");
//    }
//
//    @TestConfiguration
//    public static class ConfigurationForTest {
//        @Bean
//        public MemberService memberService() {
//            return new MemberServiceImpl();
//        }
//    }
//}
