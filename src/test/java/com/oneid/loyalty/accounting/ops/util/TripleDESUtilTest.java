package com.oneid.loyalty.accounting.ops.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.AbstractMap;
import java.util.Map;

class TripleDESUtilTest {
    private Map<String, Map.Entry<String, String>> opId2PIN2Encrypt = Map.of(
            "OP001", new AbstractMap.SimpleEntry<>("********", "Ntlq2uNBzL8="),
            "VMT001", new AbstractMap.SimpleEntry<>("********", "dGmAG/3BSDo="),
            "VMT002", new AbstractMap.SimpleEntry<>("********", "g9VTd8eKk98="),
            "VPL001", new AbstractMap.SimpleEntry<>("********", "G8oz3zHTNhI="),
            "VP0001", new AbstractMap.SimpleEntry<>("********", "Ro7UXHeHGas="),
            "VP006", new AbstractMap.SimpleEntry<>("********", "jE+EliA+gkk="),
            "OPNDC", new AbstractMap.SimpleEntry<>("********", "6A+hMliSFKM=")
    );

    @Test
    void encryptByGivenKey() {
        opId2PIN2Encrypt.forEach(
                (k, v) ->
                {
                    try {
                        Assertions.assertEquals(v.getValue(), TripleDESUtil.encryptByGivenKey(k, v.getKey()));
                    } catch (Exception e) {
                        e.printStackTrace();
                        Assertions.fail();
                    }
                });
    }

    @Test
    void decryptByGivenKey() {
        opId2PIN2Encrypt.forEach(
                (k, v) -> {
                    try {
                        Assertions.assertEquals(v.getKey(), TripleDESUtil.decryptByGivenKey(k, v.getValue()));
                    } catch (Exception e) {
                        e.printStackTrace();
                        Assertions.fail();
                    }
                }
        );
    }
}