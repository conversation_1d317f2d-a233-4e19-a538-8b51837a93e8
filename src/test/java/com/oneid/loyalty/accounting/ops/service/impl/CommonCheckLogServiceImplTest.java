package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.CheckLogSourceConfigPram;
import com.oneid.loyalty.accounting.ops.model.req.FilterCheckLogMemberReq;
import com.oneid.loyalty.accounting.ops.service.CommonCheckLogService;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.oneloyalty.common.constant.EEntityAuditTrail;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.DataAuditTrail;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.DataAuditTrailRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@TestPropertySource(properties = {"checklog.source.api-wrapper-value:13", "checklog.source.mobile.value:13",
        "checklog.source.mobile.default-store-codes:13"})
public class CommonCheckLogServiceImplTest {

    @Value("${checklog.source.api-wrapper-value}")
    public String SOURCE_API_WRAPPER_VALUE;

    @Value("${checklog.source.mobile.value}")
    public String SOURCE_MOBILE_VALUE;

    @Value("${checklog.source.mobile.default-store-codes}")
    public List<String> SOURCE_MOBILE_DEFAULT_STORE_CODES;

    @Autowired
    private CommonCheckLogService commonCheckLogService;

    @Autowired
    private CheckLogSourceConfigPram checkLogSourceConfigPram;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private DataAuditTrailRepository dataAuditTrailRepository;

    @Test
    public void filterCheckLogMember() {
        FilterCheckLogMemberReq req = FilterCheckLogMemberReq.builder()
                .timeFrom(123L)
                .timeTo(1235L)
                .build();
        DataAuditTrail auditTrail = new DataAuditTrail();

        Mockito.when(dataAuditTrailRepository.findByIdRefAndEntityRef(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                        .thenReturn(List.of(auditTrail));

        commonCheckLogService.filterCheckLogMember(1L, req);
    }

    @Test
    public void filterCheckLogCardMember() {
        DataAuditTrail entityCurrent = new DataAuditTrail();
        entityCurrent.setEntityRef(EEntityAuditTrail.MEMBER);
        entityCurrent.setIdRef("1");
        Mockito.when(dataAuditTrailRepository.findById(1L)).thenReturn(Optional.of(entityCurrent));
        commonCheckLogService.getDetailCheckLogMemberById(1L, 1L);
    }

    @Test
    public void filterCheckLogCardMember_notAuditMember() {
        DataAuditTrail entityCurrent = new DataAuditTrail();
        Mockito.when(dataAuditTrailRepository.findById(1L)).thenReturn(Optional.of(entityCurrent));
        try {
            commonCheckLogService.getDetailCheckLogMemberById(1L, 1L);
            Assert.fail("Check log not for member");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CHECKLOG_NOT_FOUND);
        }
    }

    @Test
    public void getDetailCheckLogCardMemberById() throws JsonProcessingException {
        DataAuditTrail entityCurrent = new DataAuditTrail();
        entityCurrent.setEntityRef(EEntityAuditTrail.MEMBER_CARD);
        entityCurrent.setIdRef("1");
        Map<String, String> payload = new HashMap<>();
        payload.put("card_no", "cardNo");
        entityCurrent.setPayload(payload);
        Mockito.when(dataAuditTrailRepository.findById(1L)).thenReturn(Optional.of(entityCurrent));
        commonCheckLogService.getDetailCheckLogCardMemberById(1L, "cardNo", 1L);
    }

    private Date convertTimeFrom(Long timeFrom) {
        return timeFrom != null ? DateTimes.toDate(timeFrom) : DateTimes.toDate(0L);
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public CommonCheckLogService commonCheckLogService() {
            return new CommonCheckLogServiceImpl();
        }

        @Bean
        public CheckLogSourceConfigPram checkLogSourceConfigPram() {
            return new CheckLogSourceConfigPram();
        }

        @Bean
        public ObjectMapper objectMapper() {
            return new ObjectMapper();
        }
    }
}
