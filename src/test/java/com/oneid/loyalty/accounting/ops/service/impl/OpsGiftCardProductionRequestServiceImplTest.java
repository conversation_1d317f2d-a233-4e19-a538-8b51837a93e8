package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.TestMakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.config.MakerCheckerConfigParam;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.dto.MakerCheckerChangeRequestResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateGiftCardRequestReq;
import com.oneid.loyalty.accounting.ops.service.OpsGiftCardProductionRequestService;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EGiftCardStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.entity.GiftCardBin;
import com.oneid.oneloyalty.common.entity.GiftCardRequest;
import com.oneid.oneloyalty.common.entity.GiftCardType;
import com.oneid.oneloyalty.common.entity.Program;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.Meta;
import com.oneid.oneloyalty.common.repository.GiftCardRequestRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardPolicyService;
import com.oneid.oneloyalty.common.service.ChainService;
import com.oneid.oneloyalty.common.service.CorporationService;
import com.oneid.oneloyalty.common.service.GiftCardBinService;
import com.oneid.oneloyalty.common.service.GiftCardTypeService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.SequenceService;
import com.oneid.oneloyalty.common.service.StoreService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RunWith(SpringRunner.class)
@Import(value = TestMakerCheckerConfigParam.class)
@SpringBootTest(properties = "spring.main.allow-bean-definition-overriding=true")
public class OpsGiftCardProductionRequestServiceImplTest {

    @Autowired
    private OpsGiftCardProductionRequestService opsGiftCardProductionRequestService;

    @MockBean
    private BusinessService businessService;

    @MockBean
    private ProgramService programService;

    @MockBean
    private StoreService storeService;

    @MockBean
    private GiftCardBinService giftCardBinService;

    @MockBean
    private GiftCardTypeService giftCardTypeService;

    @MockBean
    private CardPolicyService cardPolicyService;

    @MockBean
    private GiftCardRequestRepository giftCardRequestRepository;

    @MockBean
    private MakerCheckerFeignClient makerCheckerFeignClient;

    @Autowired
    private MakerCheckerConfigParam makerCheckerConfigParam;

    @MockBean
    private SequenceService sequenceService;

    @MockBean
    private CorporationService corporationService;

    @MockBean
    private ChainService chainService;

    private Business business;
    private Program program;
    private GiftCardType giftCardType;
    private CardPolicy cardPolicy;
    private Store store;
    private GiftCardBin giftCardBin;

    @MockBean
    private GiftCardRequest giftCardRequestMock;

    @Before
    public void before() {
        business = new Business();
        business.setId(1);
        business.setStatus(ECommonStatus.ACTIVE);

        program = new Program();
        program.setId(2);
        program.setStatus(ECommonStatus.ACTIVE);
        program.setBusinessId(business.getId());

        cardPolicy = new CardPolicy();
        cardPolicy.setId(4);
        cardPolicy.setStatus(ECommonStatus.ACTIVE);
        cardPolicy.setBusinessId(business.getId());
        cardPolicy.setPolicyType(ECardPolicyType.GIFT_CARD);

        giftCardType = new GiftCardType();
        giftCardType.setId(3);
        giftCardType.setStatus(ECommonStatus.ACTIVE);
        giftCardType.setCode("code");
        giftCardType.setProgramId(program.getId());
        giftCardType.setBusinessId(business.getId());
        giftCardType.setPolicyId(cardPolicy.getId());

        store = new Store();
        store.setId(5);
        store.setBusinessId(business.getId());
        store.setStatus(ECommonStatus.ACTIVE);

        giftCardBin = new GiftCardBin();
        giftCardBin.setId(8);
        giftCardBin.setStatus(ECommonStatus.ACTIVE);
        giftCardBin.setBusinessId(business.getId());
        giftCardBin.setProgramId(program.getId());
    }

    @Test
    public void createNewChangeRequest_success() {
        CreateGiftCardRequestReq req = CreateGiftCardRequestReq.builder()
                .businessId(business.getId())
                .programId(program.getId())
                .description("description")
                .giftCardBinId(giftCardBin.getId())
                .giftCardTypeId(giftCardType.getId())
                .generateQr(EBoolean.NO)
                .noOfCard(123)
                .initialGcStatus(EGiftCardStatus.ACTIVE)
                .serialSuffix("11")
                .storeId(store.getId())
                .build();

        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
        Mockito.when(storeService.findActive(store.getId())).thenReturn(store);
        Mockito.when(giftCardTypeService.findActive(giftCardType.getId())).thenReturn(giftCardType);
        Mockito.when(cardPolicyService.findActive(cardPolicy.getId())).thenReturn(cardPolicy);
        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(giftCardRequestMock);
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        response.setData(MakerCheckerChangeRequestResponse.builder().id(111L).build());
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        opsGiftCardProductionRequestService.createNewChangeRequest(req);

        Mockito.verify(businessService, Mockito.times(1)).findActive(business.getId());
        Mockito.verify(programService, Mockito.times(1)).findActive(program.getId());
        Mockito.verify(cardPolicyService, Mockito.times(1)).findActive(cardPolicy.getId());
        Mockito.verify(giftCardBinService, Mockito.times(1)).findActive(giftCardBin.getId());
        Mockito.verify(giftCardTypeService, Mockito.times(1)).findActive(giftCardType.getId());
        Mockito.verify(storeService, Mockito.times(1)).findActive(store.getId());
    }

    @Test
    public void createNewChangeRequest_badRequest() {
        CreateGiftCardRequestReq req = CreateGiftCardRequestReq.builder()
                .businessId(business.getId())
                .programId(program.getId())
                .description("description")
                .giftCardBinId(giftCardBin.getId())
                .giftCardTypeId(giftCardType.getId())
                .generateQr(EBoolean.NO)
                .noOfCard(123)
                .initialGcStatus(EGiftCardStatus.USED)
                .serialSuffix("11")
                .storeId(store.getId())
                .build();

        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
        Mockito.when(storeService.findActive(store.getId())).thenReturn(store);
        Mockito.when(giftCardTypeService.findActive(giftCardType.getId())).thenReturn(giftCardType);
        Mockito.when(cardPolicyService.findActive(cardPolicy.getId())).thenReturn(cardPolicy);
        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(giftCardRequestMock);
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        response.setData(MakerCheckerChangeRequestResponse.builder().id(111L).build());
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.createNewChangeRequest(req);
            Assert.fail("bad request here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BAD_REQUEST);
        }
    }

    @Test
    public void createNewChangeRequest_storeAndProgramNotMatch() {
        CreateGiftCardRequestReq req = CreateGiftCardRequestReq.builder()
                .businessId(business.getId())
                .programId(program.getId())
                .description("description")
                .giftCardBinId(giftCardBin.getId())
                .giftCardTypeId(giftCardType.getId())
                .generateQr(EBoolean.NO)
                .noOfCard(123)
                .initialGcStatus(EGiftCardStatus.ACTIVE)
                .serialSuffix("11")
                .storeId(store.getId())
                .build();

        store.setBusinessId(-business.getId());
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
        Mockito.when(storeService.findActive(store.getId())).thenReturn(store);
        Mockito.when(giftCardTypeService.findActive(giftCardType.getId())).thenReturn(giftCardType);
        Mockito.when(cardPolicyService.findActive(cardPolicy.getId())).thenReturn(cardPolicy);
        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(giftCardRequestMock);
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        response.setData(MakerCheckerChangeRequestResponse.builder().id(111L).build());
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.createNewChangeRequest(req);
            Assert.fail("Store/program not match with business");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BAD_REQUEST);
        }
    }

    @Test
    public void createNewChangeRequest_cardPolicyNotMatch() {
        CreateGiftCardRequestReq req = CreateGiftCardRequestReq.builder()
                .businessId(business.getId())
                .programId(program.getId())
                .description("description")
                .giftCardBinId(giftCardBin.getId())
                .giftCardTypeId(giftCardType.getId())
                .generateQr(EBoolean.NO)
                .noOfCard(123)
                .initialGcStatus(EGiftCardStatus.ACTIVE)
                .serialSuffix("11")
                .storeId(store.getId())
                .build();

        cardPolicy.setPolicyType(ECardPolicyType.MEMBER_CARD);
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
        Mockito.when(storeService.findActive(store.getId())).thenReturn(store);
        Mockito.when(giftCardTypeService.findActive(giftCardType.getId())).thenReturn(giftCardType);
        Mockito.when(cardPolicyService.findActive(cardPolicy.getId())).thenReturn(cardPolicy);
        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(giftCardRequestMock);
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        response.setData(MakerCheckerChangeRequestResponse.builder().id(111L).build());
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.createNewChangeRequest(req);
            Assert.fail("Card policy not match");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_MATCHED);
        }
    }

    @Test
    public void createNewChangeRequest_overMaxCard() {
        CreateGiftCardRequestReq req = CreateGiftCardRequestReq.builder()
                .businessId(business.getId())
                .programId(program.getId())
                .description("description")
                .giftCardBinId(giftCardBin.getId())
                .giftCardTypeId(giftCardType.getId())
                .generateQr(EBoolean.NO)
                .noOfCard(123)
                .initialGcStatus(EGiftCardStatus.ACTIVE)
                .serialSuffix("11")
                .storeId(store.getId())
                .build();

        cardPolicy.setMaxCardCpr(-1);
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(programService.findActive(program.getId())).thenReturn(program);
        Mockito.when(storeService.findActive(store.getId())).thenReturn(store);
        Mockito.when(giftCardTypeService.findActive(giftCardType.getId())).thenReturn(giftCardType);
        Mockito.when(cardPolicyService.findActive(cardPolicy.getId())).thenReturn(cardPolicy);
        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(giftCardRequestMock);
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        response.setData(MakerCheckerChangeRequestResponse.builder().id(111L).build());
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.createNewChangeRequest(req);
            Assert.fail("CPR over max card");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CPR_OVER_MAX_CARD);
        }
    }

    @Test
    public void searchInReview() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(2);

        APIResponse<ChangeRequestPageFeignRes> response = new APIResponse<>();
        response.setMeta(Meta.success());

        ChangeRequestPageFeignRes data = new ChangeRequestPageFeignRes();

        ChangeRequestPageFeignRes.ChangeRecordFeginRes record = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
        record.setChangeRequestId(1);
        record.setObjectId(String.valueOf(giftCardRequestMock.getId()));
        data.setPage(1);
        data.setPageSize(1);
        data.setRecords(Collections.singletonList(record));
        data.setTotalRecordCount(1);
        response.setData(data);

        Mockito.when(giftCardTypeService.findByIdIn(Mockito.any())).thenReturn(List.of(giftCardType));
        Mockito.when(giftCardBinService.findByIdIn(Mockito.any())).thenReturn(List.of(giftCardBin));
        Mockito.when(cardPolicyService.findByIdIn(Mockito.any())).thenReturn(List.of(cardPolicy));
        Mockito.when(programService.findByIdIn(Mockito.any())).thenReturn(List.of(program));
        Mockito.when(storeService.findByIdIn(Mockito.any())).thenReturn(List.of(store));
        Mockito.when(giftCardRequestRepository.findAllById(Mockito.any())).thenReturn(List.of(giftCardRequestMock));

        Mockito.when(giftCardRequestMock.getGcTypeId()).thenReturn(giftCardType.getId());
        Mockito.when(giftCardRequestMock.getGcBinId()).thenReturn(giftCardBin.getId());
        Mockito.when(giftCardRequestMock.getProgramId()).thenReturn(program.getId());
        Mockito.when(giftCardRequestMock.getStoreId()).thenReturn(store.getId());
        Mockito.when(giftCardRequestMock.getGcPolicyId()).thenReturn(cardPolicy.getId());

        Pageable pageRequest = PageRequest.of(1, 1);

        Mockito.when(makerCheckerFeignClient
                        .getChangeRequests(Mockito.any(), Mockito.any(), Mockito.any(),
                                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(response);

        opsGiftCardProductionRequestService.searchInReview(EApprovalStatus.PENDING, 123, 1234, pageRequest);
    }

    @Test
    public void searchInReview_pageEmpty() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(2);

        APIResponse<ChangeRequestPageFeignRes> response = new APIResponse<>();
        response.setMeta(Meta.success());

        ChangeRequestPageFeignRes data = new ChangeRequestPageFeignRes();

        ChangeRequestPageFeignRes.ChangeRecordFeginRes record = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
        data.setPage(1);
        data.setPageSize(1);
        data.setRecords(Collections.emptyList());
        data.setTotalRecordCount(0);
        response.setData(data);

        Pageable pageRequest = PageRequest.of(1, 1);

        Mockito.when(makerCheckerFeignClient
                        .getChangeRequests(Mockito.any(), Mockito.any(), Mockito.any(),
                                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(response);

        opsGiftCardProductionRequestService.searchInReview(EApprovalStatus.PENDING, 123, 1234, pageRequest);
    }

    @Test
    public void detailInReview() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(2);

        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> response = new APIResponse<>();
        response.setMeta(Meta.success());

        ChangeRequestPageFeignRes.ChangeRecordFeginRes record = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
        record.setChangeRequestId(1);
        record.setObjectId(String.valueOf(giftCardRequestMock.getId()));
        response.setData(record);

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));

        Mockito.when(giftCardTypeService.findByIdIn(Mockito.any())).thenReturn(List.of(giftCardType));
        Mockito.when(giftCardBinService.findByIdIn(Mockito.any())).thenReturn(List.of(giftCardBin));
        Mockito.when(cardPolicyService.findByIdIn(Mockito.any())).thenReturn(List.of(cardPolicy));
        Mockito.when(programService.findByIdIn(Mockito.any())).thenReturn(List.of(program));
        Mockito.when(storeService.findByIdIn(Mockito.any())).thenReturn(List.of(store));
        Mockito.when(giftCardRequestRepository.findAllById(Mockito.any())).thenReturn(List.of(giftCardRequestMock));

        Mockito.when(giftCardRequestMock.getGcTypeId()).thenReturn(giftCardType.getId());
        Mockito.when(giftCardRequestMock.getGcBinId()).thenReturn(giftCardBin.getId());
        Mockito.when(giftCardRequestMock.getProgramId()).thenReturn(program.getId());
        Mockito.when(giftCardRequestMock.getStoreId()).thenReturn(store.getId());
        Mockito.when(giftCardRequestMock.getGcPolicyId()).thenReturn(cardPolicy.getId());

        Mockito.when(makerCheckerFeignClient.getChangeRequestById(Mockito.any())).thenReturn(response);

        opsGiftCardProductionRequestService.getDetailInReview(1);
    }


    @Test
    public void detailInReview_notFound() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(2);

        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> response = new APIResponse<>();
        response.setMeta(Meta.success());

        ChangeRequestPageFeignRes.ChangeRecordFeginRes record = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
        record.setChangeRequestId(1);
        record.setObjectId(String.valueOf(giftCardRequestMock.getId()));
        response.setData(record);

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.ofNullable(null));
        Mockito.when(makerCheckerFeignClient.getChangeRequestById(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.getDetailInReview(1);
            Assert.fail("gift card production request not found");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND);
        }
    }

    /*@Test
    public void searchAvailable() {
        GiftCardSearchReq req = GiftCardSearchReq.builder()
                .businessId(business.getId())
                .corporationId(1)
                .chainId(1)
                .cprBatchNo(1L)
                .storeId(1)
                .generationStatus(EGiftCardIndicator.NO)
                .giftCardStatus(EGiftCardStatus.ACTIVE)
                .approvalStatus(EApprovalStatus.PENDING)
                .build();
        Pageable pageRequest = PageRequest.of(1,1);

        Page<Object[]> page = Mockito.mock(Page.class);

        Mockito.when(giftCardRequestMock.getGcTypeId()).thenReturn(giftCardType.getId());
        Mockito.when(giftCardRequestMock.getGcBinId()).thenReturn(giftCardBin.getId());
        Mockito.when(giftCardRequestMock.getProgramId()).thenReturn(program.getId());
        Mockito.when(giftCardRequestMock.getStoreId()).thenReturn(store.getId());
        Mockito.when(giftCardRequestMock.getGcPolicyId()).thenReturn(cardPolicy.getId());
        Mockito.when(giftCardRequestRepository.filter(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                        .thenReturn(page);

        Object[] objects = new Object[]{giftCardRequestMock, null, null, null, null, null};

        Mockito.when(page.getContent()).thenReturn(Collections.singletonList(objects));

        opsGiftCardProductionRequestService.searchAvailable(req, pageRequest);
    }*/

    /*@Test
    public void detailAvailable() {
        Mockito.when(giftCardRequestMock.getGcTypeId()).thenReturn(giftCardType.getId());
        Mockito.when(giftCardRequestMock.getGcBinId()).thenReturn(giftCardBin.getId());
        Mockito.when(giftCardRequestMock.getProgramId()).thenReturn(program.getId());
        Mockito.when(giftCardRequestMock.getStoreId()).thenReturn(store.getId());
        Mockito.when(giftCardRequestMock.getGcPolicyId()).thenReturn(cardPolicy.getId());
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));
        opsGiftCardProductionRequestService.getDetailAvailable(giftCardRequestMock.getId());
    }*/

    @Test
    public void detailAvailable_notFound() {
        Mockito.when(giftCardRequestMock.getGcTypeId()).thenReturn(giftCardType.getId());
        Mockito.when(giftCardRequestMock.getGcBinId()).thenReturn(giftCardBin.getId());
        Mockito.when(giftCardRequestMock.getProgramId()).thenReturn(program.getId());
        Mockito.when(giftCardRequestMock.getStoreId()).thenReturn(store.getId());
        Mockito.when(giftCardRequestMock.getGcPolicyId()).thenReturn(cardPolicy.getId());
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.ofNullable(null));
        try {
            opsGiftCardProductionRequestService.getDetailAvailable(giftCardRequestMock.getId());
            Assert.fail("gift card production request not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND);
        }
    }

    @Test
    public void update() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestMock.getInitGcStatus()).thenReturn(EGiftCardStatus.PENDING);
        Mockito.when(giftCardRequestMock.getApprovalStatus()).thenReturn(EApprovalStatus.APPROVED);
        Mockito.when(giftCardRequestMock.getRequestStatus()).thenReturn(ECommonStatus.ACTIVE);

        GiftCardRequest newGiftCardRequest = Mockito.mock(GiftCardRequest.class);
        Mockito.when(newGiftCardRequest.getId()).thenReturn(23);

        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(newGiftCardRequest);
        UpdateGiftCardRequestReq req = UpdateGiftCardRequestReq.builder()
                .status(EGiftCardStatus.ACTIVE)
                .build();

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        MakerCheckerChangeRequestResponse data = MakerCheckerChangeRequestResponse
                .builder()
                .id(1L)
                .objectId(String.valueOf(newGiftCardRequest.getId()))
                .build();

        response.setMeta(Meta.success());
        response.setData(data);
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        opsGiftCardProductionRequestService.update(giftCardRequestMock.getId(), req);
    }

    @Test
    public void update_badRequest() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestMock.getInitGcStatus()).thenReturn(EGiftCardStatus.PENDING);
        Mockito.when(giftCardRequestMock.getApprovalStatus()).thenReturn(EApprovalStatus.APPROVED);
        Mockito.when(giftCardRequestMock.getRequestStatus()).thenReturn(ECommonStatus.ACTIVE);

        GiftCardRequest newGiftCardRequest = Mockito.mock(GiftCardRequest.class);
        Mockito.when(newGiftCardRequest.getId()).thenReturn(23);

        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(newGiftCardRequest);
        UpdateGiftCardRequestReq req = UpdateGiftCardRequestReq.builder()
                .status(EGiftCardStatus.PENDING)
                .build();

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        MakerCheckerChangeRequestResponse data = MakerCheckerChangeRequestResponse
                .builder()
                .id(1L)
                .objectId(String.valueOf(newGiftCardRequest.getId()))
                .build();

        response.setMeta(Meta.success());
        response.setData(data);
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.update(giftCardRequestMock.getId(), req);
            Assert.fail("Bad request here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.BAD_REQUEST);
        }
    }

    @Test
    public void update_notFound() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestMock.getInitGcStatus()).thenReturn(EGiftCardStatus.PENDING);
        Mockito.when(giftCardRequestMock.getApprovalStatus()).thenReturn(EApprovalStatus.APPROVED);
        Mockito.when(giftCardRequestMock.getStatus()).thenReturn(ECommonStatus.ACTIVE);

        GiftCardRequest newGiftCardRequest = Mockito.mock(GiftCardRequest.class);
        Mockito.when(newGiftCardRequest.getId()).thenReturn(23);

        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(newGiftCardRequest);
        UpdateGiftCardRequestReq req = UpdateGiftCardRequestReq.builder()
                .status(EGiftCardStatus.ACTIVE)
                .build();

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.ofNullable(null));

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        MakerCheckerChangeRequestResponse data = MakerCheckerChangeRequestResponse
                .builder()
                .id(1L)
                .objectId(String.valueOf(newGiftCardRequest.getId()))
                .build();

        response.setMeta(Meta.success());
        response.setData(data);
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.update(giftCardRequestMock.getId(), req);
            Assert.fail("Gift card request not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND);
        }
    }

    @Test
    public void update_CannotBeEdit() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestMock.getInitGcStatus()).thenReturn(EGiftCardStatus.PENDING);
        Mockito.when(giftCardRequestMock.getApprovalStatus()).thenReturn(EApprovalStatus.REJECTED);
        Mockito.when(giftCardRequestMock.getRequestStatus()).thenReturn(ECommonStatus.ACTIVE);

        GiftCardRequest newGiftCardRequest = Mockito.mock(GiftCardRequest.class);
        Mockito.when(newGiftCardRequest.getId()).thenReturn(23);

        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(newGiftCardRequest);
        UpdateGiftCardRequestReq req = UpdateGiftCardRequestReq.builder()
                .status(EGiftCardStatus.ACTIVE)
                .build();

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        MakerCheckerChangeRequestResponse data = MakerCheckerChangeRequestResponse
                .builder()
                .id(1L)
                .objectId(String.valueOf(newGiftCardRequest.getId()))
                .build();

        response.setMeta(Meta.success());
        response.setData(data);
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.update(giftCardRequestMock.getId(), req);
            Assert.fail("Gift card request can not be edited here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_CAN_NOT_BE_EDITED);
        }
    }

    @Test
    public void update_GIFT_CARD_PRODUCTION_REQUEST_IS_ALREADY_REQUESTED() {
        Mockito.when(giftCardRequestMock.getId()).thenReturn(1);
        Mockito.when(giftCardRequestMock.getInitGcStatus()).thenReturn(EGiftCardStatus.PENDING);
        Mockito.when(giftCardRequestMock.getApprovalStatus()).thenReturn(EApprovalStatus.PENDING);
        Mockito.when(giftCardRequestMock.getRequestStatus()).thenReturn(ECommonStatus.ACTIVE);

        GiftCardRequest newGiftCardRequest = Mockito.mock(GiftCardRequest.class);
        Mockito.when(newGiftCardRequest.getId()).thenReturn(23);

        Mockito.when(giftCardRequestRepository.save(Mockito.any())).thenReturn(newGiftCardRequest);
        UpdateGiftCardRequestReq req = UpdateGiftCardRequestReq.builder()
                .status(EGiftCardStatus.ACTIVE)
                .build();

        Mockito.when(giftCardRequestRepository.findById(giftCardRequestMock.getId())).thenReturn(Optional.of(giftCardRequestMock));

        APIResponse<MakerCheckerChangeRequestResponse> response = new APIResponse<>();
        MakerCheckerChangeRequestResponse data = MakerCheckerChangeRequestResponse
                .builder()
                .id(1L)
                .objectId(String.valueOf(newGiftCardRequest.getId()))
                .build();

        response.setMeta(Meta.success());
        response.setData(data);
        Mockito.when(makerCheckerFeignClient.changes(Mockito.any())).thenReturn(response);

        try {
            opsGiftCardProductionRequestService.update(giftCardRequestMock.getId(), req);
            Assert.fail("Gift card request is already requested here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.GIFT_CARD_PRODUCTION_REQUEST_IS_ALREADY_REQUESTED);
        }
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsGiftCardProductionRequestService opsGiftCardProductionRequestService() {
            return new OpsGiftCardProductionRequestServiceImpl();
        }
    }
}
