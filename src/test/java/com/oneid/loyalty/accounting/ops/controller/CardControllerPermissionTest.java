package com.oneid.loyalty.accounting.ops.controller;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.RequestPostProcessor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.CardController;
import com.oneid.loyalty.accounting.ops.model.req.AddCardReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateCardReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardService;
import com.oneid.loyalty.accounting.ops.service.OpsCardTmpService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;

@Import(CardController.class)
public class CardControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    OpsCardService opsCardService;

    @MockBean
    OpsCardTmpService opsCardTmpService;
    
    @After
    public void teardown() {
        defaultTeardown();
        
        Mockito.reset(opsCardService);
        Mockito.reset(opsCardTmpService);
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void filterCardMember_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Page mockPage = Mockito.mock(Page.class);
        
        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);
        
        String mockBusinessCode = "BC";
        String mockProgramCode = "PC";
        String mockstoreCode = "SC";
        
        Mockito.when(opsCardService.filter(null, null, mockBusinessCode, mockProgramCode, mockstoreCode, null, 0, 20))
        .thenReturn(mockPage);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/cards")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_code", mockBusinessCode)
                .queryParam("program_code", mockProgramCode)
                .queryParam("store_code", mockstoreCode);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void filterCardMember_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        String mockBusinessCode = "BC";
        String mockProgramCode = "PC";
        String mockstoreCode = "SC";
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/cards")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_code", mockBusinessCode)
                .queryParam("program_code", mockProgramCode)
                .queryParam("store_code", mockstoreCode);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsCardService, times(0)).filter(null, null, mockBusinessCode, mockProgramCode, mockstoreCode, null, 0, 20);
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void addCard_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.CREATE))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        AddCardReq req = new AddCardReq();
        
        req.setBusinessCode("BC");
        req.setProgramCode("PC");
        req.setMemberCode("MC");
        req.setCardTypeId(Integer.MAX_VALUE);
        req.setStoreCode("SC");
        req.setCardNo("8888101025791059");
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/cards/add-card")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(1)).addCard((OPSAuthenticatedPrincipal) authentication.getPrincipal(), req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void addCard_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        AddCardReq req = new AddCardReq();
        
        req.setBusinessCode("BC");
        req.setProgramCode("PC");
        req.setMemberCode("MC");
        req.setCardTypeId(Integer.MAX_VALUE);
        req.setStoreCode("SC");
        req.setCardNo("8888101025791059");
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/cards/add-card")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(0)).addCard((OPSAuthenticatedPrincipal) authentication.getPrincipal(), req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getCard_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        String mockBusinessCode = "BC";
        String mockProgramCode = "PC";
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/cards/{id}", Integer.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_code", mockBusinessCode)
                .queryParam("program_code", mockProgramCode);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(1)).getDetail(Integer.MAX_VALUE, mockBusinessCode, mockProgramCode);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void getCard_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        String mockBusinessCode = "BC";
        String mockProgramCode = "PC";
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/cards/{id}", Integer.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_code", mockBusinessCode)
                .queryParam("program_code", mockProgramCode);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(0)).getDetail(Integer.MAX_VALUE, mockBusinessCode, mockProgramCode);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void updateCardById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.EDIT))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        UpdateCardReq req = new UpdateCardReq();
        
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/cards/{id}", Integer.MAX_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(1)).update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), Integer.MAX_VALUE, null, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void updateCardById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        UpdateCardReq req = new UpdateCardReq();
        
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/cards/{id}", Integer.MAX_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(0)).update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), Integer.MAX_VALUE, null, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void updateCard_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_CARD))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.EDIT))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        String mockCardNo = "8888101025791059";
        
        UpdateCardReq req = new UpdateCardReq();
        
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/cards/card_no/{id}", mockCardNo)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(1)).update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), null, mockCardNo, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void updateCard_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        String mockCardNo = "8888101025791059";
        
        UpdateCardReq req = new UpdateCardReq();
        
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/cards/card_no/{id}", mockCardNo)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsCardService, times(0)).update((OPSAuthenticatedPrincipal) authentication.getPrincipal(), null, mockCardNo, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
}
