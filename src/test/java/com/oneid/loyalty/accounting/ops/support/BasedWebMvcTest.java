package com.oneid.loyalty.accounting.ops.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.config.AppConfig;
import com.oneid.loyalty.accounting.ops.config.WebConfig;
import com.oneid.loyalty.accounting.ops.controller.advice.ExceptionHandlerAdvice;
import com.oneid.loyalty.accounting.ops.service.OpsTransactionService;
import com.oneid.loyalty.accounting.ops.setting.SFTPSetting;
import com.oneid.loyalty.accounting.ops.support.data.databind.OffsetSetting;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.support.web.security.oauth2.core.OPSAuthenticatedPrincipal;
import com.oneid.oneloyalty.common.support.ResponseSupport;
import lombok.Getter;
import org.apache.http.client.HttpClient;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ActiveProfiles("test")
@WebMvcTest(useDefaultFilters = false, excludeAutoConfiguration = SecurityAutoConfiguration.class)
@ContextConfiguration(classes = {AppConfig.class, WebConfig.class, AnnotationAwareAspectJAutoProxyCreator.class, ExceptionHandlerAdvice.class})
@ComponentScan(basePackageClasses = {ResponseSupport.class})
@EnableConfigurationProperties(value = { OffsetSetting.class })
@Getter
public abstract class BasedWebMvcTest {
    public static final Long DEFAULT_PLATFORM_USERID = Long.MAX_VALUE;
    public static final String DEFAULT_PLATFORM_USERNAME = "dev-unit-test";
    public static final String DEFAULT_PLATFORM_USER_EMAIL = "<EMAIL>";

    @MockBean
    protected HttpClient httpClient;

    @MockBean
    protected Authentication authentication;

    @MockBean
    protected OpsTransactionService opsTransactionService;


    @MockBean(name = "cprsSftpSetting")
    private SFTPSetting cprsSftpSetting;

    @Autowired
    private ObjectMapper objectMapper;

    public void defaultStartUp() throws Exception {
        OPSAuthenticatedPrincipal mockAuthenticatedPrincipal = getDefaultMockPrincipal();

        when(authentication.getPrincipal())
                .thenReturn(mockAuthenticatedPrincipal);

        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    public void defaultTeardown() {
        Mockito.reset(authentication);
        Mockito.reset(opsTransactionService);
        SecurityContextHolder.clearContext();
    }

    public void postInit() {
    }

    public void postTeardown() {
    }

    protected OPSAuthenticatedPrincipal getDefaultMockPrincipal() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        OPSAuthenticatedPrincipal principal = new OPSAuthenticatedPrincipal();

        principal.setId(DEFAULT_PLATFORM_USERID);
        principal.setUserEmail(DEFAULT_PLATFORM_USER_EMAIL);
        principal.setUserName(DEFAULT_PLATFORM_USERNAME);
        principal.setPermissions(permissions);

        return principal;
    }

    protected void mockOPSAuthenticatedPrincipal(Map<AccessRole, Long> permissions) {
        OPSAuthenticatedPrincipal principal = new OPSAuthenticatedPrincipal();

        principal.setId(DEFAULT_PLATFORM_USERID);
        principal.setUserEmail(DEFAULT_PLATFORM_USER_EMAIL);
        principal.setUserName(DEFAULT_PLATFORM_USERNAME);
        principal.setPermissions(permissions);

        when(authentication.getPrincipal())
                .thenReturn(principal);

        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    protected final String contentToString(Object request) throws JsonProcessingException {
        String result =  objectMapper.writeValueAsString(request);
        return result;
    }
}
