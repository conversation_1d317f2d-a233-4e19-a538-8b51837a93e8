package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.TierPolicyController;
import com.oneid.loyalty.accounting.ops.model.dto.ActionOnCounterDto;
import com.oneid.loyalty.accounting.ops.model.req.CreateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterTierPolicyReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateTierPolicyReq;
import com.oneid.loyalty.accounting.ops.service.OpsTierPolicyService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.DateTimes;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(TierPolicyController.class)
public class TierPolicyControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsTierPolicyService opsTierPolicyService;

    private CreateTierPolicyReq req;

    @Before
    public void before() {
        req = new CreateTierPolicyReq();
        req.setStatus(ECommonStatus.ACTIVE);
        req.setName("Test TierPolicy A");
        req.setCode("TEST");
        req.setProgramId(1);
        req.setBusinessId(1);
        req.setCounterIds(List.of(1));
        req.setPeriodValue(12);
        req.setEventQualificationPolicy(Boolean.TRUE);
        req.setExpirations(List.of("Jun-12"));
        req.setQualificationEventCodes(List.of("321"));
        req.setStartDate(new Date(new Date().getTime() + 10000));
        req.setEndDate(new Date(req.getStartDate().getTime() + DateTimes.DAY_IN_MILLIS * 1000));
    }

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void requestCreatingTierPolicyRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        before();

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tier-policies/requests/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(0)).create(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);
        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestCreatingTierPolicyRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER_POLICY, AccessPermission.CREATE.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tier-policies/requests/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        ArgumentCaptor<CreateTierPolicyReq> reqCaptor = ArgumentCaptor.forClass(CreateTierPolicyReq.class);

        Mockito.verify(opsTierPolicyService, times(1)).create(reqCaptor.capture());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(reqCaptor.getValue());

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingTierPolicyRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        UpdateTierPolicyReq req = new UpdateTierPolicyReq();
        req.setStatus(ECommonStatus.ACTIVE);
        req.setName("Test TierPolicy A");
        req.setPeriodValue(12);
        req.setCounters(List.of(ActionOnCounterDto.builder().id(1).build()));
        req.setExpirations(List.of("Jun-11"));
        req.setEventQualificationPolicy(Boolean.TRUE);
        req.setQualificationEventCodes(List.of("1"));

        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/tier-policies/requests/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(0)).update(any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingTierPolicyRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER_POLICY, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        UpdateTierPolicyReq req = new UpdateTierPolicyReq();
        req.setStatus(ECommonStatus.ACTIVE);
        req.setName("Test TierPolicy A");
        req.setPeriodValue(12);
        req.setCounters(List.of(ActionOnCounterDto.builder().id(1).build()));
        req.setExpirations(List.of("Jun-11"));
        req.setEventQualificationPolicy(Boolean.TRUE);
        req.setQualificationEventCodes(List.of("1"));

        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/tier-policies/requests/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        ArgumentCaptor<UpdateTierPolicyReq> reqCaptor = ArgumentCaptor.forClass(UpdateTierPolicyReq.class);
        ArgumentCaptor<Integer> requestIdCaptor = ArgumentCaptor.forClass(Integer.class);

        Mockito.verify(opsTierPolicyService, times(1)).update(requestIdCaptor.capture(), reqCaptor.capture());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(requestIdCaptor.getValue());
        assertNotNull(reqCaptor.getValue());

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getEditTierPolicyRequestSetting_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(0)).getInReviewDetail(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getEditTierPolicyRequestSetting_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER_POLICY, AccessPermission.VIEW.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(1)).getInReviewDetail(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableTierPolicyRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(0)).getAvailableDetail(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableTierPolicyRequestById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER_POLICY))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(1)).getAvailableDetail(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }


    @Test
    public void getInReviewTierPolicyRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(0))
                .getInReviews(null, null, null, null, null, null, null, 0, 20);

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getInReviewTierPolicyRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER_POLICY))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;

        Mockito.when(opsTierPolicyService.getInReviews(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("approval_status", "A")
                .queryParam("from_date", String.valueOf(1))
                .queryParam("to_date", String.valueOf(2));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableTierPolicyRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString())
                .queryParam("approval_status", "A")
                .queryParam("program_id", String.valueOf(1))
                .queryParam("code", "123")
                .queryParam("status", "A")
                .queryParam("offset", "1")
                .queryParam("limit", "1");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        FilterTierPolicyReq req = FilterTierPolicyReq.builder()
                .businessId(1)
//                .approvalStatus(EApprovalStatus.PENDING)
                .build();
        Mockito.verify(opsTierPolicyService, times(0)).getAvailable(req, new OffsetBasedPageRequest(0, 20, null));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableTierPolicyRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER_POLICY))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Mockito.when(opsTierPolicyService.getAvailable(Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);

        Integer mockBusinessId = 1;

        FilterTierPolicyReq req = FilterTierPolicyReq.builder()
                .businessId(1)
//                .approvalStatus(EApprovalStatus.PENDING)
                .programId(1)
                .code("CC")
                .status(ECommonStatus.ACTIVE)
                .build();

        Mockito.verify(opsTierPolicyService, times(0)).getAvailable(req, new OffsetBasedPageRequest(0, 20, null));

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString())
                .queryParam("approval_status", "A")
                .queryParam("program_id", String.valueOf(1))
                .queryParam("code", "123")
                .queryParam("status", "A")
                .queryParam("offset", "1")
                .queryParam("limit", "1");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getCounterAvailable() throws Exception {
        Map<AccessRole, Long> permissions = Collections.emptyMap();

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/counter-available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", "1")
                .queryParam("period", "MONTHLY");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(1)).getCounterAvailable(Mockito.any(), Mockito.any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getTierByRequestId() throws Exception {
        Map<AccessRole, Long> permissions = Collections.emptyMap();

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tier-policies/{tier_id}/tiers", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierPolicyService, times(1)).getTiersByRequestId(Mockito.any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
}
