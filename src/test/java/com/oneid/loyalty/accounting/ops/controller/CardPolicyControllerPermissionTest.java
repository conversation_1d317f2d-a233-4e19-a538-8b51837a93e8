package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.EOpsCardExpireUnit;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.CardPolicyController;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardPolicyService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERetensionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.oneid.oneloyalty.common.constant.ECardPolicyTrigger.AFTER_ACTIVE;
import static com.oneid.oneloyalty.common.constant.ECardPolicyTrigger.IMMEDIATELY;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(CardPolicyController.class)
public class CardPolicyControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsCardPolicyService opsCardPolicyService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void create_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CARD_POLICY))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CREATE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName("123");
        req.setDescription("description");
        req.setBusinessId(12800);
        req.setLength(16);
        req.setMaxCardPerCPR(1);
        req.setCardExpirationType(EOpsCardExpireUnit.DAY);
        req.setCardExpirationTrigger(IMMEDIATELY);
        req.setCardRetentionTrigger(AFTER_ACTIVE);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationValue(99L);
        req.setCardRetentionValue(10L);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setType(ECardPolicyType.GIFT_CARD);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-policy")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void create_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName("123");
        req.setDescription("description");
        req.setBusinessId(12800);
        req.setLength(16);
        req.setMaxCardPerCPR(1);
        req.setCardExpirationType(EOpsCardExpireUnit.DAY);
        req.setCardExpirationTrigger(IMMEDIATELY);
        req.setCardRetentionTrigger(AFTER_ACTIVE);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationValue(99L);
        req.setCardRetentionValue(10L);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setType(ECardPolicyType.GIFT_CARD);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/card-policy")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void view_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CARD_POLICY))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-policy/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void view_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-policy/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void filter_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CARD_POLICY))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        Mockito.when(opsCardPolicyService.searchPage(Mockito.any(), Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-policy")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", "12800")
                .queryParam("status", "A");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void filter_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(opsCardPolicyService.searchPage(Mockito.any(), Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/card-policy")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", "12800")
                .queryParam("status", "A");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void update_success() throws Exception {

        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CARD_POLICY))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.EDIT))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setName("123");
        req.setDescription("description");

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/card-policy/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void update_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());


        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setName("123");
        req.setDescription("description");

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/card-policy/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

}
