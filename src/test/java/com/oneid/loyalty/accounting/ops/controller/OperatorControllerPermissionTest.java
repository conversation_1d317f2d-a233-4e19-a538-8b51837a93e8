package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.OperatorController;
import com.oneid.loyalty.accounting.ops.model.req.CreateOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.PayloadOperatorReq;
import com.oneid.loyalty.accounting.ops.model.req.RejectOperatorRequest;
import com.oneid.loyalty.accounting.ops.model.req.SearchOperatorReq;
import com.oneid.loyalty.accounting.ops.service.OpsOperatorService;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import lombok.SneakyThrows;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(OperatorController.class)
public class OperatorControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsOperatorService operatorService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @SneakyThrows
    @Test
    public void getApproveCreate_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.OPERATOR))
                .collect(Collectors.toMap(key -> key, key -> (Long) Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.CREATE))
                        .mapToLong(AccessPermission::getCode).sum()));
        permissions.putAll(Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.MAKER_ROLE))
                        .mapToLong(AccessPermission::getCode).sum()))
        );

        mockOPSAuthenticatedPrincipal(permissions);

        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();

        Mockito.when(operatorService.getApprove(req)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/operators")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveCreate_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();

        Mockito.when(operatorService.getApprove(req)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/operators")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveUpdate_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.OPERATOR))
                .collect(Collectors.toMap(key -> key, key -> (Long) Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.EDIT))
                        .mapToLong(AccessPermission::getCode).sum()));
        permissions.putAll(Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.MAKER_ROLE))
                        .mapToLong(AccessPermission::getCode).sum()))
        );

        mockOPSAuthenticatedPrincipal(permissions);

        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();

        Mockito.when(operatorService.getApprove(req)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/operators/update")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveUpdate_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        CreateOperatorReq req = CreateOperatorReq.builder()
                .operatorId("operatorId")
                .pinCode("1234")
                .businessId(12800)
                .payload(
                        PayloadOperatorReq
                                .builder()
                                .operatorCorps(
                                        Collections.singletonList(
                                                PayloadOperatorReq
                                                        .OperatorCorp
                                                        .builder()
                                                        .id(1)
                                                        .operatorChains(
                                                                Collections.singletonList(
                                                                        PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                .builder()
                                                                                .id(2)
                                                                                .operatorStores(
                                                                                        Collections.singletonList(PayloadOperatorReq.OperatorCorp.OperatorChain.OperatorStore
                                                                                                .builder()
                                                                                                .id(3)
                                                                                                .operatorTerminals(
                                                                                                        Collections.singletonList(
                                                                                                                PayloadOperatorReq.OperatorCorp.OperatorChain
                                                                                                                        .OperatorStore.OperatorTerminal
                                                                                                                        .builder()
                                                                                                                        .id(4)
                                                                                                                        .build()
                                                                                                        ))
                                                                                                .build()))
                                                                                .build()
                                                                ))
                                                        .build()))
                                .build())
                .status(ECommonStatus.ACTIVE)
                .build();

        Mockito.when(operatorService.getApprove(req)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/operators/update")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveReject_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.CHECKER_ROLE))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        RejectOperatorRequest req = RejectOperatorRequest.builder()
                .reason("")
                .requestId(1)
                .build();

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/operators/reject")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveReject_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        RejectOperatorRequest req = RejectOperatorRequest.builder()
                .reason("")
                .requestId(1)
                .build();

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/operators/reject")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveApprove_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MAKER_CHECKER))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.CHECKER_ROLE))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/operators/approve/1")
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getApproveApprove_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/operators/approve/1")
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getDetail_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.OPERATOR))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        Mockito.when(operatorService.getDetail(1)).thenReturn(null);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/operators/1")
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getDetail_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Mockito.when(operatorService.getDetail(1)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/operators/1")
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void filter_success() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.OPERATOR))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        SearchOperatorReq searchOperatorReq = SearchOperatorReq
                .builder()
                .businessId(123)
                .corporationId(123)
                .chainId(123)
                .storeId(123)
                .terminalId(123)
                .operatorId("123")
                .approvalStatus(EApprovalStatus.APPROVED)
                .status(ECommonStatus.ACTIVE)
                .build();
        Pageable pageRequest = new OffsetBasedPageRequest(1,10,null);

        Mockito.when(operatorService.filter(Mockito.any(), Mockito.any()))
                .thenReturn(new PageImpl<>(Collections.emptyList(), pageRequest, 0));

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/operators")
                .param("business_id", String.valueOf(searchOperatorReq.getBusinessId()))
                .param("corporation_id", String.valueOf(searchOperatorReq.getCorporationId()))
                .param("chain_id", String.valueOf(searchOperatorReq.getChainId()))
                .param("store_id", String.valueOf(searchOperatorReq.getStoreId()))
                .param("terminal_id", String.valueOf(searchOperatorReq.getTerminalId()))
                .param("operator_id", searchOperatorReq.getOperatorId())
                .param("approval_status", searchOperatorReq.getApprovalStatus().getValue())
                .param("status", searchOperatorReq.getStatus().getValue())
                .param("offset", "1")
                .param("limit", "20")
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getFilter_unsuccess() {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Mockito.when(operatorService.getDetail(1)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/operators")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("approval_status", EApprovalStatus.APPROVED.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @SneakyThrows
    @Test
    public void getFilter_badRequest() {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.OPERATOR))
                .collect(Collectors.toMap(key -> key, key -> Arrays.stream(key.getPermissions())
                        .filter(permission -> permission.equals(AccessPermission.VIEW))
                        .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        Mockito.when(operatorService.getDetail(1)).thenReturn(null);
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/operators")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .param("approval_status", EApprovalStatus.APPROVED.getValue());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.BAD_REQUEST.getValue(), meta.getCode());
    }
}