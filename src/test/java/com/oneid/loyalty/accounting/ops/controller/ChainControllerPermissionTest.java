package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.ChainController;
import com.oneid.loyalty.accounting.ops.model.req.ChainUpdateReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateChainReq;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(ChainController.class)
public class ChainControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsChainService opsChainService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void create_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CHAIN))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CREATE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        CreateChainReq req = new CreateChainReq();
        req.setServiceEndDate(1L);
        req.setServiceStartDate(1L);
        req.setServiceEndDate(2L);
        req.setDescription("description");
        req.setBusinessId(12800);
        req.setCorporationId(1);
        req.setCode("34");
        req.setPhoneNo("0112345678");
        req.setAddress1("Nguyen thi minh khai");
        req.setName("xx");
        req.setStatus("A");
        req.setContactPerson("Cuong Nguyen");

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/chains")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void create_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());
        CreateChainReq req = new CreateChainReq();
        req.setServiceEndDate(1L);
        req.setServiceStartDate(1L);
        req.setServiceEndDate(2L);
        req.setDescription("description");
        req.setBusinessId(12800);
        req.setCorporationId(1);
        req.setCode("34");
        req.setPhoneNo("0112345678");
        req.setAddress1("Nguyen thi minh khai");
        req.setName("xx");
        req.setStatus("A");
        req.setContactPerson("Cuong Nguyen");

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/chains")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void view_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CHAIN))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/chains/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void view_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/chains/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void filter_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CHAIN))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);
        Mockito.when(opsChainService.filter(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/chains")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void filter_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(opsChainService.filter(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any()))
                .thenReturn(Page.empty());
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/chains")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void update_success() throws Exception {

        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.CHAIN))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.EDIT))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        ChainUpdateReq req = new ChainUpdateReq();
        req.setCorporationId(1);
        req.setServiceEndDate(1L);
        req.setServiceStartDate(1L);
        req.setServiceEndDate(2L);
        req.setDescription("description");
        req.setPhoneNo("0112345678");
        req.setAddress1("Nguyen thi minh khai");
        req.setName("xx");
        req.setStatus("A");
        req.setContactPerson("Cuong Nguyen");

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/chains/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void update_unsuccess() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());
        ChainUpdateReq req = new ChainUpdateReq();
        req.setCorporationId(1);
        req.setServiceEndDate(1L);
        req.setServiceStartDate(1L);
        req.setServiceEndDate(2L);
        req.setDescription("description");
        req.setPhoneNo("0112345678");
        req.setAddress1("Nguyen thi minh khai");
        req.setName("xx");
        req.setStatus("A");
        req.setContactPerson("Cuong Nguyen");

        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/chains/1")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req));
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    @Test
    public void getEnum_success() throws Exception {

        mockOPSAuthenticatedPrincipal(new HashMap<>());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/chains/enum-all")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
}