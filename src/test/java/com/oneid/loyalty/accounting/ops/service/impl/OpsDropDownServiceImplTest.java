package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EOpsFunctionCode;
import com.oneid.loyalty.accounting.ops.model.res.*;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProgramTierPeriodType;
import com.oneid.oneloyalty.common.entity.Currency;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.*;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.service.CardTypeService;
import com.oneid.oneloyalty.common.service.ProgramTierService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OpsDropDownServiceImplTest {

    @Mock
    private BusinessRepository mockBusinessRepository;
    @Mock
    private CorporationRepository mockCorporationRepository;
    @Mock
    private ChainRepository mockChainRepository;
    @Mock
    private StoreRepository mockStoreRepository;
    @Mock
    private PosRepository mockPosRepository;
    @Mock
    private CardPolicyRepository mockCardPolicyRepository;
    @Mock
    private ProgramRepository mockProgramRepository;
    @Mock
    private GiftCardBinRepository mockGcBinRepository;
    @Mock
    private GiftCardTypeRepository mockGcTypeRepository;
    @Mock
    private ProgramCorporationRepository mockProgramCorporationRepository;
    @Mock
    private CardTypeService mockCardTypeService;
    @Mock
    private CurrencyRepository mockCurrencyRepository;
    @Mock
    private CurrencyRateRepository mockCurrencyRateRepository;
    @Mock
    private ProgramTierService mockProgramTierService;
    @Mock
    private ReasonCodeRepository mockReasonCodeRepository;
    @Mock
    private FunctionRepository mockFunctionRepository;
    @Mock
    private ProgramTierPolicyRepository mockProgramTierPolicyRepository;
    @Mock
    private MessageEventRepository mockMessageEventRepository;
    @Mock
    private PartnerSupplierRepository mockPartnerSupplierRepository;
    @Mock
    private ProgramTierRepository mockProgramTierRepository;
    @Mock
    private MemberStatusRepository mockMemberStatusRepository;
    @Mock
    private BusinessService mockBusinessService;

    @InjectMocks
    private OpsDropDownServiceImpl opsDropDownServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        opsDropDownServiceImplUnderTest.businessRepository = mockBusinessRepository;
        opsDropDownServiceImplUnderTest.corporationRepository = mockCorporationRepository;
        opsDropDownServiceImplUnderTest.chainRepository = mockChainRepository;
        opsDropDownServiceImplUnderTest.storeRepository = mockStoreRepository;
        opsDropDownServiceImplUnderTest.posRepository = mockPosRepository;
        opsDropDownServiceImplUnderTest.cardPolicyRepository = mockCardPolicyRepository;
        opsDropDownServiceImplUnderTest.programRepository = mockProgramRepository;
        opsDropDownServiceImplUnderTest.gcBinRepository = mockGcBinRepository;
        opsDropDownServiceImplUnderTest.gcTypeRepository = mockGcTypeRepository;
        opsDropDownServiceImplUnderTest.programCorporationRepository = mockProgramCorporationRepository;
        opsDropDownServiceImplUnderTest.currencyRepository = mockCurrencyRepository;
        opsDropDownServiceImplUnderTest.currencyRateRepository = mockCurrencyRateRepository;
        opsDropDownServiceImplUnderTest.businessService = mockBusinessService;
    }

    @Test
    void testBusiness() {
        // Setup
        // Configure BusinessRepository.findAll(...).
        final Business business = new Business();
        business.setCreatedBy("viTest");
        business.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setUpdatedBy("viTest");
        business.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setApprovedBy("viTest");
        business.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setCreatedYmd(0L);
        business.setId(0);
        business.setCode("viTest_code");
        business.setName("viTest_name");
        business.setStatus(ECommonStatus.INACTIVE);
        final List<Business> businesses = List.of(business);
        when(mockBusinessRepository.findAll()).thenReturn(businesses);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.business(ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testBusiness_BusinessRepositoryReturnsNoItems() {
        // Setup
        when(mockBusinessRepository.findAll()).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.business(ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCorporation() {
        // Setup
        // Configure CorporationRepository.findByBusinessId(...).
        final Corporation corporation = new Corporation();
        corporation.setCreatedBy("createdBy");
        corporation.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation.setUpdatedBy("updatedBy");
        corporation.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation.setApprovedBy("approvedBy");
        corporation.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation.setCreatedYmd(0L);
        corporation.setId(0);
        corporation.setCode("code");
        corporation.setName("name");
        corporation.setStatus(ECommonStatus.INACTIVE);
        final List<Corporation> corporations = List.of(corporation);
        when(mockCorporationRepository.findByBusinessId(0)).thenReturn(corporations);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.corporation(0, ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testCorporation_CorporationRepositoryReturnsNoItems() {
        // Setup
        when(mockCorporationRepository.findByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.corporation(0, ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

//    @Test
//    void testGetActiveCorporationsByProgramId() {
//        Object[] corporation = new ();
//        corporation.setName("viTest");
//        corporation.setEnName("viTest");
//        corporation.setName("viTest");
//        corporation.setId(0);
//
//        // Setup
//        when(mockCorporationRepository.findActiveCorporationByProgramId(0)).thenReturn(List.of(corporation));
//
//        // Run the test
//        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActiveCorporationsByProgramId(0);
//
//        // Verify the results
//    }


    @Test
    void testGetActiveCorporationsByProgramId_CorporationRepositoryReturnsNoItems() {
        // Setup
        when(mockCorporationRepository.findActiveCorporationByProgramId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActiveCorporationsByProgramId(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCorporationByProgram() {
        // Setup
        // Configure ProgramCorporationRepository.findByProgramId(...).
        final ProgramCorporation programCorporation = new ProgramCorporation();
        programCorporation.setId(0L);
        programCorporation.setProgramId(0);
        programCorporation.setCorporationId(0);
        programCorporation.setRequestCode("requestCode");
        programCorporation.setVersion(0);
        final List<ProgramCorporation> programCorporations = List.of(programCorporation);
        when(mockProgramCorporationRepository.findByProgramId(0)).thenReturn(programCorporations);

        // Configure CorporationRepository.findById(...).
        final Corporation corporation1 = new Corporation();
        corporation1.setCreatedBy("createdBy");
        corporation1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setUpdatedBy("updatedBy");
        corporation1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setApprovedBy("approvedBy");
        corporation1.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setCreatedYmd(0L);
        corporation1.setId(0);
        corporation1.setCode("code");
        corporation1.setName("name");
        corporation1.setStatus(ECommonStatus.INACTIVE);
        final Optional<Corporation> corporation = Optional.of(corporation1);
        when(mockCorporationRepository.findById(0)).thenReturn(corporation);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.corporationByProgram(0);

        // Verify the results
    }

    @Test
    void testCorporationByProgram_ProgramCorporationRepositoryReturnsNoItems() {
        // Setup
        when(mockProgramCorporationRepository.findByProgramId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.corporationByProgram(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCorporationByProgram_CorporationRepositoryReturnsAbsent() {
        // Setup
        // Configure ProgramCorporationRepository.findByProgramId(...).
        final ProgramCorporation programCorporation = new ProgramCorporation();
        programCorporation.setId(0L);
        programCorporation.setProgramId(0);
        programCorporation.setCorporationId(0);
        programCorporation.setRequestCode("requestCode");
        programCorporation.setVersion(0);
        final List<ProgramCorporation> programCorporations = List.of(programCorporation);
        when(mockProgramCorporationRepository.findByProgramId(0)).thenReturn(programCorporations);

        when(mockCorporationRepository.findById(0)).thenReturn(Optional.empty());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.corporationByProgram(0);

        // Verify the results
    }

    @Test
    void testChain() {
        // Setup
        // Configure ChainRepository.findByCorporationId(...).
        final Chain chain = new Chain();
        chain.setCreatedBy("createdBy");
        chain.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setUpdatedBy("updatedBy");
        chain.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setApprovedBy("approvedBy");
        chain.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setCreatedYmd(0L);
        chain.setId(0);
        chain.setCode("code");
        chain.setCorporationId(0);
        chain.setName("name");
        chain.setEnName("enName");
        chain.setStatus(ECommonStatus.INACTIVE);
        final List<Chain> chains = List.of(chain);
        when(mockChainRepository.findByCorporationId(0)).thenReturn(chains);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.chain(0, ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testChain_ChainRepositoryReturnsNoItems() {
        // Setup
        when(mockChainRepository.findByCorporationId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.chain(0, ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testStore() {
        // Setup
        // Configure StoreRepository.findByChainId(...).
        final Store store = new Store();
        store.setCreatedBy("createdBy");
        store.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setUpdatedBy("updatedBy");
        store.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setApprovedBy("approvedBy");
        store.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setCreatedYmd(0L);
        store.setId(0);
        store.setCode("code");
        store.setChainId(0);
        store.setName("name");
        store.setStatus(ECommonStatus.INACTIVE);
        final List<Store> stores = List.of(store);
        when(mockStoreRepository.findByChainId(0)).thenReturn(stores);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.store(0, ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testStore_StoreRepositoryReturnsNoItems() {
        // Setup
        when(mockStoreRepository.findByChainId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.store(0, ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testTerminal() {
        // Setup
        // Configure PosRepository.findByStoreId(...).
        final Pos pos1 = new Pos();
        pos1.setCreatedBy("createdBy");
        pos1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setUpdatedBy("updatedBy");
        pos1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setApprovedBy("approvedBy");
        pos1.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setCreatedYmd(0L);
        pos1.setId(0);
        pos1.setCode("code");
        pos1.setStoreId(0);
        pos1.setName("name");
        pos1.setStatus(ECommonStatus.INACTIVE);
        final List<Pos> pos = List.of(pos1);
        when(mockPosRepository.findByStoreId(0)).thenReturn(pos);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.terminal(0, ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testTerminal_PosRepositoryReturnsNoItems() {
        // Setup
        when(mockPosRepository.findByStoreId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.terminal(0, ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCardPolicy() {
        // Setup
        // Configure CardPolicyRepository.findAll(...).
        final CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setCreatedBy("createdBy");
        cardPolicy.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardPolicy.setUpdatedBy("updatedBy");
        cardPolicy.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardPolicy.setApprovedBy("approvedBy");
        cardPolicy.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardPolicy.setCreatedYmd(0L);
        cardPolicy.setId(0);
        cardPolicy.setCode("code");
        cardPolicy.setName("name");
        final List<CardPolicy> cardPolicies = List.of(cardPolicy);
        when(mockCardPolicyRepository.findAll(any(SpecificationBuilder.class))).thenReturn(cardPolicies);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.cardPolicy(0, ECardPolicyType.MEMBER_CARD);

        // Verify the results
    }

    @Test
    void testCardPolicy_CardPolicyRepositoryReturnsNoItems() {
        // Setup
        when(mockCardPolicyRepository.findAll(any(SpecificationBuilder.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.cardPolicy(0, ECardPolicyType.MEMBER_CARD);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testProgram() {
        // Setup
        // Configure ProgramRepository.findByBusinessId(...).
        final Program program = new Program();
        program.setId(0);
        program.setCode("code");
        program.setBusinessId(0);
        program.setName("name");
        program.setStatus(ECommonStatus.INACTIVE);
        final List<Program> programs = List.of(program);
        when(mockProgramRepository.findByBusinessId(0)).thenReturn(programs);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.program(0);

        // Verify the results
    }

    @Test
    void testProgram_ProgramRepositoryReturnsNoItems() {
        // Setup
        when(mockProgramRepository.findByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.program(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGcBinByBusinessAndProgram() {
        // Setup
        // Configure GiftCardBinRepository.findAllByBusinessIdAndProgramIdAndStatus(...).
        final GiftCardBin giftCardBin = new GiftCardBin();
        giftCardBin.setCreatedBy("createdBy");
        giftCardBin.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardBin.setUpdatedBy("updatedBy");
        giftCardBin.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardBin.setApprovedBy("approvedBy");
        giftCardBin.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardBin.setCreatedYmd(0L);
        giftCardBin.setId(0);
        giftCardBin.setBinCode("code");
        giftCardBin.setName("name");
        final List<GiftCardBin> giftCardBins = List.of(giftCardBin);
        when(mockGcBinRepository.findAllByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(giftCardBins);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.gcBinByBusinessAndProgram(0, 0);

        // Verify the results
    }

    @Test
    void testGcBinByBusinessAndProgram_GiftCardBinRepositoryReturnsNoItems() {
        // Setup
        when(mockGcBinRepository.findAllByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.gcBinByBusinessAndProgram(0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGcTypeByBusinessAndProgram() {
        // Setup
        // Configure GiftCardTypeRepository.findAllByBusinessIdAndProgramIdAndStatus(...).
        final GiftCardType giftCardType = new GiftCardType();
        giftCardType.setCreatedBy("createdBy");
        giftCardType.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardType.setUpdatedBy("updatedBy");
        giftCardType.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardType.setApprovedBy("approvedBy");
        giftCardType.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        giftCardType.setCreatedYmd(0L);
        giftCardType.setId(0);
        giftCardType.setBusinessId(0);
        giftCardType.setProgramId(0);
        giftCardType.setCode("code");
        giftCardType.setPolicyId(0);
        giftCardType.setDescription("description");
        giftCardType.setBaseCurrencyId(0);
        giftCardType.setPrice(0.0);
        giftCardType.setCurrencyId(0);
        giftCardType.setPoint(0L);
        giftCardType.setStatus(ECommonStatus.INACTIVE);
        final List<GiftCardType> giftCardTypes = List.of(giftCardType);
        when(mockGcTypeRepository.findAllByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(giftCardTypes);

        // Run the test
        final List<GiftCardTypeRes> result = opsDropDownServiceImplUnderTest.gcTypeByBusinessAndProgram(0, 0);

        // Verify the results
    }

    @Test
    void testGcTypeByBusinessAndProgram_GiftCardTypeRepositoryReturnsNoItems() {
        // Setup
        when(mockGcTypeRepository.findAllByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GiftCardTypeRes> result = opsDropDownServiceImplUnderTest.gcTypeByBusinessAndProgram(0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetCardTypesByBusinessAndProgramId() {
        // Setup
        // Configure CardTypeService.findByBusinessIdAndProgramIdAndStatus(...).
        final CardType cardType = new CardType();
        cardType.setCreatedBy("createdBy");
        cardType.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardType.setUpdatedBy("updatedBy");
        cardType.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardType.setApprovedBy("approvedBy");
        cardType.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        cardType.setCreatedYmd(0L);
        cardType.setId(0);
        cardType.setCardPolicyId(0);
        cardType.setCardType("cardType");
        cardType.setName("name");
        cardType.setDescription("description");
        cardType.setStatus(ECommonStatus.INACTIVE);
        final List<CardType> cardTypes = List.of(cardType);
        when(mockCardTypeService.findByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(cardTypes);

        // Run the test
        final List<CardTypeRes> result = opsDropDownServiceImplUnderTest.getCardTypesByBusinessAndProgramId(0, 0);

        // Verify the results
    }

    @Test
    void testGetCardTypesByBusinessAndProgramId_CardTypeServiceReturnsNoItems() {
        // Setup
        when(mockCardTypeService.findByBusinessIdAndProgramIdAndStatus(0, 0, ECommonStatus.ACTIVE))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<CardTypeRes> result = opsDropDownServiceImplUnderTest.getCardTypesByBusinessAndProgramId(0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCurrencyForTransaction() {
        // Setup
        // Configure CurrencyRateRepository.findAllByBusinessId(...).
        final CurrencyRate currencyRate = new CurrencyRate();
        currencyRate.setId(0);
        currencyRate.setBusinessId(0);
        currencyRate.setCurrencyId(0);
        currencyRate.setBaseCurrencyId(0);
        currencyRate.setSellRate(0.0);
        final List<CurrencyRate> currencyRates = List.of(currencyRate);
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(currencyRates);

        // Configure CurrencyRepository.findById(...).
        final Currency currency1 = new Currency();
        currency1.setId(0);
        currency1.setCode("code");
        currency1.setName("name");
        currency1.setEnName("enName");
        currency1.setStatus(ECommonStatus.INACTIVE);
        final Optional<Currency> currency = Optional.of(currency1);
        when(mockCurrencyRepository.findById(0)).thenReturn(currency);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.currencyForTransaction(0);

        // Verify the results
    }

    @Test
    void testCurrencyForTransaction_CurrencyRateRepositoryReturnsNoItems() {
        // Setup
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.currencyForTransaction(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testCurrencyForTransaction_CurrencyRepositoryReturnsAbsent() {
        // Setup
        // Configure CurrencyRateRepository.findAllByBusinessId(...).
        final CurrencyRate currencyRate = new CurrencyRate();
        currencyRate.setId(0);
        currencyRate.setBusinessId(0);
        currencyRate.setCurrencyId(0);
        currencyRate.setBaseCurrencyId(0);
        currencyRate.setSellRate(0.0);
        final List<CurrencyRate> currencyRates = List.of(currencyRate);
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(currencyRates);

        when(mockCurrencyRepository.findById(0)).thenReturn(Optional.empty());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.currencyForTransaction(0);

        // Verify the results
    }

    @Test
    void testBaseCurrencyForTransaction() {
        // Setup
        // Configure CurrencyRateRepository.findAllByBusinessId(...).
        final CurrencyRate currencyRate = new CurrencyRate();
        currencyRate.setId(0);
        currencyRate.setBusinessId(0);
        currencyRate.setCurrencyId(0);
        currencyRate.setBaseCurrencyId(0);
        currencyRate.setSellRate(0.0);
        final List<CurrencyRate> currencyRates = List.of(currencyRate);
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(currencyRates);

        // Configure CurrencyRepository.findById(...).
        final Currency currency1 = new Currency();
        currency1.setId(0);
        currency1.setCode("code");
        currency1.setName("name");
        currency1.setEnName("enName");
        currency1.setStatus(ECommonStatus.INACTIVE);
        final Optional<Currency> currency = Optional.of(currency1);
        when(mockCurrencyRepository.findById(0)).thenReturn(currency);

        // Run the test
        final List<DropdownCurrencyRes> result = opsDropDownServiceImplUnderTest.baseCurrencyForTransaction(0);

        // Verify the results
    }

    @Test
    void testBaseCurrencyForTransaction_CurrencyRateRepositoryReturnsNoItems() {
        // Setup
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownCurrencyRes> result = opsDropDownServiceImplUnderTest.baseCurrencyForTransaction(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testBaseCurrencyForTransaction_CurrencyRepositoryReturnsAbsent() {
        // Setup
        // Configure CurrencyRateRepository.findAllByBusinessId(...).
        final CurrencyRate currencyRate = new CurrencyRate();
        currencyRate.setId(0);
        currencyRate.setBusinessId(0);
        currencyRate.setCurrencyId(0);
        currencyRate.setBaseCurrencyId(0);
        currencyRate.setSellRate(0.0);
        final List<CurrencyRate> currencyRates = List.of(currencyRate);
        when(mockCurrencyRateRepository.findAllByBusinessId(0)).thenReturn(currencyRates);

        when(mockCurrencyRepository.findById(0)).thenReturn(Optional.empty());

        // Run the test
        final List<DropdownCurrencyRes> result = opsDropDownServiceImplUnderTest.baseCurrencyForTransaction(0);

        // Verify the results
    }

    @Test
    void testGetTiers() {
        // Setup
        // Configure ProgramTierService.searchAll(...).
        final ProgramTier programTier = new ProgramTier();
        programTier.setId(0);
        programTier.setTierCode("tierCode");
        programTier.setProgramId(0);
        programTier.setName("name");
        programTier.setDescription("description");
        programTier.setStatus(ECommonStatus.INACTIVE);
        programTier.setRankNo(0);
        programTier.setExpiredPeriod(0);
        programTier.setExpiredType(EProgramTierPeriodType.DAY);
        programTier.setCardBackgroundUrl("cardBackgroundUrl");
        final List<ProgramTier> programTiers = List.of(programTier);
        when(mockProgramTierService.searchAll(any(SpecificationBuilder.class))).thenReturn(programTiers);

        // Run the test
        final List<LegacyTierRes> result = opsDropDownServiceImplUnderTest.getTiers(0);

        // Verify the results
    }

    @Test
    void testGetTiers_ProgramTierServiceReturnsNoItems() {
        // Setup
        when(mockProgramTierService.searchAll(any(SpecificationBuilder.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<LegacyTierRes> result = opsDropDownServiceImplUnderTest.getTiers(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActivatedTiers() {
        // Setup
        final Sort sort = Sort.by("properties");

        // Configure ProgramTierRepository.findByProgramIdAndStatus(...).
        final ProgramTier programTier = new ProgramTier();
        programTier.setId(0);
        programTier.setTierCode("tierCode");
        programTier.setProgramId(0);
        programTier.setName("name");
        programTier.setDescription("description");
        programTier.setStatus(ECommonStatus.INACTIVE);
        programTier.setRankNo(0);
        programTier.setExpiredPeriod(0);
        programTier.setExpiredType(EProgramTierPeriodType.DAY);
        programTier.setCardBackgroundUrl("cardBackgroundUrl");
        final List<ProgramTier> programTiers = List.of(programTier);
        when(mockProgramTierRepository.findByProgramIdAndStatus(0, ECommonStatus.ACTIVE,
                Sort.by("properties"))).thenReturn(programTiers);

        // Run the test
        final List<LegacyTierRes> result = opsDropDownServiceImplUnderTest.getActivatedTiers(0, sort);

        // Verify the results
    }

    @Test
    void testGetActivatedTiers_ProgramTierRepositoryReturnsNoItems() {
        // Setup
        final Sort sort = Sort.by("properties");
        when(mockProgramTierRepository.findByProgramIdAndStatus(0, ECommonStatus.ACTIVE,
                Sort.by("properties"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<LegacyTierRes> result = opsDropDownServiceImplUnderTest.getActivatedTiers(0, sort);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActiveReasonCodes() {
        // Setup
        // Configure FunctionRepository.findByCode(...).
        final Function function = new Function();
        function.setCreatedBy("createdBy");
        function.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setUpdatedBy("updatedBy");
        function.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setApprovedBy("approvedBy");
        function.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setCreatedYmd(0L);
        function.setId(0);
        when(mockFunctionRepository.findByCode(EOpsFunctionCode.POINT_ADJ.getValue())).thenReturn(function);

        // Configure ReasonCodeRepository.findByBusinessIdAndProgramIdAndStatusFunctionId(...).
        final ReasonCode reasonCode = new ReasonCode();
        reasonCode.setCreatedBy("createdBy");
        reasonCode.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        reasonCode.setUpdatedBy("updatedBy");
        reasonCode.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        reasonCode.setApprovedBy("approvedBy");
        reasonCode.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        reasonCode.setCreatedYmd(0L);
        reasonCode.setId(0);
        reasonCode.setCode("code");
        reasonCode.setBusinessId(0);
        reasonCode.setProgramId(0);
        reasonCode.setName("name");
        reasonCode.setEnName("enName");
        reasonCode.setDesciption("description");
        reasonCode.setStatus(ECommonStatus.INACTIVE);
        reasonCode.setFunctionId(0);
        final List<ReasonCode> reasonCodes = List.of(reasonCode);
        when(mockReasonCodeRepository.findByBusinessIdAndProgramIdAndStatusFunctionId(0, 0, ECommonStatus.ACTIVE,
                0)).thenReturn(reasonCodes);

        // Run the test
        final List<ReasonCodeRes> result = opsDropDownServiceImplUnderTest.getActiveReasonCodes(0, 0,
                EOpsFunctionCode.POINT_ADJ);

        // Verify the results
    }

    @Test
    void testGetActiveReasonCodes_FunctionRepositoryReturnsNull() {
        // Setup
        when(mockFunctionRepository.findByCode(EOpsFunctionCode.POINT_ADJ.getValue())).thenReturn(null);

        // Run the test
        final List<ReasonCodeRes> result = opsDropDownServiceImplUnderTest.getActiveReasonCodes(0, 0,
                EOpsFunctionCode.POINT_ADJ);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActiveReasonCodes_ReasonCodeRepositoryReturnsNoItems() {
        // Setup
        // Configure FunctionRepository.findByCode(...).
        final Function function = new Function();
        function.setCreatedBy("createdBy");
        function.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setUpdatedBy("updatedBy");
        function.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setApprovedBy("approvedBy");
        function.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        function.setCreatedYmd(0L);
        function.setId(0);
        when(mockFunctionRepository.findByCode(EOpsFunctionCode.POINT_ADJ.getValue())).thenReturn(function);

        when(mockReasonCodeRepository.findByBusinessIdAndProgramIdAndStatusFunctionId(0, 0, ECommonStatus.ACTIVE,
                0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ReasonCodeRes> result = opsDropDownServiceImplUnderTest.getActiveReasonCodes(0, 0,
                EOpsFunctionCode.POINT_ADJ);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSearchTerminals() {
        // Setup
        // Configure PosRepository.findByStoreIdInAndNameOrCode(...).
        final Pos pos1 = new Pos();
        pos1.setCreatedBy("createdBy");
        pos1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setUpdatedBy("updatedBy");
        pos1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setApprovedBy("approvedBy");
        pos1.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        pos1.setCreatedYmd(0L);
        pos1.setId(0);
        pos1.setCode("code");
        pos1.setStoreId(0);
        pos1.setName("name");
        pos1.setStatus(ECommonStatus.INACTIVE);
        final List<Pos> pos = List.of(pos1);
        when(mockPosRepository.findByStoreIdInAndNameOrCode(List.of(0), "nameOrCode%", ECommonStatus.ACTIVE))
                .thenReturn(pos);

        // Run the test
        final Collection<ShortEntityGroupByParentRes> result = opsDropDownServiceImplUnderTest.searchTerminals(
                List.of(0), "nameOrCode");

        // Verify the results
    }

    @Test
    void testSearchStores() {
        // Setup
        // Configure StoreRepository.findByChainIdInAndNameOrCode(...).
        final Store store = new Store();
        store.setCreatedBy("createdBy");
        store.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setUpdatedBy("updatedBy");
        store.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setApprovedBy("approvedBy");
        store.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setCreatedYmd(0L);
        store.setId(0);
        store.setCode("code");
        store.setChainId(0);
        store.setName("name");
        store.setStatus(ECommonStatus.INACTIVE);
        final List<Store> stores = List.of(store);
        when(mockStoreRepository.findByChainIdInAndNameOrCode(List.of(0), "nameOrCode%",
                ECommonStatus.ACTIVE)).thenReturn(stores);

        // Run the test
        final Collection<ShortEntityGroupByParentRes> result = opsDropDownServiceImplUnderTest.searchStores(List.of(0),
                "nameOrCode");

        // Verify the results
    }

    @Test
    void testSearchChains() {
        // Setup
        // Configure ChainRepository.findByCorporationIdInAndNameOrCode(...).
        final Chain chain = new Chain();
        chain.setCreatedBy("createdBy");
        chain.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setUpdatedBy("updatedBy");
        chain.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setApprovedBy("approvedBy");
        chain.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setCreatedYmd(0L);
        chain.setId(0);
        chain.setCode("code");
        chain.setCorporationId(0);
        chain.setName("name");
        chain.setEnName("enName");
        chain.setStatus(ECommonStatus.INACTIVE);
        final List<Chain> chains = List.of(chain);
        when(mockChainRepository.findByCorporationIdInAndNameOrCode(List.of(0), "nameOrCode%",
                ECommonStatus.ACTIVE)).thenReturn(chains);

        // Run the test
        final Collection<ShortEntityGroupByParentRes> result = opsDropDownServiceImplUnderTest.searchChains(List.of(0),
                "nameOrCode");

        // Verify the results
    }

    @Test
    void testGetActivePrograms() {
        // Setup
        final List<ProgramRes> expectedResult = List.of(ProgramRes.builder()
                .programId(0)
                .programCode("code")
                .programName("name")
                .status(ECommonStatus.INACTIVE)
                .build());

        // Configure ProgramRepository.findByBusinessId(...).
        final Program program = new Program();
        program.setId(0);
        program.setCode("code");
        program.setBusinessId(0);
        program.setName("name");
        program.setStatus(ECommonStatus.ACTIVE);
        final List<Program> programs = List.of(program);
        when(mockProgramRepository.findByBusinessId(0)).thenReturn(programs);

        // Run the test
        final List<ProgramRes> result = opsDropDownServiceImplUnderTest.getActivePrograms(0, List.of(1));

        // Verify the results
        assertThat(result.get(0).getProgramCode()).isEqualTo(expectedResult.get(0).getProgramCode());
    }

    @Test
    void testGetActivePrograms_ProgramRepositoryReturnsNoItems() {
        // Setup
        when(mockProgramRepository.findByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ProgramRes> result = opsDropDownServiceImplUnderTest.getActivePrograms(0, List.of(0));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActiveTierPolicies() {
        // Setup
        // Configure ProgramTierPolicyRepository.findActiveByProgramId(...).
        final ProgramTierPolicy programTierPolicy1 = new ProgramTierPolicy();
        programTierPolicy1.setId(0);
        programTierPolicy1.setBusinessId(0);
        programTierPolicy1.setName("name");
        programTierPolicy1.setCode("code");
        programTierPolicy1.setDescription("description");
        final Optional<ProgramTierPolicy> programTierPolicy = Optional.of(programTierPolicy1);
        when(mockProgramTierPolicyRepository.findActiveByProgramId(0)).thenReturn(programTierPolicy);

        // Run the test
        final List<TierPolicyRes> result = opsDropDownServiceImplUnderTest.getActiveTierPolicies(0, 0);

        // Verify the results
    }

    @Test
    void testGetActiveTierPolicies_ProgramTierPolicyRepositoryReturnsAbsent() {
        // Setup
        when(mockProgramTierPolicyRepository.findActiveByProgramId(0)).thenReturn(Optional.empty());

        // Run the test
        final List<TierPolicyRes> result = opsDropDownServiceImplUnderTest.getActiveTierPolicies(0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetMessageEvent() {
        // Setup
        // Configure MessageEventRepository.findByStatus(...).
        final MessageEvent messageEvent = new MessageEvent();
        messageEvent.setCreatedBy("createdBy");
        messageEvent.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        messageEvent.setUpdatedBy("updatedBy");
        messageEvent.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        messageEvent.setApprovedBy("approvedBy");
        messageEvent.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        messageEvent.setCreatedYmd(0L);
        messageEvent.setId(0);
        messageEvent.setCode("code");
        messageEvent.setName("name");
        final List<MessageEvent> messageEvents = List.of(messageEvent);
        when(mockMessageEventRepository.findByStatus(ECommonStatus.ACTIVE)).thenReturn(messageEvents);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getMessageEvent();

        // Verify the results
    }

    @Test
    void testGetMessageEvent_MessageEventRepositoryReturnsNoItems() {
        // Setup
        when(mockMessageEventRepository.findByStatus(ECommonStatus.ACTIVE)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getMessageEvent();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActivatedPartnerSuppliers() {
        // Setup
        // Configure PartnerSupplierRepository.findByBusinessIdAndStatus(...).
        final PartnerSupplier partnerSupplier = new PartnerSupplier();
        partnerSupplier.setCreatedBy("createdBy");
        partnerSupplier.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        partnerSupplier.setUpdatedBy("updatedBy");
        partnerSupplier.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        partnerSupplier.setApprovedBy("approvedBy");
        partnerSupplier.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        partnerSupplier.setCreatedYmd(0L);
        partnerSupplier.setId(0);
        partnerSupplier.setCode("code");
        partnerSupplier.setName("name");
        final List<PartnerSupplier> partnerSuppliers = List.of(partnerSupplier);
        when(mockPartnerSupplierRepository.findByBusinessIdAndStatus(0, ECommonStatus.ACTIVE))
                .thenReturn(partnerSuppliers);

        // Run the test
        final List<PartnerSupplierRes> result = opsDropDownServiceImplUnderTest.getActivatedPartnerSuppliers(0);

        // Verify the results
    }

    @Test
    void testGetActivatedPartnerSuppliers_PartnerSupplierRepositoryReturnsNoItems() {
        // Setup
        when(mockPartnerSupplierRepository.findByBusinessIdAndStatus(0, ECommonStatus.ACTIVE))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PartnerSupplierRes> result = opsDropDownServiceImplUnderTest.getActivatedPartnerSuppliers(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActivatedCurrenciesByBusinessCode() {
        // Setup
        // Configure BusinessRepository.findByCode(...).
        final Business business = new Business();
        business.setCreatedBy("createdBy");
        business.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setUpdatedBy("updatedBy");
        business.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setApprovedBy("approvedBy");
        business.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setCreatedYmd(0L);
        business.setId(0);
        business.setCode("code");
        business.setName("name");
        business.setStatus(ECommonStatus.INACTIVE);
        when(mockBusinessRepository.findByCode("businessCode")).thenReturn(business);

        // Configure CurrencyRepository.findByBusinessId(...).
        final Currency currency = new Currency();
        currency.setId(0);
        currency.setCode("code");
        currency.setName("name");
        currency.setEnName("enName");
        currency.setStatus(ECommonStatus.INACTIVE);
        final List<Currency> currencies = List.of(currency);
        when(mockCurrencyRepository.findByBusinessId(0)).thenReturn(currencies);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActivatedCurrenciesByBusinessCode(
                "businessCode");

        // Verify the results
    }

    @Test
    void testGetActivatedCurrenciesByBusinessCode_BusinessRepositoryReturnsNull() {
        // Setup
        when(mockBusinessRepository.findByCode("businessCode")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> opsDropDownServiceImplUnderTest.getActivatedCurrenciesByBusinessCode(
                "businessCode")).isInstanceOf(BusinessException.class);
    }

    @Test
    void testGetActivatedCurrenciesByBusinessCode_CurrencyRepositoryReturnsNoItems() {
        // Setup
        // Configure BusinessRepository.findByCode(...).
        final Business business = new Business();
        business.setCreatedBy("createdBy");
        business.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setUpdatedBy("updatedBy");
        business.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setApprovedBy("approvedBy");
        business.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        business.setCreatedYmd(0L);
        business.setId(0);
        business.setCode("code");
        business.setName("name");
        business.setStatus(ECommonStatus.INACTIVE);
        when(mockBusinessRepository.findByCode("businessCode")).thenReturn(business);

        when(mockCurrencyRepository.findByBusinessId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActivatedCurrenciesByBusinessCode(
                "businessCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGeActivatedtStoresByCorporationId() {
        // Setup
        // Configure StoreRepository.findByCorporationIdAndStatus(...).
        final Store store = new Store();
        store.setCreatedBy("createdBy");
        store.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setUpdatedBy("updatedBy");
        store.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setApprovedBy("approvedBy");
        store.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        store.setCreatedYmd(0L);
        store.setId(0);
        store.setCode("code");
        store.setChainId(0);
        store.setName("name");
        store.setStatus(ECommonStatus.INACTIVE);
        final List<Store> stores = List.of(store);
        when(mockStoreRepository.findByCorporationIdAndStatus(0, ECommonStatus.ACTIVE)).thenReturn(stores);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.geActivatedtStoresByCorporationId(0);

        // Verify the results
    }

    @Test
    void testGeActivatedtStoresByCorporationId_StoreRepositoryReturnsNoItems() {
        // Setup
        when(mockStoreRepository.findByCorporationIdAndStatus(0, ECommonStatus.ACTIVE))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.geActivatedtStoresByCorporationId(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActivatedChainsByCorporationCode() {
        // Setup
        // Configure CorporationRepository.findByBusinessAndCorporationCode(...).
        final Corporation corporation1 = new Corporation();
        corporation1.setCreatedBy("createdBy");
        corporation1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setUpdatedBy("updatedBy");
        corporation1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setApprovedBy("approvedBy");
        corporation1.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setCreatedYmd(0L);
        corporation1.setId(0);
        corporation1.setCode("code");
        corporation1.setName("name");
        corporation1.setStatus(ECommonStatus.INACTIVE);
        final Optional<Corporation> corporation = Optional.of(corporation1);
        when(mockCorporationRepository.findByBusinessAndCorporationCode("businessCode", "corporationCode"))
                .thenReturn(corporation);

        // Configure ChainRepository.findByCorporationId(...).
        final Chain chain = new Chain();
        chain.setCreatedBy("createdBy");
        chain.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setUpdatedBy("updatedBy");
        chain.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setApprovedBy("approvedBy");
        chain.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        chain.setCreatedYmd(0L);
        chain.setId(0);
        chain.setCode("code");
        chain.setCorporationId(0);
        chain.setName("name");
        chain.setEnName("enName");
        chain.setStatus(ECommonStatus.INACTIVE);
        final List<Chain> chains = List.of(chain);
        when(mockChainRepository.findByCorporationId(0)).thenReturn(chains);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActivatedChainsByCorporationCode(
                "businessCode", "corporationCode", ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testGetActivatedChainsByCorporationCode_CorporationRepositoryReturnsAbsent() {
        // Setup
        when(mockCorporationRepository.findByBusinessAndCorporationCode("businessCode", "corporationCode"))
                .thenReturn(Optional.empty());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActivatedChainsByCorporationCode(
                "businessCode", "corporationCode", ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetActivatedChainsByCorporationCode_ChainRepositoryReturnsNoItems() {
        // Setup
        // Configure CorporationRepository.findByBusinessAndCorporationCode(...).
        final Corporation corporation1 = new Corporation();
        corporation1.setCreatedBy("createdBy");
        corporation1.setCreatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setUpdatedBy("updatedBy");
        corporation1.setUpdatedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setApprovedBy("approvedBy");
        corporation1.setApprovedAt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        corporation1.setCreatedYmd(0L);
        corporation1.setId(0);
        corporation1.setCode("code");
        corporation1.setName("name");
        corporation1.setStatus(ECommonStatus.INACTIVE);
        final Optional<Corporation> corporation = Optional.of(corporation1);
        when(mockCorporationRepository.findByBusinessAndCorporationCode("businessCode", "corporationCode"))
                .thenReturn(corporation);

        when(mockChainRepository.findByCorporationId(0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getActivatedChainsByCorporationCode(
                "businessCode", "corporationCode", ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetAllMemberStatus() {
        // Setup
        // Configure MemberStatusRepository.findAll(...).
        final MemberStatus memberStatus = new MemberStatus();
        memberStatus.setId(0);
        memberStatus.setBusinessId(0);
        memberStatus.setCode("code");
        memberStatus.setViName("name");
        memberStatus.setEnName("enName");
        final List<MemberStatus> memberStatuses = List.of(memberStatus);
        when(mockMemberStatusRepository.findAll(any(SpecificationBuilder.class))).thenReturn(memberStatuses);

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getAllMemberStatus(0, ECommonStatus.INACTIVE);

        // Verify the results
    }

    @Test
    void testGetAllMemberStatus_MemberStatusRepositoryReturnsNoItems() {
        // Setup
        when(mockMemberStatusRepository.findAll(any(SpecificationBuilder.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<DropdownRes> result = opsDropDownServiceImplUnderTest.getAllMemberStatus(0, ECommonStatus.INACTIVE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}