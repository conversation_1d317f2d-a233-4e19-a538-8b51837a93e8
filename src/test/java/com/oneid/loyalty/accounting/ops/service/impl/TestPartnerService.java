//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.entity.WlPartner;
//import com.oneid.loyalty.accounting.ops.service.core.CorePartnerService;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//
//@RunWith(SpringRunner.class)
//public class TestPartnerService {
//
//    @Autowired
//    private PartnerService partnerService;
//
//    @MockBean
//    private CorePartnerService corePartnerService;
//
//    private List<WlPartner> partners;
//
//    @Test
//    public void getAllNull() {
//        Mockito.when(corePartnerService.getAll()).thenReturn(null);
//        Assert.assertNull(partnerService.getAll());
//    }
//
//    @Test
//    public void getAll() {
//        Mockito.when(corePartnerService.getAll()).thenReturn(partners);
//        Assert.assertEquals(partnerService.getAll(), partners);
//    }
//
//    @TestConfiguration
//    public static class ConfigTest {
//        @Bean
//        public PartnerService partnerService() {
//            return new PartnerServiceImpl();
//        }
//    }
//}
