package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.CardProductionRequestRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardProductionRequestService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.CardProductionRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.*;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@RunWith(SpringRunner.class)
@Ignore
public class CPRServiceImplTest {

    @Autowired
    private OpsCardProductionRequestService cprService;

    @MockBean
    private CardProductionRequestRepository cprRepository;

    @MockBean
    private BusinessRepository businessRepository;

    @MockBean
    private StoreRepository storeRepository;

    @MockBean
    private ChainRepository chainRepository;

    @MockBean
    private CorporationRepository corporationRepository;

    @MockBean
    private CardTypeRepository cardTypeRepository;

    @MockBean
    private CardBinRepository cardBinRepository;

    @MockBean
    private ProgramRepository programRepository;

    /* =========================== INIT PARAM =================== */
    private final Integer id = 1234;
    private final CardProductionRequest cpr = new CardProductionRequest();
    /* ========================= END INT PARAM =================== */

    @Test
    public void getById_NotFound_ExceptionThrow() {
        final Integer id = this.id;

        Mockito.when(this.cprRepository.findById(id)).thenReturn(Optional.empty());

        try {
            this.cprService.getById(id);
        } catch (BusinessException e) {
            Assert.assertEquals(ErrorCode.CARD_PRODUCTION_REQUEST_NOT_FOUND, e.getCode());
        }
    }

    @Test
    public void getById_Success() {
        final Integer id = this.id;
        cpr.setId(id);
        cpr.setStatus(ECommonStatus.PENDING);

        // Mock
        Mockito.when(this.cprRepository.findById(id)).thenReturn(Optional.of(cpr));

        // Test
        CardProductionRequestRes result = this.cprService.getById(id);
        Assert.assertEquals(id, result.getId());
    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsCardProductionRequestService cprService() {
            return new OpsCardProductionRequestServiceImpl();
        }
    }
}
