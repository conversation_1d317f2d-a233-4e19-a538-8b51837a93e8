//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.mapper.ProgramMapper;
//import com.oneid.loyalty.accounting.ops.model.req.*;
//import com.oneid.loyalty.accounting.ops.model.res.CorporationInfo;
//import com.oneid.loyalty.accounting.ops.model.res.ProgramDropDownRes;
//import com.oneid.loyalty.accounting.ops.model.res.ProgramRes;
//import com.oneid.loyalty.accounting.ops.service.OpsProgramService;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import com.oneid.oneloyalty.common.constant.ECommonStatus;
//import com.oneid.oneloyalty.common.constant.ErrorCode;
//import com.oneid.oneloyalty.common.entity.Business;
//import com.oneid.oneloyalty.common.entity.Corporation;
//import com.oneid.oneloyalty.common.entity.Program;
//import com.oneid.oneloyalty.common.entity.ProgramCorporation;
//import com.oneid.oneloyalty.common.exception.BusinessException;
//import com.oneid.oneloyalty.common.repository.ProgramProductRepository;
//import com.oneid.oneloyalty.common.search.SpecificationBuilder;
//import com.oneid.oneloyalty.common.service.BusinessService;
//import com.oneid.oneloyalty.common.service.CorporationService;
//import com.oneid.oneloyalty.common.service.ProgramCorporationService;
//import com.oneid.oneloyalty.common.service.ProgramService;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.data.domain.Pageable;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.*;
//
//@RunWith(SpringRunner.class)
//public class TestProgramOpsService {
//
//    @Autowired
//    private OpsProgramService opsProgramService;
//
//    @MockBean
//    private ProgramService programService;
//
//    @MockBean
//    private BusinessService businessService;
//
//    @MockBean
//    private CorporationService corporationService;
//
//    @MockBean
//    ProgramCorporationService programCorporationService;
//
//    private Integer businessId = 2;
//    private String businessName = "business_name";
//    private Integer programId = 3;
//    private String programCode = "programCode";
//    private String programCodeUpdate = "programCodeUpdate";
//    private Integer programIdUpdate = 43;
//    private String programName = "programName";
//    private String programDescription = "programDescription";
//    private Integer corporationId = 21;
//    private String corporationName = "Name";
//
//    private Business business = new Business();
//    private Corporation corporation = new Corporation();
//    private CorporationInfo corporationInfo = new CorporationInfo();
//    private ProgramCorporation programCorporation = new ProgramCorporation();
//
//    Set<Integer> corporationIds = new HashSet<>();
//    private List<Business> listBusiness = new ArrayList<>();
//    private List<Program> listProgram = new ArrayList<>();
//    private List<ProgramRes> listProgramRes = new ArrayList<>();
//    private List<Corporation> listCorporation = new ArrayList<>();
//    private Set<CorporationInfo> listCorporationInfo = new HashSet<>();
//    private List<ProgramCorporation> listProgramCorporation = new ArrayList<>();
//
//    private UpdateProgramReq updateReq = new UpdateProgramReq();
//
//    private ProgramRes programRes = new ProgramRes();
//    private CreateProgramReq reqCreate = new CreateProgramReq();
//
//    private Program program;
//    private Program programUpdate = new Program();
//    private ProgramRes programUpdateRes = new ProgramRes();
//    private SearchProgramReq searchReq = new SearchProgramReq();
//
//    SpecificationBuilder<Program> specificationBuilder = new SpecificationBuilder<>();
//    Pageable pageRequest = new OffsetBasedPageRequest(0, 1);
//    Page<Program> programPage;
//
//
//    @MockBean
//    ProgramProductRepository productRepository;
//
//
//    @Before
//    public void before() {
//        program = ProgramMapper.toProgram(new CreateProgramReq());
//        program.setBusinessId(businessId);
//        program.setId(programId);
//        program.setName(programName);
//        program.setCode(programCode);
//
//        programRes.setBusinessId(businessId);
//        programRes.setProgramId(programId);
//        programRes.setProgramCode(programCode);
//        programRes.setProgramName(programName);
//        programRes.setBusinessName(businessName);
//        programRes.setStatus(ECommonStatus.INACTIVE);
//        programRes.setCorporationsInfo(listCorporationInfo);
//
//        listProgramRes.add(programRes);
//
//        business.setId(businessId);
//        business.setName(businessName);
//
//        listBusiness.add(business);
//
//        corporation.setBusinessId(businessId);
//        corporation.setId(corporationId);
//        corporation.setName(corporationName);
//        listCorporation.add(corporation);
//
//        programCorporation.setCorporationId(corporationId);
//        programCorporation.setProgramId(programId);
//        listProgramCorporation.add(programCorporation);
//
//        corporationInfo.setName(corporationName);
//        corporationInfo.setId(corporationId);
//        corporationInfo.setBusinessId(businessId);
//        listCorporationInfo.add(corporationInfo);
//
//        programUpdate.setId(programIdUpdate);
//        programUpdate.setBusinessId(businessId);
//        programUpdate.setCode(programCodeUpdate);
//        programUpdate.setName(programName);
//        programUpdate.setDescription(programDescription);
//        programUpdate.setStatus(ECommonStatus.ACTIVE);
//
//        updateReq.setProgramId(programIdUpdate);
//        updateReq.setProgramName(programName);
//        updateReq.setDescription(programDescription);
//        updateReq.setStatus(ECommonStatus.ACTIVE);
//        updateReq.setListCorporationId(new HashSet<>());
//
//        programUpdateRes.setProgramId(programIdUpdate);
//        programUpdateRes.setBusinessId(businessId);
//        programUpdateRes.setProgramCode(programCodeUpdate);
//        programUpdateRes.setProgramName(programName);
//        programUpdateRes.setDescription(programDescription);
//        programUpdateRes.setStatus(ECommonStatus.ACTIVE);
//
//        searchReq.setStatus(ECommonStatus.ACTIVE);
//        searchReq.setProgramCode(programCode);
//        searchReq.setProgramId(programId);
//        searchReq.setProgramName(programName);
//        searchReq.setBusinessId(null);
//
//        reqCreate.setBusinessId(businessId);
//        reqCreate.setProgramCode(programCode);
//        corporationIds.add(corporationId);
//        reqCreate.setListCorporationId(corporationIds);
//        updateReq.setListCorporationId(corporationIds);
//
//        Mockito.when(programCorporationService.findByProgramId(programId)).thenReturn(listProgramCorporation);
//        updateReq.setListCorporationId(corporationIds);
//        Mockito.when(programService.create(program)).thenReturn(program);
//        Mockito.when(programService.find(programId)).thenReturn(Optional.of(program));
//        Mockito.when(programService.find(programIdUpdate)).thenReturn(Optional.of(programUpdate));
//        Mockito.when(programService.update(programUpdate)).thenReturn(programUpdate);
//
//        Mockito.when(businessService.findActive(businessId)).thenReturn(business);
//        Mockito.when(corporationService.findByBusinessId(businessId)).thenReturn(listCorporation);
//        Mockito.when(businessService.searchAll(Mockito.any())).thenReturn(listBusiness);
//    }
//
//    @Test
//    public void create() {
//        ProgramRes exp = opsProgramService.create(reqCreate);
//        Assert.assertEquals(exp, programRes);
//    }
//
//    @Test
//    public void create_emptyCorporation() {
//        reqCreate.setListCorporationId(new HashSet<>());
//        programRes.setCorporationsInfo(new HashSet<>());
//        ProgramRes exp = opsProgramService.create(reqCreate);
//        Assert.assertEquals(exp, programRes);
//    }
//
//    @Test
//    public void create_nullCorporation() {
//        reqCreate.setListCorporationId(null);
//        programRes.setCorporationsInfo(new HashSet<>());
//        Mockito.when(programCorporationService.findByProgramId(programId)).thenReturn(new ArrayList<>());
//        ProgramRes exp = opsProgramService.create(reqCreate);
//        Assert.assertEquals(exp, programRes);
//    }
//
//    @Test
//    public void getOne() {
//        ProgramRes exp = opsProgramService.getOne(programId);
//        Assert.assertEquals(exp, programRes);
//    }
//
//    @Test
//    public void update_hasCreateAndNothing() {
//        Mockito.when(programCorporationService.findByProgramId(programIdUpdate)).thenReturn(new ArrayList<>());
//        updateReq.setListCorporationId(corporationIds);
//        programUpdateRes.setCorporationsInfo(listCorporationInfo);
//        Mockito.when(programService.find(programIdUpdate)).thenReturn(Optional.of(programUpdate));
//        ProgramRes exp = opsProgramService.update(updateReq);
//        Assert.assertEquals(exp, programUpdateRes);
//    }
//
//    @Test
//    public void update_hasRemove() {
//        updateReq.setListCorporationId(new HashSet<>());
//        programUpdateRes.setCorporationsInfo(new HashSet<>());
//        Mockito.when(programCorporationService.findByProgramId(programIdUpdate)).thenReturn(listProgramCorporation);
//        Mockito.when(programService.find(programIdUpdate)).thenReturn(Optional.of(programUpdate));
//        ProgramRes exp = opsProgramService.update(updateReq);
//        Assert.assertEquals(exp, programUpdateRes);
//    }
//
//    @Test
//    public void programNotFound() {
//        Mockito.when(programService.find(programId)).thenReturn(Optional.ofNullable(null));
//        try {
//            opsProgramService.getOne(programId);
//        } catch (BusinessException e) {
//            Assert.assertEquals(ErrorCode.PROGRAM_NOT_FOUND, e.getCode());
//        }
//    }
//
//    @Test
//    public void programCodeExisted() {
//        CreateProgramReq createProgramReq = new CreateProgramReq();
//        createProgramReq.setProgramCode(programCode);
//        createProgramReq.setBusinessId(businessId);
//        Mockito.when(programService.find(businessId, programCode)).thenReturn(Optional.of(program));
//        try {
//            opsProgramService.create(createProgramReq);
//        } catch (BusinessException e) {
//            Assert.assertEquals(ErrorCode.PROGRAM_CODE_EXISTED, e.getCode());
//        }
//    }
//
//    @Test
//    public void search() {
//        // cannot get info corporation
//        listProgram.add(program);
//        programPage = new PageImpl<Program>(listProgram, pageRequest, 1);
//        programRes.setCorporationsInfo(null);
//        listProgramRes.clear();
//        listProgramRes.add(programRes);
//        Mockito.when(programCorporationService.findByProgramId(program.getId()))
//                .thenReturn(listProgramCorporation);
//        Mockito.when(programService.searchPaging(Mockito.any(), Mockito.any())).thenReturn(programPage);
//        Page<ProgramRes> exp = opsProgramService.search(searchReq, pageRequest);
//        Assert.assertEquals(exp, new PageImpl<>(listProgramRes, pageRequest, 1));
//    }
//
//    @Test
//    public void filter() {
//        listProgram.add(program);
//        List<ProgramDropDownRes> actual = new ArrayList<>();
//        ProgramDropDownRes programDropDownRes = ProgramDropDownRes.valueOf(program);
//        actual.add(programDropDownRes);
//        ECommonStatus status = ECommonStatus.ACTIVE;
//        ProgramGetAllReq programGetAllReq = new ProgramGetAllReq();
//        programGetAllReq.setStatus(status);
//        programGetAllReq.setBusinessId(businessId);
//
//        Mockito.when(programService.searchAll(Mockito.any())).thenReturn(listProgram);
//        List<ProgramDropDownRes> exp = opsProgramService.filter(programGetAllReq);
//        Assert.assertEquals(exp, actual);
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public OpsProgramService loyaltyProgramService() {
//            return new OpsProgramServiceImpl();
//        }
//    }
//}