package com.oneid.loyalty.accounting.ops.controller;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import com.oneid.loyalty.accounting.ops.service.CommonCheckLogService;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.request.RequestPostProcessor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.UsersController;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.AddCardWithMemberRegistrationReq;
import com.oneid.loyalty.accounting.ops.model.req.CreateMemberReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateMemberReq;
import com.oneid.loyalty.accounting.ops.service.OpsGenerateTemplateService;
import com.oneid.loyalty.accounting.ops.service.OpsMemberService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.oneloyalty.common.constant.EIdentifyType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;

@Import(UsersController.class)
public class UsersControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @MockBean
    OpsMemberService opsMemberService;

    @MockBean
    OpsGenerateTemplateService opsGenerateTemplateService;

    @MockBean
    CommonCheckLogService commonCheckLogService;
    
    @After
    public void teardown() {
        defaultTeardown();
        
        Mockito.reset(opsMemberService);
        Mockito.reset(opsGenerateTemplateService);
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void getMemberProfiles_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Page mockPage = Mockito.mock(Page.class);
        
        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);
        
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        
        Mockito.when(opsMemberService.getMemberProfiles(mockBusinessId, mockProgramId, null, null, null, null, 20, 0, null, null, null))
        .thenReturn(mockPage);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString())
                .queryParam("program_id", mockProgramId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void getMemberProfiles_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        Integer mockBusinessId = 1;
        Integer mockProgramId = 1;
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString())
                .queryParam("program_id", mockProgramId.toString());
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).getMemberProfiles(mockBusinessId, mockProgramId, null, null, null, null, 20, 0, null, null, null);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getMemberProfileById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/member/{memberId}/profile", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(1)).getProfileById(any());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void getMemberProfileById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/member/{memberId}/profile", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).getProfileById(any());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void updateMember_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.EDIT))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        UpdateMemberReq req = new UpdateMemberReq();
        
        req.setFirstName("A");
        req.setLastName("Mr");
        req.setFullName("Mr A");
        req.setIdentifyType(EIdentifyType.IDENTIFICATION_CARD);
        req.setIdentifyNo("024101202");
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/users/member/{memberId}/update", Long.MAX_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsMemberService, times(1)).updateMember(Long.MAX_VALUE, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void updateMember_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        UpdateMemberReq req = new UpdateMemberReq();
        
        req.setFirstName("A");
        req.setLastName("Mr");
        req.setFullName("Mr A");
        req.setIdentifyType(EIdentifyType.IDENTIFICATION_CARD);
        req.setIdentifyNo("024101202");
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/users/member/{memberId}/update", Long.MAX_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).updateMember(Long.MAX_VALUE, req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void addMember_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.CREATE))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        CreateMemberReq req = new CreateMemberReq();
        
        AddCardWithMemberRegistrationReq cardReq = new AddCardWithMemberRegistrationReq();
        
        cardReq.setCardNo("8888200037071564");
        cardReq.setCardTypeId(161);
        
        req.setFirstName("A");
        req.setLastName("Mr");
        req.setFullName("Mr A");
        req.setIdentifyType(EIdentifyType.IDENTIFICATION_CARD);
        req.setIdentifyNo("024101202");
        req.setPhoneNo("0383565675");
        req.setBusinessCode("VGC");
        req.setProgramCode("VGC");
        req.setStoreCode("VIN");
        req.setCard(cardReq);
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/users/member/add")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsMemberService, times(1)).addMember(req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void addMember_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        CreateMemberReq req = new CreateMemberReq();
        
        AddCardWithMemberRegistrationReq cardReq = new AddCardWithMemberRegistrationReq();
        
        cardReq.setCardNo("8888200037071564");
        cardReq.setCardTypeId(161);
        
        req.setFirstName("A");
        req.setLastName("Mr");
        req.setFullName("Mr A");
        req.setIdentifyType(EIdentifyType.IDENTIFICATION_CARD);
        req.setIdentifyNo("024101202");
        req.setPhoneNo("0383565675");
        req.setBusinessCode("VGC");
        req.setProgramCode("VGC");
        req.setStoreCode("VIN");
        req.setCard(cardReq);
        
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/users/member/add")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(req));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
        .andExpect(status().isOk())
        .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).addMember(req);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void getTransactionByMemberCode_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/{memberId}/transactions", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsTransactionService, times(0)).search(any(), any(), any());
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void getTransactionByMemberCode_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TRANSACTION))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Page mockPage = Mockito.mock(Page.class);
        
        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);
        
        Mockito.when(opsTransactionService.search(any(), any(), any()))
        .thenReturn(mockPage);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/{memberId}/transactions", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("member_id", String.valueOf(Long.MAX_VALUE));
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        BaseResponseData  baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    @SuppressWarnings({ "rawtypes", "unchecked" })
    public void getMemberBalanceById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER_BALANCE))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        APIResponse mockAPIResponse = new APIResponse<String>();
        
        mockAPIResponse.setData(new String());
        mockAPIResponse.setMeta(new Meta(ErrorCode.SUCCESS.getValue(), ""));
        
        Mockito.when(opsMemberService.getMemberBalanceById(Long.MAX_VALUE)).thenReturn(mockAPIResponse);
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/member/{memberId}/balance", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(1)).getMemberBalanceById(Long.MAX_VALUE);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }
    
    @Test
    public void getMemberBalanceById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/member/{memberId}/balance", Long.MAX_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).getMemberBalanceById(Long.MAX_VALUE);
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void downloadRegisterMemberTemplate_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.EXPORT))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        Path tmp = Files.createTempFile(null, null);
        
        tmp.toFile().deleteOnExit();
        
        Mockito.when(opsGenerateTemplateService.registerMember()).thenReturn(Files.createTempFile(null, null));
        
        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/users/bulk/registration/template-file")
                .accept(MediaType.APPLICATION_JSON_VALUE);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        MockHttpServletResponse response = result.getResponse();
        
        assertEquals("", response.getContentAsString());
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8", response.getContentType());
        
    }
    
    @Test
    public void registerMemberByBatchFile_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());
        
        MockMultipartFile firstFile = new MockMultipartFile("files", "test.xlsx", MediaType.APPLICATION_OCTET_STREAM_VALUE, "".getBytes());
       
        MockHttpServletRequestBuilder servletRequestBuilder = MockMvcRequestBuilders.multipart("/v1/users/bulk/registration")
                .file(firstFile)
                .contentType(MediaType.APPLICATION_OCTET_STREAM);
        
        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        
        Mockito.verify(opsMemberService, times(0)).registerMemberByBatchFile(any(), any());
        
        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);
        
        assertNotNull(baseResponseData);
        
        Meta meta = baseResponseData.getMeta();
        
        assertNotNull(meta);
        
        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
    
    @Test
    public void registerMemberByBatchFile_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.MEMBER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.IMPORT))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));
        
        mockOPSAuthenticatedPrincipal(permissions);
        
        MockMultipartFile firstFile = new MockMultipartFile("files", "test.xlsx", MediaType.APPLICATION_OCTET_STREAM_VALUE, "".getBytes());
        
        Path tmp = Files.createTempFile(null, null);
        
        tmp.toFile().deleteOnExit();
        
        ResponseEntity<Resource> responseEntity = ResponseEntity.ok(new FileSystemResource(tmp));
        
        Mockito.when(opsMemberService.registerMemberByBatchFile(any(), any())).thenReturn(responseEntity);
        
        MockHttpServletRequestBuilder servletRequestBuilder = MockMvcRequestBuilders.multipart("/v1/users/bulk/registration")
                .file(firstFile)
                .contentType(MediaType.APPLICATION_OCTET_STREAM);
        
        
        MvcResult result = mockMvc.perform(servletRequestBuilder.with(new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setUserPrincipal(authentication);
                return request;
            }
        }))
        .andExpect(status().isOk())
        .andReturn();
        
        MockHttpServletResponse response = result.getResponse();
        
        assertEquals("", response.getContentAsString());
    }
}
