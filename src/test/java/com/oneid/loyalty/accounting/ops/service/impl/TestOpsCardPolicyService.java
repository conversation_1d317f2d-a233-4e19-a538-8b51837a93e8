package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.constant.EOpsCardExpireUnit;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import com.oneid.loyalty.accounting.ops.model.res.CardPolicyRes;
import com.oneid.loyalty.accounting.ops.service.OpsCardPolicyService;
import com.oneid.oneloyalty.common.constant.*;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CardPolicyRepository;
import com.oneid.oneloyalty.common.service.BusinessService;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.Validation;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;

@RunWith(SpringRunner.class)
@Ignore
public class TestOpsCardPolicyService {

    @Autowired
    OpsCardPolicyService opsCardPolicyService;

    @MockBean
    private CardPolicyRepository cardPolicyRepository;

    @MockBean
    private BusinessService businessService;

    /* INIT VALUE */
    private Integer businessId = 4576;
    private String businessCode = "BUSINESS_CODE";
    private Business business = new Business();

    private Integer cardPolicyId = 8;
    private String name = "card policy name";
    private String description = "card policy description";
    private Integer length = 24;
    private CardPolicy cardPolicy = new CardPolicy();
    long expireValue = 19l;
    Integer maxCardPerCPR = 1999;
    /* INIT VALUE */

    private CardPolicy clone(CardPolicy cardPolicy) {
        CardPolicy cardPolicyRes = new CardPolicy();
        cardPolicyRes.setPolicyType(cardPolicy.getPolicyType());
        cardPolicyRes.setId(cardPolicy.getId());
        cardPolicyRes.setRetentionTrigger(cardPolicy.getRetentionTrigger());
        cardPolicyRes.setCardNoLength(cardPolicy.getCardNoLength());
        cardPolicyRes.setRetentionPeriod(cardPolicy.getRetentionPeriod());
        cardPolicyRes.setCode(cardPolicy.getCode());
        cardPolicyRes.setRetentionType(cardPolicy.getRetentionType());
        cardPolicyRes.setExpirationUnit(cardPolicy.getExpirationUnit());
        cardPolicyRes.setExpirationPeriod(cardPolicy.getExpirationPeriod());
        cardPolicyRes.setExpirationTrigger(cardPolicy.getExpirationTrigger());
        cardPolicyRes.setBusinessId(cardPolicy.getBusinessId());
        cardPolicyRes.setExpirationFixedTime(cardPolicy.getExpirationFixedTime());
        cardPolicyRes.setName(cardPolicy.getName());
        cardPolicyRes.setDescription(cardPolicy.getDescription());
        return cardPolicyRes;
    }


    @Before
    public void before() {
        business.setId(businessId);
        business.setCode(businessCode);
        Mockito.when(businessService.findActive(businessId)).thenReturn(business);
        Mockito.when(businessService.find(businessId)).thenReturn(Optional.ofNullable(business));

        cardPolicy.setName(name);
        cardPolicy.setDescription(description);
        cardPolicy.setPolicyType(ECardPolicyType.MEMBER_CARD);
        cardPolicy.setBusinessId(businessId);
        cardPolicy.setCardNoLength(length);
        cardPolicy.setMaxCardCpr(maxCardPerCPR);
        cardPolicy.setStatus(ECommonStatus.ACTIVE);
        cardPolicy.setExpirationUnit(ECardExpireUnit.DAY);
        cardPolicy.setExpirationPeriod((int) expireValue);
        cardPolicy.setExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        cardPolicy.setRetentionType(ERetensionType.END_DAY);
        cardPolicy.setRetentionPeriod(56789);
        cardPolicy.setHasChecksum(EHasChecksum.YES);
        cardPolicy.setRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
    }

    @Test
    public void create_success_day() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.DAY);
        req.setCardExpirationValue(expireValue);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(59l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }

    @Test
    public void create_success_month() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.MONTH);
        req.setCardExpirationValue(expireValue);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(988l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }

    @Test
    public void create_success_year() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.YEAR);
        req.setCardExpirationValue(expireValue);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(789l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }

    @Test
    public void create_success_never() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.NEVER);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(59l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }


    @Test
    public void create_success_fixed_time() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.FIXED_TIME);
        req.setCardExpirationValue(5678900000l);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(56789l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }

    @Test
    public void create_success_n_day() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setName(name);
        req.setDescription(description);
        req.setType(ECardPolicyType.MEMBER_CARD);
        req.setBusinessId(businessId);
        req.setLength(length);
        req.setMaxCardPerCPR(maxCardPerCPR);
        req.setStatus(ECommonStatus.ACTIVE);
        req.setCardExpirationType(EOpsCardExpireUnit.FIXED_TIME);
        req.setCardExpirationValue(5678900000l);
        req.setCardExpirationTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setCardRetentionType(ERetensionType.N_DAY);
        req.setCardRetentionValue(56789l);
        req.setCardRetentionTrigger(ECardPolicyTrigger.IMMEDIATELY);
        req.setHasChecksum(EHasChecksum.YES);
        Set<?> violationSet = Validation.buildDefaultValidatorFactory().getValidator().validate(req);
        Assert.assertTrue(violationSet.isEmpty());
        Mockito.when(cardPolicyRepository.save(cardPolicy)).thenReturn(cardPolicy);

        cardPolicy.setId(cardPolicyId);
        CardPolicyRes exp = opsCardPolicyService.create(req);
        CardPolicyRes act = CardPolicyRes.valueOf(cardPolicy);
        Assert.assertEquals(exp, act);
    }

    @Test
    public void update_success() {
        String nameUpdate = "nameUpdate";
        String desUpdate = "desUpdate";
        ECommonStatus statusUpdate = ECommonStatus.INACTIVE;
        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setId(cardPolicy.getId());
        req.setName(nameUpdate);
        req.setDescription(desUpdate);
        req.setStatus(ECommonStatus.ACTIVE);
        Mockito.when(cardPolicyRepository.findById(req.getId())).thenReturn(Optional.ofNullable(cardPolicy));
        CardPolicy cardPolicyUpdate = clone(cardPolicy);
        cardPolicyUpdate.setDescription(desUpdate);
        cardPolicyUpdate.setName(nameUpdate);
        cardPolicyUpdate.setStatus(statusUpdate);
        Mockito.when(cardPolicyRepository.save(cardPolicyUpdate)).thenReturn(cardPolicyUpdate);
        Assert.assertEquals(opsCardPolicyService.update(req), CardPolicyRes.valueOf(cardPolicyUpdate));
    }

    @Test
    public void update_cardPolicyNotFound() {
        try {
            String nameUpdate = "nameUpdate";
            String desUpdate = "desUpdate";
            ECommonStatus statusUpdate = ECommonStatus.INACTIVE;
            CardPolicyUpdateReq req = new CardPolicyUpdateReq();
            req.setId(cardPolicy.getId());
            req.setName(nameUpdate);
            req.setDescription(desUpdate);
            req.setStatus(ECommonStatus.ACTIVE);
            opsCardPolicyService.update(req);
            Assert.fail("Expected exception card_policy_not_found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_FOUND);
        }
    }

    @Test
    public void update_cardPolicyNotActive() {
        try {
            String nameUpdate = "nameUpdate";
            String desUpdate = "desUpdate";
            CardPolicyUpdateReq req = new CardPolicyUpdateReq();
            req.setId(cardPolicy.getId());
            req.setName(nameUpdate);
            req.setDescription(desUpdate);
            req.setStatus(ECommonStatus.ACTIVE);
            CardPolicy cardPolicyNotActive = clone(cardPolicy);
            cardPolicyNotActive.setStatus(ECommonStatus.INACTIVE);
            Mockito.when(cardPolicyRepository.findById(cardPolicyNotActive.getId()))
                    .thenReturn(Optional.ofNullable(cardPolicyNotActive));
            opsCardPolicyService.update(req);
            Assert.fail("Expected exception card_policy_not_active here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_ACTIVE);
        }
    }

    @Test
    public void update_cannotUpdate() {
        try {
            String nameUpdate = "nameUpdate";
            String desUpdate = "desUpdate";
            CardPolicyUpdateReq req = new CardPolicyUpdateReq();
            req.setId(cardPolicy.getId());
            req.setName(nameUpdate);
            req.setDescription(desUpdate);
            req.setStatus(ECommonStatus.INACTIVE);
            CardPolicy cardPolicyNotActive = clone(cardPolicy);
            cardPolicyNotActive.setStatus(ECommonStatus.ACTIVE);
            Mockito.when(cardPolicyRepository.findById(cardPolicyNotActive.getId()))
                    .thenReturn(Optional.ofNullable(cardPolicyNotActive));
            opsCardPolicyService.update(req);
            Assert.fail("Expected exception CANNOT_UPDATE here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CANNOT_UPDATE);
        }
    }

    @Test
    public void searchPage_success() {
        CardPolicySearchReq req = new CardPolicySearchReq();
        req.setCardPolicyType(ECardPolicyType.GIFT_CARD);
        req.setBusinessId(businessId);
        req.setStatus(ECommonStatus.ACTIVE);

        Pageable pageable = new OffsetBasedPageRequest(10,10);

        Mockito.when(cardPolicyRepository.findAll((Specification<CardPolicy>) Mockito.any(), (Pageable) Mockito.any()))
                .thenReturn(new PageImpl<>(Collections.EMPTY_LIST, pageable, 0));

        Assert.assertEquals(opsCardPolicyService.searchPage(req, pageable).getContent().size(),0);
    }

    @Test
    public void viewDetail() {
        Mockito.when(cardPolicyRepository.findById(cardPolicy.getId())).thenReturn(Optional.ofNullable(cardPolicy));
        Assert.assertEquals(opsCardPolicyService.viewDetails(cardPolicy.getId()), CardPolicyRes.valueOf(cardPolicy));
    }

    @Test
    public void viewDetail_cardPolicyNotFound() {

        try {
            opsCardPolicyService.viewDetails(cardPolicy.getId());
            Assert.fail("Card policy not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_FOUND);
        }
    }

    @TestConfiguration
    public static class Configuration {
        @Bean
        public OpsCardPolicyService opsCardPolicyService() {
            return new OpsCardPolicyServiceImpl();
        }
    }
}