package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.req.CardPolicyCreateReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicySearchReq;
import com.oneid.loyalty.accounting.ops.model.req.CardPolicyUpdateReq;
import com.oneid.loyalty.accounting.ops.service.OpsCardPolicyService;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECardPolicyType;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ERetensionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.CardPolicy;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.CardPolicyRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.BusinessService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Optional;

@RunWith(SpringRunner.class)
public class OpsCardPolicyServiceImplTest {
    @Autowired
    private OpsCardPolicyService opsCardPolicyService;

    @MockBean
    private CardPolicyRepository cardPolicyRepository;

    @MockBean
    private BusinessService businessService;

    private Business business;

    @Before
    public void before() {
        business = new Business();
        business.setId(1);
        business.setCode("BUSINESS");
        business.setName("business name");
    }

    @Test
    public void create() {
        CardPolicyCreateReq req = new CardPolicyCreateReq();
        req.setBusinessId(business.getId());
        req.setCardRetentionType(ERetensionType.END_DAY);
        req.setCardRetentionValue(1L);

        CardPolicy newCardPolicy = new CardPolicy();

        Mockito.when(businessService.findActive(business.getId())).thenReturn(new Business());
        Mockito.when(cardPolicyRepository.save(Mockito.any())).thenReturn(newCardPolicy);
        opsCardPolicyService.create(req);
        Mockito.verify(businessService, Mockito.times(1)).findActive(Mockito.any());
    }

    @Test
    public void update() {
        Integer cardPolicyId = 1;
        CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setId(cardPolicyId);
        cardPolicy.setStatus(ECommonStatus.INACTIVE);
        cardPolicy.setBusinessId(business.getId());

        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setId(cardPolicyId);
        req.setStatus(ECommonStatus.ACTIVE);

        Mockito.when(cardPolicyRepository.findById(cardPolicyId)).thenReturn(Optional.of(cardPolicy));
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(cardPolicyRepository.save(Mockito.any())).thenReturn(cardPolicy);

        opsCardPolicyService.update(req);
        Mockito.verify(businessService, Mockito.times(1)).findActive(Mockito.any());
    }

    @Test
    public void update_cardPolicyNotFound() {
        Integer cardPolicyId = 1;
        CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setId(cardPolicyId);
        cardPolicy.setStatus(ECommonStatus.INACTIVE);
        cardPolicy.setBusinessId(business.getId());

        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setId(cardPolicyId);
        req.setStatus(ECommonStatus.ACTIVE);

        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(cardPolicyRepository.save(Mockito.any())).thenReturn(cardPolicy);

        try {
            opsCardPolicyService.update(req);
            Assert.fail("Card policy not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_FOUND);
        }
    }

    @Test
    public void update_cannotUpdate() {
        Integer cardPolicyId = 1;
        CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setId(cardPolicyId);
        cardPolicy.setStatus(ECommonStatus.ACTIVE);
        cardPolicy.setBusinessId(business.getId());

        CardPolicyUpdateReq req = new CardPolicyUpdateReq();
        req.setId(cardPolicyId);
        req.setStatus(ECommonStatus.INACTIVE);
        req.setQrUrl("test");

        Mockito.when(cardPolicyRepository.findById(cardPolicyId)).thenReturn(Optional.of(cardPolicy));
        Mockito.when(businessService.findActive(business.getId())).thenReturn(business);
        Mockito.when(cardPolicyRepository.save(Mockito.any())).thenReturn(cardPolicy);

        try {
            opsCardPolicyService.update(req);
            Assert.fail("Cannot update card policy status from active to other status");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CANNOT_UPDATE);
        }
        Mockito.verify(businessService, Mockito.times(1)).findActive(Mockito.any());
    }

    @Test
    public void viewDetail() {
        CardPolicy cardPolicy = new CardPolicy();
        cardPolicy.setBusinessId(business.getId());
        Mockito.when(businessService.find(business.getId())).thenReturn(Optional.of(business));
        Mockito.when(cardPolicyRepository.findById(1)).thenReturn(Optional.of(cardPolicy));
        opsCardPolicyService.viewDetails(1);
    }

    @Test
    public void viewDetail_internalServerError() {
        Mockito.when(cardPolicyRepository.findById(1)).thenReturn(Optional.of(new CardPolicy()));
        try {
            opsCardPolicyService.viewDetails(1);
            Assert.fail("Not found business, conflict data");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.SERVER_ERROR);
        }
    }

    @Test
    public void viewDetail_cardPolicyNotFound() {
        try {
            opsCardPolicyService.viewDetails(1);
            Assert.fail("card policy not found here");
        } catch (BusinessException e) {
            Assert.assertEquals(e.getCode(), ErrorCode.CARD_POLICY_NOT_FOUND);
        }
    }

    @Test
    public void searchPage() {
        CardPolicySearchReq req = new CardPolicySearchReq();
        req.setBusinessId(business.getId());
        req.setCardPolicyType(ECardPolicyType.MEMBER_CARD);
        req.setStatus(ECommonStatus.ACTIVE);

        Pageable pageRequest = new OffsetBasedPageRequest(1, 1, null);
        Mockito.when(cardPolicyRepository.findAll((SpecificationBuilder) Mockito.any(), (Pageable) Mockito.any())).
                thenReturn(new PageImpl(new ArrayList(), pageRequest, 0));
        opsCardPolicyService.searchPage(req, pageRequest);
    }


    @TestConfiguration
    public static class classConfiguration {
        @Bean
        public OpsCardPolicyService opsCardPolicyService() {
            return new OpsCardPolicyServiceImpl();
        }
    }
}
