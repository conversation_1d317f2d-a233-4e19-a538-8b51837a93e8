package com.oneid.loyalty.accounting.ops.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.oneid.loyalty.accounting.ops.feign.MakerCheckerFeignClient;
import com.oneid.loyalty.accounting.ops.feign.model.res.ChangeRequestPageFeignRes;
import com.oneid.loyalty.accounting.ops.model.APIResponse;
import com.oneid.loyalty.accounting.ops.model.req.CreateLimitationReq;
import com.oneid.loyalty.accounting.ops.model.req.FilterLimitationAvailableReq;
import com.oneid.loyalty.accounting.ops.model.req.RuleReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateLimitationReq;
import com.oneid.loyalty.accounting.ops.service.OpsLimitationService;
import com.oneid.loyalty.accounting.ops.service.OpsRuleRequestService;
import com.oneid.loyalty.accounting.ops.support.data.databind.DateDeserializer;
import com.oneid.loyalty.accounting.ops.validation.OpsCode;
import com.oneid.loyalty.accounting.ops.validation.OpsName;
import com.oneid.oneloyalty.common.constant.EApprovalStatus;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EServiceType;
import com.oneid.oneloyalty.common.entity.*;
import com.oneid.oneloyalty.common.repository.*;
import com.oneid.oneloyalty.common.service.CounterHistoryService;
import com.oneid.oneloyalty.common.service.ProgramService;
import com.oneid.oneloyalty.common.service.ProgramTierPolicyService;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.validator.constraints.Length;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

//@RunWith(SpringRunner.class)
//@TestPropertySource(properties = "maker-checker.module.limitation:limitation:123")
public class OpsLimitationServiceImplTest {

//    @Autowired
//    private OpsLimitationService opsLimitationService;
//
//    @Value("${maker-checker.module.limitation:limitation}")
//    private String moduleId;
//
//    @MockBean
//    private PlatformTransactionManager platformTransactionManager;
//
//    @MockBean
//    private TransactionTemplate transactionTemplate;
//
//    @MockBean
//    private OpsRuleRequestService opsRuleRequestService;
//
//    @MockBean
//    private MakerCheckerFeignClient makerCheckerServiceClient;
//
//    @MockBean
//    private ProgramService programService;
//
//    @MockBean
//    private BusinessRepository businessRepository;
//
//    @MockBean
//    private CounterRepository counterRepository;
//
//    @MockBean
//    private LimitationRepository limitationRepository;
//
//    @MockBean
//    private LimitationRequestRepository requestRepository;
//
//    @MockBean
//    private CounterRequestRepository counterRequestRepository;
//
//    @MockBean
//    private CounterHistoryService counterHistoryService;
//
//    @MockBean
//    private ProgramTierPolicyService programTierPolicyService;
//
//    @MockBean
//    private ProgramTierPolicyRepository programTierPolicyRepository;
//
//    @MockBean
//    private ObjectMapper objectMapper;
//
//    @MockBean
//    private CounterHistoryRepository counterHistoryRepository;

//    @Test
//    public void createRequest() {
//        Date startDate = DateUtils.addDays(new Date(), 1);
//        Date endDate = DateUtils.addDays(new Date(), 2);
//
//        CreateLimitationReq req = CreateLimitationReq.builder()
//                .name("name")
//                .code("code")
//                .description("description")
//                .businessId(1)
//                .programId(2)
//                .startDate(startDate)
//                .endDate(endDate)
//                .status(ECommonStatus.ACTIVE)
//                .threshold(BigDecimal.ONE)
//                .rules(new ArrayList<>())
//                .build();
//
//        Business business = new Business();
//        business.setStatus(ECommonStatus.ACTIVE);
//
//        Counter counter = new Counter();
//        counter.setProgramId(req.getProgramId());
//        counter.setBusinessId(req.getBusinessId());
//        counter.setStatus(ECommonStatus.ACTIVE);
//
//        LimitationRequest limitationRequest = new LimitationRequest();
//        limitationRequest.setId(1);
//
//        Mockito.when(businessRepository.findById(1))
//                .thenReturn(Optional.of(business));
//        Mockito.when(programService.findActive(req.getProgramId()))
//                .thenReturn(new Program());
//        Mockito.when(counterRepository.findById(req.getCounterId()))
//                .thenReturn(Optional.of(counter));
//        Mockito.when(transactionTemplate.execute(Mockito.any()))
//                .thenReturn(limitationRequest);
//        Mockito.when(requestRepository.save(Mockito.any()))
//                .thenReturn(limitationRequest);
//
//        opsLimitationService.createRequest(req);
//    }

//    @Test
//    public void getAvailableLimitationByRequestId() {
//        LimitationRequest limitationRequest = new LimitationRequest();
//        limitationRequest.setId(1);
//        limitationRequest.setProgramId(2);
//        limitationRequest.setBusinessId(3);
//        limitationRequest.setCounterId(4);
//        limitationRequest.setStartDate(new Date());
//
//        Program program = new Program();
//        program.setBusinessId(3);
//
//        Counter counter = new Counter();
//        counter.setEndDate(DateUtils.addDays(new Date(), 1));
//
//        Mockito.when(requestRepository.findById(Mockito.any()))
//                .thenReturn(Optional.of(limitationRequest));
//        Mockito.when(programService.find(2)).thenReturn(Optional.of(program));
//        Mockito.when(businessRepository.findById(3)).thenReturn(Optional.of(new Business()));
//        Mockito.when(counterRepository.findById(4)).thenReturn(Optional.of(counter));
//
//        opsLimitationService.getAvailableLimitationByRequestId(1);
//    }

//    @Test
//    public void getInReviewLimitationRequestById() {
//        LimitationRequest limitationRequest = new LimitationRequest();
//        limitationRequest.setId(1);
//        limitationRequest.setProgramId(2);
//        limitationRequest.setBusinessId(3);
//        limitationRequest.setCounterId(4);
//        limitationRequest.setStartDate(new Date());
//
//        Program program = new Program();
//        program.setBusinessId(3);
//
//        Counter counter = new Counter();
//        counter.setEndDate(DateUtils.addDays(new Date(), 1));
//
//        APIResponse<ChangeRequestPageFeignRes.ChangeRecordFeginRes> response = new APIResponse<>();
//        ChangeRequestPageFeignRes.ChangeRecordFeginRes res = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
//        res.setChangeRequestId(123);
//        res.setObjectId("1");
//        response.setData(res);
//
//        Mockito.when(requestRepository.findById(Mockito.any()))
//                .thenReturn(Optional.of(limitationRequest));
//        Mockito.when(programService.find(2)).thenReturn(Optional.of(program));
//        Mockito.when(businessRepository.findById(3)).thenReturn(Optional.of(new Business()));
//        Mockito.when(counterRepository.findById(4)).thenReturn(Optional.of(counter));
//        Mockito.when(makerCheckerServiceClient.getChangeRequestById("123"))
//                .thenReturn(response);
//
//        opsLimitationService.getInReviewLimitationRequestById(123);
//    }

//    @Test
//    public void getAvailableCounterRuleById() {
//        Mockito.when(counterRepository.findById(1)).thenReturn(Optional.of(new Counter()));
//        Mockito.when(counterRequestRepository.findEffectedVersion(Mockito.any(), Mockito.any()))
//                .thenReturn(Optional.of(new CounterRequest()));
//        opsLimitationService.getAvailableCounterRuleById(1);
//    }

//    @Test
//    public void editLimitationRequest() {
//        UpdateLimitationReq req = UpdateLimitationReq.builder()
//                .status(ECommonStatus.ACTIVE)
//                .endDate(DateUtils.addDays(new Date(), 1))
//                .build();
//
//        LimitationRequest newLimitationRequest = new LimitationRequest();
//        newLimitationRequest.setId(3);
//
//        LimitationRequest limitationRequest = new LimitationRequest();
//        limitationRequest.setApprovalStatus(EApprovalStatus.APPROVED);
//        limitationRequest.setRequestStatus(ECommonStatus.ACTIVE);
//        limitationRequest.setEndDate(DateUtils.addDays(new Date(), 1));
//        limitationRequest.setVersion(1);
//        Mockito.when(requestRepository.findById(1))
//                .thenReturn(Optional.of(limitationRequest));
//        Mockito.when(requestRepository.save(Mockito.any()))
//                .thenReturn(newLimitationRequest);
//
//        opsLimitationService.update(1, req);
//
//    }

//    @Test
//    public void getAvailableLimitations() {
//        FilterLimitationAvailableReq req = FilterLimitationAvailableReq.builder()
//                .businessId(1)
//                .status(ECommonStatus.ACTIVE)
//                .build();
//
//        Pageable mockPageable = Mockito.mock(Pageable.class);
//        Page mockpage = Mockito.mock(Page.class);
//
//        Object[] objects = new Object[]{new Program(), new LimitationRequest()};
//        List list = new ArrayList();
//        list.add(objects);
//
//        Mockito.when(businessRepository.findById(1)).thenReturn(Optional.of(new Business()));
//        Mockito.when(requestRepository.filter(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
//                        .thenReturn(mockpage);
//        Mockito.when(mockpage.getContent()).thenReturn(list);
//        opsLimitationService.getAvailableLimitations(req, mockPageable);
//    }

//    @Test
//    public void getInReviewLimitations() {
//        Pageable mockPageable = Mockito.mock(Pageable.class);
//
//        APIResponse<ChangeRequestPageFeignRes> response = new APIResponse<>();
//        ChangeRequestPageFeignRes res = new ChangeRequestPageFeignRes();
//        ChangeRequestPageFeignRes.ChangeRecordFeginRes record = new ChangeRequestPageFeignRes.ChangeRecordFeginRes();
//        record.setObjectId("1");
//        record.setChangeRequestId(123);
//        res.setRecords(List.of(record));
//        res.setTotalRecordCount(1);
//        response.setData(res);
//
//        Mockito.when(makerCheckerServiceClient.getChangeRequests(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
//                        .thenReturn(response);
//        Mockito.when(requestRepository.findByIdIn(Mockito.any()))
//                        .thenReturn(List.of(new LimitationRequest()));
//        opsLimitationService.getInReviewLimitations(EApprovalStatus.APPROVED, null, null, mockPageable);
//    }

//    @TestConfiguration
//    public static class ConfigurationTest {
//
//        @Bean
//        public OpsLimitationService opsLimitationService() {
//            return new OpsLimitationServiceImpl();
//        }
//    }
}
