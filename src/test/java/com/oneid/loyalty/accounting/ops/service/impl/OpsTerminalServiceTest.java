package com.oneid.loyalty.accounting.ops.service.impl;

import com.oneid.loyalty.accounting.ops.model.res.TerminalRes;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.OpsChainService;
import com.oneid.loyalty.accounting.ops.service.OpsCorporationService;
import com.oneid.loyalty.accounting.ops.service.OpsStoreService;
import com.oneid.loyalty.accounting.ops.service.OpsTerminalService;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.Business;
import com.oneid.oneloyalty.common.entity.Chain;
import com.oneid.oneloyalty.common.entity.Corporation;
import com.oneid.oneloyalty.common.entity.Pos;
import com.oneid.oneloyalty.common.entity.Store;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.BusinessRepository;
import com.oneid.oneloyalty.common.repository.ChainRepository;
import com.oneid.oneloyalty.common.repository.CorporationRepository;
import com.oneid.oneloyalty.common.repository.PosRepository;
import com.oneid.oneloyalty.common.repository.StoreRepository;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.PosService;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
@Ignore
public class OpsTerminalServiceTest {

    @Autowired
    OpsTerminalService opsTerminalService;

    @MockBean
    BusinessRepository businessRepository;

    @MockBean
    CorporationRepository corporationRepository;

    @MockBean
    ChainRepository chainRepository;

    @MockBean
    StoreRepository storeRepository;

    @MockBean
    PosRepository posRepository;

    @MockBean
    PosService posService;

    @MockBean
    OpsBusinessService opsBusinessService;

    @MockBean
    OpsCorporationService opsCorporationService;

    @MockBean
    OpsChainService opsChainService;

    @MockBean
    OpsStoreService opsStoreService;

    Pos pos1 = new Pos();

    @Before
    public void before() {
        Business business = new Business();
        business.setId(1);
        business.setCode("bzncode1");
        business.setName("bznname1");
        business.setStatus(ECommonStatus.ACTIVE);
        Corporation corporation = new Corporation();
        corporation.setId(1);
        corporation.setCode("cprtcode1");
        corporation.setName("cprtcode1");
        corporation.setStatus(ECommonStatus.ACTIVE);
        corporation.setBusinessId(1);
        Chain chain1 = new Chain();
        chain1.setId(1);
        chain1.setCode("chaincode1");
        chain1.setName("chainname1");
        chain1.setStatus(ECommonStatus.ACTIVE);
        chain1.setBusinessId(1);
        chain1.setCorporationId(1);
        Store store1 = new Store();
        store1.setId(1);
        store1.setCode("storecode1");
        store1.setName("storename1");
        store1.setStatus(ECommonStatus.ACTIVE);
        store1.setBusinessId(1);
        store1.setCorporationId(1);
        store1.setChainId(1);

        pos1.setId(1);
        pos1.setCode("storecode1");
        pos1.setName("storename1");
        pos1.setStatus(ECommonStatus.ACTIVE);
        pos1.setBusinessId(1);
        pos1.setCorporationId(1);
        pos1.setChainId(1);
        pos1.setStoreId(1);

        Mockito.when(this.businessRepository.findAll()).thenReturn(Arrays.asList(business));
        Mockito.when(this.corporationRepository.findAll()).thenReturn(Arrays.asList(corporation));
        Mockito.when(this.chainRepository.findAll()).thenReturn(Arrays.asList(chain1));
        Mockito.when(this.posRepository.findAll()).thenReturn(Arrays.asList(pos1));
    }

    @Test
    public void search() {
        Mockito.when(this.posService.find(Mockito.mock(SpecificationBuilder.class), Mockito.any(Pageable.class)))
                .thenReturn(new PageImpl<>(Arrays.asList(pos1)));

        Page<TerminalRes> res = this.opsTerminalService.filter(1, null, null, null, null, null, "A", 0, 10);
        assertEquals(1, res.getTotalElements());
    }

    @Test
    public void get_success() {
        Mockito.when(this.posService.find(1)).thenReturn(java.util.Optional.ofNullable(pos1));

        this.opsTerminalService.get(1);
    }

    @Test
    public void get_fail() {
        try {
            this.opsTerminalService.get(1);
        } catch (BusinessException e) {
            assertEquals(ErrorCode.POS_NOT_FOUND, e.getCode());
        }
    }

    @TestConfiguration
    public static class ConfigurationTest {
        @Bean
        public OpsTerminalService opsTerminalService() {
            return new OpsTerminalServiceImpl();
        }
    }
}
