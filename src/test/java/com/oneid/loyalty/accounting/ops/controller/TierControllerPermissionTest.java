package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.component.constant.EAttributeOperator;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.TierController;
import com.oneid.loyalty.accounting.ops.model.req.*;
import com.oneid.loyalty.accounting.ops.service.OpsTierService;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EConditionType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(TierController.class)
public class TierControllerPermissionTest extends BasedWebMvcTest {
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OpsTierService opsTierService;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    @SuppressWarnings({"rawtypes"})
    public void getInReviewTierRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).getInReviewTiers(any(), any(), any(), any(), any(), any(), any(), any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getInReviewTierRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;

        Mockito.when(opsTierService.getInReviewTiers(any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/in-review")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getInReviewTierRequestById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(1)).getInReviewTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getInReviewTierRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/in-review/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).getInReviewTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes"})
    public void getAvailableTierRequests_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();
        FilterProgramTierReq req = FilterProgramTierReq.builder()
                .businessId(mockBusinessId)
                .build();
        Mockito.verify(opsTierService, times(0)).getAvailableTiers(req, new OffsetBasedPageRequest(0, 20, null));

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    @SuppressWarnings({"rawtypes", "unchecked"})
    public void getAvailableTierRequests_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return (Long) Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .mapToLong(AccessPermission::getCode).sum();
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Integer mockBusinessId = 1;
        FilterProgramTierReq req = FilterProgramTierReq.builder()
                .businessId(mockBusinessId)
                .build();

        Mockito.when(opsTierService.getAvailableTiers(Mockito.any(), Mockito.any()))
                .thenReturn(mockPage);


        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/available")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .queryParam("business_id", mockBusinessId.toString());

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingCounterRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tiers/request/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(getValidEditTierReq()));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).update(any(), any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestEditingCounterRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        Integer mockRequestId = 1;

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tiers/request/{id}/request-approval", mockRequestId)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(getValidEditTierReq()));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        ArgumentCaptor<ProgramTierReq> reqCaptor = ArgumentCaptor.forClass(ProgramTierReq.class);
        ArgumentCaptor<Integer> requestIdCaptor = ArgumentCaptor.forClass(Integer.class);

        Mockito.verify(opsTierService, times(1)).update(requestIdCaptor.capture(), reqCaptor.capture());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(requestIdCaptor.getValue());
        assertNotNull(reqCaptor.getValue());

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getEditTierRequestSetting_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/request/{id}/request-approval", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).getEditTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getEditTierRequestSetting_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER, AccessPermission.EDIT.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/request/{id}/request-approval", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(1)).getEditTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableCounterRequestById_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.TIER))
                .collect(Collectors.toMap(key -> key, key -> {
                    return Arrays.stream(key.getPermissions())
                            .filter(permission -> permission.equals(AccessPermission.VIEW))
                            .collect(Collectors.summingLong(AccessPermission::getCode));
                }));

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(1)).getAvailableTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAvailableCounterRequestById_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/tiers/requests/available/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).getAvailableTier(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestCreatingTierRequest_without_role_access_denied() throws Exception {
        mockOPSAuthenticatedPrincipal(Collections.emptyMap());

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tiers/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(getValidCreateTierReq()));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        Mockito.verify(opsTierService, times(0)).create(any());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void requestCreatingTierRequest_success() throws Exception {
        Map<AccessRole, Long> permissions = new HashMap<AccessRole, Long>();

        permissions.put(AccessRole.TIER, AccessPermission.CREATE.getCode().longValue());
        permissions.put(AccessRole.MAKER_CHECKER, AccessPermission.MAKER_ROLE.getCode().longValue());

        mockOPSAuthenticatedPrincipal(permissions);

        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/tiers/request-approval")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(getValidCreateTierReq()));

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        ArgumentCaptor<ProgramTierReq> reqCaptor = ArgumentCaptor.forClass(ProgramTierReq.class);

        Mockito.verify(opsTierService, times(1)).create(reqCaptor.capture());

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(reqCaptor.getValue());

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    private ProgramTierReq getValidEditTierReq() {
        TierPrivilegeReq tierPrivilegeReq = new TierPrivilegeReq();
        tierPrivilegeReq.setId(1);
        tierPrivilegeReq.setName("name");
        tierPrivilegeReq.setEnName("enName");
        tierPrivilegeReq.setBrand("brand");
        tierPrivilegeReq.setDisplayOrder(1);

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setId(1);
        ruleConditionReq.setAttribute("attribute");
        ruleConditionReq.setValue("value");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);


        RuleReq ruleReq = new RuleReq();
        ruleReq.setId(1);
        ruleReq.setName("name");
        ruleReq.setConditionLogic(EConditionType.ANY);
        ruleReq.setConditions(List.of(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());

        ProgramTierReq editTierReq = new ProgramTierReq();
        editTierReq.setName("name");
        editTierReq.setEnName("enName");
        editTierReq.setQualifyPoint(1L);
        editTierReq.setRankNo(1);
        editTierReq.setTierStatus(ECommonStatus.ACTIVE);
        editTierReq.setRules(List.of(ruleReq));
        editTierReq.setPrivileges(List.of(tierPrivilegeReq));

        return editTierReq;
    }

    private ProgramTierReq getValidCreateTierReq() {
        TierPrivilegeReq tierPrivilegeReq = new TierPrivilegeReq();
        tierPrivilegeReq.setId(1);
        tierPrivilegeReq.setName("name");
        tierPrivilegeReq.setEnName("enName");
        tierPrivilegeReq.setBrand("brand");
        tierPrivilegeReq.setDisplayOrder(1);

        RuleConditionReq ruleConditionReq = new RuleConditionReq();
        ruleConditionReq.setId(1);
        ruleConditionReq.setAttribute("attribute");
        ruleConditionReq.setValue("value");
        ruleConditionReq.setOperator(EAttributeOperator.EQUAL);


        RuleReq ruleReq = new RuleReq();
        ruleReq.setId(1);
        ruleReq.setName("name");
        ruleReq.setConditionLogic(EConditionType.ANY);
        ruleReq.setConditions(List.of(ruleConditionReq));
        ruleReq.setStartDate(new Date());
        ruleReq.setEndDate(new Date());

        ProgramTierReq req = new ProgramTierReq();
        req.setName("name");
        req.setEnName("enName");
        req.setCode("code");
        req.setBusinessId(1);
        req.setProgramId(1);
        req.setTierPolicyId(1);
        req.setQualifyPoint(1L);
        req.setRankNo(1);
        req.setTierStatus(ECommonStatus.ACTIVE);
        req.setRules(List.of(ruleReq));
        req.setPrivileges(List.of(tierPrivilegeReq));

        return req;
    }
}
