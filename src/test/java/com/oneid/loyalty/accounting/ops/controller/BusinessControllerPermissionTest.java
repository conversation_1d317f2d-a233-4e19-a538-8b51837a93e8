package com.oneid.loyalty.accounting.ops.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.loyalty.accounting.ops.constant.OpsErrorCode;
import com.oneid.loyalty.accounting.ops.controller.v1.BusinessController;
import com.oneid.loyalty.accounting.ops.model.req.CreateBusinessReq;
import com.oneid.loyalty.accounting.ops.model.req.UpdateBusinessReq;
import com.oneid.loyalty.accounting.ops.service.OpsBusinessService;
import com.oneid.loyalty.accounting.ops.service.outbound.makerchecker.AbsGetApproveRes;
import com.oneid.loyalty.accounting.ops.service.outbound.model.data.AbstractRes;
import com.oneid.loyalty.accounting.ops.support.BasedWebMvcTest;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessPermission;
import com.oneid.loyalty.accounting.ops.support.web.acl.AccessRole;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.model.BaseResponseData;
import com.oneid.oneloyalty.common.model.Meta;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Import(BusinessController.class)
public class BusinessControllerPermissionTest extends BasedWebMvcTest {

    @Autowired
    MockMvc mockMvc;

    @MockBean
    OpsBusinessService businessService;

    @Autowired
    ObjectMapper objectMapper;

    @After
    public void teardown() {
        defaultTeardown();
    }

    @Test
    public void getAll_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.BUSINESS))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        mockOPSAuthenticatedPrincipal(permissions);

        Page mockPage = Mockito.mock(Page.class);

        Mockito.when(mockPage.getContent()).thenReturn(Collections.emptyList());
        Mockito.when(mockPage.getTotalElements()).thenReturn(0L);

        Mockito.when(businessService.search(Mockito.any(), Mockito.any())).thenReturn(mockPage);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/business/search")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getAll_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(businessService.search(Mockito.any(), Mockito.any())).thenReturn(null);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/business/search")
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void updated_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.BUSINESS))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.EDIT))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        AbstractRes.Meta meta = new AbstractRes.Meta();
        meta.setCode(200);
        meta.setMessage("SUCCESS");
        AbsGetApproveRes response = new AbsGetApproveRes();
        response.setMeta(meta);
        Mockito.when(businessService.getApproveUpdate(Mockito.any())).thenReturn(response);

        UpdateBusinessReq req = new UpdateBusinessReq();
        req.setName("name");
        req.setStatus("A");
        req.setStartDate(1L);
        req.setEndDate(2L);
        req.setContactPerson("contact person");
        req.setAddress1("add ress");
        req.setPhone("0935170032");
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/business/change/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req))
                .header("Authorization", "author");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void update_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(businessService.search(Mockito.any(), Mockito.any())).thenReturn(null);

        UpdateBusinessReq req = new UpdateBusinessReq();
        req.setName("name");
        req.setStatus("A");
        req.setStartDate(1L);
        req.setEndDate(2L);
        req.setContactPerson("contact person");
        req.setAddress1("add ress");
        req.setPhone("0935170032");
        MockHttpServletRequestBuilder servletRequestBuilder = put("/v1/business/change/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req))
                .header("Authorization", "author");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void create_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.BUSINESS))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.CREATE))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        AbstractRes.Meta meta = new AbstractRes.Meta();
        meta.setCode(200);
        meta.setMessage("SUCCESS");
        AbsGetApproveRes response = new AbsGetApproveRes();
        response.setMeta(meta);
        Mockito.when(businessService.getApproveCreate(Mockito.any())).thenReturn(response);

        CreateBusinessReq req = new CreateBusinessReq();
        req.setCode("code");
        req.setName("name");
        req.setStatus("A");
        req.setStartDate(1L);
        req.setEndDate(2L);
        req.setContactPerson("contact person");
        req.setAddress1("add ress");
        req.setPhone("0935170032");
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/business/change")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req))
                .header("Authorization", "author");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void create_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(businessService.search(Mockito.any(), Mockito.any())).thenReturn(null);


        CreateBusinessReq req = new CreateBusinessReq();
        req.setCode("code");
        req.setName("name");
        req.setStatus("A");
        req.setStartDate(1L);
        req.setEndDate(2L);
        req.setContactPerson("contact person");
        req.setAddress1("add ress");
        req.setPhone("0935170032");
        MockHttpServletRequestBuilder servletRequestBuilder = post("/v1/business/change")
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(contentToString(req))
                .header("Authorization", "author");

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }

    @Test
    public void getOne_success() throws Exception {
        Map<AccessRole, Long> permissions = Arrays.stream(AccessRole.values())
                .filter(role -> role.equals(AccessRole.BUSINESS))
                .collect(Collectors.toMap(
                        key -> key,
                        key -> Arrays
                                .stream(key.getPermissions())
                                .filter(permission -> permission.equals(AccessPermission.VIEW))
                                .mapToLong(AccessPermission::getCode).sum()));

        mockOPSAuthenticatedPrincipal(permissions);

        mockOPSAuthenticatedPrincipal(permissions);

        Mockito.when(businessService.getOne(Mockito.any())).thenReturn(null);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/business/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(ErrorCode.SUCCESS.getValue(), meta.getCode());
    }

    @Test
    public void getOne_unsuccess() throws Exception {
        mockOPSAuthenticatedPrincipal(new HashMap<>());
        Mockito.when(businessService.search(Mockito.any(), Mockito.any())).thenReturn(null);

        MockHttpServletRequestBuilder servletRequestBuilder = get("/v1/business/{id}", 1)
                .accept(MediaType.APPLICATION_JSON_VALUE);

        MvcResult result = mockMvc.perform(servletRequestBuilder)
                .andExpect(status().isOk())
                .andReturn();

        BaseResponseData baseResponseData = objectMapper.readValue(result.getResponse().getContentAsByteArray(), BaseResponseData.class);

        assertNotNull(baseResponseData);

        Meta meta = baseResponseData.getMeta();

        assertNotNull(meta);

        assertEquals(OpsErrorCode.ACCESS_DENIED.getValue(), meta.getCode());
    }
}
