//package com.oneid.loyalty.accounting.ops.service.impl;
//
//import com.oneid.loyalty.accounting.ops.constant.EBalanceExpirePolicyType;
//import com.oneid.loyalty.accounting.ops.constant.ECommonStatus;
//import com.oneid.loyalty.accounting.ops.constant.ERetentionPolicyType;
//import com.oneid.loyalty.accounting.ops.entity.*;
//import com.oneid.loyalty.accounting.ops.model.req.BalanceExpirePolicyReq;
//import com.oneid.loyalty.accounting.ops.model.req.PoolCreateReq;
//import com.oneid.loyalty.accounting.ops.model.req.PoolUpdateReq;
//import com.oneid.loyalty.accounting.ops.model.req.RetentionPolicyReq;
//import com.oneid.loyalty.accounting.ops.model.res.PoolDetailRes;
//import com.oneid.loyalty.accounting.ops.model.res.PoolRes;
//import com.oneid.loyalty.accounting.ops.repository.*;
//import com.oneid.loyalty.accounting.ops.service.PoolService;
//import com.oneid.loyalty.accounting.ops.util.OffsetBasedPageRequest;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageImpl;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//import java.util.Date;
//import java.util.Optional;
//
//import static org.junit.Assert.assertEquals;
//
//@RunWith(SpringRunner.class)
//public class PoolServiceTest {
//    @MockBean
//    PoolRepository poolRepository;
//
//    @MockBean
//    CurrencyRepository currencyRepository;
//
//    @MockBean
//    WlPartnerRepository partnerRepository;
//
//    @MockBean
//    RetentionPolicyRepository retentionPolicyRepository;
//
//    @MockBean
//    BalanceExpirePolicyRepository balanceExpirePolicyRepository;
//
//    @Autowired
//    PoolService poolService;
//
//    String codeCurrency1 = "VND";
//    ECommonStatus statusCurrency1 = ECommonStatus.ACTIVE;
//    String codeCurrency2 = "USD";
//    ECommonStatus statusCurrency2 = ECommonStatus.ACTIVE;
//    String codeCurrency3 = "YEN";
//    ECommonStatus statusCurrency3 = ECommonStatus.ACTIVE;
//    String partnerCode = "partnerCode";
//    ECommonStatus statusPartner = ECommonStatus.ACTIVE;
//    Integer retentionId = 2;
//    ERetentionPolicyType retentionType = ERetentionPolicyType.N_DAY;
//    Integer retentionPeriodDay = 5;
//    Integer expireId = 4;
//    EBalanceExpirePolicyType expireType = EBalanceExpirePolicyType.FIX_TIME;
//    Integer expirePeriod = 10;
//    Date expireFixTimeExpire = new Date();
//    String poolId = "pi1";
//    String poolName = "pn1";
//    ECommonStatus poolStatus = ECommonStatus.ACTIVE;
//    PoolDefinition poolDefinition;
//
//    @Before
//    public void before() {
//        // Prepare data
//        Currency currency1 = new Currency();
//        currency1.setCode(codeCurrency1);
//        currency1.setStatus(statusCurrency1);
//
//        Currency currency2 = new Currency();
//        currency2.setCode(codeCurrency2);
//        currency2.setStatus(statusCurrency2);
//
//        Currency currency3 = new Currency();
//        currency2.setCode(codeCurrency3);
//        currency2.setStatus(statusCurrency3);
//
//        WlPartner wlPartner = new WlPartner();
//        wlPartner.setPartnerCode(partnerCode);
//        wlPartner.setStatus(statusPartner.getValue());
//
//        RetentionPolicy retentionPolicy = new RetentionPolicy();
//        retentionPolicy.setId(retentionId);
//        retentionPolicy.setType(retentionType);
//        retentionPolicy.setPeriodDay(retentionPeriodDay);
//
//        BalanceExpirePolicy balanceExpirePolicy = new BalanceExpirePolicy();
//        balanceExpirePolicy.setId(expireId);
//        balanceExpirePolicy.setType(expireType);
//        balanceExpirePolicy.setPeriod(expirePeriod);
//        balanceExpirePolicy.setFixTimeExpire(expireFixTimeExpire);
//
//        poolDefinition = new PoolDefinition();
//        poolDefinition.setPoolId(poolId);
//        poolDefinition.setName(poolName);
//        poolDefinition.setPartnerCode(partnerCode);
//        poolDefinition.setCurrencyCode(codeCurrency1);
//        poolDefinition.setRetentionPolicyId(retentionId);
//        poolDefinition.setBalanceExpiredPolicyId(expireId);
//        poolDefinition.setStatus(poolStatus);
//
//        // Mock
//        Mockito.when(currencyRepository.findOne(codeCurrency1)).thenReturn(currency1);
//        Mockito.when(currencyRepository.findOne(codeCurrency2)).thenReturn(currency2);
//        Mockito.when(currencyRepository.findById(codeCurrency2)).thenReturn(Optional.of(currency2));
//        Mockito.when(currencyRepository.findOne(codeCurrency3)).thenReturn(currency3);
//        Mockito.when(partnerRepository.findById(partnerCode)).thenReturn(Optional.of(wlPartner));
//        Mockito.when(retentionPolicyRepository.getOne(retentionId)).thenReturn(retentionPolicy);
//        Mockito.when(balanceExpirePolicyRepository.getOne(expireId)).thenReturn(balanceExpirePolicy);
//        Mockito.when(poolRepository.findById(poolId)).thenReturn(Optional.of(poolDefinition));
//    }
//
//    @Test
//    public void getDetail() {
//        PoolDetailRes result = this.poolService.getPool(poolId);
//
//        assertEquals(poolId, result.getPoolId());
//        assertEquals(poolName, result.getName());
//        assertEquals(partnerCode, result.getPartnerCode());
//        assertEquals(codeCurrency1, result.getCurrencyCode());
//        assertEquals(retentionId, result.getRetentionPolicy().getId());
//        assertEquals(retentionType.getValue(), result.getRetentionPolicy().getType());
//        assertEquals(retentionPeriodDay, result.getRetentionPolicy().getPeriodDay());
//        assertEquals(expireId, result.getBalanceExpiredPolicy().getId());
//        assertEquals(expireType.getValue(), result.getBalanceExpiredPolicy().getType());
//        assertEquals(expirePeriod, result.getBalanceExpiredPolicy().getPeriod());
//        assertEquals(ECommonStatus.ACTIVE.getValue(), result.getStatus());
//    }
//
//    @Test
//    public void getList() {
//        int total = 1;
//        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(0, 10);
//        Page<PoolDefinition> page = new PageImpl<>(Arrays.asList(poolDefinition), pageRequest, total);
//        Mockito.when(poolRepository.filter(null, null, null, ECommonStatus.ACTIVE, pageRequest))
//                .thenReturn(page);
//        Page<PoolRes> result = this.poolService.filterPool(null, null, null, ECommonStatus.ACTIVE.getValue(), 0, 10);
//        assertEquals(total, result.getTotalElements());
//    }
//
//    @Test
//    public void create() {
//        PoolCreateReq poolCreateReq = new PoolCreateReq();
//        poolCreateReq.setPoolId("pi2");
//        poolCreateReq.setName("pn2");
//        poolCreateReq.setDescription("pd2");
//        poolCreateReq.setCurrencyCode(codeCurrency2);
//        poolCreateReq.setPartnerCode(partnerCode);
//        poolCreateReq.setRetentionPolicy(new RetentionPolicyReq());
//        poolCreateReq.getRetentionPolicy().setType(ERetentionPolicyType.N_DAY.getValue());
//        poolCreateReq.getRetentionPolicy().setPeriodDay(5);
//        poolCreateReq.setBalanceExpirePolicy(new BalanceExpirePolicyReq());
//        poolCreateReq.getBalanceExpirePolicy().setType(EBalanceExpirePolicyType.FIX_TIME.getValue());
//        poolCreateReq.getBalanceExpirePolicy().setPeriod(10);
//        poolCreateReq.getBalanceExpirePolicy().setFixTimeExpire(new Date());
//        poolCreateReq.setStatus(ECommonStatus.ACTIVE.getValue());
//
//        // Mock
//        Mockito.when(poolRepository.findById("pi2")).thenReturn(Optional.empty());
//
//        // Test
//        this.poolService.addPool(poolCreateReq);
//    }
//
//    @Test
//    public void update() {
//        PoolUpdateReq poolUpdateReq = new PoolUpdateReq();
//        poolUpdateReq.setName("pn2");
//        poolUpdateReq.setDescription("pd2");
//        poolUpdateReq.setCurrencyCode(codeCurrency2);
//        poolUpdateReq.setPartnerCode(partnerCode);
//        poolUpdateReq.setRetentionPolicy(new RetentionPolicyReq());
//        poolUpdateReq.getRetentionPolicy().setType(ERetentionPolicyType.N_DAY.getValue());
//        poolUpdateReq.getRetentionPolicy().setPeriodDay(5);
//        poolUpdateReq.setBalanceExpirePolicy(new BalanceExpirePolicyReq());
//        poolUpdateReq.getBalanceExpirePolicy().setType(EBalanceExpirePolicyType.FIX_TIME.getValue());
//        poolUpdateReq.getBalanceExpirePolicy().setPeriod(10);
//        poolUpdateReq.getBalanceExpirePolicy().setFixTimeExpire(new Date());
//        poolUpdateReq.setStatus(ECommonStatus.ACTIVE.getValue());
//
//        // Mock
//        Mockito.when(poolRepository.findById(poolId)).thenReturn(Optional.of(poolDefinition));
//
//        // Test
//        this.poolService.updatePool(poolId, poolUpdateReq);
//    }
//
//    @TestConfiguration
//    public static class ConfigurationTest {
//        @Bean
//        public PoolService poolService() {
//            return new PoolServiceImpl();
//        }
//    }
//
//}
