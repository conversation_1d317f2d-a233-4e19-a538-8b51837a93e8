<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <server>
      <username>${ARTIFACTORY_USER}</username>
      <password>${ARTIFACTORY_PASSWORD}</password>
      <id>central</id>
    </server>
    <server>
      <username>${ARTIFACTORY_USER}</username>
      <password>${ARTIFACTORY_PASSWORD}</password>
      <id>snapshots</id>
    </server>
  </servers>
  <profiles>
    <profile>
      <repositories>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>central</id>
          <name>maven-releases</name>
          <url>https://artifactory.int.vinid.dev:443/artifactory/maven-releases</url>
        </repository>
        <repository>
          <snapshots />
          <id>snapshots</id>
          <name>maven-snapshots</name>
          <url>https://artifactory.int.vinid.dev:443/artifactory/maven-snapshots</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>central</id>
          <name>libs-release</name>
          <url>https://artifactory.int.vinid.dev:443/artifactory/libs-release</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots />
          <id>snapshots</id>
          <name>libs-snapshot</name>
          <url>https://artifactory.int.vinid.dev:443/artifactory/libs-snapshot</url>
        </pluginRepository>
      </pluginRepositories>
      <id>artifactory</id>
    </profile>
  </profiles>
  <activeProfiles>
    <activeProfile>artifactory</activeProfile>
  </activeProfiles>
</settings>
